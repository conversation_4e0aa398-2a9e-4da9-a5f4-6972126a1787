import logging
import os
import uuid
from concurrent_log_handler import ConcurrentTimedRotatingFileHandler
import socket


class Logger:
    _instance = None  # 单例模式

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(Logger, cls).__new__(cls)
            cls._instance._init_logger()
        return cls._instance

    def get_local_ip(self):
        try:
            # 创建一个 UDP socket，连接到外部地址以获取本地 IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))  # 连接到 Google 的公共 DNS
                local_ip = s.getsockname()[0]  # 获取本地 IP 地址
            return local_ip
        except Exception as e:
            print(f"获取服务器IP错误: {e}")
            return "127.0.0.1"

    def _init_logger(self):
        """初始化日志配置"""
        LOGS_DIR = os.path.abspath("logs")
        os.makedirs(LOGS_DIR, exist_ok=True)
        log_file = os.path.join(LOGS_DIR, 'hydee-auto_server.info.log')

        self.logger = logging.getLogger("hydee-auto_server")
        self.logger.setLevel(logging.INFO)

        if not self.logger.handlers:  # 避免重复添加handler
            # 1. 定义日志格式 |服务名|主机IP|链路ID|||时间戳|线程信息|日志级别|文件名:行号|消息内容
            log_format = (
                "|%(service)s|%(hostname)s|%(link_id)s|||"
                "%(asctime)s%(msecs)03d|%(threadName)s:%(thread)s|"
                "%(levelname)s|%(filename)s:%(lineno)d|%(message)s"
            )
            formatter = logging.Formatter(
                fmt=log_format,
                datefmt='%Y%m%d%H%M%S'  # 时间格式精确到毫秒
            )

            # 2. 创建按天切分的Handler
            handler = ConcurrentTimedRotatingFileHandler(
                filename=log_file,
                when="midnight",  # 每天午夜切割
                interval=1,
                backupCount=7,  # 保留7天日志
                chmod=0o644,
                encoding="utf-8",
                utc=False
            )
            handler.suffix = "%Y-%m-%d"  # 日志文件后缀格式
            handler.setFormatter(formatter)

            # 控制台 Handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.DEBUG)
            console_handler.setFormatter(formatter)

            # 3. 添加默认字段（链路ID、主机IP等）
            extra_fields = {
                'service': 'hydee-auto_server',
                'hostname': self.get_local_ip(),
                'link_id': os.getenv('LINK_ID', uuid.uuid4().hex),
                'classname': ''  # 默认空，可通过extra参数覆盖
            }

            # 4. 使用Filter注入额外字段
            class ExtraFieldsFilter(logging.Filter):
                def filter(self, record):
                    for k, v in extra_fields.items():
                        setattr(record, k, v)
                    return True

            handler.addFilter(ExtraFieldsFilter())
            self.logger.addHandler(handler)
            self.logger.addHandler(console_handler) # 控制台输出
            self.logger.propagate = False  # 防止重复日志

    @classmethod
    def get_logger(cls):
        """获取日志实例"""
        if not cls._instance:
            cls._instance = Logger()
        return cls._instance.logger


# 使用示例
if __name__ == "__main__":
    logger = Logger.get_logger()
    logger.info("测试信息", extra={'classname': 'TestClass'})