# -*- coding:utf-8 -*-
import random
from core.commonUtils.address import addr

'''
@File  :phoneAndIDUtil.py
@Author:Pengle
@Date  :2021/3/31 14:14
@Email  :<EMAIL>
@Desc  :
'''

'''
排列顺序从左至右依次为：六位数字地址码，八位数字出生日期码，三位数字顺序码和一位校验码:
1、地址码 
表示编码对象常住户口所在县(市、旗、区)的行政区域划分代码，按GB/T2260的规定执行。
2、出生日期码 
表示编码对象出生的年、月、日，按GB/T7408的规定执行，年、月、日代码之间不用分隔符。 
3、顺序码 
表示在同一地址码所标识的区域范围内，对同年、同月、同日出生的人编定的顺序号，顺序码的奇数分配给男性，偶数分配给女性。 
4、校验码计算步骤
    (1)十七位数字本体码加权求和公式 
    S = Sum(Ai * Wi), i = 0, ... , 16 ，先对前17位数字的权求和 
    Ai:表示第i位置上的身份证号码数字值(0~9) 
    Wi:7 9 10 5 8 4 2 1 6 3 7 9 10 5 8 4 2 （表示第i位置上的加权因子）
    (2)计算模 
    Y = mod(S, 11)
    (3)根据模，查找得到对应的校验码 
    Y: 0 1 2 3 4 5 6 7 8 9 10 
    校验码: 1 0 X 9 8 7 6 5 4 3 2
原文：https://blog.csdn.net/caoxinjian423/article/details/81027029 
'''

# 身份证类
class IdNumeber(object):
    def __init__(self):
        super(IdNumeber, self).__init__()

    #核心方法
    def getRandomIdNum(self):
        num17 = str(self.getAddressNum()) + self.getBirthNum() + self.getNum()
        idNum = num17 + str(self.getCheckNum(num17))
        return idNum

    # 获取六位地址码
    def getAddressNum(self):
        addrIndex = random.randint(0, len(addr) - 1)
        addrNum, addrName = addr[addrIndex]
        return addrNum

    # 获取八位生辰码
    def getBirthNum(self):
        year = self.getYear()
        month = self.getMonth()
        birthNum = str(year) + month + self.getDay(year, month)
        return birthNum

    # 获取降生时的三位编号
    def getNum(self):
        num = ''
        for x in range(2):
            num += str(random.randint(0, 9))
        num += str(random.randrange(random.randint(0, 1), 9, 2))
        return num

    # 获取最后一位校验码
    def getCheckNum(self, num17):
        Wi = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        checkCode = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
        zipWiNum17 = zip(list(num17), Wi)
        S = sum(int(i) * j for i, j in zipWiNum17)
        Y = S % 11
        return checkCode[Y]

    # 获取随机年份
    def getYear(self):
        year = random.randint(1949, 2019)
        return year

    # 获取随机月份
    def getMonth(self):
        monthList = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
        month = monthList[random.randint(0, len(monthList) - 1)]
        return month

    # 获取随机日期
    def getDay(self, year, month):
        if month in ('01', '03', '05', '07', '08', '10', '12'):
            tempDay = random.randint(1, 31)
        elif month in ('04', '06', '09', '11'):
            tempDay = random.randint(1, 30)
        else:
            if (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0):
                tempDay = random.randint(1, 29)
            else:
                tempDay = random.randint(1, 28)
        if tempDay < 10:
            day = '0' + str(tempDay)
        else:
            day = str(tempDay)
        return day


# 生成随机手机号码
class PhoneNumber(object):
    """docstring for PhoneNumber"""

    def __init__(self):
        super(PhoneNumber, self).__init__()

    def getPhoneNumber(self):
        numList = ["130", "131", "132", "133", "134", "135", "136", "137", "138", "139",
                   "140", "145", "146", "147", "148", "149", "162", "165", "166", "167",
                   "150", "151", "152", "153", "155", "156", "157", "158", "159",
                   "170", "171", "173", "176", "177", "178", "198", "199",
                   "180", "181", "183", "184", "186", "187", "188", "189"]
        num8 = "".join(random.choice("0123456789") for i in range(8))
        phoneNumber = str(random.choice(numList)) + num8
        return phoneNumber
