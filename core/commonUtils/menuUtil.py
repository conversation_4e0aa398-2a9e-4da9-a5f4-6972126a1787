'''
@Date  : 2021/4/30 15:06
@Desc  : 根据用户角色，获取菜单信息
'''

from base.models import *


def getMenu(roleId, keyRolePermissions):
    rightInfo = RightInfo.objects.filter(is_active=1)
    # 处理一级菜单
    rootPermissionsResult = {}
    for right in rightInfo:
        if right.right_level == '1':
            if roleId != 0:
                if right.right_id not in keyRolePermissions:
                    continue
            rootPermissionsResult[right.right_id] = {
                "id": right.right_id,
                "authName": right.right_name,
                "path": right.right_path,
                "children": []
            }
    # 处理二级菜单
    for right in rightInfo:
        if right.right_level == '2':
            if roleId != 0:
                if right.right_id not in keyRolePermissions:
                    continue
            parentPermissionResult = rootPermissionsResult[right.parent_right_id]
            parentPermissionResult['children'].append({
                "id": right.right_id,
                "authName": right.right_name,
                "path": right.right_path,
                "children": []
            })
    # 处理三级菜单
    for right in rightInfo:
        if right.right_level == '3':
            if roleId != 0:
                if right.right_id not in keyRolePermissions:
                    continue
            parentPermissionResult = rootPermissionsResult[right.grandpa_right_id]
            for item in parentPermissionResult['children']:
                if item['id'] == right.parent_right_id:
                    item['children'].append({
                        "id": right.right_id,
                        "authName": right.right_name,
                        "path": right.right_path
                    })
    rightList = list(rootPermissionsResult.values())
    return rightList


def userMenu(roleId):
    if roleId == 0:
        rightList = getMenu(0, None)
    else:
        roleInfo = RoleInfo.objects.get(role_id = roleId)
        rolePermissions = list(map(int, roleInfo.role_rights.split(',')))
        rightList = getMenu(roleId, rolePermissions)
    return rightList
