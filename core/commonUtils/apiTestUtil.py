# -*- coding:utf-8 -*-
import json
import traceback
import requests
import os
import time
import io
from requests.adapters import HTTPA<PERSON>pter
from requests.packages.urllib3.util.retry import Retry
#日志对象实例化
from urllib3.exceptions import InsecureRequestWarning

from core.commonUtils.logger import Logger
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()
'''
@File  :apiTestUtil.py
@Date  :2021/4/1 11:23
@Desc  :接口测试类   该类为接口API测试的底层运行逻辑
'''

class APITest:
    """docstring for ClassName"""

    # 初始化函数，构造函数
    def __init__(self, type, url, bodydata={}, header=None):
        default_header = {
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Connection": "close",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36",
            "Content-Type": "application/json;charset=UTF-8"
        }
        self.url = url
        self.type = type
        self.file_content = None
        self.last_modified_time = 0
        if type != "x-www-form-urlencoded_POST" and type != "multipart/form-data_POST" and bodydata:
            self.bodydata = json.loads(bodydata, strict=False)
        else:
            self.bodydata = bodydata
        if header:
            self.header = json.loads(header)
            # 去除默认ua头信息中的 python-requests
            self.header["User-Agent"] = self.header.get("User-Agent", default_header["User-Agent"])
        else:
            self.header = default_header

    # 核心方法，实现接口的get/post等请求
    def doAutoAPITest(self):
        # 解决Max retries exceeded with url问题
        requests.adapters.DEFAULT_RETRIES = 3  # 增加重试连接次数
        req = requests.session()
        req.keep_alive = False  # 关闭多余连接
        # 增加重连机制
        retry = Retry(total=3, backoff_factor=0.5, status_forcelist=[500, 502, 503, 504])
        adapter = HTTPAdapter(max_retries=retry)
        req.mount('http://', adapter)
        req.mount('https://', adapter)

        respose_dict = {}
        default_timeout = 120
        response = None
        try:
            if self.type == "POST":
                response = req.post(self.url, json=self.bodydata, headers=self.header, timeout=default_timeout,
                                         verify=False)
            elif self.type == "multipart/form-data_POST":
                bodydata = json.loads(self.bodydata)
                if not bodydata.get("upload_param") or bodydata["upload_param"] == "":
                    bodydata["upload_param"] = "{}"
                # 使用pop移除并获取upload_param字典
                upload_param = json.loads(bodydata.pop("upload_param", "{}"))
                # 将upload_param中的所有键值对更新到bodydata中
                bodydata.update(upload_param)
                # 准备表单字段数据, 提取除了 'name' 之外的所有参数到 form_data 中（如果有的话）
                form_data = {key: value for key, value in upload_param.items() if key != 'name'}

                # 取出值为list的键值对, 转为JSON 数组
                result = {key: value for key, value in form_data.items() if isinstance(value, list)}
                logger.info(f"result: {result}")
                if result:
                    # 遍历 result，打印 key 和 value
                    for key, value in result.items():
                        # 更新form_data中的值为list的键值对
                        form_data[key] = json.dumps(value)

                # 准备文件数据
                file_path = bodydata.get("template_file")  # 从bodydata中获取文件路径
                # 如果模版文件不存在，则取数据文件
                if not file_path or not os.path.exists(file_path):
                    file_path = bodydata.get("file_path")
                file_name = upload_param.get("name", "file")  # 从upload_param中获取文件字段名
                file_obj = open(file_path, 'rb')
                files = {file_name: file_obj}  # 创建文件数据字典
                response = req.post(
                    url=self.url,
                    data=form_data if form_data else None,
                    files=files,
                    headers=self.header,
                    timeout=default_timeout,
                    verify=False  # 不启用SSL验证
                )
                file_obj.close()
            elif self.type == "x-www-form-urlencoded_POST":
                self.bodydata = str(self.bodydata).replace("+", "%2B")
                self.bodydata = self.bodydata.encode("utf-8")
                response = req.post(self.url, data=self.bodydata, headers=self.header, timeout=default_timeout,verify=False)
            elif self.type == "PUT":
                response = req.put(self.url, json=self.bodydata, headers=self.header, timeout=default_timeout,
                                        verify=False)
            elif self.type == "DELETE":
                response = req.delete(self.url, json=self.bodydata, headers=self.header, timeout=default_timeout,
                                           verify=False)
            elif self.type == "PATCH":
                response = req.patch(self.url, data=self.bodydata, headers=self.header, timeout=default_timeout,
                                          verify=False)
            else:
                response = req.get(self.url, params=self.bodydata, headers=self.header, timeout=default_timeout,
                                        verify=False)
            # response.close()  # 解决Max retries exceeded with url 的错误
            respose_dict["response"] = response
            respose_dict["status_code"] = response.status_code
            # respose_dict["result"] = response.text
            # headers["Content-Type"]=json时,将HTTP响应的文本内容转换为字典，再转回JSON格式的字符串，存入respose_dict的"result"键中，确保中文不被转义
            content_type = response.headers.get("Content-Type", "")
            if content_type == "application/json;charset=utf-8":
                respose_dict["result"] = json.dumps(json.loads(response.text), ensure_ascii=False)
            else:
                respose_dict["result"] = response.text
            respose_dict["time"] = str(response.elapsed.total_seconds())
            respose_dict["request_header"] = response.request.headers
            respose_dict["header"] = response.headers
            if "traceId" in response.headers:
                respose_dict["traceId"] = response.headers["traceId"]
            if "Set-Cookie" in response.headers:
                respose_dict["Set-Cookie"] = response.headers["Set-Cookie"]

            return respose_dict
        except Exception as e:
            error_info = traceback.format_exc()
            respose_dict["error_info"] = e
            logger.info("执行接口案例报错：%s" % error_info)
            raise e
