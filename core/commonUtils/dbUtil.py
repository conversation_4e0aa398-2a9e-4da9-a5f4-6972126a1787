import cx_Oracle, pymysql, pymssql, traceback
import redis

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()
'''
@File  :dbUtil.py
@Date  :2021/3/31 14:20
@Desc  :数据库操作类
'''

class DBUtils:

    def __init__(self, dbType, host, account, pwd, dbName):
        self.dbType = dbType
        self.host = host
        self.account = account
        self.pwd = pwd
        self.dbName = dbName
        self.connect = None
        self.cur = None

    # 数据库连接方法，当前仅支持Oracle MySQL SQLServer
    def getConnect(self):
        try:
            if self.dbType == 'MySQL':
                port = 3306
                host = self.host
                if self.host.split(":")[1]:
                    port = int(self.host.split(":")[1])
                    host = self.host.split(":")[0]
                self.connect = pymysql.connect(host=host, port=port, user=self.account, passwd=self.pwd,
                                               db=self.dbName, charset='utf8')
            elif self.dbType == 'Oracle':
                parameter = self.account + '/' + self.pwd + '@' + self.host + '/' + self.dbName
                self.connect = cx_Oracle.connect(parameter)
            elif self.dbType == 'SQLServer':
                self.host = self.host.split(":")[0]
                self.connect = pymssql.connect(host=self.host, user=self.account, password=self.pwd, database=self.dbName, tds_version="7.0")
                # cursor = self.connect.cursor()
                # self.connect = pyodbc.connect(SERVER=self.host, UID=self.account, PWD=self.pwd, DATABASE=self.dbName,
                #                               DRIVER='{SQL Server}', TDS_Version=7.0)
            elif self.dbType == 'Redis':
                port = int(self.host.split(":")[1])
                host = self.host.split(":")[0]
                pool = redis.ConnectionPool(host=host, password=self.pwd, port=port, db=self.dbName,
                                            decode_responses=True)  # 实现一个连接池
                self.connect = redis.Redis(connection_pool=pool)
                print("测试连接：", self.connect.ping())
            else:
                logger.info("数据库类型参数异常：%s", self.dbType)
        except Exception as e:
            return traceback.print_exc()

    # 执行SQL
    def excuteSQL(self, sql):
        self.getConnect()
        # 创建游标
        self.cur = self.connect.cursor()
        # 执行sql
        try:
            print("excuteSQL= " + sql)
            self.cur.execute(sql)
            self.connect.commit()
            result = self.cur.fetchall()
        except Exception as e:
            errorInfo = traceback.format_exc()
            logger.info("执行SQL报错：%s", errorInfo)
            result = None
        finally:
            self.closeDB()
            return result

    # 查询SQL
    def queryDB(self, sql):
        self.getConnect()
        # 创建游标
        self.cur = self.connect.cursor()
        # 执行sql
        try:
            print("执行的sql: 【" + sql+ '】')
            self.cur.execute(sql)
            result = self.cur.fetchall()
            print("sql结果===================================》",result)
            # 将返回结果转换为字典形式,多行数据取最后一行 2022.02.08 huangjiaojiao
            cols = [d[0].lower() for d in self.cur.description]
            b = {}
            for row in result:
                b = dict(zip(cols, row))
        except Exception as e:
            error_info = traceback.format_exc()
            logger.info("执行数据库查询报错：%s", error_info)
            result = None
        finally:
            self.closeDB()
            return b

    # 删除，添加，更新
    def update(self, sql):
        self.getConnect()
        # 创建游标
        self.cur = self.connect.cursor()
        # 执行sql
        result = "执行成功"
        try:
            print("执行的sql: " + sql)
            self.cur.execute(sql)
            self.connect.commit()
        except Exception as e:
            error_info = traceback.format_exc()
            logger.info("执行delete语句报错：%s", error_info)
            result = "执行失败"
            self.connect.rollback()
        finally:
            self.closeDB()
            return result

    #查询key
    #目前仅支持获取字符串、字典格式的数据
    # def queryKey(self, key):
    #     self.getConnect()
    #     try:
    #         result = self.connect.get(key)
    #     except Exception as e:
    #         error_info = traceback.format_exc()
    #         logger.info("执行报错：%s", error_info)
    #     finally:
    #         # 关闭连接
    #         self.connect.connection_pool.disconnect()
    #         return result
    # 目前支持获取字符串、字典格式、Hash的数据
    def queryKey(self, key):
        self.getConnect()
        try:
            if key.find("||") < 0:
                result = self.connect.get(key)
            else:
                hlist = key.split('||')
                name = hlist[0]
                keys = hlist[1]
                result = self.connect.hget(name, keys)
        except Exception as e:
            error_info = traceback.format_exc()
            logger.info("执行报错：%s", error_info)
        finally:
            # 关闭连接
            self.connect.connection_pool.disconnect()
            return result

    # 关闭连接
    def closeDB(self):
        self.cur.close()
        self.connect.close()

    # 测试连接
    def testConnect(self):
        self.getConnect()
        if self.dbType == 'Redis':
            try:
                if self.connect.ping() != True:
                    return "数据库连接失败，请重试！"
                else:
                    return "reids连接成功！"
            except Exception as e:
                error_info = traceback.format_exc()
                print("连接报错：%s", error_info)
                return error_info
        elif self.connect is None:
            return "数据库连接失败，请重试！"
        else:
            self.connect.close()
            return "数据库连接成功"
