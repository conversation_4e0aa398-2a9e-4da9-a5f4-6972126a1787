# -*- coding:utf-8 -*-
import traceback
from threading import Thread
from django.utils.timezone import now, timedelta
from core.commonUtils.apiTestUtil import APITest
from core.commonUtils.jsonUtil import *
from data_config.models import *
from interface_test.models import *
from ui_test.models import *
import hashlib, time, datetime, random, string, json
# 日志对象实例化
from django.conf import settings
import os
import shutil
from django.db.models import Count, Q

from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import base64
# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()
'''
@File  :commonUtil.py
@Date  :2021/3/31 15:11
@Desc  :
'''

# 读取配置文件，获取密钥
key = settings.MEMBER_KEY
KEY_SIZE = settings.KEY_SIZE
BLOCK_SIZE = AES.block_size  # AES块大小是16字节


def async_call(fn):
    def wrapper(*args, **kwargs):
        Thread(target=fn, args=args, kwargs=kwargs).start()

    return wrapper


# 公共类
class CommonUtil:
    def __init__(self):
        super(CommonUtil, self).__init__()

    @async_call
    def recordSystemLog(self, user, content):
        logger.info(content)  # 输出到日志文件
        # 再登记到数据库
        temp_time = self.get_current_ymdhms()
        log = LogRecord(log_type="1", log_content=content, person=user, temp_time=temp_time)
        log.save()

    def str2Md5(self, test):
        md5 = hashlib.md5()  # 创建md5对象
        # 此处必须声明encode
        md5.update(test.encode(encoding='utf-8'))
        # 把输入的旧密码转换为md5格式
        result = md5.hexdigest()
        # 返回加密结果
        return result

    def get_encrypt(self, data: str):
        try:
            # 确保密钥长度符合AES-256要求（32字节）
            key_bytes = key.encode('utf-8').ljust(KEY_SIZE)[:KEY_SIZE]

            # 生成一个随机的IV（在实际应用中，IV应该与密文一起存储或传输）
            iv = key_bytes[:BLOCK_SIZE]

            # 创建Cipher对象，指定AES算法、CBC模式和NoPadding填充（通过手动填充实现）
            cipher = AES.new(key_bytes, AES.MODE_CBC, iv)

            # UTF-8编码数据并填充到块大小的整数倍
            data_bytes = data.encode('utf-8')
            padded_data_bytes = pad(data_bytes, BLOCK_SIZE)

            # 加密
            encrypted_bytes = cipher.encrypt(padded_data_bytes)

            # Base64编码
            encoded_content = base64.b64encode(encrypted_bytes).decode('utf-8')
            return encoded_content

        except Exception as e:
            logger.error(f"加密出错: {e}")
            return None

    def get_decrypt(self, data: str):
        try:
            # 确保密钥长度符合AES-256要求（32字节）
            key_bytes = key.encode('utf-8').ljust(KEY_SIZE)[:KEY_SIZE]

            # 使用密钥的前16个字节作为IV
            iv = key_bytes[:BLOCK_SIZE]

            # 创建Cipher对象，指定AES算法、CBC模式和NoPadding填充（因为Java代码指定了NoPadding，我们需要手动去除填充）
            cipher = AES.new(key_bytes, AES.MODE_CBC, iv)

            # Base64解码
            decoded_data = base64.b64decode(data)

            # 解密
            decrypted_bytes = cipher.decrypt(decoded_data)

            decrypted_str = unpad(decrypted_bytes, BLOCK_SIZE).decode('utf-8').strip()

            # 返回解密后的字符串（去除可能的空白字符）
            return decrypted_str

        except Exception as e:
            logger.error(f"解密出错: {e}")
            return None

    # 获取年月日
    def get_current_ymd(self):
        return time.strftime("%Y-%m-%d")

    # 获取年月
    def get_current_ym(self):
        return time.strftime("%Y-%m")


    # 获取年月日,并可加减日期
    def get_current_ymd_new(self, days=0):
        return (datetime.datetime.now() + datetime.timedelta(days=days)).strftime("%Y-%m-%d")

    # 获取年月日时分秒
    def get_current_ymdhms(self, seconds=0, minutes=0, hours=0, days=0):
        return (datetime.datetime.now() + datetime.timedelta(seconds=seconds) + datetime.timedelta(
            minutes=minutes) + datetime.timedelta(hours=hours) +datetime.timedelta(days=days)).strftime("%Y-%m-%d %H:%M:%S")

    # 获取时间段
    def get_interval_ymdhms(self, start_migration, end_migration):
        start_time = (datetime.datetime.now() + datetime.timedelta(seconds=start_migration[0]) + datetime.timedelta(
            minutes=start_migration[1]) + datetime.timedelta(hours=start_migration[2])).strftime("%Y-%m-%d %H:%M:%S")
        end_time = (datetime.datetime.now() + datetime.timedelta(seconds=end_migration[0]) + datetime.timedelta(
            minutes=end_migration[1]) + datetime.timedelta(hours=end_migration[2])).strftime("%Y-%m-%d %H:%M:%S")
        time_range = start_time + ',' + end_time
        return time_range

    def get_current_ymdhmsf(self):
        return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')

    # 获取时间并转化成整数
    def get_current_timestamp(self):
        return datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')

    # 获取时间并转化成整数
    def get_appoint_timestamp(self, digit):
        t = time.time()
        if digit == 10:
            t = int(t)
        elif digit == 13:
            t = int(round(t * 1000))
        elif digit == 16:
            t = int(round(t * 1000000))
        return t

    # 获取指定长度的随机数字字符
    def get_random_num(self, num):
        if num > 1:
            return random.choice("123456789") + "".join(random.choice("0123456789") for i in range(num - 1))
        elif num == 1:
            return random.choice("123456789") + ""
        else:
            raise ValueError("参数必须大于0！")

    # 获取指定区间内的数字
    def get_num_with_size(self, max: int, min=0):
        if max < 0 or min < 0:
            raise ValueError("max or min must greater equals 0")
        elif max < min:
            raise ValueError("min must less than max")
        else:
            return random.randint(min, max)

    def get_ch_string(self, length):
        '''
    获取指定长度的中文字符串
    :param length: 长度
    :return: 获取指定长度的中文字符串
    '''
        l = []
        if length > 0:
            for _ in range(length):
                zone_num = random.randint(0xb0, 0xd7)
                bit_num = random.randint(0xa1, 0xf9)
                hex = format(zone_num, 'x') + format(bit_num, 'x')
                l.append(bytes().fromhex(hex).decode('gb2312'))
            return "".join(l)
        else:
            raise ValueError("size invalid")

    def get_upper_en_string(self, length):
        '''
    获取指定长度的大写英文字符串
    :param length: 长度
    :return: 获取指定长度的大写英文字符串
    '''
        if length > 0:
            return ''.join([random.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ') for _ in range(length)])
        else:
            raise ValueError("size invalid")

    def get_lower_en_string(self, length):
        '''
    获取指定长度的小写英文字符串
    :param length: 长度
    :return: 获取指定长度的小写英文字符串
    '''
        if length > 0:
            return ''.join([random.choice('abcdefghijklmnopqrstuvwxyz') for _ in range(length)])
        else:
            raise ValueError("size invalid")

    def get_random_secure_key(self, length=16):
        '''
    获取指定长度的随机秘钥
    :param length:
    :return:
    '''
        chars = string.ascii_letters + string.digits
        if length > 0:
            return ''.join([random.choice(chars) for _ in range(length)])
        else:
            raise ValueError("size invalid")

    # 插入新的断言 主要用在接口测试案例的添加、编写时
    def insertAssertResultInfo(self, asserts, username, case_id, is_case_assert=True):
        create_time = self.get_current_ymdhms()
        assert_list = asserts
        assertId = []
        for assert_dict in assert_list:
            if assert_dict['assert_type'] == "1":  # 数据匹配
                assertResultInfo = AssertResultInfo.objects.create(assert_type="1",
                                                                   return_value=assert_dict[
                                                                       'return_value'],
                                                                   assert_operator=assert_dict[
                                                                       'assert_operator'],
                                                                   expect_result=assert_dict[
                                                                       'expect_result'],
                                                                   creater=username,
                                                                   is_case_assert=is_case_assert,
                                                                   create_time=create_time,
                                                                   case_id=case_id)
                asserts = str(assertResultInfo.assert_id)
            else:  # 四则运算
                assertResultInfo = AssertResultInfo.objects.create(assert_type="2",
                                                                   return_value=assert_dict[
                                                                       'return_value'],
                                                                   assert_operator=assert_dict[
                                                                       'assert_operator'],
                                                                   operation_value=assert_dict[
                                                                       'operation_value'],
                                                                   expect_result=assert_dict[
                                                                       'expect_result'],
                                                                   creater=username,
                                                                   is_case_assert=is_case_assert,
                                                                   create_time=create_time,
                                                                   case_id=case_id)
                asserts = str(assertResultInfo.assert_id)
            assertId.append(asserts)
        assertId = '|'.join(assertId)
        return assertId

    # 老断言信息的插入   主要用在WEB-UI案例，安卓-UI案例的添加、编写时
    def insertOldAssertInfo(self, assert_str, username):
        temp_time = self.get_current_ymdhms()
        assertId = []
        for assert_dict in assert_str:
            if assert_dict['assert_type'] == "1":
                assert_info = AssertInfo.objects.create(assert_result=assert_dict['assert_result'],
                                                        creater=username, create_time=temp_time, assert_type="1",
                                                        assert_database="", update_time=temp_time,
                                                        assert_operator=assert_dict['assert_operator'],
                                                        assert_sql="", update_person=username)
                asserts = str(assert_info.assert_id)
            else:
                if isContainsDDL(assert_dict['assert_sql']):
                    return JsonResponse(data=result_json("HD5004", "断言SQL语句包含敏感信息：" + sql_error),
                                        json_dumps_params={'ensure_ascii': False})
                assert_info = AssertInfo.objects.create(assert_type="2",
                                                        assert_result=assert_dict['assert_result'],
                                                        creater=username, create_time=temp_time,
                                                        assert_database=assert_dict['assert_database'],
                                                        assert_operator=assert_dict['assert_operator'],
                                                        assert_sql=assert_dict['assert_sql'],
                                                        update_time=temp_time, update_person=username)
                asserts = str(assert_info.assert_id)
            assertId.append(asserts)
        assertId = '|'.join(assertId)
        return assertId

    # 根据组成信息串 构建案例组成信息  主要用在WEB-UI案例，安卓-UI案例的添加、编写时
    def insertCaseFormation(self, formation_str, username):
        # 案例组成信息，插入案例组成表
        case_formation = []
        for item in formation_str:
            create_time = self.get_current_ymdhms()
            caseFormation = CaseFormationInfo(element_id=item['element_id'], chunk_id=item['chunk_id'],
                                              formation_type=item['formation_type'],
                                              create_time=create_time, creater=username)
            caseFormation.save()
            case_formation.append(str(caseFormation.case_formation_id))
        case_formation = '|'.join(case_formation)
        return case_formation

    def interface_copy(self, interface_id, username):
        try:
            interface = InterfaceInfo.objects.get(interface_id=interface_id)
            interface.interface_id = None
            interface.interface_name = interface.interface_name + CommonUtil().get_current_timestamp()
            interface.create_time = CommonUtil().get_current_ymdhms()
            interface.creater = username
            interface.is_related = 0
            interface.save()
            content = "用户 %s 复制接口成功，案例信息：%s" % (username, interface.__dict__)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return True, "复制成功"
        except Exception as e:
            errorInfo = traceback.format_exc()
            return False, "复制失败，错误信息：%s" % errorInfo

    # 案例复制
    def caseCopy(self, caseId, username):
        try:
            caseInfo = CaseInfo.objects.get(case_id=caseId)
            case_name = caseInfo.case_name + CommonUtil().get_current_timestamp()
            create_time = CommonUtil().get_current_ymdhms()
            # 因为断言关系表与案例是一对一的关系，故作区分设计
            caseInfo.case_id = None
            caseInfo.case_name = case_name
            caseInfo.update_time = create_time
            caseInfo.update_person = username
            caseInfo.creater = username
            caseInfo.create_time = create_time
            caseInfo.is_related = 0
            caseInfo.save()
            case_id = caseInfo.case_id
            if caseInfo.is_assert:
                assert_list = caseInfo.asserts.split("|")
                assertId_list = []
                for id in assert_list:
                    assertInfo = AssertResultInfo.objects.get(assert_id=id)
                    assertInfo.assert_id = None
                    assertInfo.case_id = case_id
                    assertInfo.create_time = create_time
                    assertInfo.save()
                    assertId_list.append(str(assertInfo.assert_id))
                assertId = "|".join(assertId_list)
                CaseInfo.objects.filter(case_id=case_id).update(asserts=assertId)
            if caseInfo.case_type == 'UPLOAD':
                interface_data = json.loads(caseInfo.interface_data)
                if interface_data and 'file_path' in interface_data:
                    file_path = interface_data['file_path']
                    # 获取文件名
                    file_name = os.path.basename(file_path)
                    new_path = os.path.join(settings.FILE_PATH, 'case', str(case_id))
                    # 将参数文件移动到案例目录下
                    if not os.path.exists(new_path):
                        os.makedirs(new_path, exist_ok=True)
                    # 构建目标文件的完整路径
                    target_file_path = os.path.join(new_path, file_name)
                    # 复制文件至指定目录
                    try:
                        shutil.copy(file_path, new_path)
                        # 修改file_path的值
                        interface_data['file_path'] = target_file_path
                        # 更新复制案例的interface_data
                        CaseInfo.objects.filter(case_id=case_id).update(interface_data=json.dumps(interface_data))
                    except Exception as e:
                        raise RuntimeError(f"移动文件时发生错误: {e}")

            content = "用户 %s 复制API案例成功，案例信息：%s" % (username, caseInfo.__dict__)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return True, "复制成功"
        except Exception as e:
            errorInfo = traceback.format_exc()
            return False, "复制失败，错误信息：%s" % errorInfo

    # 场景复制
    def sceneCopy(self, sceneId, username):
        try:
            sceneInfo = SceneInfo.objects.get(scene_id=sceneId)
            scene_name = sceneInfo.scene_name + CommonUtil().get_current_timestamp()
            relation_id = sceneInfo.scene_relation_id
            create_time = CommonUtil().get_current_ymdhms()
            relationId_list = []
            for id in relation_id.split("|"):
                scene_relation = SceneRelation.objects.get(scene_relation_id=id)
                scene_relation.scene_relation_id = None
                scene_relation.create_time = create_time
                scene_relation.save()
                relationId_list.append(str(scene_relation.scene_relation_id))
            relation = "|".join(relationId_list)
            scene = SceneInfo(scene_name=scene_name, scene_describe=sceneInfo.scene_describe,
                              scene_relation_id=relation, business_id=sceneInfo.business_id,
                              creater=username, update_person=username, create_time=create_time,
                              before_param=sceneInfo.before_param, update_time=create_time,
                              scene_type=sceneInfo.scene_type, is_related=0)
            scene.save()
            content = "用户 %s 复制场景成功，复制场景信息：%s" % (username, scene.__dict__)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return True, "复制成功"
        except Exception as e:
            errorInfo = traceback.format_exc()
            return False, "复制失败，错误信息：%s" % errorInfo

    # 更新测试环境请求头
    @async_call
    def updateTestHeader(self):
        interface_data = '{"account":"778899_admin","pwd":"F379EAF3C831B04DE153469D1BEC345E","verificationCode":"","clientId":"18615zr8i2zklrm7rnr"}'
        # 执行接口请求
        test = APITest("POST", "https://merchants.test.ydjia.cn/businesses-gateway/merchant/1.0/acc/_login",
                       interface_data)
        result = json.loads((test.doAutoAPITest())["result"])
        logger.info("当前替换请求头时登录信息：%s" % result)
        # 获取登录后返回的token信息
        data = result["data"]["token"]
        # 从数据库查询
        header = ParameterInfo.objects.get(parameter_name='b_header', is_active=True).test_parameter
        temp = json.loads(header)
        # 执行替换
        temp2 = header.replace(temp['Authorization'], data)
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        ParameterInfo.objects.filter(parameter_name='b_header', is_active=True).update(test_parameter=temp2,
                                                                                       update_time=update_time)

    # 更新预发环境请求头
    @async_call
    def updateUatHeader(self):
        interface_data = '{"account":"444444_admin","pwd":"57FEDE2B01A5E922B5E2B9CC0EAD88F1","verificationCode":"","clientId":"nocf2pvuh1bklu7ssqp"}'
        # 执行接口请求
        test = APITest("POST", "https://merchants.uat.ydjia.cn/businesses-gateway/merchant/1.0/acc/_login",
                       interface_data)
        result = json.loads((test.doAutoAPITest())["result"])
        logger.info("当前替换请求头时登录信息：%s" % result)
        # 获取登录后返回的token信息
        data = result["data"]["token"]
        # 从数据库查询
        header = ParameterInfo.objects.get(parameter_name='b_header', is_active=True).uat_parameter
        temp = json.loads(header)
        # 执行替换
        temp2 = header.replace(temp['Authorization'], data)
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        ParameterInfo.objects.filter(parameter_name='b_header', is_active=True).update(uat_parameter=temp2,
                                                                                       update_time=update_time)

    # 获取首页上数据展示
    def getCount(self):
        data_dict = {}
        test_rate = []
        uat_rate = []
        today_date = now().date() + timedelta(days=0)  # 今天
        test_list = TaskRunningRecord.objects.filter(task_id='53').order_by('time_stamp')[:10]
        for x in test_list:
            # test_rate.append(x.test_pass.replace('%', ''))
            test_rate.append(x.test_pass)
        uat_list = TaskRunningRecord.objects.filter(task_id='54').order_by('time_stamp')[:10]
        for y in uat_list:
            # uat_rate.append(y.test_pass.replace('%', ''))
            uat_rate.append(y.test_pass)
        data_dict['test_rate'] = test_rate
        data_dict['uat_rate'] = uat_rate
        # 场景数据
        scene_stats = SceneInfo.objects.filter(is_active=True).aggregate(
            total_count=Count('scene_id'),
            yesterday_count=Count('scene_id', filter=Q(create_time__lt=today_date)),
            today_count=Count('scene_id', filter=Q(create_time__gte=today_date))
        )
        # 接口数据
        interface_stats = InterfaceInfo.objects.filter(is_active=True).aggregate(
            total_count=Count('interface_id'),
            yesterday_count=Count('interface_id', filter=Q(create_time__lt=today_date)),
            today_count=Count('interface_id', filter=Q(create_time__gte=today_date))
        )
        # 案例数据
        case_stats = CaseInfo.objects.filter(is_active=True).aggregate(
            total_count=Count('case_id'),
            yesterday_count=Count('case_id', filter=Q(create_time__lt=today_date)),
            today_count=Count('case_id', filter=Q(create_time__gte=today_date))
        )
        # 接口任务数据
        task_stats = TaskInfo.objects.filter(is_active=True, task_type='API').aggregate(
            total_count=Count('task_id'),
            yesterday_count=Count('task_id', filter=Q(create_time__lt=today_date)),
            today_count=Count('task_id', filter=Q(create_time__gte=today_date))
        )
        # 核心任务清单场景数
        task_pools = TaskInfo.objects.filter(
            task_id__in=[237, 240, 138, 143, 230]
        ).values('task_id', 'case_pool')
        test_core_scene = sum(
            len(task['case_pool'].split("|"))
            for task in task_pools
            if task['task_id'] in [237, 240, 138] and task['case_pool']
        )
        pro_core_scene = sum(
            len(task['case_pool'].split("|"))
            for task in task_pools
            if task['task_id'] in [143, 230] and task['case_pool']
        )
        # 接口覆盖数
        interface_coverage_num = (
            InterfaceInfo.objects.filter(
                Q(is_active=True, is_related=True) &
                ~Q(interface_name__icontains='yby')
            )
            .values('interface_address')
            .distinct()
            .count()
        )
        # 更新数据字典
        data_dict.update({
            'scene_count': scene_stats['total_count'],
            'yesterday_scene': scene_stats['yesterday_count'],
            'today_scene': scene_stats['today_count'],

            'interface_count': interface_stats['total_count'],
            'yesterday_interface': interface_stats['yesterday_count'],
            'today_interface': interface_stats['today_count'],

            'case_count': case_stats['total_count'],
            'yesterday_case': case_stats['yesterday_count'],
            'today_case': case_stats['today_count'],

            'api_task_count': task_stats['total_count'],
            'yesterday_api_task': task_stats['yesterday_count'],
            'today_api_task': task_stats['today_count'],

            'test_core_scene': test_core_scene,
            'pro_core_scene': pro_core_scene,
            'interface_coverage_num': interface_coverage_num
        })
        return data_dict        # # UI块数
        # chunk_count = ChunkInfo.objects.count()
        # yesterday_chunk = ChunkInfo.objects.filter(create_time__lt=today_date, is_active=True).count()  # 创建日期今天之前的
        # data_dict['chunk_count'] = chunk_count
        # data_dict['yesterday_chunk'] = yesterday_chunk
        # # 元素数
        # # web案例的
        # element_count = ElementInfo.objects.count()
        # yesterday_element = ElementInfo.objects.filter(create_time__lt=today_date, is_active=True).count()  # 创建日期今天之前的
        # # 安卓案例的
        # appElement_count = AndroidElementInfo.objects.count()
        # yesterday_appElement = AndroidElementInfo.objects.filter(create_time__lt=today_date,
        #                                                          is_active=True).count()  # 创建日期今天之前的
        # data_dict['element_count'] = element_count + appElement_count
        # data_dict['yesterday_element'] = yesterday_element + yesterday_appElement
        # # web案例数
        # # web案例的
        # webcase_count = WebCaseInfo.objects.count()
        # yesterday_webcase = WebCaseInfo.objects.filter(create_time__lt=today_date, is_active=True).count()  # 创建日期今天之前的
        # # 安卓案例的
        # appCase_count = AndroidCaseInfo.objects.count()
        # yesterday_appCase = AndroidCaseInfo.objects.filter(create_time__lt=today_date,
        #                                                    is_active=True).count()  # 创建日期今天之前的
        # data_dict['webcase_count'] = webcase_count + appCase_count
        # data_dict['yesterday_webcase'] = yesterday_webcase + yesterday_appCase
        # # UI任务数据
        # ui_task_count = TaskInfo.objects.filter(task_type='UI', is_active=True).count()
        # yesterday_ui_task = TaskInfo.objects.filter(task_type='UI', is_active=True).filter(
        #     create_time__lt=today_date).count()  # 创建日期今天之前的
        # data_dict['ui_task_count'] = ui_task_count
        # data_dict['yesterday_ui_task'] = yesterday_ui_task
