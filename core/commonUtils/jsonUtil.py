# -*- coding:utf-8 -*-

import time
from django.http import JsonResponse

'''
@File  :jsonUtil.py
@Author:Pengle
@Date  :2021/3/26 9:50
@Email  :<EMAIL>
@Desc  :json工具类
'''

# 定义统一的json返回格式
def result_json(status, msg, data=None):
    # 创建一个空字典
    result = {"data": data, "meta": {"msg": msg, "status": status}, "timestamp": int(round(time.time() * 1000))}
    return result


def str2Boolean(test):
    if test:
        return True
    else:
        return False


# 判断文本是否包含DDL关键字
def isContainsDDL(content):
    key_values = ['create', 'alter', 'drop', 'truncate', 'grant', 'revoke']
    content_list = content.lower().split(" ")
    test = [x for x in content_list if x in key_values]
    if test:
        return True
    else:
        return False

sql_error = "SQL语句不能包含[create,alter,drop,truncate,grant,revoke]关键字!"

def judgeParameter(parameter):
    if isContainsDDL(parameter):
        return JsonResponse(data=result_json("HD3004", "参数值包含敏感信息："+sql_error),
                            json_dumps_params={'ensure_ascii': False})
