# -*- coding:utf-8 -*-
import copy
import traceback

from django.forms import model_to_dict
from base.models import *
from data_config.models import *
from interface_test.models import *
from performance_test.models import LocustTaskRecord
from tools_help.models import ReleaseRecordInfo
from ui_test.models import *
from core.commonUtils.aesCryptUtil import PrpCrypt
from core.commonUtils.commonUtil import CommonUtil
from core.commonUtils.dbUtil import DBUtils
from core.commonUtils.phoneAndIDUtil import PhoneNumber, IdNumeber

import json
import os
import shutil
import time
from urllib.parse import unquote, urlparse, parse_qs
import pandas as pd
from django.db import transaction


# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()
'''
@File  :getModelInfoUtil.py
@Author:Pengle
@Date  :2021/3/31 15:02
@Email  :<EMAIL>
@Desc  :工具类：旨在从数据库取数据并以指定格式输出
'''


class GetModelInfo:
    def __init__(self):
        super(GetModelInfo, self).__init__()
        self.info_list = ["emails", "projects", "interfaces", "cases", "scenes", "pages",
                          "webElements", "appElements", "appCases", "webCases", "databases", "redis",
                          "webChunks", "appChunks", "devices", "applications", "roles", "businesses"]

    def getInfo(self, type):
        info_dict = {"emails": self.getEmailModelInfo,
                     "databases": self.getDataBaseInfo,
                     "redis": self.getRedisInfo,
                     "projects": self.getProjectInfo,
                     "interfaces": self.getInterfaceInfo,
                     "cases": self.getCaseInfo,
                     "scenes": self.getSceneInfo,
                     "roles": self.getRoleInfo,
                     "pages": self.getPageInfo,
                     "webElements": self.getWebElementInfo,
                     "appElements": self.getAndroidElementInfo,
                     "appCases": self.getAndroidCaseInfo,
                     "webCases": self.getWebCaseInfo,
                     "webChunks": self.getChunkInfoForWEB,
                     "appChunks": self.getChunkInfoForAndroid,
                     "devices": self.getDeviceInfo,
                     "applications": self.getApplicationInfo,
                     "businesses": self.getBusinessInfo
                     }
        if type in info_dict:
            return info_dict[type]()
        else:
            return None

    # 获取参数  参数来源于：字符串、自定义函数、查询数据库
    def getParameter(self, parameter_name, env_type):
        try:
            param = ParameterInfo.objects.get(parameter_name=parameter_name, is_active=True)
        except Exception as e:
            print("参数异常：", e)
            return None
        if param.parameter_type == '1':  # 参数类型为1，表示字符串
            if env_type == '1':
                return param.test_parameter
            elif env_type == '2':
                return param.uat_parameter
            elif env_type == '3':
                return param.produce_parameter
            else:
                return None
        elif param.parameter_type == '2':  # 参数类型为2，表示函数式参数
            if env_type == '1':
                param_value = self.getValueByFunction(param.test_parameter)
            elif env_type == '2':
                param_value = self.getValueByFunction(param.uat_parameter)
            elif env_type == '3':
                param_value = self.getValueByFunction(param.produce_parameter)
            else:
                param_value = ""
            return param_value
        elif param.parameter_type == '3':  # 参数类型为3，表示数据库参数
            result = None
            db = DataBaseInfo.objects.get(data_base_id=param.database_id, is_active=True)
            dbUtils = DBUtils(db.data_base_type, db.ip_address + ":" + db.data_base_port,
                              db.db_account, PrpCrypt('HP9lYhuDeeJjEnAo').decrypt(db.db_pwd), db.db_name)
            dbUtils.getConnect()
            if env_type == '1':
                result = dbUtils.queryDB(param.test_parameter)
            elif env_type == '2':
                result = dbUtils.queryDB(param.uat_parameter)
            elif env_type == '3':
                result = dbUtils.queryDB(param.produce_parameter)
            else:
                print("环境ID传参错误")
            # dbUtils.closeDB()
            test_param_value = None
            if result:
                test_param_value = result[0]
            return test_param_value
        elif param.parameter_type == '4':  # 参数类型为4，表示Redis参数
            result = None
            db = DataBaseInfo.objects.get(data_base_id=param.database_id, is_active=True)
            dbUtils = DBUtils(db.data_base_type, db.ip_address + ":" + db.data_base_port,
                              db.db_account, PrpCrypt('HP9lYhuDeeJjEnAo').decrypt(db.db_pwd), db.db_name)
            dbUtils.getConnect()
            if env_type == '1':
                result = dbUtils.queryKey(param.test_parameter)
            elif env_type == '2':
                result = dbUtils.queryKey(param.uat_parameter)
            elif env_type == '3':
                result = dbUtils.queryKey(param.produce_parameter)
            else:
                print("环境ID传参错误")
            # dbUtils.closeDB()
            test_param_value = None
            if result:
                test_param_value = result[0]
            return test_param_value
        else:
            logger.info("类型参数异常：%s", param.parameter_type)

    # 通过自定义函数获取参数
    def getValueByFunction(self, funcName):
        if "get_current_ymd()" in funcName:
            return CommonUtil().get_current_ymd()
        if "get_current_ym()" in funcName:
            return CommonUtil().get_current_ym()
        elif "get_current_ymd_new" in funcName:
            days = int(funcName[funcName.index("(") + 1:funcName.index(")")])
            return CommonUtil().get_current_ymd_new(days)
        elif "get_current_ymdhms" in funcName:
            seconds = int(funcName[funcName.index("(") + 1:funcName.index(")")].split(",")[0])
            minutes = int(funcName[funcName.index("(") + 1:funcName.index(")")].split(",")[1])
            hours = int(funcName[funcName.index("(") + 1:funcName.index(")")].split(",")[2])
            days = int(funcName[funcName.index("(") + 1:funcName.index(")")].split(",")[3])
            return CommonUtil().get_current_ymdhms(seconds, minutes, hours, days)
        elif "get_appoint_timestamp" in funcName:
            digit = int(funcName[funcName.index("(") + 1:funcName.index(")")])
            return CommonUtil().get_appoint_timestamp(digit)
        elif "get_interval_ymdhms" in funcName:
            tiem_list = json.loads(funcName[funcName.index("(") + 1:funcName.index(")")])
            start_migration = tiem_list[0]
            end_migration = tiem_list[1]
            return CommonUtil().get_interval_ymdhms(start_migration, end_migration)
        elif "get_current_timestamp" in funcName:
            return CommonUtil().get_current_timestamp()
        elif "get_random_num" in funcName:
            num = int(funcName[funcName.index("(") + 1:funcName.index(")")])
            return CommonUtil().get_random_num(num)
        elif "get_num_with_size" in funcName:
            max = int(funcName[funcName.index("(") + 1:funcName.index(")")].split(",")[0])
            min = int(funcName[funcName.index("(") + 1:funcName.index(")")].split(",")[1])
            return str(CommonUtil().get_num_with_size(max, min))
        elif "get_ch_string" in funcName:
            num = int(funcName[funcName.index("(") + 1:funcName.index(")")])
            return CommonUtil().get_ch_string(num)
        elif "get_upper_en_string" in funcName:
            num = int(funcName[funcName.index("(") + 1:funcName.index(")")])
            return CommonUtil().get_upper_en_string(num)
        elif "get_lower_en_string" in funcName:
            num = int(funcName[funcName.index("(") + 1:funcName.index(")")])
            return CommonUtil().get_lower_en_string(num)
        elif "getPhoneNumber" in funcName:
            return PhoneNumber().getPhoneNumber()
        elif "getRandomIdNum" in funcName:
            return IdNumeber().getRandomIdNum()
        else:
            logger.info("函数参数异常：%s", funcName)

    # 以x.xx.xxx形式获取数据
    def get_element_by_params(self, params, tmp_dict):
        tmp = copy.deepcopy(tmp_dict)
        try:
            target_arr = params.split(".")
            cur_index = 0
            for index, k in enumerate(target_arr):
                cur_index = index
                if k.isdigit():
                    try:
                        tmp = tmp[int(k)]
                    except IndexError:
                        return None
                else:
                    if tmp is None:
                        return None
                    else:
                        tmp = tmp.get(k, None)
        except Exception as e:
            if cur_index > 0:
                cur_index = cur_index - 1
            key = target_arr[cur_index]
            error_msg = '接口返回信息中  %s 的 【%s】 数据值有问题，请查看接口返回数据!' % (
            json.dumps(self.to_map(tmp_dict), ensure_ascii=False), str(key))
            raise Exception(error_msg)
        return tmp

    def to_map(self, data):
        if type(data) == list:
            for item in data:
                self.to_map(item)

        if type(data) == dict:
            for attr in data:
                self.to_map(data[attr])

        if (type(data) != str) and (type(data) != int):
            return str(data)

        return data

    # 当前该方法主要面向优惠金额计算，主要用来通过SQL查询获取计算前置参数
    def getActivityParam(self, dbId, merCode, shopId):
        activity_list = []
        # 获取数据库对象
        dbinfo = DataBaseInfo.objects.get(data_base_id=dbId)
        host = dbinfo.ip_address + ":" + dbinfo.data_base_port
        dbUtils = DBUtils(dbinfo.data_base_type, host, dbinfo.db_account,
                          PrpCrypt('HP9lYhuDeeJjEnAo').decrypt(dbinfo.db_pwd), dbinfo.db_name)
        dbUtils.getConnect()
        # 通过商品ID查询活动ID
        sqlStr = """SELECT info.id,info.pmt_type FROM pmt_activity_info info 
    				JOIN pmt_activity_store store ON info.id = store.activity_id 
    				JOIN pmt_activity_item  item ON item.activity_id = info.id
    				AND info.status = 1 and info.isvalid = 1
    				AND info.start_time < NOW() 
    				AND NOW() < info.end_time
    				And info.pmt_type in ('11','14','21')
    				AND info.mer_code = '%s'
    				AND item.prod_spec_id = '%s';""" % (merCode, shopId)
        result_list = dbUtils.queryDB(sqlStr)
        if result_list:
            for result in result_list:
                activity = {}
                activity_id = result[0]
                activity_type = result[1]
                if activity_type == 11:  # 限时特惠
                    activity['activity_type'] = "1"
                    sql = """select pmt_mode,discount from pmt_rule_restrict_seckill where activity_id = '%s'
                                        and spec_id = '%s';""" % (activity_id, shopId)
                    data = dbUtils.query(sql)[0]
                    activity['child_type'] = str(data[0])
                    activity['param'] = str(data[1])
                elif activity_type == 21:  # 多买优惠
                    activity['activity_type'] = "2"
                    sql = """select rule_selection,threshold,discount from pmt_rule_more_discount 
                                        where activity_id = '%s';""" % activity_id
                    data = dbUtils.query(sql)[0]
                    if data[0] == 0:  # M件N元
                        activity['child_type'] = "1"
                    else:
                        activity['child_type'] = "2"
                    activity['param'] = str(data[1]) + "|" + str(data[2])
                elif activity_type == 14:  # 满减赠
                    activity['activity_id'] = activity_id
                    # 先查询满减类型是循环还是阶梯
                    sql_type = "select distinct(rule_type) from pmt_rule_full where activity_id = '%s';" % activity_id
                    rule_type = str(dbUtils.query(sql_type)[0][0])
                    # 再查询单位是 元 还是 件
                    sql_uint = "select distinct(uint) from pmt_rule_full where activity_id = '%s';" % activity_id
                    uint = str(dbUtils.query(sql_uint)[0][0])
                    # 查询门槛 优惠 以及优惠类型字段 组成参数
                    sql = "select threshold,discount_type,discount from pmt_rule_full where activity_id = '%s' order by threshold;" % activity_id
                    data_list = dbUtils.query(sql)
                    if rule_type == '0':  # 循环满减
                        activity['activity_type'] = "3"
                        if uint == '0':
                            activity['child_type'] = "1"  # 满M元减N元
                        else:
                            activity['child_type'] = "2"  # 满M件减N元
                        activity['param'] = str(data[0]) + "|" + str(data[2])
                    else:  # 阶梯满减
                        activity['activity_type'] = "4"
                        activity['child_type'] = uint
                        param_list = []
                        for data in data_list:
                            temp = str(data[0]) + "|" + str(data[2]) + "|" + str(data[1])
                            param_list.append(temp)
                        activity['param'] = param_list
                else:
                    print("暂不支持的其他活动类型计算！")
                activity_list.append(activity)
        return activity_list

    # 获取三层 项目-接口-案例的数据信息
    def getApiCases(self):
        projectInfo = ProjectInfo.objects.filter(is_active=True).all()
        data_list = []
        for project in projectInfo:
            project_dict = {"id": str(project.project_id), "pid": 0, "value": str(project.project_id),
                            "label": str(project.project_id) + "-" + project.project_name}
            interfaceInfo = InterfaceInfo.objects.filter(project_id=project.project_id, is_active=True)
            if interfaceInfo:
                interface_list = []
                for interface in interfaceInfo:
                    interface_dict = {"id": str(project.project_id) + "-" + str(interface.interface_id),
                                      "pid": str(project.project_id), "value": str(interface.interface_id),
                                      "label": str(interface.interface_id) + "-" + interface.interface_name}
                    caseInfo = CaseInfo.objects.filter(interface_id=interface.interface_id, is_active=True)
                    if caseInfo:
                        case_list = []
                        for case in caseInfo:
                            case_dict = {"id": str(project.project_id) + "-" + str(
                                interface.interface_id) + "-" + str(case.case_id), "pid": str(interface.interface_id),
                                         "value": str(case.case_id), "label": str(case.case_id) + "-" + case.case_name}
                            case_list.append(case_dict)
                        interface_dict["children"] = case_list
                    else:
                        interface_dict["children"] = []
                    interface_list.append(interface_dict)
                project_dict["children"] = interface_list
            else:
                project_dict["children"] = []
            data_list.append(project_dict)
        sqlCaseInfo = CaseInfo.objects.filter(case_type='SQL', is_active=True)
        rootDict = {"id": "0", "pid": 0, "value": "0", "label": "SQL案例"}
        sqlCaseList = []
        for sqlCase in sqlCaseInfo:
            sqlCaseDict = {"id": "0-" + str(sqlCase.case_id), "pid": "0",
                           "value": str(sqlCase.case_id), "label": str(sqlCase.case_id) + "-" + sqlCase.case_name}
            sqlCaseList.append(sqlCaseDict)
        rootDict["children"] = sqlCaseList
        data_list.append(rootDict)
        return data_list

    # 获取两层项目-接口数据
    def getApiInterface(self):
        projectInfo = ProjectInfo.objects.filter(is_active=True).all()
        data_list = []
        for project in projectInfo:
            project_dict = {"id": str(project.project_id), "pid": 0, "value": str(project.project_id),
                            "label": str(project.project_id) + "-" + project.project_name}
            interfaceInfo = InterfaceInfo.objects.filter(project_id=project.project_id, is_active=True)
            if interfaceInfo:
                interface_list = []
                for interface in interfaceInfo:
                    interface_dict = {"id": str(project.project_id) + "-" + str(interface.interface_id),
                                      "pid": str(project.project_id), "value": str(interface.interface_id),
                                      "label": str(interface.interface_id) + "-" + interface.interface_name}
                    interface_list.append(interface_dict)
                project_dict["children"] = interface_list
            else:
                project_dict["children"] = []
            data_list.append(project_dict)
        return data_list

    # 获取两层业务线-场景数据
    def getApiScenes(self):
        businessInfo = BusinessInfo.objects.filter(is_active=True).all()
        data_list = []
        for business in businessInfo:
            business_dict = {"id": str(business.business_id), "pid": 0, "value": str(business.business_id),
                            "label": str(business.business_id) + "-" + business.business_name,
                             "children": []}
            sceneInfo = SceneInfo.objects.filter(business_id = business.business_id, is_active=True)
            if sceneInfo:
                for scene in sceneInfo:
                    scene_dict = {"id": str(business.business_id) + "-" + str(scene.scene_id),
                                  "pid": str(business.business_id), "value": str(scene.scene_id),
                                   "label": str(scene.scene_id) + "-" + scene.scene_name}
                    business_dict["children"].append(scene_dict)
            data_list.append(business_dict)
        return data_list

    # 获取全部数据库源信息并格式化成： id-名称 格式
    def getDataBaseInfo(self):
        dbInfo = DataBaseInfo.objects.exclude(data_base_type='Redis').exclude(is_active=False).all()
        dbInfo_list = []
        for db in dbInfo:
            db_dict = {"id": db.data_base_id, "data_base_type": db.data_base_type, "label": str(db.data_base_id) + "-" + db.data_base_name}
            dbInfo_list.append(db_dict)
        return dbInfo_list

    # 获取全部redis数据源信息并格式化成： id-名称 格式
    def getRedisInfo(self):
        dbInfo = DataBaseInfo.objects.filter(data_base_type='Redis').filter(is_active=True).all()
        dbInfo_list = []
        for db in dbInfo:
            db_dict = {"id": db.data_base_id, "data_base_type": db.data_base_type, "label": str(db.data_base_id) + "-" + db.data_base_name}
            dbInfo_list.append(db_dict)
        return dbInfo_list

    # 以 id-名称 的形式返回邮件模板信息
    def getEmailModelInfo(self):
        emailModelInfo = EmailModelInfo.objects.filter(is_active=True).all()
        email_list = []
        for email in emailModelInfo:
            email_dict = {"id": email.email_model_id, "label": str(email.email_model_id) + "-" + email.email_model_name}
            email_list.append(email_dict)
        return email_list

    # 以 id-名称 的形式返回项目信息
    def getProjectInfo(self):
        project = ProjectInfo.objects.filter(is_active=True).all()
        projectInfo_list = []
        for pro in project:
            project_dict = {"id": pro.project_id, "label": str(pro.project_id) + "-" + pro.project_name}
            projectInfo_list.append(project_dict)
        return projectInfo_list

    # 以 id-名称 的形式返回接口信息
    def getInterfaceInfo(self):
        interface = InterfaceInfo.objects.filter(is_active=True).all()
        interfaceInfo_list = []
        for itf in interface:
            interface_dict = {"id": itf.interface_id, "label": str(itf.interface_id) + "-" + itf.interface_name}
            interfaceInfo_list.append(interface_dict)
        return interfaceInfo_list

    # 以 id-名称 的形式返回案例信息
    def getCaseInfo(self):
        caseInfo = CaseInfo.objects.filter(is_active=True).all()
        case_list = []
        for case in caseInfo:
            case_dict = {"id": case.case_id, "label": str(case.case_id) + "-" + case.case_name}
            case_list.append(case_dict)
        return case_list

    # 以 id-名称 的形式返回场景信息
    def getRoleInfo(self):
        roleInfo = RoleInfo.objects.all()
        role_list = []
        for role in roleInfo:
            role_dict = {"id": role.role_id, "label": role.role_name}
            role_list.append(role_dict)
        return role_list

    # 以 id-名称 的形式返回场景信息
    def getSceneInfo(self):
        sceneInfo = SceneInfo.objects.filter(is_active=True).all()
        scene_list = []
        for scene in sceneInfo:
            scene_dict = {"id": scene.scene_id, "label": str(scene.scene_id) + "-" + scene.scene_name}
            scene_list.append(scene_dict)
        return scene_list

    # 以 id-名称 的形式返回页面信息
    def getPageInfo(self):
        pageInfo = PageInfo.objects.filter(is_active=True).all()
        page_list = []
        for page in pageInfo:
            page_dict = {"id": page.page_id, "label": str(page.page_id) + "-" + page.page_name}
            page_list.append(page_dict)
        return page_list

    # 以 id-名称 的形式返回元素操作信息
    def getWebElementInfo(self):
        elementInfo = ElementInfo.objects.filter(is_active=True).all()
        element_list = []
        for element in elementInfo:
            # 组装成 ID-标签名-元素名
            if element.label_name:
                web_element_dict = {"id": element.element_id,
                                    "label": str(
                                        element.element_id) + "-" + element.label_name + "-" + element.element_name}
                element_list.append(web_element_dict)
            else:
                web_element_dict = {"id": element.element_id,
                                    "label": str(element.element_id) + "-其他标签-" + element.element_name}
                element_list.append(web_element_dict)
        return element_list

    # 以 id-名称 的形式返回安卓元素操作信息
    def getAndroidElementInfo(self):
        elementInfo = AndroidElementInfo.objects.filter(is_active=True).all()
        element_list = []
        for element in elementInfo:
            app_element_dict = {"id": element.appElement_id,
                                "label": str(element.appElement_id) + "-" + element.appElement_name}
            element_list.append(app_element_dict)
        return element_list

    # 以 id-名称 的形式返回web案例信息
    def getWebCaseInfo(self):
        webCaseInfo = WebCaseInfo.objects.filter(is_active=True).all()
        webCase_list = []
        for webCase in webCaseInfo:
            web_case_dict = {"id": webCase.web_case_id,
                             "label": str(webCase.web_case_id) + "-" + webCase.web_case_name}
            webCase_list.append(web_case_dict)
        return webCase_list

    # 以 id-名称 的形式返回安卓案例信息
    def getAndroidCaseInfo(self):
        androidCaseInfo = AndroidCaseInfo.objects.filter(is_active=True).all()
        appCase_list = []
        for androidCase in androidCaseInfo:
            app_case_dict = {"id": androidCase.androidCase_id,
                             "label": str(androidCase.androidCase_id) + "-" + androidCase.androidCase_name}
            appCase_list.append(app_case_dict)
        return appCase_list

    # 以 id-名称 的形式返回web块信息
    def getChunkInfoForWEB(self):
        chunkInfo = ChunkInfo.objects.filter(is_active=True).all().exclude(element_type="Android")
        chunk_list = []
        for chunk in chunkInfo:
            chunk_dict = {"id": chunk.chunk_id, "label": str(chunk.chunk_id) + "-" + chunk.chunk_name}
            chunk_list.append(chunk_dict)
        return chunk_list

    # 以 id-名称 的形式返回安卓块信息
    def getChunkInfoForAndroid(self):
        chunkInfo = ChunkInfo.objects.filter(is_active=True).all().exclude(element_type="WEB")
        chunk_list = []
        for chunk in chunkInfo:
            chunk_dict = {"id": chunk.chunk_id, "label": str(chunk.chunk_id) + "-" + chunk.chunk_name}
            chunk_list.append(chunk_dict)
        return chunk_list

    # 以 id-名称 的形式返回设备信息
    def getDeviceInfo(self):
        deviceInfo = DeviceInfo.objects.filter(is_active=True).all()
        device_list = []
        for device in deviceInfo:
            device_dict = {"id": device.device_id, "label": str(device.device_id) + "-" + device.device_name}
            device_list.append(device_dict)
        return device_list

    # 以 id-名称 的形式返回待测应用信息
    def getApplicationInfo(self):
        applicationInfo = ApplicationInfo.objects.filter(is_active=True).all()
        app_list = []
        for application in applicationInfo:
            device_dict = {"id": application.application_id,
                           "label": str(application.application_id) + "-" + application.application_name}
            app_list.append(device_dict)
        return app_list

    # 以 id-名称 的形式返回业务线信息
    def getBusinessInfo(self):
        businessInfo = BusinessInfo.objects.filter(is_active=True).all()
        business_list = []
        for business in businessInfo:
            business_dict = { "id": business.business_id,
                            "label": str(business.business_id) + "-" + business.business_name }
            business_list.append(business_dict)
        return business_list

    # 任务查询时获取任务信息列表
    def getTaskList(self, task_id, task_name, environment, task_type, creater, api_type):
        task_list = TaskInfo.objects.filter(is_active=True).order_by('-update_time')
        if task_id:
            task_list = task_list.filter(task_id=task_id)
        if task_name:
            task_list = task_list.filter(task_name__icontains=task_name)
        if environment:
            task_list = task_list.filter(environment=environment)
        if task_type:
            task_list = task_list.filter(task_type=task_type)
        if api_type:
            task_list = task_list.filter(api_type=api_type)
        if creater:
            task_list = task_list.filter(creater__icontains=creater)
        task_list = task_list.order_by('-update_time')
        temp_list = []
        for task in task_list:
            case_pool = task.case_pool
            if case_pool:
                scene_total = len(case_pool.split("|"))
            else:
                scene_total = "0"
            temp_dict = {"task_id": task.task_id, "task_name": task.task_name, "environment": task.environment,
                         "task_status": task.task_status, "is_send_email": task.is_send_email,
                         "email_model_id": task.email_model_id, "recent_record_id": task.recent_record_id,
                         "task_type": task.task_type, "api_type": task.api_type, "remark": task.remark,
                         "update_time": task.update_time, "update_person": task.update_person, "creater": task.creater,
                         "is_open": task.is_open, "is_monitoring": task.is_monitoring, "cron": task.cron, "is_wx_push": task.is_wx_push, "wx_address": task.wx_address, "scene_total": scene_total}
            # if task.api_type == "CASE":
            #     for caseID in task.case_pool.split("|"):
            #         temp_dict["defaultCase"].append(self.getFullCaseID(caseID))
            # elif task.api_type == "SCENE":
            #     for sceneID in task.case_pool.split("|"):
            #         sceneInfo = SceneInfo.objects.get(scene_id = sceneID, is_active=True)
            #         temp_dict["defaultCase"].append(str(sceneInfo.business_id) + '-' + str(sceneInfo.scene_id))
            # else:
            #     temp_dict["defaultCase"] = task.case_pool.split("|")
            temp_list.append(temp_dict)
        return temp_list

    def saveFailScene(self, record_id):
        scene_list = SceneRunningRecord.objects.filter(parent_id=record_id, run_result__in=["fail", "error"])
        # 获取所有 scene_id
        scene_ids = [scene.scene_id for scene in scene_list]
        # 查询 SceneInfo 表，获取 scene_id 和 creater 的映射关系
        scene_info_map = {
            str(scene.scene_id): scene.creater
            for scene in SceneInfo.objects.filter(scene_id__in=scene_ids)
        }
        environment = TaskRunningRecord.objects.get(task_record_id=record_id).environment
        # 构造 TaskMonitoringFailRecord 数据
        task_monitoring_fail_records = []
        for scene in scene_list:
            # 从 scene_info_map 中获取 creater
            creater = scene_info_map.get(scene.scene_id, '')  # 如果找不到 creater，则设置为空字符串
            task_monitoring_fail_records.append(
                TaskMonitoringFailRecord(
                    scene_id=scene.scene_id,
                    scene_name=scene.scene_name,
                    run_result=scene.run_result,
                    parent_id=scene.parent_id,
                    business_id=scene.business_id,
                    environment=environment,
                    fail_reason='',  # 设置为空
                    is_repair='0',  # 设置为 0
                    repair_plan='',  # 设置为空
                    fail_type='',  # 设置为空
                    creater=creater,  # 从 SceneInfo 表中获取 creater
                )
            )
        # 批量插入数据
        with transaction.atomic():  # 使用事务确保数据一致性
            TaskMonitoringFailRecord.objects.bulk_create(task_monitoring_fail_records)
        logger.info(f"成功插入 {len(task_monitoring_fail_records)} 条记录到 TaskMonitoringFailRecord 表")


    # 通过案例ID获取    项目ID-接口ID-案例ID
    def getFullCaseID(self, caseId):
        try:
            caseInfo = CaseInfo.objects.get(case_id=caseId)
            if caseInfo.case_type == "SQL":
                info = "SQL-" + str(caseId)
            else:
                interfaceInfo = InterfaceInfo.objects.get(interface_id=caseInfo.interface_id)
                info = str(interfaceInfo.project_id) + "-" + str(caseInfo.interface_id) + "-" + str(caseId)
        except Exception as e:
            info = traceback.format_exc()
        finally:
            return info

    # 获取老的断言数据信息
    def getOldAssert(self, casId, caseType):
        if caseType == "WEB":
            caseInfo = WebCaseInfo.objects.get(web_case_id=casId)
        else:
            caseInfo = AndroidCaseInfo.objects.get(androidCase_id=casId)
        assert_list = []
        # 数据断言部分
        if caseInfo.is_assert:
            assertId_list = caseInfo.asserts.split("|")
            for assertId in assertId_list:
                asserts = AssertInfo.objects.get(assert_id=assertId)
                assert_dict = {'assert_type': asserts.assert_type}
                if asserts.assert_type == "1":
                    assert_dict['assert_operator'] = asserts.assert_operator
                    assert_dict['assert_result'] = asserts.assert_result
                else:
                    assert_database = str(asserts.assert_database) + "-" + DataBaseInfo.objects.get(
                        data_base_id=asserts.assert_database).data_base_name
                    assert_dict['assert_database'] = assert_database
                    assert_dict['assert_operator'] = asserts.assert_operator
                    assert_dict['assert_sql'] = asserts.assert_sql
                    assert_dict['assert_result'] = asserts.assert_result
                assert_list.append(assert_dict)
        return assert_list

    # 获取案例组成的列表
    def getCaseFormationList(self, type, formationId_list):
        formation_list = []
        if type == "WEB":
            for formationId in formationId_list:
                formations = CaseFormationInfo.objects.get(case_formation_id=formationId)
                formation_dict = {'formation_type': formations.formation_type}
                if formations.formation_type == "0":  # 元素操作
                    element_info = ElementInfo.objects.get(element_id=formations.element_id)
                    formation_dict['element_id'] = int(element_info.element_id)
                    formation_dict['chunk_id'] = ""
                else:  # 块
                    formation_dict['element_id'] = ""
                    formation_dict['chunk_id'] = int(formations.chunk_id)
                formation_list.append(formation_dict)
        else:
            for formationId in formationId_list:
                formations = CaseFormationInfo.objects.get(case_formation_id=formationId)
                formation_dict = {'formation_type': formations.formation_type}
                if formations.formation_type == "0":  # 元素操作
                    element_info = AndroidElementInfo.objects.get(appElement_id=formations.element_id)
                    element = str(element_info.appElement_id) + "-" + element_info.appElement_name
                    formation_dict['element_id'] = int(element_info.appElement_id)
                    formation_dict['chunk_id'] = ""
                else:
                    chunk = str(formations.chunk_id) + "-" + ChunkInfo.objects.get(
                        chunk_id=formations.chunk_id).chunk_name
                    formation_dict['element_id'] = ""
                    formation_dict['chunk_id'] = int(formations.chunk_id)
                formation_list.append(formation_dict)
        return formation_list

    # 获取编辑时的场景数据
    def getOnlyScene(self, sceneId):
        sceneInfo = SceneInfo.objects.get(scene_id=sceneId)
        scene_dict = model_to_dict(sceneInfo)
        relation_list = []
        for relation_id in sceneInfo.scene_relation_id.split("|"):
            sceneRelation = SceneRelation.objects.get(scene_relation_id=relation_id)
            caseId = sceneRelation.relation_case_id
            caseInfo = CaseInfo.objects.get(case_id=caseId)
            relation_dict = sceneRelation.__dict__
            relation_dict.pop('_state')
            relation_dict.update(dict(relation_case_id=caseId, relation_case_name=caseInfo.case_name,
                                      relation_case_type=caseInfo.case_type))
            if caseInfo.case_type in ["API", "EXPORT", "UPLOAD"] :
                interfaceId =caseInfo.interface_id
                interaceInfo = InterfaceInfo.objects.get(interface_id=interfaceId)
                relation_dict.update(dict( relation_interface_id=interfaceId,
                                          relation_interface_name=interaceInfo.interface_name,
                                          relation_interface_address=interaceInfo.interface_address))
            if caseInfo.case_type == 'UPLOAD' and relation_dict['interface_data']:
                interface_data = json.loads(relation_dict['interface_data'])
                relation_dict.update(dict(file_path=interface_data['file_path'], upload_min_rows= interface_data['upload_min_rows'],
                                          upload_max_rows=interface_data['upload_max_rows'],temp_file=interface_data['template_file'],upload_param=interface_data.get('upload_param', '')))

            assert_list = []
            if sceneRelation.is_assert:
                assertId_list = sceneRelation.asserts.split("|")

                for assertId in assertId_list:
                    asserts = AssertResultInfo.objects.get(assert_id=assertId)
                    assert_dict = {'assert_type': asserts.assert_type, 'return_value': asserts.return_value,
                                   'assert_operator': asserts.assert_operator,
                                   'operation_value': asserts.operation_value, 'expect_result': asserts.expect_result}
                    assert_list.append(assert_dict)
            relation_dict.update(dict(asserts=assert_list))

            relation_list.append(relation_dict)
        scene_dict['relation'] = relation_list
        return scene_dict

    # 获取编辑时的项目数据
    def getOnlyProject(self, projectId):
        projectInfo = ProjectInfo.objects.get(project_id=projectId)
        project_dict = model_to_dict(projectInfo)
        project_dict['test_case'] = []
        project_dict['uat_case'] = []
        project_dict['pro_case'] = []
        if projectInfo.test_case:
            test_list = []
            for relation_id in projectInfo.test_case.split("|"):
                sceneRelation = SceneRelation.objects.get(scene_relation_id=relation_id)
                caseId = sceneRelation.relation_case_id
                caseInfo = CaseInfo.objects.get(case_id=caseId)
                relation_dict = sceneRelation.__dict__
                relation_dict.pop('_state')
                relation_dict.update(dict(relation_case_id=caseId, relation_case_name=caseInfo.case_name,
                                          relation_case_type=caseInfo.case_type))
                assert_list = []
                if sceneRelation.is_assert:
                    assertId_list = sceneRelation.asserts.split("|")

                    for assertId in assertId_list:
                        asserts = AssertResultInfo.objects.get(assert_id=assertId)
                        assert_dict = {'assert_type': asserts.assert_type, 'return_value': asserts.return_value,
                                       'assert_operator': asserts.assert_operator,
                                       'operation_value': asserts.operation_value, 'expect_result': asserts.expect_result}
                        assert_list.append(assert_dict)
                relation_dict.update(dict(asserts=assert_list))

                test_list.append(relation_dict)
            project_dict['test_case'] = test_list
        if projectInfo.uat_case:
            uat_list = []
            for relation_id in projectInfo.uat_case.split("|"):
                sceneRelation = SceneRelation.objects.get(scene_relation_id=relation_id)
                caseId = sceneRelation.relation_case_id
                caseInfo = CaseInfo.objects.get(case_id=caseId)
                relation_dict = sceneRelation.__dict__
                relation_dict.pop('_state')
                relation_dict.update(dict(relation_case_id=caseId, relation_case_name=caseInfo.case_name,
                                          relation_case_type=caseInfo.case_type))
                assert_list = []
                if sceneRelation.is_assert:
                    assertId_list = sceneRelation.asserts.split("|")

                    for assertId in assertId_list:
                        asserts = AssertResultInfo.objects.get(assert_id=assertId)
                        assert_dict = {'assert_type': asserts.assert_type, 'return_value': asserts.return_value,
                                       'assert_operator': asserts.assert_operator,
                                       'operation_value': asserts.operation_value, 'expect_result': asserts.expect_result}
                        assert_list.append(assert_dict)
                relation_dict.update(dict(asserts=assert_list))

                uat_list.append(relation_dict)
            project_dict['uat_case'] = uat_list
        if projectInfo.pro_case:
            pro_list = []
            for relation_id in projectInfo.pro_case.split("|"):
                sceneRelation = SceneRelation.objects.get(scene_relation_id=relation_id)
                caseId = sceneRelation.relation_case_id
                caseInfo = CaseInfo.objects.get(case_id=caseId)
                relation_dict = sceneRelation.__dict__
                relation_dict.pop('_state')
                relation_dict.update(dict(relation_case_id=caseId, relation_case_name=caseInfo.case_name,
                                          relation_case_type=caseInfo.case_type))
                assert_list = []
                if sceneRelation.is_assert:
                    assertId_list = sceneRelation.asserts.split("|")

                    for assertId in assertId_list:
                        asserts = AssertResultInfo.objects.get(assert_id=assertId)
                        assert_dict = {'assert_type': asserts.assert_type, 'return_value': asserts.return_value,
                                       'assert_operator': asserts.assert_operator,
                                       'operation_value': asserts.operation_value, 'expect_result': asserts.expect_result}
                        assert_list.append(assert_dict)
                relation_dict.update(dict(asserts=assert_list))

                pro_list.append(relation_dict)
            project_dict['pro_case'] = pro_list
        return project_dict

    # 获取编辑时的案例信息
    def getOnlyCase(self, caseId, scene_relation_id=None):
        case = CaseInfo.objects.get(case_id=caseId)
        caseDict = model_to_dict(case)
        if case.case_type == 'API' or case.case_type == 'EXPORT' or case.case_type == 'UPLOAD':
            interface = InterfaceInfo.objects.get(interface_id=case.interface_id)
            caseDict['interface_name'] = str(interface.interface_id) + '-' + interface.interface_name
            caseDict['project_id'] = interface.project_id
            caseDict['interface_address'] = interface.interface_address
        if case.case_type == 'UPLOAD':
            interface_data = json.loads(caseDict['interface_data'])
            caseDict['file_path'] = interface_data['file_path']
            caseDict['upload_min_rows'] = interface_data['upload_min_rows']
            caseDict['upload_max_rows'] = interface_data['upload_max_rows']
            caseDict['temp_file'] = interface_data['template_file']
            caseDict['upload_param'] = interface_data.get('upload_param','')
        if caseDict['is_init']:
            caseDict['init_database'] = int(caseDict['init_database'])
        if scene_relation_id is None:
            # 未传场景关联id时，则取案例中的断言信息
            if case.is_assert:
                assertId_list = case.asserts.split("|")
                assert_list = []
                for assertId in assertId_list:
                    asserts = AssertResultInfo.objects.get(assert_id=assertId)
                    assert_dict = {'assert_type': asserts.assert_type, 'return_value': asserts.return_value,
                                   'assert_operator': asserts.assert_operator,
                                   'operation_value': asserts.operation_value, 'expect_result': asserts.expect_result}
                    assert_list.append(assert_dict)
                caseDict['asserts'] = assert_list
            else:
                caseDict['asserts'] = []
            return caseDict
        else:
            # 传场景关联id时，则取场景关联表中的断言信息
            relation = SceneRelation.objects.get(scene_relation_id=scene_relation_id, is_active=True)
            # 判断场景关联表中是否重设参数，如果重设参数为true,则取关联表的断言信息
            if relation.reset_param:
                if relation.is_assert:
                    assertId_list = relation.asserts.split("|")
                    assert_list = []
                    for assertId in assertId_list:
                        asserts = AssertResultInfo.objects.get(assert_id=assertId)
                        assert_dict = {'assert_type': asserts.assert_type, 'return_value': asserts.return_value,
                                       'assert_operator': asserts.assert_operator,
                                       'operation_value': asserts.operation_value,
                                       'expect_result': asserts.expect_result}
                        assert_list.append(assert_dict)
                    caseDict['asserts'] = assert_list
                    caseDict['is_assert'] = True
                else:
                    caseDict['asserts'] = []
                # 如果重设参数，则取关联表中的导入请求参数信息
                if case.case_type == 'UPLOAD':
                    interface_data = json.loads(relation.interface_data)
                    caseDict['file_path'] = interface_data['file_path']
                    caseDict['upload_min_rows'] = interface_data['upload_min_rows']
                    caseDict['upload_max_rows'] = interface_data['upload_max_rows']
                    caseDict['temp_file'] = interface_data['template_file']
                caseDict['case_describe'] = relation.instructions
                return caseDict
            else:
                # 传场景关联id时，但未重设参数，也取案例中的断言信息
                if case.is_assert:
                    assertId_list = case.asserts.split("|")
                    assert_list = []
                    for assertId in assertId_list:
                        asserts = AssertResultInfo.objects.get(assert_id=assertId)
                        assert_dict = {'assert_type': asserts.assert_type, 'return_value': asserts.return_value,
                                       'assert_operator': asserts.assert_operator,
                                       'operation_value': asserts.operation_value,
                                       'expect_result': asserts.expect_result}
                        assert_list.append(assert_dict)
                    caseDict['asserts'] = assert_list
                else:
                    caseDict['asserts'] = []
                caseDict['case_describe'] = relation.instructions
                return caseDict

     # 获取编辑时的WEB案例信息
    def getOnlyWebCase(self, webCaseId):
        web_case_info = WebCaseInfo.objects.get(web_case_id=webCaseId)
        webCaseDict = model_to_dict(web_case_info)
        # 案例组成部分
        webCaseDict["formation"] = self.getCaseFormationList("WEB", web_case_info.web_case_formation.split("|"))
        webCaseDict["asserts"] = self.getOldAssert(webCaseId, "WEB")
        return webCaseDict

    # 获取编辑时的安卓案例信息
    def getOnlyAppCase(self, appCaseId):
        info = AndroidCaseInfo.objects.get(androidCase_id=appCaseId)
        androidCase_dict = model_to_dict(info)
        androidCase_dict['test_device'] = int(info.test_device)
        androidCase_dict['test_application'] = int(info.test_application)
        # 案例组成部分
        androidCase_dict['formation'] = self.getCaseFormationList("Android", info.androidCase_formation.split("|"))
        # 数据断言部分
        androidCase_dict['asserts'] = self.getOldAssert(appCaseId, "Android")
        return androidCase_dict

    # 获取编辑时的块信息
    def getOnlyChunk(self, chunkId):
        chunk = ChunkInfo.objects.get(chunk_id=chunkId)
        chunk_dict = model_to_dict(chunk)
        chunk_dict['element_pool'] = list(map(int, chunk.element_pool.split("|")))
        return chunk_dict

    # 获取编辑时的性能配置信息
    def getOnlyPerformance(self, performanceId):
        performance = LocustTaskRecord.objects.get(locust_task_id=performanceId)
        performance_dict = model_to_dict(performance)
        performance_dict['formation'] = []
        formation_list = performance.case_id.split("|")
        for item in formation_list:
            data_list = list(map(int, item.split(",")))
            formation_dict = {}
            formation_dict['case'] = data_list[0]
            formation_dict['rate'] = data_list[1]
            performance_dict['formation'].append(formation_dict)
        return performance_dict

    # 获取编辑时场景任务关联的场景用例信息
    def getOnlyTask(self, taskId):
        taskInfo = TaskInfo.objects.get(task_id=taskId)
        task_dict = model_to_dict(taskInfo)
        # 取出所有场景信息
        scene_ids = taskInfo.case_pool.split("|")
        # 场景数据为空异常处理
        if scene_ids[0] == '':
            task_dict['scene'] = []
            task_dict['case_pool'] = []
            return task_dict
        scene_info_dict = {scene.scene_id: scene for scene in SceneInfo.objects.filter(scene_id__in=scene_ids)}

        # 取出所有场景对应的业务线id，并去重
        business_ids = set([scene_info.business_id for scene_info in scene_info_dict.values()])
        # 根据业务线id获取名称
        business_info_data = BusinessInfo.objects.filter(business_id__in=business_ids, is_active=True).values(
            'business_id', 'business_name')
        # 将业务线数据写入字典进行缓存
        business_info_mapping = {info['business_id']: info['business_name'] for info in business_info_data}

        scene_list = []
        for scene_id in scene_ids:
            sceneInfo = scene_info_dict.get(int(scene_id))
            # businessInfo = BusinessInfo.objects.get(business_id=sceneInfo.business_id)
            scene_dict = {
                "scene_id": sceneInfo.scene_id,
                "scene_name": sceneInfo.scene_name,
                "scene_describe": sceneInfo.scene_describe,
                "businessName": business_info_mapping.get(sceneInfo.business_id),
                "creater": sceneInfo.creater,
                "update_time": sceneInfo.update_time.strftime('%Y-%m-%d %H:%M:%S')
            }
            scene_list.append(scene_dict)
        task_dict["scene"] = scene_list
        task_dict["case_pool"] = []
        return task_dict

    # # def getOnlyTask(self, taskId, page, limit):
    # def getOnlyTask(self, taskId):
    #     taskInfo = TaskInfo.objects.get(task_id=taskId)
    #     task_dict = model_to_dict(taskInfo)
    #     scene_list = []
    #     # scene = taskInfo.case_pool.split("|")
    #     # paginator = Paginator(scene, limit)
    #     # data = paginator.page(page)
    #     for scene_id in taskInfo.case_pool.split("|"):
    #     # for scene_id in data:
    #         sceneInfo = SceneInfo.objects.get(scene_id=scene_id)
    #         businessInfo = BusinessInfo.objects.get(business_id=sceneInfo.business_id)
    #         scene_dict = {
    #             "scene_id": sceneInfo.scene_id,
    #             "scene_name": sceneInfo.scene_name,
    #             "scene_describe": sceneInfo.scene_describe,
    #             "businessName": businessInfo.business_name,
    #             "creater": sceneInfo.creater,
    #             "update_time": sceneInfo.update_time.strftime('%Y-%m-%d %H:%M:%S')
    #         }
    #         scene_list.append(scene_dict)
    #     task_dict["scene"] = scene_list
    #     task_dict["case_pool"] = []
    #     # task_dict["scene_pool"] = scene
    #     return task_dict

    def getReleaseList(self, hops_id, code_name, task_name, username):
        """
        获取发版记录列表
        """
        sqlStr = """SELECT
                        rr.record_id,
                        rr.hops_id,
                        rr.code_name,
                        rr.task_id,
                        rr.environment,
                        ti.task_name,
                        trr.task_record_id,
                        trr.old_record_id,
                        CASE WHEN COALESCE(trr.total_time,'')='' THEN '运行中' ELSE '已完成' END AS running_status,
                        rr.create_time,
                        rr.username 
                    FROM
                        release_record rr
                    LEFT JOIN task_info ti ON rr.task_id = ti.task_id
                    LEFT JOIN task_running_record trr ON rr.hops_id = trr.batch_number 
                        AND rr.task_id = trr.task_id
                        AND trr.time_stamp = (
                            SELECT MAX(time_stamp) 
                            FROM task_running_record 
                            WHERE batch_number = rr.hops_id AND task_id = rr.task_id
                        )
                    WHERE 1=1"""
        # 拼接查询条件
        if hops_id:
            sqlStr += " AND rr.hops_id = '%s'" % hops_id
        if code_name:
            sqlStr += " AND rr.code_name LIKE '%%%%%s%%%%'" % code_name
        if task_name:
            sqlStr += " AND ti.task_name LIKE '%%%%%s%%%%'" % task_name
        if username:
            sqlStr += " AND rr.username = '%s'" % username
        sqlStr += " ORDER BY rr.create_time DESC;"
        release_list = ReleaseRecordInfo.objects.raw(sqlStr)
        temp_list = []
        for release in release_list:
            temp_dict = {"record_id": release.record_id, "task_id": release.task_id,"hops_id": release.hops_id, "code_name": release.code_name,
                         "task_name": release.task_name, "task_record_id": release.task_record_id, "old_task_record_id": release.old_record_id, "running_status": release.running_status, "environment": release.environment, "create_time": release.create_time,
                         "username": release.username}
            temp_list.append(temp_dict)
        return temp_list

    # 下载文件
    def download_excel(self, filename, response, case_id=None):
        """
          下载文件方法
         :param request:  filename: 文件路径+文件名
                          response: 访问oss下载文件get请求返回的response
         :return:  extracted_files:下载并解压后的文件名
                   destination_directory: 文件存放路径
         """
        # 常见的压缩文件类型扩展名
        compress_extensions = ['zip', 'rar', 'tar.gz', 'tar.bz2', 'tar', 'gz', 'bz2']
        # 获取文件名称
        parsed_url = urlparse(filename)
        file_name = os.path.basename(parsed_url.path)
        # 截取文件后缀类型
        file_extension = file_name.split("/")[-1].split(".")[-1]

        # 获取当前目录
        current_path = os.getcwd()
        # 获取上一级目录
        parent_path = os.path.abspath(os.path.join(current_path, os.pardir))
        # 获取时间戳
        timestamp = int(round(time.time() * 1000))
        # 下载文件到临时目录
        temp_file_path = parent_path + "/temp/" + str(timestamp) + "/"
        os.makedirs(temp_file_path, exist_ok=True)  # 创建目标目录（如果不存在）
        with open(temp_file_path + file_name, "wb") as temp_file:
            for chunk in response.iter_content(chunk_size=1024):
                temp_file.write(chunk)
        # 将文件移动到指定目录
        destination_directory = parent_path + "/download/" + str(case_id) + "/" + str(timestamp) + "/"
        os.makedirs(destination_directory, exist_ok=True)  # 创建目标目录（如果不存在）
        # 判断文件是否已存在，如存在先进行删除
        if os.path.isfile(destination_directory + file_name):
            os.remove(destination_directory + file_name)
        # 移动文件至指定目录
        shutil.move(temp_file_path + file_name, destination_directory)
        # 判断文件类型是否为压缩文件
        if file_extension in compress_extensions:
            # 解压zip文件
            self.unzip_file(destination_directory, file_name)
            # 删除压缩包
            os.remove(destination_directory + file_name)
        # 获取目标目录中的文件列表
        extracted_files = os.listdir(destination_directory)[0]
        # 获取文件完整路径
        file = destination_directory + extracted_files
        return file

    # 解压zip文件
    def unzip_file(self, destination_directory, file_name):
        file = destination_directory + file_name
        shutil.unpack_archive(file, destination_directory)
        # with zipfile.ZipFile(file, 'r') as zip_ref:
        #     zip_ref.extractall(destination_directory)

    # 读取excel数据
    def read_excel_data(self, file, row=0):
        """
          解析excel方法
         :param request:  file: 文件路径
                          row: 读取数据行号（不包含标题），默认首行
         :return:  data_list:返回总条数、行数据，字典格式
         """
        data_list = {}
        new_data_list = {}
        # 获取文件后缀
        file_extension = os.path.splitext(file)[1].lower()  # 转换为小写以便比较
        if file_extension == '.xlsx':
            df = pd.read_excel(file, engine='openpyxl')
        elif file_extension == '.xls':
            df = pd.read_excel(file, engine='xlrd')
        else:
            raise ValueError(f"暂不支持该文件格式: {file_extension}")
        # 将数据转换为JSON格式
        total_json_data = df.to_json(orient="records")
        total_data = json.loads(total_json_data)
        # 统计条数
        count = len(total_data)
        try:
            # 获取某一行数据
            row_data = df.iloc[row]
            # 转换数值类型为整数（如果它们是浮点数且没有小数部分）
            row_data = row_data.apply(lambda x: int(x) if isinstance(x, float) and x.is_integer() else x)
            row_dict = row_data.to_dict()
        except IndexError:
            # self.add_log(f"行 {row}数据在文件中不存在!")
            logger.error(f"行 {row}数据在文件中不存在!")
            row_dict = None
        data_list["total"] = count
        data_list["file"] = file
        new_data_list.update(data_list)
        new_data_list['data'] = [row_dict] if row_dict is not None else None
        data_list = new_data_list
        return data_list

    # 移动文件
    def move_file(self, file_path, new_path):
        # 规范化路径，防止路径注入攻击
        file_path = os.path.abspath(file_path)
        # 获取文件名
        file_name = os.path.basename(file_path)
        new_path = os.path.abspath(new_path)
        # 将参数文件移动到案例目录下
        if not os.path.exists(new_path):
            os.makedirs(new_path, exist_ok=True)
        # 构建目标文件的完整路径
        target_file_path = os.path.join(new_path, file_name)

        # 如果目标文件已存在，则删除
        if os.path.exists(target_file_path):
            os.remove(target_file_path)
        # 移动文件至指定目录
        try:
            shutil.move(file_path, new_path)
            return target_file_path
        except Exception as e:
            raise RuntimeError(f"移动文件时发生错误: {e}")

    # 删除上传文件
    def delete_file(self, file_path):
        # 验证路径是否合法
        if not os.path.abspath(file_path).startswith(os.getcwd()):
            logger.error("非法路径: {file_path}")
            return
        # 检查文件夹是否存在
        if os.path.exists(file_path):
            try:
                shutil.rmtree(file_path)
                logger.info(f"文件删除成功: {file_path}")
            except Exception as e:
                raise RuntimeError(f"删除文件时发生错误: {e}")
        else:
            logger.info(f"文件不存在: {file_path}")

    # 比对excel文件列名
    def compare_column_names(self, data_file, template_file, sheet_name1=0, sheet_name2=0):
        # 读取第一个 Excel 文件
        df_data = pd.read_excel(data_file, sheet_name=sheet_name1)
        # 读取第二个 Excel 文件
        df_template = pd.read_excel(template_file, sheet_name=sheet_name2)
        print("df_data.columns:", df_data.columns)
        print("df_template.columns:", df_template.columns)

        # 获取两个数据框的列名
        data_columns = list(df_data.columns)
        template_columns = list(df_template.columns)

        # 比较列名
        if data_columns == template_columns:
            logger.info(f"文件列名和顺序一致")
            return None
        else:
            if set(data_columns) == set(template_columns):
                logger.info(f"文件列名一致，但顺序不一致")
                diff_columns = { "diff_columns":"列名顺序不一致，可能影响上传数据准确性"}
            else:
                diff_columns = {
                "diff_columns": list(set(data_columns) - set(template_columns)) + list(set(template_columns) - set(data_columns))
                }
                logger.info(f"文件列名不一致，列名差异：", diff_columns)
            return diff_columns