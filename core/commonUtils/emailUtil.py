from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import smtplib

from data_config.models import *
from django.template import loader

from django.conf import settings
# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()

# 读取配置文件，服务接口IP
service_ip = settings.SERVICE_IP

'''
@File  :emailUtil.py
@Desc  :邮件发送工具类
'''


# 邮件发送类
class SendEmail:

    # 初始化函数
    def __init__(self, path, to_list, cc_list=None, subject=None, content=None):
        self.path = path
        self.to_list = to_list
        if subject:
            self.subject = subject
        else:
            self.subject = '测试平台自动化测试任务报告'
        if content:
            self.content = content
        else:
            self.content = '见信好，这是测试平台自动化测试任务报告，请查收。本邮件为自动发送，无需回复'
        if cc_list:
            self.cc_list = cc_list
        else:
            # 默认抄送人
            self.cc_list = ['liu<PERSON><PERSON>@hydee.cn']
        # 邮件发送者及默认的邮件接收人

        # 更改邮件发送人员   2022/10/27
        self.my_addr = 'liu<PERSON><PERSON>@hydee.cn'
        self.my_pwd = 'QdeJ8eS9KcBA72Df'

    def send_html(self, task_id, old_record_id=None):
        logger.info(f'开始发送邮件,任务id: {task_id}')
        task_record = TaskRunningRecord.objects.values('test_all', 'test_pass', 'test_fail', 'test_error', 'total_time',
                                                       'time_stamp').get(task_record_id=task_id)
        test_all = task_record['test_all']
        test_pass = task_record['test_pass']
        total_time = float(task_record.get('total_time'))
        test_fail = task_record.get('test_fail')
        test_error = task_record.get('test_error')
        create_time = task_record.get('time_stamp')
        if old_record_id:
            old_task_record = TaskRunningRecord.objects.values('test_pass', 'total_time',
                                                               'time_stamp').get(task_record_id=old_record_id)
            url = 'http://' + service_ip + ':8000/api/task_info/task_report/?task_id=' + str(task_id)+'&old_task_id=' + str(old_record_id)
            old_pass = old_task_record['test_pass']
            old_total_time = float(old_task_record.get('total_time'))
            test_all += old_pass
            test_pass += old_pass
            total_time += old_total_time
        # 减少查询操作
        else:
            url = 'http://' + service_ip + ':8000/api/task_info/task_report/?task_id=' + str(task_id)
        body = loader.render_to_string(
            "email_report.html",
            {
               "content": self.content,
               "test_all": test_all,
               "test_pass": test_pass,
               "test_fail": test_fail,
               "test_error": test_error,
               "total_time": round(total_time / 60, 2),  # 将运行时长转化为分钟
               "create_time": create_time,
               "url": url
               # "scene_record": json_dict

            }
        )

        msg = MIMEText(body, "html")
        msg['Subject'] = self.subject
        msg['From'] = '海典自动化测试平台<%s>' % self.my_addr
        msg['To'] = ";".join(self.to_list)
        msg['Cc'] = ";".join(self.cc_list)
        try:
            # server = smtplib.SMTP()
            # server.connect("smtp.exmail.qq.com")
            server = smtplib.SMTP_SSL("smtp.exmail.qq.com", 465)
            server.login(self.my_addr, self.my_pwd)
            logger.info(f'发送邮件列表: {self.to_list + self.cc_list}')
            server.sendmail(self.my_addr, self.to_list + self.cc_list, msg.as_string())
            server.quit()
            logger.info(f'成功发送邮件,任务id: {task_id}')
        except Exception as e:
            logger.info(f'发送邮件失败,任务id: {task_id}；原因：{e}')
            raise e

    # 邮件发送方法
    def send(self):
        # 创建一个带附件的实例
        msg = MIMEMultipart('mixed')
        # 构建附件
        with open(self.path, "rb") as f:
            fileContent = f.read()
        text = MIMEText(fileContent, 'html', 'utf-8')
        msg.attach(MIMEText(self.content, 'plain', 'utf-8'))
        text.add_header("Content-Disposition", "attachment", filename=("utf-8", "", "接口自动化测试报告.html"))
        msg.attach(text)
        msg['Subject'] = self.subject
        msg['From'] = '海典自动化测试平台<%s>' % self.my_addr
        msg['To'] = ";".join(self.to_list)
        msg['Cc'] = ";".join(self.cc_list)
        try:
            server = smtplib.SMTP()
            server.connect("smtp.exmail.qq.com")
            server.login(self.my_addr, self.my_pwd)
            server.sendmail(self.my_addr, self.to_list + self.cc_list, msg.as_string())
            server.quit()
        except Exception as e:
            raise e
