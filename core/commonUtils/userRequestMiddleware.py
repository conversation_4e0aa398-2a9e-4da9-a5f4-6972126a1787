# -*- coding:utf-8 -*-
from django.utils.deprecation import MiddlewareMixin

from base.models import UserToken, UserInfo
from datetime import datetime, timedelta
from django.core.cache import cache
from core.commonUtils.jsonUtil import *
from django.conf import settings
from django.http import StreamingHttpResponse


from urllib.parse import quote

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()
'''
@File  :userRequestMiddleware.py
@Date  :2021/3/26 9:59
@Desc  : 用户验证中间件，主要做两件事：
①校验token与IP在数据库是否存在，存在则表示该请求有效，否则用户认证失败；
②该IP在单位时间内是否多次访问同一接口，防止被恶意压测。
'''

visit_ip_pool = {}

# 读取配置文件，请求屏蔽中间件的token验证
path_list = settings.NO_TOKEN_LIST
# 读取配置文件，导出接口
export_paths = settings.EXPORT_API_LIST


class UserRequestMiddleware(MiddlewareMixin):

    # 获取客户端的IP地址
    def get_client_ip(self, request):
        """
        从请求中获取客户端的真实 IP 地址
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            # X-Forwarded-For 可能包含多个 IP，第一个是客户端的真实 IP
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            # 如果没有 X-Forwarded-For，则使用 REMOTE_ADDR
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def process_request(self, request):
        request.start_time = time.time()
        if request.method in ("POST", "PUT", "DELETE"):
            body = request.body
        if request.method == "GET":
            body = request.GET
        logger.info("当前请求接口：%s，请求方式：%s，请求参数为：%s" % (request.get_raw_uri(), request.method, body))
        ip = self.get_client_ip(request)
        logger.info(f"当前接口请求：IP {ip}")
        # IP 白名单检查
        if ip in settings.ALLOWED_IPS:
            logger.info(f"当前接口请求：IP {ip} 在白名单中，放行")
            return None  # 直接放行，不进行后续验证

        # # 第一轮验证H5请求
        # user_agent = request.META.get('HTTP_USER_AGENT', '')
        # logger.info(f"当前接口请求：user_agent:{user_agent}")
        # # 判断是否为 H5 请求，例如通过检查 User-Agent 是否包含 "Mobile"
        # if self.is_h5_request(user_agent):
        #     return self.check_wechat_auth(request)

        # 第二轮token验证
        if request.path in path_list:
            return None
        else:
            token = request.META.get("HTTP_AUTHORIZATION")
            token_obj = UserToken.objects.filter(token=token).order_by('-create_time').first()
            if not token_obj:
                logger.info("当前接口请求：用户认证失败，拒绝服务")
                return JsonResponse(data=result_json("HD1003", "用户认证失败，拒绝服务"),
                                    json_dumps_params={'ensure_ascii': False})
            else:
                user_info = UserInfo.objects.filter(username=token_obj.user, is_active=True)
                if not user_info:
                    logger.info("当前接口请求：用户状态为失效，拒绝服务")
                    return JsonResponse(data=result_json("HD1003", "用户认证失败，拒绝服务"),
                                        json_dumps_params={'ensure_ascii': False})
            create_time = token_obj.create_time
            now_time = datetime.now()
            delta = now_time - create_time
            if delta < timedelta(days=1):
                remain_time = timedelta(days=1) - delta
                cache.set(token_obj.token, token_obj.user, min(remain_time.total_seconds(), 3600 * 24))
            else:
                # # 判断是否为 H5 请求，如果是，再走一遍微信授权
                # if self.is_h5_request(user_agent):
                #     return self.check_wechat_auth(request, "H5")
                # else:
                logger.info("当前接口请求：token过期，需重新登录")
                return JsonResponse(data=result_json("HD1004", "token过期，请重新登陆"),
                                json_dumps_params={'ensure_ascii': False})
        # 第三轮访问次数控制
        visit_time = time.time()
        if ip not in visit_ip_pool:
            # 维护字典,将新的ip地址加入字典
            visit_ip_pool[ip] = [visit_time]
        else:
            # 已经存在，则将ip对应值的插入列表开始位置
            visit_ip_pool[ip].insert(0, visit_time)
        # 获取ip_list列表
        ip_list = visit_ip_pool[ip]
        # 计算访问时间差
        lead_time = ip_list[0] - ip_list[-1]
        # 两个条件同时成立则，间隔时间在5s内
        while ip_list and lead_time > 5:
            # 默认移除列表中的最后一个元素
            ip_list.pop()
        # 间隔在5s内判断列表的长度即访问的次数是否大于50次
        if len(ip_list) > 50:
            logger.info("当前接口请求：频繁请求，拒绝服务")
            return JsonResponse(data=result_json("HD1005", "频繁请求，拒绝服务"),
                                json_dumps_params={'ensure_ascii': False})

    def process_response(self, request, response):
        execute_time = time.time() - request.start_time
        # 跳过流式响应的内容记录
        if isinstance(response, StreamingHttpResponse):
            logger.info(f"流式响应 - 状态码: {response.status_code}, 耗时: {execute_time:.2f}秒")
            return response
        if response.status_code in[200, 302]  and request.path not in export_paths:
            if response.status_code == 200:
                # 检查内容类型是否为文本类型
                content_type = response.get('Content-Type', '')
                if 'text' in content_type or 'json' in content_type:
                    logger.info("当前请求返回：%s" % response.content.decode("utf-8"))
            elif response.status_code == 302:
                redirect_url = response.get('Location', 'N/A')
                logger.info("302 重定向到：%s" % redirect_url)
                if redirect_url:
                    encoded_url = quote(redirect_url, safe='=&')
                    # return HttpResponseRedirect(encoded_url)
        elif response.status_code not in[200, 302]:
            logger.info("接口请求异常：%s" % response)
        logger.info("当前请求耗时：%s 秒" % execute_time)
        return response

    # # 判断是否为H5请求
    # def is_h5_request(self, user_agent):
    #     # 简单的 User-Agent 检查，可以根据实际情况调整
    #     mobile_pattern = re.compile(r'phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone')
    #     return bool(mobile_pattern.search(user_agent))
    #
    # # 检查微信授权
    # def check_wechat_auth(self, request, type=None):
    #     # code = request.GET.get("code", "").strip()
    #     user_info = request.session.get("user_info", "")
    #
    #     logger.info(f"当前接口请求：微信授权user_info:{user_info}")
    #     logger.info(f"当前接口请求：微信授权type:{type}")
    #     redirect_url = None
    #
    #     try:
    #         if not user_info or type is not None:
    #             redirect_url = self.get_wechat_auth_url(request)
    #         else:
    #             pass
    #
    #         if redirect_url:
    #             logger.info("开始重定向")
    #             return HttpResponseRedirect(redirect_url)
    #
    #     except Exception as e:
    #         logger.error(f"Error generating wechat auth URL: {e}")
    #
    #     # 如果没有重定向或发生错误，返回默认页面
    #     return HttpResponseRedirect('/')
    #
    # # 获取微信授权地址
    # def get_wechat_auth_url(self, request):
    #     app_id = settings.WECHAT_APP_ID
    #     agentid = settings.AGENTID
    #     state = str(uuid.uuid4())  # 生成一个随机的UUID作为state
    #     scope = 'snsapi_privateinfo'
    #     # msg_id = request.GET.get("msgId")
    #     msg_id_value = None
    #     referer = request.META.get('HTTP_REFERER')
    #     if referer:
    #         # 解析 URL
    #         parsed_url = urlparse(referer)
    #         # 解析查询参数
    #         query_params = parse_qs(parsed_url.query)
    #         # 获取 msgId 的值
    #         msg_id_value = query_params.get('msgId', [None])[0]
    #
    #     # 检查 msg_id 是否为 None，并在日志中记录
    #     if msg_id_value is None:
    #         logger.info("当前接口请求：未提供 msg_id")
    #         redirect_uri = settings.WECHAT_REDIRECT_URI
    #         logger.info(f"当前接口请求-无msg_id：redirect_uri:{redirect_uri}")
    #     else:
    #         # redirect_uri = f"{settings.H5_URL}?msgId={msg_id_value}"
    #         redirect_uri = quote(f"{settings.H5_URL}?msgId={msg_id_value}", safe='=&')
    #         logger.info(f"当前接口请求：redirect_uri:{redirect_uri}")
    #
    #     wechat_auth_url = (
    #         f"https://open.weixin.qq.com/connect/oauth2/authorize?appid={app_id}"
    #         f"&redirect_uri={redirect_uri}&response_type=code&scope={scope}&state={state}"
    #         f"&agentid={agentid}"
    #         f"#wechat_redirect"
    #     )
    #
    #     return wechat_auth_url