from django.db import models
from scaffold.models.abstract.meta import ActiveModel, HierarchicalModel


# Create your models here.

class DateModel(models.Model):
    create_time = models.DateTimeField(verbose_name='创建时间', auto_now_add=True, db_index=True)
    creater = models.CharField(max_length=128, verbose_name='创建人')
    update_time = models.DateTimeField(verbose_name='修改时间', auto_now_add=True, db_index=True)
    update_person = models.CharField(max_length=128, verbose_name='修改人')

    class Meta:
        abstract = True
