"""
@Project: TestReport
@Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
@Data: 2021/03/03
@File: __init__.py.py
"""

import os
import json

template_path = os.path.join(os.path.dirname(__file__), 'template', 'template.html')


def render_template(result_data, template: str):
    name = '${resultData}'
    template = template.replace(name, json.dumps(result_data))
    return template

def output_report(result_data, output_path, filename):
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    with open(template_path, 'rb') as file:
        body = file.read().decode('utf-8')
    output_path = os.path.join(output_path, filename)+'.html'
    with open(output_path, 'w', encoding='utf-8', newline='\n') as write_file:
        html = render_template(result_data, body)
        write_file.write(html)