""" Current project exception classes.
"""
from django.conf import settings
from django.http import Http404

from rest_framework import exceptions
from rest_framework.exceptions import PermissionDenied, ErrorDetail
from rest_framework.response import Response
from rest_framework.views import set_rollback

from scaffold.exceptions.exceptions import AppError


class AppErrors:
    ERROR_ARGS_EXCEPTION = AppError("HD0000", "参数异常")
    ERROR_NOT_IMPLEMENTED = AppError(90001, '功能尚未实现')
    ERROR_PARAMETER_MISSING = AppError(90002, '缺少参数')
    ERROR_ACCESS_OUT_OF_RANGE = AppError(90003, '越界访问')
    ERROR_ACTION_DENIED = AppError(90004, '无权操作', http_status=403)
    ERROR_BRANCH_UNEXPECTED = AppError(90005, '预期外的逻辑分支', debug=True)
    ERROR_INPUT_OUT_OF_RANGE = AppError(90006, '输入参数越界')
    ERROR_MISSING_FILE_UPLOAD = AppError(90007, '缺少文件上传')
    ERROR_SESSION_MIDDLEWARE_NOT_ENABLED = AppError(90008, 'SESSION插件尚未启用')
    ERROR_OBJECT_NOT_FOUND = AppError(90009, '找不到对象')
    ERROR_DELETE_FAIL = AppError(90010, '删除失败')
    ERROR_IMPORT_FAIL = AppError(90011, '导入失败')
    ERROR_GENERATION_TESTCASE_FAIL = AppError(90012, '用例生成失败')
    ERROR_CALLBACK_TESTCASE_FAIL = AppError(90012, '用例召回状态更新失败')
    ERROR_PUSH_TESTCASE_FAIL = AppError(90013, '用例推送失败')
    ERROR_MERGE_TESTCASE_FAIL = AppError(90014, '用例合并失败')
    ERROR_MATCH_TESTCASE_FAIL = AppError(90015, '未找到匹配条件的用例')


if settings.DEBUG:
    for key, val in AppErrors.__dict__.items():
        if key.startswith('ERROR_') and isinstance(val, AppError):
            val.debug = True


# pylint: disable=unused-argument
def exception_handler(exc, context):
    """
    Returns the response that should be used for any given exception.

    By default we handle the REST framework `APIException`, and also
    Django's built-in `Http404` and `PermissionDenied` exceptions.

    Any unhandled exceptions may return `None`, which will cause a 500 error
    to be raised.
    """
    if isinstance(exc, Http404):
        exc = exceptions.NotFound()
    elif isinstance(exc, PermissionDenied):
        exc = exceptions.PermissionDenied()

    if isinstance(exc, exceptions.APIException):
        headers = {}
        if getattr(exc, 'auth_header', None):
            headers['WWW-Authenticate'] = exc.auth_header
        if getattr(exc, 'wait', None):
            headers['Retry-After'] = '%d' % exc.wait

        if isinstance(exc.detail, ErrorDetail):
            if exc.detail.code == 'not_authenticated':
                exc.status_code = 401
                data = dict(
                    ok=False,
                    msg=str(exc.detail),
                    errcode=10006
                )
            else:
                data = dict(
                    ok=False,
                    msg=exc.detail
                )
        elif isinstance(exc.detail, (list, dict)):
            data = dict(
                ok=False,
                msg=str(exc.detail),
                data=exc.detail
            )
        else:
            data = dict(
                ok=False,
                msg=exc.detail
            )

        set_rollback()
        return Response(data, status=exc.status_code, headers=headers)

    return None
