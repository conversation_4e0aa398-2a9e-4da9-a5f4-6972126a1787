from core.servers.caseRun import TestCase
from interface_test.models import *

class APIAutoTest:
    def __init__(self, case_id, env_id, operator):
        self.env_id = env_id
        self.operator = operator
        self.case_id = case_id
        self.caseInfo = CaseInfo.objects.get(case_id=self.case_id)
        self.param = {}
        self.log_data = []

    def execute(self):
        code = "0000"
        try:
            if self.caseInfo.case_type == 'API':
                interfaceInfo = InterfaceInfo.objects.get(interface_id=self.caseInfo.interface_id)
                projectInfo = ProjectInfo.objects.get(project_id=interfaceInfo.project_id)
                if projectInfo.project_mode:
                    if self.env_id == "1":
                        pre_case = ProjectInfo.test_case.split("|")
                    elif self.env_id == "2":
                        pre_case = ProjectInfo.uat_case.split("|")
                    elif self.env_id == "3":
                        pre_case = ProjectInfo.pro_case.split("|")
                    if pre_case:
                        for relation_id in pre_case:
                            scene_relation = SceneRelation.objects.get(scene_relation_id=relation_id)
                            if scene_relation.is_enable:
                                case_id = scene_relation.relation_case_id
                                caseInfo = CaseInfo.objects.get(case_id=case_id)
                                if scene_relation.reset_param:
                                    caseInfo.interface_data = scene_relation.interface_data
                                    caseInfo.is_assert = scene_relation.is_assert
                                    caseInfo.asserts = scene_relation.asserts
                                api_result, case_msg = TestCase(caseInfo, self.env_id, self.person, self.param).case_run()
                                self.log_data.append(case_msg)
                                self.api_result, case_msg = TestCase(self.caseInfo, self.env_id, self.person,
                                                                     self.param).case_run()
                                self.log_data.append(case_msg)
                                respon_param = self.api_result['response']
                                # 提取参数
                                if scene_relation.is_get_param:
                                    self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                                            + "========================= 提取参数 =======================")
                                    if scene_relation.request_param:
                                        str_list = scene_relation.request_param.split(";")
                                        str_list = [i for i in str_list if i != '']  # 去除空元素
                                        for x in str_list:
                                            self.param[x] = GetModelInfo().get_element_by_params(x, self.api_result[
                                                'request'])
                                            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                                                    + "请求参数，%s：%s" % (x, str(self.param[x])))
                                    if scene_relation.response_param:
                                        str_list = scene_relation.response_param.split(";")
                                        param_alias_list = scene_relation.response_param_alias.split(";")
                                        str_list = [i for i in str_list if i != '']  # 去除空元素
                                        i = 0
                                        for x in str_list:
                                            self.param[param_alias_list[i]] = GetModelInfo().get_element_by_params(x,
                                                                                                                   respon_param)
                                            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                                                    + "返回参数，%s：%s" % (
                                                                    x, str(self.param[param_alias_list[i]])))
                                            i = i + 1
                                    if scene_relation.response_header_param:
                                        str_list = scene_relation.response_header_param.split(";")
                                        str_list = [i for i in str_list if i != '']  # 去除空元素
                                        for x in str_list:
                                            self.param[x] = GetModelInfo().get_element_by_params(x,
                                                                                                 self.api_result[
                                                                                                     'response'][
                                                                                                     'header'])
                                            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                                                    + "返回头部，%s：%s" % (x, str(self.param[x])))
                                # 增加休眠
                                if scene_relation.sleep_time != '0':
                                    time.sleep(int(scene_relation.sleep_time))
                                    self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                                            + "========================= 程序休眠 =======================")
                                    self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                                            + "休眠 %s 秒" % scene_relation.sleep_time)

        except Exception as e:
            self.add_log("案例调试失败：%s" % repr(e))
            code = "1001"

        def report1(self):
            code = "0000"
            try:
                self.case_run()
            except Exception as e:
                self.add_log("案例调试失败：%s" % repr(e))
                code = "1001"
            finally:
                if code == "0000":
                    if [s for s in self.log_content if '无需断言' in s]:
                        data = {
                            "assert": "1",
                            "title": "案例ID：" + (self.caseInfo.case_id + " " + self.caseInfo.case_name) + " 无需断言",
                            "detail": self.log_content
                        }
                    elif [s for s in self.log_content if '断言成功' in s]:
                        data = {
                            "assert": "1",
                            "title": "案例ID：" + (self.caseInfo.case_id + " " + self.caseInfo.case_name) + " 断言成功",
                            "detail": self.log_content
                        }
                    else:
                        data = {
                            "assert": "0",
                            "title": "案例ID：" + (self.caseInfo.case_id + " " + self.caseInfo.case_name) + " 断言失败",
                            "detail": self.log_content
                        }
                    result_log = {
                        "code": "0000",
                        "msg": "运行结果",
                        "data": [data]
                    }
                else:
                    result_log = {
                        "code": "1001",
                        "msg": "运行结果",
                        "data": [
                            {
                                "assert": "0",
                                "title": "运行出错",
                                "detail": self.log_content
                            }
                        ]
                    }
                logRe = LogRecord(log_type='2', running_log_type='1', running_log_id=self.caseInfo.case_id,
                                  log_content="\r\n".join(self.log_content), person=self.operator,
                                  temp_time=CommonUtil().get_current_ymdhms())
                logRe.save()
                return result_log
