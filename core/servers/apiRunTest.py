# -*- coding:utf-8 -*-
import datetime
import re
import time
from decimal import Decimal
from core.commonUtils.aesCryptUtil import PrpCrypt
from core.commonUtils.commonUtil import CommonUtil
from core.commonUtils.dbUtil import DBUtils
from core.commonUtils.getModelInfoUtil import GetModelInfo
from core.commonUtils.logger import Logger
from data_config.models import *
from interface_test.models import *
import traceback
# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()

# 断言体系公共方法
def assertResultRule(result, assert_id, env_id, param={}):
    assertResultInfo = AssertResultInfo.objects.get(assert_id=assert_id)
    if assertResultInfo.assert_type == '1':  # 模糊匹配断言
        # return_value返回值
        if assertResultInfo.return_value:
            value_name = assertResultInfo.return_value
            # 返回值参数化
            assertResultInfo.return_value = str(
                GetModelInfo().get_element_by_params(assertResultInfo.return_value, result))
        else:
            if isinstance(result, list):  # 判断
                assertResultInfo.return_value = str(result[0])
            else:
                assertResultInfo.return_value = result
        # 期望结果参数化
        # expect_result预期值
        assertResultInfo.expect_result = parameterization(assertResultInfo.expect_result, env_id, param=param)
        # assert_operator判断符
        if assertResultInfo.assert_operator == '1':  # 等于
            symbol = "="
            if assertResultInfo.return_value == assertResultInfo.expect_result:
                assertResult = "结果为真"
            else:
                assertResult = "结果为假"
            assertMsg = "数据匹配，%s，%s：%s %s %s" % (
            assertResult, symbol, value_name, assertResultInfo.return_value, assertResultInfo.expect_result)
        elif assertResultInfo.assert_operator == '2':  # 不等于
            symbol = "!="
            if assertResultInfo.return_value != assertResultInfo.expect_result:
                assertResult = "结果为真"
            else:
                assertResult = "结果为假"
        elif assertResultInfo.assert_operator == '3':  # 包含
            symbol = "包含"
            if assertResultInfo.expect_result in str(assertResultInfo.return_value):
                assertResult = "结果为真"
            else:
                assertResult = "结果为假"
        elif assertResultInfo.assert_operator == '4':  # 不包含
            symbol = "不包含"
            if assertResultInfo.expect_result not in str(assertResultInfo.return_value):
                assertResult = "结果为真"
            else:
                assertResult = "结果为假"
        assertMsg = "数据匹配，%s：%s %s %s，%s" % (
            value_name, assertResultInfo.return_value, symbol, assertResultInfo.expect_result, assertResult)
    else:  # 四则运算
        if assertResultInfo.return_value:
            # 返回值参数化
            assertResultInfo.return_value = parameterization(assertResultInfo.return_value, env_id, param=result)
        else:
            assertResultInfo.return_value = result
        # 操作值参数化
        assertResultInfo.operation_value = parameterization(assertResultInfo.operation_value, env_id,
                                                                 param=result)
        # 期望结果参数化
        assertResultInfo.expect_result = parameterization(assertResultInfo.expect_result, env_id,
                                                               param=result)
        if assertResultInfo.assert_operator == '1':  # 加法
            temp_1 = Decimal(assertResultInfo.return_value).quantize(Decimal('0.00'))
            temp_2 = Decimal(assertResultInfo.operation_value).quantize(Decimal('0.00'))
            temp_result = Decimal(assertResultInfo.expect_result).quantize(Decimal('0.00'))
            temp = temp_1 + temp_2
            symbol = "+"
            if temp == temp_result:
                assertResult = "结果为真"
            else:
                assertResult = "结果为假"
        elif assertResultInfo.assert_operator == '2':  # 减法
            temp_1 = Decimal(assertResultInfo.return_value).quantize(Decimal('0.00'))
            temp_2 = Decimal(assertResultInfo.operation_value).quantize(Decimal('0.00'))
            temp_result = Decimal(assertResultInfo.expect_result).quantize(Decimal('0.00'))
            temp = temp_1 - temp_2
            symbol = "-"
            if temp == temp_result:
                assertResult = "结果为真"
            else:
                assertResult = "结果为假"
        elif assertResultInfo.assert_operator == '3':  # 乘法
            temp_1 = Decimal(assertResultInfo.return_value).quantize(Decimal('0.00'))
            temp_2 = Decimal(assertResultInfo.operation_value).quantize(Decimal('0.00'))
            temp_result = Decimal(assertResultInfo.expect_result).quantize(Decimal('0.00'))
            temp = temp_1 * temp_2
            symbol = "×"
            if temp == temp_result:
                assertResult = "结果为真"
            else:
                assertResult = "结果为假"
        elif assertResultInfo.assert_operator == '4':  # 除法
            temp_1 = Decimal(assertResultInfo.return_value).quantize(Decimal('0.00'))
            temp_2 = Decimal(assertResultInfo.operation_value).quantize(Decimal('0.00'))
            temp_result = Decimal(assertResultInfo.expect_result).quantize(Decimal('0.00'))
            temp = round(temp_1 / temp_2, 2)
            symbol = "÷"
            if temp == temp_result:
                assertResult = True
            else:
                assertResult = "结果为假"
        assertMsg = "四则运算，%s %s %s = %s，%s" % (
            temp_1, symbol, temp_2, temp_result, assertResult)
    logger.info(assertMsg)
    return assertMsg

# 断言函数
def assertResult(result, log_content, caseInfo, env_id, param={}):
    log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                            + "========================= 断言信息 =======================")
    if caseInfo.is_assert and caseInfo.asserts != " ":
        assertResult_list = []
        for asrt in caseInfo.asserts.split('|'):
            temp = assertResultRule(result, asrt, env_id, param)
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + temp)
            if "结果为真" in temp:
                assertResult_list.append(True)
            else:
                assertResult_list.append(False)
        if all(assertResult_list):
            assert_result_str = "断言成功"
        else:
            assert_result_str = "断言失败"
        log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                + "%s" % assert_result_str)
    else:
        log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                + "无需断言")

# 解析参数
def parameterization(tempStr, env_id, param={}):
        """
            根据类型参数化其请求数据
        :param type:  @ : 测试参数模块获取  $: 从前置参数中或接口返回值中获取  #：断言中从请求参数中获取
        :param tempStr:
        :param param:
        :return:
        """
        result = re.findall('@{(.*?)}@', tempStr)
        if result:
            for x in result:
                temp_result = GetModelInfo().getParameter(x, env_id)
                if isinstance(temp_result, tuple):
                    temp_result = temp_result[0]
                tempStr = tempStr.replace("@{" + x + "}@", str(temp_result))
        result = re.findall('\${(.*?)}\$', tempStr)
        if result:
            for y in result:
                # 逗号后为默认值，格式${参数名,默认值}$ 2021.12.13 huangjiaojiao
                # 获取返回值类型为list：[{key:value}]时，进行参数处理
                if isinstance(param[y.split(",")[0]], list):
                    temp = str(param[y.split(",")[0]]).replace("\'", "\"")
                    tempStr = tempStr.replace("${" + y + "}$", temp)
                tempStr = tempStr.replace("${" + y + "}$", str(param[y.split(",")[0]]))
        # result = re.findall('.*?#{(.*?)}#.*?', tempStr)
        result = re.findall('#{(.*?)}#', tempStr)
        if result:
            for y in result:
                tempStr = str(GetModelInfo().get_element_by_params(y, param))
        return tempStr

# 数据初始化
def initData(caseInfo, log_content, env_id, person, param={}):
    # 开始执行案例
    if caseInfo.case_type == 'SQL':
        log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                           + "========================= 案例信息 =======================")
        log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                           + "案例ID：%s" % caseInfo.case_id)
        log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                           + "案例名称：%s" % caseInfo.case_name)
        interface_data = caseInfo.interface_data

        interface_data = parameterization(interface_data, env_id, param=param)
        # 执行sql查询
        dbinfo = DataBaseInfo.objects.get(data_base_id=caseInfo.init_database)
        dbUtil = DBUtils(dbinfo.data_base_type, dbinfo.ip_address + ":" + dbinfo.data_base_port,
                         dbinfo.db_account, PrpCrypt('HP9lYhuDeeJjEnAo').decrypt(dbinfo.db_pwd), dbinfo.db_name)
        sql_list = interface_data.split(";;")
        sql_list = [i for i in sql_list if i != '']  # 去除空元素
        result = []
        for sql in sql_list:
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "执行语句：%s" % sql)
            if sql and (sql.strip().upper().startswith('SELECT')):
                # sql案例执行结果由数组类型修改为字典，key值为字段名 2022.02.08 huangjiaojiao
                result = dbUtil.queryDB(sql)
            else:
                result.append(dbUtil.update(sql))

        if len(result) != 0:
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "执行结果：%s" % result)
        return result
    elif caseInfo.case_type == 'REDIS':
        log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                           + "========================= 案例信息 =======================")
        log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                           + "案例ID：%s" % caseInfo.case_id)
        log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                           + "案例名称：%s" % caseInfo.case_name)
        interface_data = caseInfo.interface_data

        interface_data = parameterization(interface_data, env_id, param=param)
        # 执行key查询
        dbinfo = DataBaseInfo.objects.get(data_base_id=caseInfo.init_database)
        dbUtil = DBUtils(dbinfo.data_base_type, dbinfo.ip_address + ":" + dbinfo.data_base_port,
                         dbinfo.db_account, PrpCrypt('HP9lYhuDeeJjEnAo').decrypt(dbinfo.db_pwd), dbinfo.db_name)
        key_list = interface_data.split(";;")
        key_list = [i for i in key_list if i != '']  # 去除空元素
        result = []
        for key in key_list:
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "执行语句：%s" % key)
            result = dbUtil.queryKey(key)

        if len(result) != 0:
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                               + "执行结果：%s" % result)
        return result
    else:
        try:
            # 根据接口ID查询接口信息
            interfaceInfo = InterfaceInfo.objects.get(interface_id=caseInfo.interface_id)
            # 根据接口信息查询项目ID,并查询项目信息
            projectInfo = ProjectInfo.objects.get(project_id=interfaceInfo.project_id)
            # 从参数列表提取
            interface_address = parameterization(interfaceInfo.interface_address, env_id, param=param)
            interface_data = parameterization(caseInfo.interface_data, env_id, param=param)
            header = parameterization(interfaceInfo.header, env_id, param=param)
            ip = parameterization(projectInfo.ip, env_id)
            # 生成案例运行预登记ID（自定义）
            pre_record_id = "preRecord" + datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
            if projectInfo.port is None:
                projectInfo.port = ''
            # 没有参数，则给一个默认值：{}
            if interface_data is None or interface_data == '':
                interface_data = str({})
            # 案例运行预登记
            caseRunPreRecord = CaseRunPreRecord(pre_record_id=pre_record_id, environment_id=env_id,
                                                run_type="sceneDebug", case_id=caseInfo.case_id,
                                                case_name=caseInfo.case_name,
                                                interface_agreement=interfaceInfo.interface_agreement,
                                                ip=ip, port=projectInfo.port, interface_way=interfaceInfo.interface_way,
                                                interface_address=interface_address, header=header,
                                                interface_data=interface_data, is_init=caseInfo.is_init,
                                                init_database=caseInfo.init_database, init_sql=caseInfo.init_sql,
                                                is_back=caseInfo.is_back, back_sql=caseInfo.back_sql, creater=person,
                                                is_encrypt=caseInfo.is_encrypt, asserts=caseInfo.asserts,
                                                create_time=time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
            caseRunPreRecord.save()
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "========================= 域名信息 =======================")
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "域名ID：%s" % projectInfo.project_id)
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "域名名称：%s" % projectInfo.project_name)
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "域名/主机：%s" % projectInfo.ip)
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "========================= 接口信息 =======================")
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "接口ID：%s" % interfaceInfo.interface_id)
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "接口名称：%s" % interfaceInfo.interface_name)
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "请求头：%s" % interfaceInfo.header)
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "请求地址：%s" % interfaceInfo.interface_address)
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "========================= 案例信息 =======================")
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "案例ID：%s" % caseInfo.case_id)
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "案例名称：%s" % caseInfo.case_name)

        except Exception as e:
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "接口请求中的变量参数：%s" % caseInfo.interface_data)
            log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "替换接口请求中的变量参数失败：%s" % str(e))
            errorInfo = traceback.format_exc(2)
            # 日志记录
            logger.info(errorInfo)
            raise e
        return caseRunPreRecord