# -*- coding:utf-8 -*-

import datetime
import os
import re
import subprocess
import time
import traceback
from shutil import move

from selenium import webdriver  # 导入库
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
from selenium.webdriver.support.ui import Select
from core.commonUtils.aesCryptUtil import PrpCrypt
from core.commonUtils.commonUtil import CommonUtil
from core.commonUtils.dbUtil import DBUtils
from core.commonUtils.getModelInfoUtil import GetModelInfo
from data_config.models import *
from interface_test.models import *
from ui_test.models import *
# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()
"""
WEB案例执行逻辑
"""


class WebAutoTest:

    # 初始化函数
    def __init__(self, webCaseId, env_type, creater, ipAddress):
        self.webCaseId = webCaseId
        self.env_type = env_type
        self.creater = creater
        self.ipAddress = ipAddress
        self.webCase = WebCaseInfo.objects.get(web_case_id=self.webCaseId)
        formationId = self.webCase.web_case_formation.split("|")[0]
        # 获取元素ID或块ID
        self.formation = CaseFormationInfo.objects.get(case_formation_id=formationId)
        # 组成类型为元素
        if self.formation.formation_type == '0':
            self.element = ElementInfo.objects.get(element_id=self.formation.element_id)
        # 组成类型为块
        else:
            chunk = ChunkInfo.objects.get(chunk_id=self.formation.chunk_id)
            self.element = ElementInfo.objects.get(element_id=chunk.element_pool.split("|")[0])
        self.page = PageInfo.objects.get(page_id=self.element.page_id)
        if self.env_type == '1':
            environment = "测试环境"
        elif self.env_type == '2':
            environment = "预发环境"
        elif self.env_type == '3':
            environment = "生产环境"
        else:
            environment = "未知环境"
        self.driver = None
        self.log_content = []
        self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                + "开始执行WEB-UI测试，当前案例：%s，运行环境为：%s" % (str(self.webCaseId)
                                                                     + "-" + self.webCase.web_case_name, environment))

    # web案例执行主函数
    def debugWebCase(self):
        try:
            self.initDriver()
            self.stepRunning()
            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "休眠5秒后断言并截图保存")
            time.sleep(5)
            self.assertAndScreenShots()
        except Exception as e:
            errorInfo = traceback.format_exc()
            # 日志记录
            logger.info(errorInfo)
            self.log_content.append("案例调试失败：%s" % e)
        finally:
            # 保存日志到数据库
            id = self.logRecord()
            # 数据清理并退出浏览器，解除资源占用
            self.afterDebug()
            # 返回日志内容
            logRecord = LogRecord.objects.get(log_id=id)
            return logRecord.log_content

    # 将案例转换成可执行的脚本
    def transforScript(self):
        script_list = []
        # 构建脚本生成目录以及脚本名
        scriptFile_dir = os.path.abspath("resource/Scripts/")
        os.makedirs(scriptFile_dir, exist_ok=True)
        name = "WEB_" + self.webCaseId + "_%s" % datetime.datetime.now().strftime('%Y%m%d%H%M%S%f') + ".txt"
        file_name = os.path.join(scriptFile_dir, name)
        url_str = self.parameterization(self.page.page_url)
        script_list.append("driver.get('%s')" % url_str)
        # 逐步运行，获取执行步骤并写入列表
        for id in self.webCase.web_case_formation.split("|"):
            formation = CaseFormationInfo.objects.get(case_formation_id=id)
            if formation.formation_type == '0':
                script_list.append(self.getScriptLine(formation.element_id))
            else:
                chunk = ChunkInfo.objects.get(chunk_id=formation.chunk_id)
                for ele_id in chunk.element_pool.split("|"):
                    script_list.append(self.getScriptLine(ele_id))
        # 遍历列表，生成步骤运行 字符串
        scriptContent = ""
        for x in script_list:
            scriptContent = scriptContent + x + "\r\t"
        # 读取模板并在指定位置插入
        with open('resource/model.txt', 'r', encoding='utf-8') as f:
            content = f.read()
            # 找到插入点
            pos = content.find("$")
            # 将插入点的标志替换为空
            content = content.replace("$", "")
            if pos != -1:
                # 在标志位置插入生成好的脚本代码
                content = content[:pos] + scriptContent + content[pos:]
                # 写入到脚本文件
                with open(file_name, "w+", encoding='utf-8') as f2:
                    f2.write(content)
        # 重命名为.py
        new_name = file_name.split(".")[0] + ".py"
        move(file_name, new_name)
        return new_name

    # 获取每一步的脚本代码
    def getScriptLine(self, elmentId):
        operation = ""
        element = ElementInfo.objects.get(element_id=elmentId)
        if element.send_value:
            element.send_value = self.parameterization(element.send_value)
        element_value = (element.element_value).replace("'", "\"")
        num = int(element.element_num)
        webElement = ""
        if element.find_element_type == 'ID':
            webElement = "driver.find_elements_by_id('%s')[%s]" % (element_value, num)
        elif element.find_element_type == 'NAME':
            webElement = "driver.find_elements_by_name('%s')[%s]" % (element_value, num)
        elif element.find_element_type == 'CLASS':
            # 这里重点记录下，通过Class属性定位不到元素时可能是 定位值 包含空格 要把空格替换成 .
            # 参考 https://blog.csdn.net/york1996/article/details/106470045
            temp_str = element_value.replace(" ", ".")
            webElement = "driver.find_elements_by_class_name('%s')[%s]" % (temp_str, num)
        elif element.find_element_type == 'XPATH':
            webElement = "driver.find_elements_by_xpath('%s')[%s]" % (element_value, num)
        elif element.find_element_type == 'CSS_SELECTOR':
            webElement = "driver.find_element_by_css_selector('%s')" % (element_value)
        elif element.find_element_type == 'LINK_TEXT':
            webElement = "driver.find_elements_by_link_text('%s')[%s]" % (element_value, num)
        elif element.find_element_type == 'TAG_NAME':
            webElement = "driver.find_elements_by_tag_name('%s')[%s]" % (element_value, num)
        else:
            logger.info("未知的元素定位方式！")
        # 元素操作
        if element.operate_element_type == 'click':
            operation = webElement + ".click()"
        elif element.operate_element_type == 'sendKeys':
            operation = webElement + ".send_keys('%s')" % element.send_value
        elif element.operate_element_type == 'select':
            Select(webElement).select_by_value(element.send_value)
            operation = "Select(%s)" % webElement + ".select_by_value('%s')" % element.send_value
        elif element.operate_element_type == 'hover':
            operation = "ActionChains(driver).move_to_element(%s).perform()" % webElement
        elif element.operate_element_type == 'scrollTop':
            if element.find_element_type == 'ID':
                js_str = "document.getElementById('%s').scrollTop=%s" % (element_value, element.send_value)
            elif element.find_element_type == 'NAME':
                js_str = "document.getElementsByName('%s')[%s].scrollTop=%s" % (
                    element_value, num, element.send_value)
            elif element.find_element_type == 'CLASS':
                js_str = "document.getElementsByClassName('%s')[%s].scrollTop=%s" % (
                    element_value, num, element.send_value)
            elif element.find_element_type == 'TAG_NAME':
                js_str = "document.getElementsByTagName('%s')[%s].scrollTop=%s" % (
                    element_value, num, element.send_value)
            else:
                # 日志记录
                logger.info("未知的元素定位方式！")
            operation = 'driver.execute_script("%s")' % js_str
        elif element.operate_element_type == 'scrollLeft':
            if element.find_element_type == 'ID':
                jsStr = "document.getElementById('%s').scrollLeft=%s" % (element_value, element.send_value)
            elif element.find_element_type == 'NAME':
                jsStr = "document.getElementsByName('%s')[%s].scrollLeft=%s" % (
                    element_value, num, element.send_value)
            elif element.find_element_type == 'CLASS':
                jsStr = "document.getElementsByClassName('%s')[%s].scrollLeft=%s" % (
                    element_value, num, element.send_value)
            elif element.find_element_type == 'TAG_NAME':
                jsStr = "document.getElementsByTagName('%s')[%s].scrollLeft=%s" % (
                    element_value, num, element.send_value)
            else:
                # 日志记录
                logger.info("未知的元素定位方式！")
            operation = 'driver.execute_script("%s")' % jsStr
        elif element.operate_element_type == 'uploadFile':
            operation = webElement + ".click()"
            temp = element.send_value.replace("\\", "/")
            operation = operation + "\r\t" + "p = subprocess.Popen('%s')" % temp + "\r\t" + "p.kill"
        else:
            # 日志记录
            logger.info("未知的元素操作方式！")
        # 该步骤运行后的其他操作
        # 是否需要休眠
        if element.sleep_time != "0":
            count = int(element.sleep_time)
            operation = operation + "\r\t" + "time.sleep(%s)" % count
        # 页面是否需要滑动
        if element.sweep_page != "0":
            location = self.driver.get_window_size()
            x = location['width'] / 3
            y = location['height'] / 3
            if element.sweep_page == "1":  # 上滑
                js = "window.scrollBy(0, -%s)" % str(y)
                self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                        + "页面上滑")
            elif element.sweep_page == "2":  # 下滑
                js = "window.scrollBy(0, %s)" % str(y)
                self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                        + "页面下滑")
            elif element.sweep_page == "3":  # 左滑
                js = "window.scrollBy(-%s, 0)" % str(x)
                self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                        + "页面左滑")
            elif element.sweep_page == "4":  # 右滑
                js = "window.scrollBy(%s, 0)" % str(x)
                self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                        + "页面右滑")
            operation = operation + "\r\t" + 'driver.execute_script("%s")' % js
        # 页面是否需要切换窗口
        if element.is_switch_window == '0':
            pass
        elif element.is_switch_window == '1':
            operation = operation + "\r\t" + "driver.switch_to.frame('%s')" % element.window_value
        elif element.is_switch_window == '2':
            operation = operation + "\r\t" + "driver.switch_to.default_content()"
        else:
            # 日志记录
            logger.info("非法的窗口切换参数！")
        return operation

    # 实例化浏览器驱动
    def initDriver(self):
        service_url = 'http://%s:12580/wd/hub' % self.ipAddress
        if self.page.page_type == 'WAP':
            mobile_emulation = {"deviceName": "Nexus 5"}
            chrome_options = webdriver.ChromeOptions()
            chrome_options.add_experimental_option("mobileEmulation", mobile_emulation)  # 这里看清楚了，不是add_argument
            self.driver = webdriver.Remote(command_executor=service_url,
                                           desired_capabilities=chrome_options.to_capabilities())
            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "WAP页面，实例化浏览器驱动")
        else:
            self.driver = webdriver.Remote(command_executor=service_url,
                                           desired_capabilities=DesiredCapabilities.CHROME)
            # 最大化
            self.driver.maximize_window()
            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "WEB页面，实例化浏览器驱动并最大化")
        # 隐式等待 设置等待时间为30秒
        self.driver.implicitly_wait(30)

    # 逐步运行
    def stepRunning(self):
        # 检测页面URl是否需要参数化
        self.page.page_url = self.parameterization(self.page.page_url)
        self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                + "打开页面：%s" % self.page.page_url)
        self.driver.get(self.page.page_url)
        for id in self.webCase.web_case_formation.split("|"):
            formation = CaseFormationInfo.objects.get(case_formation_id=id)
            if formation.formation_type == '0':
                self.elementOperate(formation.element_id)
            else:
                chunk = ChunkInfo.objects.get(chunk_id=formation.chunk_id)
                for ele_id in chunk.element_pool.split("|"):
                    self.elementOperate(ele_id)

    # 元素操作
    def elementOperate(self, elementId):
        element = ElementInfo.objects.get(element_id=elementId)
        if element.send_value:
            element.send_value = self.parameterization(element.send_value)
        # 元素定位
        webElement = None
        element_value = element.element_value
        num = int(element.element_num)
        if element.find_element_type == 'ID':
            webElement = self.driver.find_elements_by_id(element_value)[num]
        elif element.find_element_type == 'NAME':
            webElement = self.driver.find_elements_by_name(element_value)[num]
        elif element.find_element_type == 'CLASS':
            # 这里重点记录下，通过Class属性定位不到元素时可能是 定位值 包含空格 要把空格替换成 .
            # 参考 https://blog.csdn.net/york1996/article/details/106470045
            temp_str = element_value.replace(" ", ".")
            webElement = self.driver.find_elements_by_class_name(temp_str)[num]
        elif element.find_element_type == 'XPATH':
            webElement = self.driver.find_elements_by_xpath(element_value)[num]
        elif element.find_element_type == 'CSS_SELECTOR':
            webElement = self.driver.find_element_by_css_selector(element_value)
        elif element.find_element_type == 'LINK_TEXT':
            webElement = self.driver.find_elements_by_link_text(element_value)[num]
        elif element.find_element_type == 'TAG_NAME':
            webElement = self.driver.find_elements_by_tag_name(element_value)[num]
        else:
            # 日志记录
            logger.info("未知的元素定位方式：%s" % element.find_element_type)
        # 元素操作
        if element.operate_element_type == 'click':
            webElement.click()
            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "点击元素：%s" % element.element_name)
        elif element.operate_element_type == 'sendKeys':
            if webElement.get_attribute('value'):
                webElement.clear()
            webElement.send_keys(element.send_value)
            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "元素：%s 赋值为：%s" % (element.element_name, element.send_value))
        elif element.operate_element_type == 'select':
            Select(webElement).select_by_value(element.send_value)
            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "元素：%s选中为：%s" % (element.element_name, element.send_value))
        elif element.operate_element_type == 'hover':
            ActionChains(self.driver).move_to_element(webElement).perform()
            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "鼠标悬停在元素：%s 上" % element.element_name)
        elif element.operate_element_type == 'scrollTop':
            if element.find_element_type == 'ID':
                js_str = 'document.getElementById("%s").scrollTop=%s' % (element_value, element.send_value)
            elif element.find_element_type == 'NAME':
                js_str = 'document.getElementsByName("%s")[%s].scrollTop=%s' % (element_value, num, element.send_value)
            elif element.find_element_type == 'CLASS':
                js_str = 'document.getElementsByClassName("%s")[%s].scrollTop=%s' % (
                element_value, num, element.send_value)
            elif element.find_element_type == 'TAG_NAME':
                js_str = 'document.getElementsByTagName("%s")[%s].scrollTop=%s' % (
                element_value, num, element.send_value)
            else:
                # 日志记录
                logger.info("未知的元素定位方式：%s" % element.find_element_type)
            self.driver.execute_script(js_str)
            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "上下滑动滚动条至 %s 像素" % element.send_value)
        elif element.operate_element_type == 'scrollLeft':
            if element.find_element_type == 'ID':
                jsStr = 'document.getElementById("%s").scrollLeft=%s' % (element_value, element.send_value)
            elif element.find_element_type == 'NAME':
                jsStr = 'document.getElementsByName("%s")[%s].scrollLeft=%s' % (element_value, num, element.send_value)
            elif element.find_element_type == 'CLASS':
                jsStr = 'document.getElementsByClassName("%s")[%s].scrollLeft=%s' % (
                element_value, num, element.send_value)
            elif element.find_element_type == 'TAG_NAME':
                jsStr = 'document.getElementsByTagName("%s")[%s].scrollLeft=%s' % (
                element_value, num, element.send_value)
            else:
                # 日志记录
                logger.info("未知的元素定位方式：%s" % element.find_element_type)
            self.driver.execute_script(jsStr)
            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "左右滑动滚动条至 %s 像素" % element.send_value)
        elif element.operate_element_type == 'uploadFile':
            webElement.click()
            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "调用文件上传组件")
            temp = element.send_value.replace("\\", "/")
            p = subprocess.Popen(temp)
            p.kill
        else:
            # 日志记录
            logger.info("未知的元素操作方式：%s" % element.operate_element_type)
        # 该步骤运行后的其他操作
        # 是否需要休眠
        if element.sleep_time != "0":
            count = int(element.sleep_time)
            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "休眠：%s秒钟" % count)
            time.sleep(count)
        # 页面是否需要滑动
        if element.sweep_page != "0":
            location = self.driver.get_window_size()
            x = location['width'] / 3
            y = location['height'] / 3
            if element.sweep_page == "1":  # 上滑
                js = "window.scrollBy(0, -%s)" % str(y)
                self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                        + "页面上滑")
            elif element.sweep_page == "2":  # 下滑
                js = "window.scrollBy(0, %s)" % str(y)
                self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                        + "页面下滑")
            elif element.sweep_page == "3":  # 左滑
                js = "window.scrollBy(-%s, 0)" % str(x)
                self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                        + "页面左滑")
            elif element.sweep_page == "4":  # 右滑
                js = "window.scrollBy(%s, 0)" % str(x)
                self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                        + "页面右滑")
            self.driver.execute_script(js)
        # 页面是否需要切换窗口
        if element.is_switch_window == '0':
            pass
        elif element.is_switch_window == '1':
            self.driver.switch_to.frame(element.window_value)
            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "切换到新窗口：%s" % element.window_value)
        elif element.is_switch_window == '2':
            self.driver.switch_to.default_content()
            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "切回主窗口")
        else:
            # 日志记录
            logger.info("非法的窗口切换参数！")

    # 断言以及截图部分
    def assertAndScreenShots(self):
        # 断言部分
        self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                + "判断是否需要断言")
        if self.webCase.is_assert:
            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "需要断言")
            # 获取最后操作后的页面内容
            result = self.driver.page_source
            for assertId in self.webCase.asserts.split("|"):
                assertInfo = AssertInfo.objects.get(assert_id=assertId)
                self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                        + "当前断言：%s" % assertInfo.assert_id)
                if assertInfo.assert_type == '1':  # 模糊匹配断言
                    self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                            + "断言类型为模糊匹配，期望结果为：%s" % assertInfo.assert_result)
                    if assertInfo.assert_operator == '==':
                        if assertInfo.assert_result in result:
                            assertResult = "断言成功！"
                        else:
                            assertResult = "断言失败！"
                    elif assertInfo.assert_operator == '!=':
                        if assertInfo.assert_result not in result:
                            assertResult = "断言成功！"
                        else:
                            assertResult = "断言失败！"
                else:
                    self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                            + "断言类型为数据库断言，期望结果为：%s" % assertInfo.assert_result)
                    db = DataBaseInfo.objects.get(data_base_id=assertInfo.assert_database)
                    host2 = db.ip_address + ":" + db.data_base_port
                    dbUtils1 = DBUtils(db.data_base_type, host2, db.db_account,
                                       PrpCrypt('HP9lYhuDeeJjEnAo').decrypt(db.db_pwd), db.db_name)
                    sql_result = dbUtils1.excuteSQL(assertInfo.assert_sql)[0]
                    self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                            + "sql执行结果为：%s" % sql_result)
                    if assertInfo.database_assert_operator == '==':
                        if assertInfo.assert_result == sql_result:
                            assertResult = "断言成功！"
                        else:
                            assertResult = "断言失败！"
                    else:
                        if assertInfo.assert_result != sql_result:
                            assertResult = "断言成功！"
                        else:
                            assertResult = "断言失败！"
            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "最终断言结果为：%s" % assertResult)
        else:
            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "无需断言")
        # 截图并保存
        screenShots_dir = os.path.abspath("resource/ScreenShots/%s/" % time.strftime("%Y%m%d"))
        os.makedirs(screenShots_dir, exist_ok=True)
        name = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f') + ".png"
        file_name = os.path.join(screenShots_dir, name)
        try:
            self.driver.save_screenshot(file_name)
            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "截图成功，图片路径存放在：%s" % file_name)
            WebCaseInfo.objects.filter(web_case_id=self.webCaseId).update(recent_img_url=file_name)
        except Exception as e:
            errorInfo = traceback.format_exc()
            self.log_content.append("截图失败：%s" % errorInfo)
        finally:
            self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                    + "当前案例：%s执行完毕！" % (str(self.webCaseId) + "-" + self.webCase.web_case_name))

    # 参数化
    def parameterization(self, tempStr):
        result = re.findall('.*?@{(\\w*?)}@.*?', tempStr)
        if result:
            for y in result:
                tempStr = tempStr.replace("@{" + y + "}@", GetModelInfo().getParameter(y, self.env_type))
        return tempStr

    def logRecord(self):
        log = ""
        for content in self.log_content:
            log = log + content + "</br>"
        logRe = LogRecord(log_type='2', running_log_type='1', running_log_id=self.webCaseId,
                          log_content=log, person=self.creater,
                          temp_time=time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
        logRe.save()
        return logRe.log_id

    def afterDebug(self):
        self.log_content.clear()
        if self.driver:
            self.driver.quit()
