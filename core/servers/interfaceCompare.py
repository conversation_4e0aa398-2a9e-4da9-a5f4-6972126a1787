from datetime import date
from interface_test.models import *
import time
from tools_help.models import ServiceConfig
# 日志对象实例化
from core.commonUtils.logger import Logger
logger =  Logger.get_logger()


def interface_compare(self):
    nowtime = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    """
    swagger比对skywalking
    """
    # 获取配置表中开启比对的服务配置 compare_enable =1
    serviceconfig_ids = list(ServiceConfig.objects.filter(compare_enable=True).values_list('id', flat=True))
    interface_details = InterfaceDetail.objects.filter(is_handle=0, service_config_id__in=serviceconfig_ids)
    for interfacedetail in interface_details:
        # 取出swagger数据
        detail_address = interfacedetail.interface_address
        detail_service = interfacedetail.service
        detail_way = interfacedetail.interface_way
        detail_description = interfacedetail.interface_description
        detail_body = interfacedetail.interface_body
        detail_param = interfacedetail.interface_parameter
        detail_header = interfacedetail.interface_header
        # 根据swagger中的地址与服务过滤skywalking表中的数据
        interface_visit = InterfaceVisit.objects.filter(interface_address=detail_address, service=detail_service)
        if interface_visit:
            # 如果skywalking表中有数据,循环与interface_info表比对
            for interfacevisit in interface_visit:
                visit_source = interfacevisit.source
                # 处理url中占位符
                processed_address = detail_address.split("/{")[0]
                # 拼出完整接口路径
                detail_full_address = "/"+visit_source+"/"+detail_service+processed_address
                # 入库接口地址
                detail_reall_address = "/" + visit_source + "/" + detail_service + detail_address
                # 根据请求方式+完整地址判断interface_info表中数据存在
                detail_interface_info = InterfaceInfo.objects.filter(interface_address__startswith=detail_full_address,
                                                                     interface_way=detail_way, is_active=1)
                # 若存在,判断入参是否一致
                if detail_interface_info:
                    for detail_interface in detail_interface_info:
                        # 比对interface_info的body与swagger中的是否一致
                        body_result = diff_json("body", detail_body, detail_interface)
                        # 比对interface_info的parameter与swagger中的是否一致
                        param_result = diff_json("param", detail_param, detail_interface)
                        # body和param存在不一致
                        if not body_result or not param_result:
                            # 更新入参数据
                            update_interface_info(detail_body, detail_param, detail_interface)
                    interfacedetail.is_handle = 1
                    interfacedetail.handle_time = nowtime
                    interfacedetail.save()
                # interface_info表中不存在,判断为新增数据,直接插入interface_info表
                else:
                    project_id = ProjectInfo.objects.filter(project_name='自动化域名（请更改）').first().project_id
                    itf1 = InterfaceInfo(project_id=project_id, interface_name=detail_description,
                                        interface_agreement="HTTPS",
                                        interface_way=detail_way, interface_address=detail_reall_address, header=detail_header,
                                        creater="auto", update_person="auto",
                                        create_time=nowtime, update_time=nowtime, body_demo=detail_body,
                                        param_demo=detail_param, is_cover=0, is_handle=0, compare_result="3",
                                        remark="接口比对自动生成接口,请注意此接口暂未实现自动化!")
                    itf1.save()
                    #将skywalking数据置为已处理
                    interfacedetail.is_handle = 1
                    interfacedetail.handle_time = nowtime
                    interfacedetail.save()
    # skywalking表中不存在swagger数据,不处理该swagger数据,输出日志
    logger.info("swagger比对完成,共处理[%s]条数据", len(interface_details))
    """
    skywalking比对swagger数据
    """
    interfaceVisit = InterfaceVisit.objects.filter(is_handle=0)
    for interface in interfaceVisit:
        # 取出skywalking数据
        address = interface.interface_address
        service = interface.service
        source = interface.source
        # 处理url中占位符
        processed_address = address.split("/{")[0]
        # 拼出完整接口路径
        full_address = "/"+source+"/"+service+processed_address
        # 入库接口地址
        reall_address = "/"+source+"/"+service+address
        # 根据skywalking中的地址与服务过滤swagger表中的数据
        interface_detail = InterfaceDetail.objects.filter(interface_address=address, service=service)
        if interface_detail:
            # 如果swagger表中有数据,循环与interface_info表比对
            for detail in interface_detail:
                way = detail.interface_way
                description = detail.interface_description
                body = detail.interface_body
                param = detail.interface_parameter
                header = detail.interface_header
                # 根据请求方式+完整地址判断interface_info表中数据存在
                interface_info = InterfaceInfo.objects.filter(interface_address__startswith=full_address, interface_way=way, is_active=1)
                # 若存在,判断入参是否一致
                if interface_info:
                    for single_interface in interface_info:
                        # 比对interface_info的body与swagger中的是否一致
                        body_result = diff_json("body", body, single_interface)
                        # 比对interface_info的parameter与swagger中的是否一致
                        param_result = diff_json("param", param, single_interface)
                        if not body_result or not param_result:  # body和param存在不一致
                            update_interface_info(body, param, single_interface)
                    interface.is_handle = 1
                    interface.handle_time = nowtime
                    interface.save()
                # interface_info表中不存在,判断为新增数据,直接插入interface_info表
                else:
                    project_id = ProjectInfo.objects.filter(project_name='自动化域名（请更改）').first().project_id
                    itf = InterfaceInfo(project_id=project_id, interface_name=description,
                                        interface_agreement="HTTPS",
                                        interface_way=way, interface_address=reall_address, header=header,
                                        creater="auto", update_person="auto",
                                        create_time=nowtime, update_time=nowtime, body_demo=body,
                                        param_demo=param, is_cover=0, is_handle=0, compare_result="2",
                                        remark="接口比对自动生成接口,请注意此接口暂未实现自动化!")
                    itf.save()
                    #将skywalking数据置为已处理
                    interface.is_handle = 1
                    interface.handle_time = nowtime
                    interface.save()
        # swagger表中不存在skywalking数据,将skywalking数据置为已处理
        else:
            interface.is_handle = 1
            interface.handle_time = nowtime
            interface.save()
    logger.info("skywalking比对完成,共处理[%s]条数据", len(interfaceVisit))
    # 全部待处理数据比对完后,将待处理状态的数据存入interface_compare_result表
    # 先删除当天比对的数据
    handle_list = InterfaceInfo.objects.filter(is_handle=0, is_active=1)
    today_compare_list = InterfaceCompareResult.objects.filter(date=date.today())
    today_compare_list.delete()
    """
    往compare_result表注入待处理的数据
    """
    for handle in handle_list:
        result = InterfaceCompareResult(interface_id=handle.interface_id, compare_result=handle.compare_result, date=date.today())
        result.save()


def diff_json(field, response_data,interface_info):
    # 获取任一行url+请求方式一致的接口信息
    if field == "body":
        assert_data = interface_info.body_demo
    else:
        assert_data = interface_info.param_demo
    # 获取所有url+post一致的接口信息
    # interface_info = InterfaceInfo.objects.filter(interface_address__startswith=adrress, interface_way=way, is_active=1)
    if isinstance(response_data, dict):
        for key in assert_data:
            if key not in response_data:
                # 比对不一致
                return False
        for key in response_data:
            if key in assert_data:
                diff_json(response_data[key], assert_data[key])
            else:
                # 比对不一致
                return False
    elif isinstance(response_data, list):
        if len(response_data) == 0:
            # 比对不一致
            return False
        if len(response_data) != len(assert_data):
            # 比对不一致
            return False
        if response_data:
            if isinstance(response_data[0], dict):
                response_data = sorted(response_data, key=lambda x: x[list(response_data[0].keys())[0]])
            else:
                response_data = sorted(response_data)
        if assert_data:
            if isinstance(assert_data[0], dict):
                assert_data = sorted(assert_data, key=lambda x: x[list(assert_data[0].keys())[0]])
            else:
                assert_data = sorted(assert_data)

        for src_list, dst_list in zip(response_data, assert_data):
            diff_json(src_list, dst_list)
    else:
        if str(response_data) != str(assert_data):
            # 比对不一致
            return False
        else:
            return True


# swagger与interface比对参数不一致时,更新interface_info表方法
def  update_interface_info(body, param, interface_info):
    nowtime = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    """
    is_cover=0,接口未覆盖,更新入参,但比对状态仍保持为原状态,将处理状态更新为待处理
    """
    if interface_info.is_cover == 0:
        if body != interface_info.body_demo:
            interface_info.body_demo_bak = interface_info.body_demo
            interface_info.body_demo = body
        if param != interface_info.param_demo:
            interface_info.param_demo_bak = interface_info.param_demo
            interface_info.param_demo = param
        interface_info.update_time = nowtime
        interface_info.is_handle = 0
        interface_info.update_person = "auto"
        interface_info.save()
    else:
        """
        is_cover=1,接口已覆盖,更新入参,且更新比对状态为"变更"
        """
        # body和param都为空(历史已覆盖,只将入参注入到interface_info表中)
        if interface_info.body_demo is None and interface_info.param_demo is None:
            interface_info.param_demo_bak = interface_info.param_demo
            interface_info.body_demo_bak = interface_info.body_demo
            interface_info.param_demo = param
            interface_info.body_demo = body
            interface_info.update_time = nowtime
            interface_info.update_person = "auto"
            interface_info.save()
        # body或param存在不为空的字段,则证明接口已被初始化过
        # 且is_cover=1,则证明已经实现自动化,因此更新入参,is_cover=1,比对结果为变更,is_handle=0(待处理)
        else:
            if body != interface_info.body_demo:
                interface_info.body_demo_bak = interface_info.body_demo
                interface_info.body_demo = body
            if param != interface_info.param_demo:
                interface_info.param_demo_bak = interface_info.param_demo
                interface_info.param_demo = param
            interface_info.is_handle = 0
            interface_info.compare_result = 1
            interface_info.update_time = nowtime
            interface_info.update_person = "auto"
            interface_info.save()
