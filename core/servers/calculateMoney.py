# -*- coding:utf-8 -*-
from decimal import Decimal

"""
优惠金额计算逻辑类
"""
class OrderMoney(object):

	"""docstring for OrderMoney"""
	def __init__(self, price,num):
		self.price = Decimal(price)
		self.num = int(num)
		self.all_amt = round(self.price * self.num,2)
		self.all_youhui_amt = Decimal('0.00')
		self.youhui_amt = Decimal('0.00')
		self.discount_rate = Decimal('0.1')


	#限时优惠
	def time_limit(self,type,arg):
		if type == '1': #1表示限时折扣，arg为折扣
			money = round(self.price * self.num * Decimal(arg) * self.discount_rate,2)
		elif type == '2':#2表示限时减价，arg表示减价金额
			money = round((self.price - Decimal(arg)) * self.num,2)
		else:#表示固定价格为arg
			money = round(Decimal(arg) * self.num,2)
		self.youhui_amt = round(self.all_amt - money,2)
		self.all_youhui_amt = self.all_youhui_amt + self.youhui_amt
		self.price = round(money / self.num, 4)
		self.all_amt = money

	#多买优惠
	def more_buy(self,type,param):
		goods_num = Decimal(param[0])
		arg = param[1]
		if type == '1':# M件N元
			temp = self.num // goods_num #满足优惠活动的商品件数
			if temp < 1:
				money = self.all_amt #不满足优惠活动
			else:
				money = round(Decimal(arg) + self.price * (self.num - goods_num),2)
		else:# 第M件N折
			youhui_num = self.num // goods_num
			if youhui_num >= 1:
				temp_num = self.num - youhui_num
			else:
				temp_num = self.num
			money = round(self.price * temp_num + youhui_num * (self.price * Decimal(arg) * self.discount_rate),2)
		self.youhui_amt = round(self.all_amt - money, 2)
		self.all_youhui_amt = self.all_youhui_amt + self.youhui_amt
		self.price = round(money / self.num, 4)
		self.all_amt = money

	#循环满减
	def xunhuan_manjian(self,type,arg):
		cill = arg[0]
		discount = arg[1]
		if type == '1':  # 满多少元减价
			if self.all_amt < Decimal(cill):
				money = self.all_amt
			else:
				count = self.all_amt // Decimal(cill)
				money = self.all_amt - Decimal(discount) * count
		else:  # 满多少件减价
			if self.num < int(cill):
				money = self.all_amt
			else:
				youhui_num = self.num // int(cill)  # 循环次数
				money = (self.num * self.price) - (Decimal(discount) * youhui_num)
		money = round(money,2)
		self.youhui_amt = round(self.all_amt - money, 2)
		self.all_youhui_amt = self.all_youhui_amt + self.youhui_amt
		self.price = round(money / self.num, 4)
		self.all_amt = money

	#阶梯满减
	def jieti_manjian(self,type,param):
		cill_list = []
		discount_list = []
		type_list = []
		for rule in param:
			data = rule.split("|")
			cill_list.append(Decimal(data[0]))
			discount_list.append(Decimal(data[1]))
			type_list.append(data[2])
		if type == "1":#件
			# 判断件数是否在规则列表门槛里
			if self.num in cill_list:
				index = cill_list.index(self.num)
				discount_type = type_list[index]
				if discount_type == "0":#减价
					money = round(self.all_amt - discount_list[index], 2)
				else:# 1打折
					money = round(self.all_amt * discount_list[index] * self.discount_rate, 2)
			else:# 不在，将件数添加到规则列表门槛中
				cill_list.append(Decimal(self.num))
				cill_list.sort()
				# 判断件数在门槛列表中的位置
				if cill_list.index(Decimal(self.num)) == 0:  # 表示件数不满足最小规则门槛，直接返回总金额
					money = self.all_amt
				else:  # 表示件数满足规则门槛，返回序列号
					index = cill_list.index(Decimal(self.num)) - 1
					discount_type = type_list[index]
					if discount_type == "0":  # 减价
						money = round(self.all_amt - discount_list[index], 2)
					else:  # 1打折
						money = round(self.all_amt * discount_list[index] * self.discount_rate, 2)
		else:#0   元
			# 判断金额是否在规则列表门槛里
			if self.all_amt in cill_list:
				index = cill_list.index(self.all_amt)
				discount_type = type_list[index]
				if discount_type == "0":  # 减价
					money = round(self.all_amt - discount_list[index], 2)
				else:  # 1打折
					money = round(self.all_amt * discount_list[index] * self.discount_rate, 2)
			else:  # 不在，将件数添加到规则列表门槛中
				cill_list.append(self.all_amt)
				cill_list.sort()
				# 判断件数在门槛列表中的位置
				if cill_list.index(self.all_amt) == 0:  # 表示件数不满足最小规则门槛，直接返回总金额
					money = self.all_amt
				else:  # 表示件数满足规则门槛，返回序列号
					index = cill_list.index(self.all_amt) - 1
					discount_type = type_list[index]
					if discount_type == "0":  # 减价
						money = round(self.all_amt - discount_list[index], 2)
					else:  # 1打折
						money = round(self.all_amt * discount_list[index] * self.discount_rate, 2)
		self.youhui_amt = round(self.all_amt - money, 2)
		self.all_youhui_amt = self.all_youhui_amt + self.youhui_amt
		self.price = round(money / Decimal(self.num), 4)
		self.all_amt = money

	#优惠券计算
	def ticket(self,ticket_type,original_amt,discount):
		if ticket_type == '1':#折扣券
			money = round(Decimal(original_amt) * Decimal(discount) * self.discount_rate)
		else:#抵扣券
			money = round(Decimal(original_amt) - Decimal(discount),2)
		self.all_youhui_amt = round(self.all_youhui_amt + (original_amt-money), 2)

"""
优惠金额计算工具类
"""
class CalculateLog(object):

	#初始化函数
	def __init__(self):
		super(CalculateLog, self).__init__()
		self.log_list = []

	#获取计算结果并整理成数据字典输出
	def getCalculateLog(self,shop_list):
		manjian_list = []
		for shop in shop_list:
			log_dict = {'shop_id': shop['shop_id'], 'shop_price': shop['shop_price'], 'shop_num': shop['shop_num'],
						'time_limit': 0, 'more_buy': 0, 'manjianzeng': 0}
			calculater = OrderMoney(shop['shop_price'], shop['shop_num'])
			if shop['activies']:
				for act in shop['activies']:
					if act['activity_type'] == "1":
						calculater.time_limit(act['child_type'], act['param'])
						log_dict['time_limit'] = calculater.youhui_amt
					elif act['activity_type'] == "2":
						param = act['param'].split("|")
						calculater.more_buy(act['child_type'], param)
						log_dict['more_buy'] = calculater.youhui_amt
					else:
						if str(shop_list).count(str(act['activity_id'])) <= 1:  # 不与其他商品的满减赠活动相同
							if act['activity_type'] == "3":
								param = act['param'].split("|")
								calculater.xunhuan_manjian(act['child_type'], param)
							else:
								calculater.jieti_manjian(act['child_type'], act['param'])
							log_dict['manjianzeng'] = calculater.youhui_amt
						else:  # 与其他商品的满减赠活动相同
							act['money'] = calculater.all_amt
							act['shop_price'] = calculater.price
							act['shop_num'] = shop['shop_num']
							act['shop_id'] = shop['shop_id']
							manjian_list.append(act)
			self.log_list.append(log_dict)
		ladder_dict = {}
		for shop in manjian_list:
			if shop['activity_id'] not in ladder_dict.keys():
				ladder_dict[shop['activity_id']] = {}
				ladder_dict[shop['activity_id']]['child_type'] = shop['child_type']
				ladder_dict[shop['activity_id']]['param'] = shop['param']
				ladder_dict[shop['activity_id']]['total_sum'] = shop['money']
				ladder_dict[shop['activity_id']]['total_num'] = shop['shop_num']
				ladder_dict[shop['activity_id']]['average_sum'] = shop['shop_price']
				ladder_dict[shop['activity_id']]['shop'] = [[shop['shop_id'], shop['money']]]
			else:
				ladder_dict[shop['activity_id']]['total_sum'] = shop['money'] + ladder_dict[shop['activity_id']][
					'total_sum']
				ladder_dict[shop['activity_id']]['total_num'] = shop['shop_num'] + ladder_dict[shop['activity_id']][
					'total_num']
				ladder_dict[shop['activity_id']]['average_sum'] = (
							ladder_dict[shop['activity_id']]['total_sum'] / Decimal(
						ladder_dict[shop['activity_id']]['total_num'])).quantize(Decimal('0.00'))
				ladder_dict[shop['activity_id']]['shop'].append([shop['shop_id'], shop['money']])
		for act in ladder_dict.values():
			calculater = OrderMoney(act['average_sum'], act['total_sum'])
			calculater.jieti_manjian(act['child_type'], act['param'])
			discount_sum = calculater.youhui_amt
			for index, shop in enumerate(act['shop']):
				shop_discount = (shop[1] / act['total_sum'] * discount_sum).quantize(Decimal('0.00'))
				act['shop'][index].append(shop_discount)
				for index, log in enumerate(self.log_list):
					if log['shop_id'] == shop[0]:
						self.log_list[index]['manjianzeng'] = shop_discount

	#将数据字典转换成用户可读的数据展示
	def initLog(self,shop_list):
		self.getCalculateLog(shop_list)
		order_money = Decimal('0.00')
		msg = ''
		for log in self.log_list:
			amt = Decimal(log['shop_num'])*Decimal(log['shop_price'])-\
				  (Decimal(log['time_limit'])+Decimal(log['more_buy'])+Decimal(log['manjianzeng']))
			order_money = order_money + amt
			msg = msg + '商品ID: ' + log['shop_id'] + '    商品数量：' + log['shop_num'] + '    商品单价：' + log['shop_price'] + '\n' \
				  + '限时优惠：' + str(log['time_limit']) + '\n' \
				  + '多买优惠：' + str(log['more_buy']) + '\n' \
				  + '满减赠优惠：' + str(log['manjianzeng']) + '\n' \
				+ '实付金额：' + str(amt) + '\n\n'
		msg = msg + '订单总金额：' + str(order_money)
		return msg

	#将数据字典转换成json格式
	def initLog2(self,shop_list):
		self.getCalculateLog(shop_list)
		order_money = Decimal('0.00')
		msg_list = []
		for log in self.log_list:
			amt = Decimal(log['shop_num'])*Decimal(log['shop_price'])-\
				  (Decimal(log['time_limit'])+Decimal(log['more_buy'])+Decimal(log['manjianzeng']))
			order_money = order_money + amt
			msg_dict = {'shop_id': log['shop_id'], 'shop_num': log['shop_num'], 'shop_price': log['shop_price'],
						'time_limit': log['time_limit'], 'more_buy': log['more_buy'], 'manjianzeng': log['manjianzeng'],
						'amt': amt}
			msg_list.append(msg_dict)
		msg = {'shop_infos': msg_list}
		if ".00" in str(order_money):
			temp = (str(order_money)).split(".")[0] + ".0"
			msg['order_money'] = Decimal(temp)
		else:
			msg['order_money'] = order_money
		return msg