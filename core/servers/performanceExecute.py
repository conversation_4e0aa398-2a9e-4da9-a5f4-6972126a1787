# -*- coding:utf-8 -*-
from data_config.models import *
from shutil import move
import datetime,time,os,re,traceback
from core.commonUtils.aesCryptUtil import PrpCrypt
from core.commonUtils.commonUtil import async_call
from core.commonUtils.dbUtil import DBUtils
from core.commonUtils.getModelInfoUtil import GetModelInfo
from interface_test.models import *
from performance_test.models import *

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()

'''
@Desc  : 基于Locust，提供简单的压测服务，当前类主要是创建locust脚本
'''

class PerfomanceExecute:

    def __init__(self,locust_task_id,enviroment,creater):
        self.locust_task_id = locust_task_id
        self.enviroment = enviroment
        self.creater = creater
        self.locustInfo = LocustTaskRecord.objects.get(locust_task_id=self.locust_task_id)
        self.host = ""

    @async_call
    def runLocust(self):
        try:
            name = self.transforScript()
            # 报告路径
            report_dir = "resource/test_report/%s/" % time.strftime("%Y%m%d")
            if not os.path.exists(report_dir):
                os.makedirs(report_dir)
            # 报告名称
            filename = "locust_report_" + datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
            #报告完整地址
            file_path = os.path.join(os.path.abspath(report_dir), filename + ".html")
            #运行
            # -f执行性能测试脚本
            #--headless非WEB界面，命令行窗口运行
            # -u并发虚拟用户数
            # -r每秒启动用户数
            # -t运行时间
            print("locust -f %s --host=%s --headless -u %s -r %s -t %ss --html %s" %
                      (name,self.host,self.locustInfo.number_of_user,self.locustInfo.user_spawned_second,
                         self.locustInfo.run_time,file_path))
            os.system("locust -f %s --host=%s --headless -u %s -r %s -t %ss --html %s" %
                      (name,self.host,self.locustInfo.number_of_user,self.locustInfo.user_spawned_second,
                         self.locustInfo.run_time,file_path))
            #读取报告内容
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            #将报告内容等存入数据库LocustReport表
            locustReport = LocustReport(locust_file_name=name,report_content=content,creater=self.creater,create_time=create_time)
            locustReport.save()
            record_id = locustReport.record_id
            #更新性能测试任务locust_report_id指向LocustReport主键，以及任务状态为S成功
            LocustTaskRecord.objects.filter(locust_task_id=self.locust_task_id).update(locust_task_status='S',locust_report_id=record_id)
        except Exception as e:
            errorInfo = traceback.format_exc()
            logger.info("性能测试任务执行失败：%s" % errorInfo)
            LocustTaskRecord.objects.filter(locust_task_id=self.locust_task_id).update(locust_task_status='F')
        finally:
            self.host = ""


    # 转换成可执行的脚本
    def transforScript(self):
        # 构建脚本生成目录以及脚本名
        scriptFile_dir = os.path.abspath("resource/Scripts/")
        os.makedirs(scriptFile_dir, exist_ok=True)
        #脚本名
        name = "locust_" + str(self.locust_task_id) + "_%s" % datetime.datetime.now().strftime('%Y%m%d%H%M%S%f') + ".txt"
        file_name = os.path.join(scriptFile_dir, name)
        #调用方法生成待测案例数据
        scriptContent = self.getContentInfo()
        # 重命名为.py
        new_name = file_name.split(".")[0] + ".py"
        # 读取模板并在指定位置插入
        with open('resource/locustModel.txt', 'r', encoding='utf-8') as f:
            content = f.read()
            # 找到插入点1，插入接口案例执行数据
            pos = content.find("${}$")
            # 将插入点的标志替换为空
            content = content.replace("${}$", "")
            if pos != -1:
                # 在标志位置插入生成好的脚本代码
                content = content[:pos] + scriptContent + content[pos:]
                # 写入到脚本文件
                with open(file_name, "w+", encoding='utf-8') as f2:
                    f2.write(content)
            # 找到插入点2，写入host数据
            pos2 = content.find("#{}#")
            # 将插入点的标志替换为空
            content = content.replace("#{}#", "")
            if pos2 != -1:
                # 在标志位置插入生成好的脚本代码
                content = content[:pos2] + ('host = "%s"' % self.host) + content[pos2:]
                # 写入到脚本文件
                with open(file_name, "w+", encoding='utf-8') as f3:
                    f3.write(content)
        #重命名
        move(file_name, new_name)
        return new_name

    #获取脚本内容
    def getContentInfo(self):
        step_list = ""
        for x in self.locustInfo.case_id.split("|"):
            case_id = x.split(",")[0]
            weight = x.split(",")[1]
            caseInfo = CaseInfo.objects.get(case_id=case_id)
            step_list = step_list + '@task(%s)' % weight + "\r\t\t"
            step_list = step_list + 'def test_%s(self):' % case_id + "\r\t\t\t"
            if caseInfo.case_type == "SQL":
                # 从参数列表提取
                interface_data = self.parameterization("@", caseInfo.interface_data)
                # 执行sql查询
                dbinfo = DataBaseInfo.objects.get(data_base_id=caseInfo.init_database)
                dbUtil = DBUtils(dbinfo.data_base_type, dbinfo.ip_address + ":" + dbinfo.data_base_port,
                                 dbinfo.db_account, PrpCrypt('HP9lYhuDeeJjEnAo').decrypt(dbinfo.db_pwd), dbinfo.db_name)
                result = str(dbUtil.queryDB(interface_data)[0][0])
            else:
                interfaceInfo = InterfaceInfo.objects.get(interface_id=caseInfo.interface_id)
                projectInfo = ProjectInfo.objects.get(project_id=interfaceInfo.project_id)
                projectInfo.ip = self.parameterization("@",projectInfo.ip)
                if projectInfo.port:
                    if interfaceInfo.interface_agreement == "HTTPS":
                        self.host = "https://" + projectInfo.ip + ":" + projectInfo.port
                    else:
                        self.host = "http://" + projectInfo.ip + ":" + projectInfo.port
                else:
                    if interfaceInfo.interface_agreement == "HTTPS":
                        self.host = "https://" + projectInfo.ip
                    else:
                        self.host = "http://" + projectInfo.ip
                #先判断是否需要初始化
                if caseInfo.is_init:
                    dbinfo = DataBaseInfo.objects.get(data_base_id=caseInfo.init_database)
                    host = dbinfo.ip_address + ":" + dbinfo.data_base_port
                    step_list = step_list + 'dbUtils = DBUtils("%s", "%s", "%s","%s", "%s")' % (dbinfo.data_base_type,host, dbinfo.db_account,
							  PrpCrypt('HP9lYhuDeeJjEnAo').decrypt(dbinfo.db_pwd), dbinfo.db_name) + "\r\t\t\t"
                    step_list = step_list + 'for sql in "%s".split(";;"):' % caseInfo.init_sql + "\r\t\t\t\t"
                    step_list = step_list + 'dbUtils.excuteSQL(sql)' + "\r\t\t\t"
                #请求头
                if interfaceInfo.header:
                    headers = self.parameterization("@", interfaceInfo.header)
                else:
                    headers = {
                        "Accept": "*/*",
                        "Accept-Encoding": "gzip, deflate, br",
                        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                        "Connection": "keep-alive",
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36"
                    }
                step_list = step_list + ("headers = %s" % headers + "\r\t\t\t")
                #请求数据
                data = self.parameterization("@", caseInfo.interface_data)
                data = self.parameterization("$", data)
                step_list = step_list + ("data = %s" % data + "\r\t\t\t")
                if interfaceInfo.interface_way == "POST":
                    step_list = step_list + (
                            'with self.client.post("%s",json=data,headers=headers,catch_response=True) as response:'
                            % interfaceInfo.interface_address + "\r\t\t\t\t")
                elif interfaceInfo.interface_way == "GET":
                    step_list = step_list + (
                            'with self.client.get("%s",params=data,headers=headers,catch_response=True) as response:'
                            % interfaceInfo.interface_address + "\r\t\t\t\t")
                elif interfaceInfo.interface_way == "PATCH":
                    step_list = step_list + (
                            'with self.client.patch("%s",data=data,headers=headers,catch_response=True) as response:'
                            % interfaceInfo.interface_address + "\r\t\t\t\t")
                elif interfaceInfo.interface_way == "PUT":
                    step_list = step_list + (
                            'with self.client.put("%s",json=data,headers=headers,catch_response=True) as response:'
                            % interfaceInfo.interface_address + "\r\t\t\t\t")
                else:
                    pass
                step_list = step_list + ('print("response.text====================>",response.text)' + "\r\t\t\t\t")
                step_list = step_list + ('if "操作成功" in response.text:' + "\r\t\t\t\t\t")
                step_list = step_list + ('response.success()' + "\r\t\t\t\t")
                step_list = step_list + ('else:' + "\r\t\t\t\t\t")
                step_list = step_list + ('response.failure("no data")' + "\r\n\t\t")
        return step_list


    # 根据类型参数化其请求数据
    def parameterization(self, type, tempStr, param={}):
        if type == "@":
            result = re.findall('.*?@{(.*?)}@.*?', tempStr)
            if result:
                for x in result:
                    tempStr = tempStr.replace("@{" + x + "}@", str(GetModelInfo().getParameter(x, self.enviroment)))
        elif type == "$":
            result = re.findall('.*?\${(.*?)}\$.*?', tempStr)
            if result:
                for y in result:
                    if len(y.split(",")) != 1:
                        tempStr = tempStr.replace("${" + y + "}$", y.split(",")[1])
                    else:
                        tempStr = tempStr.replace("${" + y + "}$", "")
        elif type == "#":
            result = re.findall('.*?#{(.*?)}#.*?', tempStr)
            if result:
                for y in result:
                    if self.caseInfo.case_type == 'API':
                        tempStr = tempStr.replace("#{" + y + "}#", str(param[y]))
                    else:  # sql案例直接取列表中的值即可
                        tempStr = tempStr.replace("#{" + y + "}#", param[0])
        else:
            tempStr = tempStr
        return tempStr
