# -*- coding:utf-8 -*-

from appium import webdriver
from appium.webdriver.common.touch_action import TouchAction
import datetime,traceback,time,os,re
from shutil import move
from core.commonUtils.aesCryptUtil import PrpCrypt
from core.commonUtils.dbUtil import DBUtils
from core.commonUtils.getModelInfoUtil import GetModelInfo
from data_config.models import *
from interface_test.models import *
from ui_test.models import *

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()

"""
安卓自动化测试底层运行类
"""
class AndroidAutoTest:
    #初始化函数
    def __init__(self,androidCase_id,env_type,creater,ip):
        self.androidCase_id = androidCase_id
        self.env_type = env_type
        self.creater = creater
        self.ip = ip
        #安卓案例信息
        self.androidCase = AndroidCaseInfo.objects.get(androidCase_id=self.androidCase_id)
        #待测设备信息
        self.deviceInfo = DeviceInfo.objects.get(device_id=self.androidCase.test_device)
        #待测应用信息
        self.applicationInfo = ApplicationInfo.objects.get(application_id=self.androidCase.test_application)

        if self.env_type == '1':
            environment = "测试环境"
        elif self.env_type == '2':
            environment = "预发环境"
        elif self.env_type == '3':
            environment = "生产环境"
        else:
            environment = "未知环境"
        self.log_content = []
        self.driver = None
        self.width = None
        self.height = None
        self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                + "开始执行Android-UI测试，当前案例：%s，运行环境为：%s" % (str(self.androidCase_id)
                                + "-" + self.androidCase.androidCase_name, environment))
    #案例运行主函数
    def debugAndroidCase(self):
        try:
            self.initDriver()
            self.stepRunning()
            self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                    + "休眠5秒后断言" )
            time.sleep(5)
            self.assertResults()
            self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                    + "当前案例：%s执行完毕！" % (
                                            str(self.androidCase_id) + "-" + self.androidCase.androidCase_name))
        except Exception as e:
            errorInfo = traceback.format_exc()
            # 日志记录
            logger.info(errorInfo)
            self.log_content.append("案例调试失败：%s" % e)
        finally:
            self.screenShot()
            #保存日志到数据库
            id = self.logRecord()
            #数据清理并退出浏览器，解除资源占用
            self.afterDebug()
            #返回日志内容
            logRecord = LogRecord.objects.get(log_id=id)
            return logRecord.log_content

    #实例化驱动
    def initDriver(self):
        service_url = 'http://%s:4723/wd/hub' % self.ip
        # app启动参数
        desired_caps = {
            "platformName": "Android",#测试平台 一般是Android和IOS
            "deviceName": self.deviceInfo.device_name,#待测设备名
            "appPackage": self.applicationInfo.application_packageName,#待测应用包名
            "appActivity": self.applicationInfo.application_activityName,#待测应用启动页
            "automationName": "uiautomator2",#执行引擎
            "udid": self.deviceInfo.device_udid,#待测设备唯一标识
            "platformVersion": self.deviceInfo.platform_version,#待测设备系统版本号，这里是指安卓版本
            "noSign": True,#不重置应用签名
            "noReset": True,#不重置应用
            "newCommandTimeout": 120,#未收到下一个操作时的超时时间
            "androidDeviceReadyTimeout": 50,#安卓设备等待就绪的最大超时时间
            "sessionOverride": True#是否覆盖原有Session
        }
        self.driver = webdriver.Remote(service_url, desired_caps)  # 启动app
        self.driver.implicitly_wait(20)  # 设置智能等待20秒
        # 屏幕宽
        self.width = self.driver.get_window_size()['width']
        # 屏幕高
        self.height = self.driver.get_window_size()['height']

    #判断元素是否存在
    def elementIsExists(self, element, type):
        try:
            if type == 'ID':
                self.driver.find_element_by_id(element)
                return True
            elif type == 'NAME':
                self.driver.find_element_by_name(element)
                return True
            elif type == 'CLASS':
                self.driver.find_element_by_class_name(element)
                return True
            elif type == 'XPATH':
                self.driver.find_element_by_xpath(element)
                return True
            elif type == 'AccessibilityId':
                self.driver.find_element_by_accessibility_id(element)
                return True
            elif type == 'AndroidUiautomator':
                self.driver.find_element_by_android_uiautomator(element)
                return True
            else:
                return False
        except Exception as e:
            return False

    #逐步运行
    def stepRunning(self):
        for id in self.androidCase.androidCase_formation.split("|"):
            formation = CaseFormationInfo.objects.get(case_formation_id=id)
            if formation.formation_type == '0':#组成类型为单一元素
                self.elementOperate(formation.element_id)
            else:#组成类型为块
                chunk = ChunkInfo.objects.get(chunk_id=formation.chunk_id)
                for ele_id in chunk.element_pool.split("|"):
                    self.elementOperate(ele_id)

    #元素操作
    def elementOperate(self,elementId):
        element = AndroidElementInfo.objects.get(appElement_id=elementId)
        #参数化赋值
        if element.send_value:
            element.send_value = self.parameterization(element.send_value)
        # 元素定位
        androidElement = None
        if element.find_appElement_type == 'Point':
            self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                   + "仅通过坐标定位元素：%s" % (elementId + "-" + element.appElement_name))
        else:
            flag = self.elementIsExists(element.appElement_value,element.find_appElement_type)
            if flag:
                if element.find_appElement_type == 'ID':
                    androidElement = self.driver.find_element_by_id(element.appElement_value)
                elif element.find_appElement_type == 'NAME':
                    androidElement = self.driver.find_element_by_name(element.appElement_value)
                elif element.find_appElement_type == 'CLASS':
                    androidElement = self.driver.find_element_by_class_name(element.appElement_value)
                elif element.find_appElement_type == 'XPATH':
                    androidElement = self.driver.find_element_by_xpath(element.appElement_value)
                elif element.find_appElement_type == 'AccessibilityId':
                    androidElement = self.driver.find_element_by_accessibility_id(element.appElement_value)
                elif element.find_appElement_type == 'AndroidUiautomator':
                    androidElement = self.driver.find_element_by_android_uiautomator(element.appElement_value)
                else:
                    logger.info("元素定位方式参数错误：%s" % element.find_appElement_type)
            else:
                self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                        + "元素：%s 不存在" % (elementId+"-"+element.appElement_name))
        # 元素操作
        if element.operate_appElement_type == 'click':#元素点击事件
            if element.find_appElement_type == 'Point':#当元素定位类型为Point时表示无法通过元素定位，只能通过坐标点
                point_x = element.appElement_value.split(",")[0]
                point_y = element.appElement_value.split(",")[1]
                TouchAction(self.driver).press(x=point_x, y=point_y).release().perform()
            else:#常规的元素点击事件
                androidElement.click()
                self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                    + "点击元素：%s" % element.appElement_name)
        elif element.operate_appElement_type == 'sendKeys':#元素赋值事件
            androidElement.clear()
            androidElement.send_keys(element.send_value)
            self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                    + "元素：%s 赋值为：%s" % (element.appElement_name, element.send_value))
        elif element.operate_appElement_type == 'isExists':#判断元素是否存在
            if flag and element.appElement_exists:
                self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                        + "执行元素存在时的操作：")
                self.elementOperate(element.appElement_exists)
            if (1-flag) and element.appElement_not_exists:#元素不存在时进行的操作
                self.elementOperate(element.appElement_not_exists)
                self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                + "执行元素不存在时的操作：")
        else:
            logger.info("元素操作类型参数错误：%s" % element.operate_appElement_type)
        # 该步骤运行后的其他操作
        # 是否需要休眠
        if element.sleep_time != "0":
            count = int(element.sleep_time)
            self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                    + "休眠：%s秒钟" % count)
            time.sleep(count)
        # 页面是否需要滑动
        if element.sweep_page != "0":
            if element.sweep_page == "1":  # 页面自下向上滑动
                self.driver.swipe(self.width * 0.5,self.height * 0.9,self.width * 0.5,self.height * 0.1,1000)
                self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                        + "页面上滑")
            elif element.sweep_page == "2":  # 页面自上向下滑动
                self.driver.swipe(self.width * 0.5,self.height * 0.1,self.width * 0.5,self.height * 0.9,1000)
                self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                        + "页面下滑")
            elif element.sweep_page == "3":  # 页面自左向右滑动
                self.driver.swipe(self.width * 0.2,self.height * 0.5,self.width * 0.8,self.height * 0.5,1000)
                self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                        + "页面左滑")
            elif element.sweep_page == "4":  # 页面自右向右左滑动
                self.driver.swipe(self.width * 0.8,self.height * 0.5,self.width * 0.2,self.height * 0.5,1000)
                self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                        + "页面右滑")
            else:
                logger.info("页面滑动类型参数错误：%s" % element.sweep_page)

    # 断言
    def assertResults(self):
        # 断言
        self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                + "判断是否需要断言")
        if self.androidCase.is_assert:
            self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                    + "需要断言")
            # 获取最后操作后的页面内容
            result = self.driver.page_source
            assertResult_list = []
            for asrt in self.androidCase.asserts.split('|'):
                assert_result = False
                assertInfo = AssertInfo.objects.get(assert_id=asrt)
                self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                        + "当前断言：%s" % assertInfo.assert_id)
                if assertInfo.assert_type == '1':  # 模糊匹配断言
                    self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                            + "断言类型为模糊匹配，期望结果为：%s" % assertInfo.assert_result)
                    if assertInfo.assert_operator == '==':
                        if assertInfo.assert_result in result:
                            assert_result = True
                        else:
                            assert_result = False
                    elif assertInfo.assert_operator == '!=':
                        if assertInfo.assert_result not in result:
                            assert_result = True
                        else:
                            assert_result = False
                else:
                    self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                            + "断言类型为数据库断言，期望结果为：%s" % assertInfo.assert_result)
                    dbinfo = DataBaseInfo.objects.get(data_base_id=assertInfo.assert_database)
                    self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                            + "执行数据库断言操作：在 %s 数据库执行 %s SQL语句" % (dbinfo.db_name, assertInfo.assert_sql))
                    host = dbinfo.ip_address + ":" + dbinfo.data_base_port
                    dbUtils = DBUtils(dbinfo.data_base_type, host, dbinfo.db_account,
                                      PrpCrypt('HP9lYhuDeeJjEnAo').decrypt(dbinfo.db_pwd), dbinfo.db_name)
                    sql_result = dbUtils.queryDB(assertInfo.assert_sql)[0][0]
                    self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                            + "sql执行结果为：%s" % sql_result)
                    if assertInfo.assert_operator == '==':
                        if assertInfo.assert_result == str(sql_result):
                            assert_result = True
                        else:
                            assert_result = False
                    else:
                        if assertInfo.assert_result != str(sql_result):
                            assert_result = True
                        else:
                            assert_result = False
                assertResult_list.append(assert_result)
            if all(assertResult_list):
                assert_result_str = "断言成功！"
            else:
                assert_result_str = "断言失败！"
            self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                    + "最终断言结果为：%s" % assert_result_str)
        else:
            self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                    + "无需断言")

    #案例执行完成后截图
    def screenShot(self):
        # 截图并保存
        screenShots_dir = os.path.abspath("resource/ScreenShots/%s/" % time.strftime("%Y%m%d"))
        os.makedirs(screenShots_dir, exist_ok=True)
        name = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f') + ".png"
        file_name = os.path.join(screenShots_dir, name)
        try:
            self.driver.get_screenshot_as_file(file_name)
            self.log_content.append("【" + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S:%f') + "】 "
                                    + "截图成功，图片路径存放在：%s" % file_name)
            AndroidCaseInfo.objects.filter(androidCase_id=self.androidCase_id).update(recent_img_url=file_name)
        except Exception as e:
            errorInfo = traceback.format_exc()
            # 日志记录
            logger.info(errorInfo)
            self.log_content.append("截图失败：%s" % e)


    #参数化
    def parameterization(self,tempStr):
        result = re.findall('.*?@{(\\w*?)}@.*?', tempStr)
        if result:
            for y in result:
                tempStr = tempStr.replace("@{" + y + "}@",str(GetModelInfo().getParameter(y, self.env_type)))
        return tempStr

    #日志记录
    def logRecord(self):
        log = ""
        for content in self.log_content:
            log = log + content + "</br>"
        logRe = LogRecord(log_type='2', running_log_type='1', running_log_id=self.androidCase_id,
                          log_content=log, person=self.creater,
                          temp_time=time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
        logRe.save()
        return logRe.log_id

    #案例执行完成后释放全局变量
    def afterDebug(self):
        self.log_content.clear()
        self.width = None
        self.height = None
        if self.driver:
            self.driver.quit()

    #将案例转换成可执行的py脚本
    def transforScript(self):
        script_list = []
        #构建脚本生成目录以及脚本名
        scriptFile_dir = os.path.abspath("resource/Scripts/")
        os.makedirs(scriptFile_dir, exist_ok=True)
        name = "Android_" +self.androidCase_id + "_%s" % datetime.datetime.now().strftime('%Y%m%d%H%M%S%f') + ".txt"
        file_name = os.path.join(scriptFile_dir, name)
        # app启动参数
        desired_caps = '''{
            "platformName": "Android",
            "deviceName": "%s",
            "appPackage": "%s",
            "appActivity": "%s",
            "automationName": "uiautomator2",
            "udid": "%s",
            "platformVersion": "%s",
            "noSign": True,
            "noReset": True,
            "newCommandTimeout": 120,
            "androidDeviceReadyTimeout": 50,
            "sessionOverride": True
        }''' % (self.deviceInfo.device_name,self.applicationInfo.application_packageName,
             self.applicationInfo.application_activityName,self.deviceInfo.device_udid,self.deviceInfo.platform_version)
        script_list.append("driver = webdriver.Remote('http://127.0.0.1:4723/wd/hub', %s)" % desired_caps)
        script_list.append("driver.implicitly_wait(20)")
        script_list.append("width = driver.get_window_size()['width']")
        script_list.append("height = driver.get_window_size()['height']")
        #逐步运行，获取执行步骤并写入列表
        for id in self.androidCase.androidCase_formation.split("|"):
            formation = CaseFormationInfo.objects.get(case_formation_id=id)
            if formation.formation_type == '0':
                script_list.append(self.getScriptLine(formation.element_id))
            else:
                chunk = ChunkInfo.objects.get(chunk_id=formation.chunk_id)
                for ele_id in chunk.element_pool.split("|"):
                    script_list.append(self.getScriptLine(ele_id))
        #遍历列表，生成步骤运行 字符串
        scriptContent = ""
        for x in script_list:
            scriptContent = scriptContent + x + "\r\t"
        #读取模板并在指定位置插入
        with open('resource/androidModel.txt','r',encoding='utf-8') as f:
            content = f.read()
            #找到插入点
            pos = content.find("$")
            #将插入点的标志替换为空
            content = content.replace("$", "")
            if pos != -1:
                #在标志位置插入生成好的脚本代码
                content = content[:pos] + scriptContent + content[pos:]
                #写入到脚本文件
                with open(file_name,"w+",encoding='utf-8') as f2:
                    f2.write(content)
        #重命名为.py
        new_name = file_name.split(".")[0]+".py"
        move(file_name, new_name)
        return new_name

    # 获取每一步的脚本代码
    def getScriptLine(self, elementId):
        operation = ""
        element = AndroidElementInfo.objects.get(appElement_id=elementId)
        # 参数化赋值
        if element.send_value:
            element.send_value = self.parameterization(element.send_value)
        #为方便转换脚本，将用户输入的元素值全部转成 单引号 '
        element_value = (element.appElement_value).replace("\"", "'")
        # 元素定位
        androidElement = ""
        if element.find_appElement_type == 'Point':
            pass
        else:
            if element.find_appElement_type == 'ID':
                androidElement = 'driver.find_element_by_id("%s")' % element_value
            elif element.find_appElement_type == 'NAME':
                androidElement = 'driver.find_element_by_name("%s")' % element_value
            elif element.find_appElement_type == 'CLASS':
                androidElement = 'driver.find_element_by_class_name("%s")' % element_value
            elif element.find_appElement_type == 'XPATH':
                androidElement = 'driver.find_element_by_xpath("%s")' % element_value
            elif element.find_appElement_type == 'AccessibilityId':
                androidElement = 'driver.find_element_by_accessibility_id("%s")' % element_value
            elif element.find_appElement_type == 'AndroidUiautomator':
                androidElement = 'driver.find_element_by_android_uiautomator("%s")' % element_value
            else:
                logger.info("元素定位方式参数错误：%s" % element.find_appElement_type)
        # 元素操作
        if element.operate_appElement_type == 'click':
            if element.find_appElement_type == 'Point':
                point_x = element.appElement_value.split(",")[0]
                point_y = element.appElement_value.split(",")[1]
                operation = "TouchAction(driver).press(x=%s, y=%s).release().perform()" % (point_x,point_y)
            else:
                operation = androidElement + ".click()"
        elif element.operate_appElement_type == 'sendKeys':
            operation = androidElement + ".send_keys('%s')" % element.send_value
        elif element.operate_appElement_type == 'isExists':
            if element.appElement_exists:
                operation = self.getScriptLine(element.appElement_exists)
            if element.appElement_not_exists:
                operation = self.getScriptLine(element.appElement_not_exists)
        else:
            logger.info("元素操作类型参数错误：%s" % element.operate_appElement_type)
        # 该步骤运行后的其他操作
        # 是否需要休眠
        if element.sleep_time != "0":
            count = int(element.sleep_time)
            operation = operation + "\r\t" + "time.sleep(%s)" % count
        # 页面是否需要滑动
        if element.sweep_page != "0":
            if element.sweep_page == "1":  # 页面自下向上滑动
                operation = operation + "\r\t" +  "driver.swipe(width * 0.5, height * 0.9, width * 0.5, height * 0.1, 1000)"
            elif element.sweep_page == "2":  # 页面自上向下滑动
                operation = operation + "\r\t" + "driver.swipe(width * 0.5, height * 0.1, width * 0.5, height * 0.9, 1000)"
            elif element.sweep_page == "3":  # 页面自左向右滑动
                operation = operation + "\r\t" + "driver.swipe(width * 0.2, height * 0.5, width * 0.8, height * 0.5, 1000)"
            elif element.sweep_page == "4":  # 页面自右向右左滑动
                operation = operation + "\r\t" + "driver.swipe(width * 0.8, height * 0.5, width * 0.2, height * 0.5, 1000)"
        return operation

