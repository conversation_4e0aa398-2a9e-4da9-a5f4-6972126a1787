# -*- coding:utf-8 -*-
import re
import time
import traceback
import asyncio

from decimal import Decimal

from base.models import UserInfo
from core.commonUtils.commonUtil import CommonUtil
from core.commonUtils.emailUtil import SendEmail
from core.servers.androidAutoTest import AndroidAutoTest
from core.servers.apiAutoTest import APIAutoTest
from core.servers.sceneExecute import SceneExecute
from core.servers.webAutoTest import WebAutoTest
from data_config.models import *
from interface_test.models import *
from ui_test.models import *
import requests
import concurrent.futures
from datetime import datetime
from django.conf import settings

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()

# 读取配置文件，服务接口IP
service_ip = settings.SERVICE_IP
"""
任务执行器
"""


class TaskExecute:
    def __init__(self, task_id, ipAddress="", batch_number="", code_name="", email="" , record_id="", channel=""):
        self.task_id = task_id
        self.batch_number = batch_number
        self.channel = channel
        self.code_name = code_name
        self.email = email
        self.ipAddress = ipAddress
        if self.task_id is not None:
            self.taskInfo = TaskInfo.objects.get(task_id=self.task_id)
            self.environment = self.taskInfo.environment
            self.creater = self.taskInfo.creater
        self.record_id = record_id
        self.old_record_id = record_id
        self.date = datetime.now().date().strftime('%Y-%m-%d')

    # @async_call
    def task_execute(self):
        try:
            # 案例运行 并  数据存储到数据库
            if self.taskInfo.task_type == 'API':
                if self.taskInfo.api_type == 'CASE':
                    self.caseTaskRunning()
                else:
                    logger.info("111task_execute-record_id--------------------%s" % self.old_record_id)
                    self.sceneTaskRunning()
                    logger.info("222task_execute-record_id--------------------%s" % self.old_record_id)
            else:
                if self.taskInfo.api_type == 'WEBCASE':
                    self.webCaseTaskRunning()
                else:
                    self.appCaseTaskRunning()
            # 判断是否存在执行失败或错误用例,若存在则调用重试方法将错误用例再执行一次
            report_content_obj = TaskRunningRecord.objects.filter(task_record_id=self.record_id).values(
                 'test_fail', 'test_error').first()
            logger.info("old_record_id-------------------：%s" % self.old_record_id)
            if report_content_obj['test_fail'] + report_content_obj['test_error'] > 0 and self.old_record_id == '':
                # 自动执行有报错
                self.task_execute_fail_scene(self.record_id)
            else:
                # 手动重试或自动执行无报错
                if self.old_record_id:
                    TaskInfo.objects.filter(task_id=self.task_id).update(task_status='S',
                                                                         recent_record_id=self.record_id)
                else:
                    self.old_record_id = ''
                    self.pushMessage()
                    self.afterRunning()
        except Exception as e:
            errorInfo = traceback.format_exc()
            logger.info("任务运行失败：%s" % errorInfo)
        finally:
            task_record_id = self.record_id
            self.afterExecute()
            return task_record_id

    # 任务执行完后,将失败&错误用例再执行一遍
    def task_execute_fail_scene(self, old_record):
        try:
            self.old_record_id = self.record_id
            self.sceneTaskRunning()
            self.pushMessage()
            self.afterRunning()
        except Exception as e:
            errorInfo = traceback.format_exc()
            logger.info("任务运行失败：%s" % errorInfo)

    # 接口案例任务的执行
    def caseTaskRunning(self):
        taskRunningRecord = TaskRunningRecord(task_id=self.task_id, task_name=self.taskInfo.task_name,
                                              environment=self.environment, task_type="1",
                                              time_stamp=CommonUtil().get_current_ymdhms(), creater=self.creater)
        taskRunningRecord.save()
        self.record_id = taskRunningRecord.task_record_id
        belong_task = True
        total_time = Decimal("0").quantize(Decimal('0.00'))
        for case_id in self.taskInfo.case_pool.split("|"):
            case_name = CaseInfo.objects.get(case_id=case_id).case_name
            api = APIAutoTest(case_id, self.environment, self.creater)
            log = api.debug()
            if '请求耗时' in log["data"]:
                temp = re.findall("请求耗时：(.*?)秒", log["data"])[0]
            else:
                temp = 0
            total_time += Decimal(temp).quantize(Decimal('0.00'))
            if "案例调试失败" in log["data"]:
                run_result = "error"
                assert_result = False
            elif "断言失败" in log["data"]:
                run_result = "fail"
                assert_result = False
            else:
                run_result = "pass"
                assert_result = True
            caseRunningRecord = CaseRunningRecord(case_id=case_id, case_name=case_name, parent_id=self.record_id,
                                                  assert_result=assert_result, belong_task=belong_task,
                                                  run_result=run_result, running_log=log, total_time=temp)
            caseRunningRecord.save()
        test_all = len(self.taskInfo.case_pool.split("|"))
        test_error = CaseRunningRecord.objects.filter(parent_id=self.record_id, run_result="error").count()
        test_pass = CaseRunningRecord.objects.filter(parent_id=self.record_id, run_result="pass").count()
        test_fail = CaseRunningRecord.objects.filter(parent_id=self.record_id, run_result="fail").count()
        TaskRunningRecord.objects.filter(task_record_id=self.record_id).update(test_all=test_all, total_time=total_time,
                                                                               test_error=test_error,
                                                                               test_pass=test_pass,
                                                                               test_fail=test_fail)

    # 场景任务的执行-支持多线程并发执行
    def sceneTaskRunning(self):
        # 根据判断是否存在record_id来判断是否为重跑，若存在则为重跑，则scene_info_ids去报告中错误&失败的场景id
        if self.record_id:
            logger.info("重跑场景任务中...")
            scene_info_ids = SceneRunningRecord.objects.filter(parent_id=self.record_id,
                                                               run_result__in=["fail", "error"]).values_list('scene_id', flat=True)
            old_record_id = self.record_id
            logger.info("record_id: %s" % self.record_id)
            logger.info("scene_info_ids: %s" % scene_info_ids)
        else:
            scene_info_ids = self.taskInfo.case_pool.split("|")
            old_record_id = None
        # 记录任务执行开始时间
        start_time = int(time.time() * 1000)
        taskRunningRecord = TaskRunningRecord(task_id=self.task_id, task_name=self.taskInfo.task_name,
                                              environment=self.environment, task_type="2", batch_number=self.batch_number,
                                              time_stamp=CommonUtil().get_current_ymdhms(), creater=self.creater,
                                              channel=self.channel, old_record_id=old_record_id)
        taskRunningRecord.save()
        self.record_id = taskRunningRecord.task_record_id
        scene_info_nonexport = []
        scene_info_export = []
        scene_ids_nonexport = []
        scene_ids_export = []
        # 对场景进行分类，分出导出场景和非导出场景
        for scene_id in scene_info_ids:
            try:
                # 尝试获取 scene_type=0 的 SceneInfo 对象
                nonexport_scene_info = SceneInfo.objects.filter(scene_id=scene_id, scene_type=0)
                if nonexport_scene_info.exists():
                    # 如果找到，则添加到非导出场景列表
                    scene_info_nonexport.append(nonexport_scene_info.first())
                    scene_ids_nonexport.append(scene_id)
                else:
                    # 否则，尝试获取 scene_type=1 的 SceneInfo 对象
                    export_scene_info = SceneInfo.objects.filter(scene_id=scene_id, scene_type__in=[1, 2])
                    if export_scene_info.exists():
                        # 如果找到，则添加到导入导出场景列表
                        scene_info_export.append(export_scene_info.first())
                        scene_ids_export.append(scene_id)
                    else:
                        # 如果两个都不存在，可以选择记录日志或进行其他处理
                        print(f"No SceneInfo found for scene_id: {scene_id}")
            except Exception as e:
                # 处理其他可能的异常
                logger.info(f"存在异常,请检查: {e}")
        logger.info("scene_info_export：%s" % scene_info_export)
        logger.info("scene_ids_export：%s" % scene_info_export)
        logger.info("scene_ids_nonexport：%s" % scene_ids_nonexport)
        logger.info("scene_info_nonexport：%s" % scene_info_nonexport)
        # 非导出场景
        str_scene_id_list, max_workers = self.sort_scene(scene_info_nonexport, scene_ids_nonexport)
        logger.info("非导出场景：str_scene_id_list----：%s,并发数：%s" % (str_scene_id_list,  max_workers))
        # 导出场景
        if len(scene_info_export) > 0:
            str_scene_info_export, export_max_workers = self.sort_scene(scene_info_export, scene_ids_export)
            logger.info("导入导出场景：str_scene_info_export----：%s,并发数：%s" % (str_scene_info_export, export_max_workers))
        else:
            # 无导出用例时，设置默认值，保证后续能正常执行
            export_max_workers = 1
            str_scene_info_export = []
        # str_scene_id_list = scene_info_ids.copy()
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor,\
                concurrent.futures.ThreadPoolExecutor(max_workers=export_max_workers) as executor_export:

            # 提交每个场景的执行任务
            futures = [executor.submit(self.execute_scene, scene_id) for scene_id in str_scene_id_list]

            # 提交每个导出场景的执行任务
            futures_export = [executor_export.submit(self.execute_scene, scene_id) for scene_id in str_scene_info_export]
            # 等待所有场景的执行任务完成
            concurrent.futures.wait(futures)
            concurrent.futures.wait(futures_export)

            # 将场景执行结果存入任务记录表
            query = SceneRunningRecord.objects.filter(parent_id=self.record_id).values_list("run_result", flat=True)
            # total_temp = query.aggregate(total=Sum("total_time"))
            # total_time = Decimal(total_temp['total']).quantize(Decimal('0.00'))
            test_all = len(scene_info_ids)
            test_error = query.filter(run_result="error").count()
            test_pass = query.filter(run_result="pass").count()
            test_fail = query.filter(run_result="fail").count()
            # 记录任务执行结束时间
            end_time = int(time.time() * 1000)
            # 计算任务运行时长，并转换为秒,保留两位小数
            run_time = round((end_time - start_time) / 1000, 2)
            TaskRunningRecord.objects.filter(task_record_id=self.record_id).update(test_all=test_all,
                                                                                       total_time=run_time,
                                                                                       test_error=test_error,
                                                                                       test_pass=test_pass,
                                                                                       test_fail=test_fail)

    # 场景执行数据处理入库
    def execute_scene(self, scene_id):
        # logger.info("场景数据处理：%s" % scene_id)
        # 记录场景开始执行时间
        start_time = int(time.time() * 1000)
        # scene_time = Decimal("0").quantize(Decimal('0.00'))
        scene_info = SceneInfo.objects.filter(scene_id=scene_id).first()
        # 执行场景的代码逻辑
        sceneExe = SceneExecute(scene_id, self.environment, self.creater)
        log = sceneExe.debug()
        data = eval(log)  # 尽量避免使用 eval()
        # 处理日志和结果
        assert_list = []
        for x in data.get("data", []):
            assert_list.append(x["assert"])
        if "error" in data:
            run_result = "error"
            assert_result = False
        elif "0" in assert_list:
            run_result = "fail"
            assert_result = False
        else:
            run_result = "pass"
            assert_result = True
        # 记录场景执行结束时间
        end_time = int(time.time() * 1000)
        # 计算场景运行时长，并转换为秒,保留两位小数
        run_time = round((end_time - start_time) / 1000, 2)
        sceneRunningRecord = SceneRunningRecord(scene_id=scene_id, scene_name=scene_info.scene_name,
                                                parent_id=self.record_id,
                                                assert_result=assert_result,
                                                run_result=run_result, running_log=data, total_time=run_time,
                                                business_id=scene_info.business_id)
        sceneRunningRecord.save()

    # 场景按业务线分组排序
    def sort_scene(self, scene_info_result, scene_ids):
        # 获取场景对应的业务线id
        business_ids = [getattr(item, 'business_id') for item in scene_info_result]
        # 去重
        unique_business_ids = list(set(business_ids))
        logger.info("unique_business_ids：%s" % unique_business_ids)
        # 判断业务线是否存在多个，如果存在多个，则先按业务线分组处理
        if len(unique_business_ids) > 2:
            # 创建一个字典来存储按业务线分组的场景信息
            scene_info_dict = {}

            # 遍历场景信息对象，按业务线ID分组
            for scene in scene_info_result:
                business_id = scene.business_id

                # 检查字典中是否已存在该业务线ID的键
                if business_id in scene_info_dict:
                    # 存在则追加
                    scene_info_dict[business_id].append(scene)
                else:
                    # 不存在则新建
                    scene_info_dict[business_id] = [scene]
            # logger.info("scene_info_dict-循环前------:%s" % scene_info_dict)

            # 创建一个新的列表来存储场景id
            scene_id_list = []

            # 循环从分组的业务线中分别取出一个场景，按业务线和场景进行排序
            while True:
                # 用于标记是否有场景可以取
                scenes_available = False

                # 遍历分组的业务线
                for business_id, scenes in scene_info_dict.items():
                    # 如果该业务线有场景可以取
                    if scenes:
                        # 取出一个场景
                        scene = scenes.pop(0)
                        scene_id_list.append(scene.scene_id)
                        # logger.info(f"取出业务线ID为 {business_id} 的场景ID为 {scene.scene_id} 的场景")

                        # 标记有场景可以取
                        scenes_available = True
                # 获取没有场景的业务线键
                keys_to_delete = [key for key, scenes in scene_info_dict.items() if not scenes]

                # 当 keys_to_delete 不为空时才执行删除操作
                if keys_to_delete:
                    for key in keys_to_delete:
                        del scene_info_dict[key]

                # 如果没有场景可以取了，则退出循环
                if not scenes_available:
                    break

            # logger.info("scene_info_dict-循环后------:%s" % scene_info_dict)
            # logger.info("scene_id_list------:%s" % scene_id_list)
            # 待执行的场景列表scene_id_list中的元素转换成字符串，解决场景执行后未存SceneRunningRecord表问题
            str_scene_id_list = [str(num) for num in scene_id_list]
            # 设置并发数
            max_workers = 2  # 多业务线的任务设置并发数
            return str_scene_id_list, max_workers
        else:
            # 设置并发数
            max_workers = 1
            # 待执行的场景列表直接赋值scene_info_ids
            str_scene_id_list = scene_ids.copy()
            logger.info("str_scene_id_list：%s" % str_scene_id_list)
            return str_scene_id_list, max_workers

    # web案例任务的执行
    def webCaseTaskRunning(self):
        taskRunningRecord = TaskRunningRecord(task_id=self.task_id, task_name=self.taskInfo.task_name,
                                              environment=self.environment, task_type="3",
                                              time_stamp=CommonUtil().get_current_ymdhms(), creater=self.creater)
        taskRunningRecord.save()
        self.record_id = taskRunningRecord.task_record_id
        total_time = Decimal("0").quantize(Decimal('0.00'))
        for case_id in self.taskInfo.case_pool.split("|"):
            case_name = WebCaseInfo.objects.get(web_case_id=case_id).web_case_name
            # 统计案例执行时间：
            start_time = time.time()
            web = WebAutoTest(case_id, self.environment, self.creater, self.ipAddress)
            log = web.debugWebCase()
            # 案例执行完成
            end_time = time.time()
            log = log.replace("</br>", "\r\n")
            # 计算案例执行耗时
            temp = end_time - start_time
            total_time += Decimal(temp).quantize(Decimal('0.00'))
            if "案例调试失败" in log:
                run_result = "error"
                assert_result = False
            elif "断言失败" in log:
                run_result = "fail"
                assert_result = False
            else:
                run_result = "pass"
                assert_result = True
            webCaseRunningRecord = WebCaseRunningRecord(web_case_id=case_id, web_case_name=case_name,
                                                        assert_result=assert_result, parent_id=self.record_id,
                                                        run_result=run_result, running_log=log, total_time=temp)
            webCaseRunningRecord.save()
        test_all = len(self.taskInfo.case_pool.split("|"))
        test_error = WebCaseRunningRecord.objects.filter(parent_id=self.record_id, run_result="error").count()
        test_pass = WebCaseRunningRecord.objects.filter(parent_id=self.record_id, run_result="pass").count()
        test_fail = WebCaseRunningRecord.objects.filter(parent_id=self.record_id, run_result="fail").count()
        TaskRunningRecord.objects.filter(task_record_id=self.record_id).update(test_all=test_all, total_time=total_time,
                                                                               test_error=test_error,
                                                                               test_pass=test_pass, test_fail=test_fail)

    # 安卓案例任务的执行
    def appCaseTaskRunning(self):
        taskRunningRecord = TaskRunningRecord(task_id=self.task_id, task_name=self.taskInfo.task_name,
                                              environment=self.environment, task_type="4",
                                              time_stamp=CommonUtil().get_current_ymdhms(), creater=self.creater)
        taskRunningRecord.save()
        self.record_id = taskRunningRecord.task_record_id
        total_time = Decimal("0").quantize(Decimal('0.00'))
        for case_id in self.taskInfo.case_pool.split("|"):
            case_name = AndroidCaseInfo.objects.get(androidCase_id=case_id).androidCase_name
            # 统计案例执行时间：
            start_time = time.time()
            web = AndroidAutoTest(case_id, self.environment, self.creater, self.ipAddress)
            log = web.debugAndroidCase()
            # 案例执行完成
            end_time = time.time()
            log = log.replace("</br>", "\r\n")
            # 计算案例执行耗时
            temp = end_time - start_time
            total_time += Decimal(temp).quantize(Decimal('0.00'))
            if "案例调试失败" in log:
                run_result = "error"
                assert_result = False
            elif "断言失败" in log:
                run_result = "fail"
                assert_result = False
            else:
                run_result = "pass"
                assert_result = True
            appCaseRunningRecord = AppCaseRunningRecord(app_case_id=case_id, app_case_name=case_name,
                                                        assert_result=assert_result, parent_id=self.record_id,
                                                        run_result=run_result, running_log=log, total_time=temp)
            appCaseRunningRecord.save()
        test_all = len(self.taskInfo.case_pool.split("|"))
        test_error = AppCaseRunningRecord.objects.filter(parent_id=self.record_id, run_result="error").count()
        test_pass = AppCaseRunningRecord.objects.filter(parent_id=self.record_id, run_result="pass").count()
        test_fail = AppCaseRunningRecord.objects.filter(parent_id=self.record_id, run_result="fail").count()
        TaskRunningRecord.objects.filter(task_record_id=self.record_id).update(test_all=test_all, total_time=total_time,
                                                                               test_error=test_error,
                                                                               test_pass=test_pass, test_fail=test_fail)
    # 执行完成后的操作
    def afterRunning(self):
        TaskInfo.objects.filter(task_id=self.task_id).update(task_status='S', recent_record_id=self.record_id)
        # 发送邮件
        if self.taskInfo.is_send_email:
            email_model = EmailModelInfo.objects.get(email_model_id=self.taskInfo.email_model_id)
            if email_model.email_CC:
                sender = SendEmail(path=None, to_list=email_model.email_TO.split(";"),
                                   cc_list=email_model.email_CC.split(";"), subject=email_model.email_subject,
                                   content=email_model.email_content)
            else:
                sender = SendEmail(path=None, to_list=email_model.email_TO.split(";"),
                                   cc_list=None, subject=email_model.email_subject,
                                   content=email_model.email_content)
            sender.send_html(self.record_id, self.old_record_id)

    # 推消息至企业微信群聊
    def pushMessage(self, type=None):
        # # 企业微信助手-典典
        # address = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f2f8ab69-9d6a-4232-add5-5d8fb67a83a8"
        # 企业微信助手-自动化消息
        address1 = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b8117802-6a90-4d4a-a0ae-62582110de7b"

        content_type = "application/json;charset=UTF-8"
        headers = {}
        headers["Content-Type"] = content_type
        print("开始推送消息")
        print("record_id:", self.record_id)
        print("old_record_id:", self.old_record_id)
        if type is None:
            report_content_obj = TaskRunningRecord.objects.filter(task_record_id=self.record_id).values(
                'task_id', 'task_name', 'task_type', 'environment', 'test_all', 'test_pass', 'test_fail', 'test_error',
                'time_stamp', 'total_time').first()
            test_total = report_content_obj['test_all']
            test_pass = report_content_obj['test_pass']
            test_error = report_content_obj['test_error']
            test_fail = report_content_obj['test_fail']
            total_time = float(report_content_obj['total_time'])
            if test_fail + test_error > 0:
                # 增加失败用例负责人字段展示
                scene_fail_records = SceneRunningRecord.objects.filter(parent_id=self.record_id,
                                                                       run_result__in=["fail", "error"]).values_list(
                    "scene_id", flat=True)
                creater_list = SceneInfo.objects.filter(scene_id__in=scene_fail_records, is_active=True).values_list(
                    "creater", flat=True)
                unique_data = list(set(creater_list))

                mobile_list = UserInfo.objects.filter(username__in=unique_data, is_active=True).values_list(
                    "mobile", flat=True)
                mobile_data = list(set(mobile_list))
                logger.info("mobile_data-------------------：%s" % mobile_data)
            else:
                unique_data = "无"
                mobile_data = "无"
            if self.old_record_id:
                old_report_content_obj = TaskRunningRecord.objects.filter(task_record_id=self.old_record_id).values(
                    'task_id', 'task_name', 'task_type', 'environment', 'test_all', 'test_pass', 'test_fail',
                    'test_error',
                    'time_stamp', 'total_time').first()
                old_test_pass = old_report_content_obj['test_pass']
                old_total_time = float(old_report_content_obj['total_time'])
                test_total += old_test_pass
                test_pass += old_test_pass
                total_time += old_total_time
            # 获取企微推送信息
            if self.taskInfo.is_wx_push:
                address_temp = self.taskInfo.wx_address
                address_list = address_temp.split(";")
                # 环境类型枚举转义成中文
                if report_content_obj['environment'] == "1":
                    environment = "测试环境"
                elif report_content_obj['environment'] == "2":
                    environment = "预发环境"
                elif report_content_obj['environment'] == "3":
                    environment = "生产环境"
                else:
                    environment = "错误"
                # 若为hops调度，通知中加入工单信息
                if self.batch_number == '':
                    release_content = "\n工单信息：无"
                else:
                    release_content = "\n工单信息：[" + str(self.batch_number) + " ] " + str(self.code_name)
                if self.taskInfo.is_monitoring:
                    report_address = ("\n点击链接进入测试平台处理：http://" + service_ip
                                      + ":8889/#/daily-monitoring-detail-list?date=" + str(
                                self.date) + "&environment=" + report_content_obj['environment'])
                else:
                    # 若有重试，则报告地址拼接新老record_id
                    if self.old_record_id:
                        report_address = ("\n测试报告访问地址：http://" + service_ip
                                              +":8000/api/task_info/task_report/?task_id=" + str(self.record_id)) + '&old_task_id=' + str(self.old_record_id)
                    else:
                        report_address = ("\n测试报告访问地址：http://" + service_ip
                                          + ":8000/api/task_info/task_report/?task_id=" + str(
                                    self.record_id))
                content = (environment + "-接口自动化报告"
                          + "\n任务名称：" + str(report_content_obj['task_name']) \
                          + "\n用例总数：" + str(test_total) + "\n成功个数：" \
                          + str(test_pass) + "\n失败个数：" \
                          + str(test_fail) + "\n错误个数：" \
                          + str(test_error) \
                          + "\n运行时长：" + str(round((total_time) / 60, 2)) + "分钟" \
                          + "\n故障负责人：" + str(unique_data))
                content += release_content \
                          + report_address
                data = {
                    "touser": "15986827962",
                    "toparty": "",
                    "totag": "",
                    "msgtype": "text",
                    "agentid": 1,
                    "text": {
                        "content": content,
                        "mentioned_mobile_list": mobile_data
                    },
                    "safe": 0,
                    "enable_id_trans": 0,
                    "enable_duplicate_check": 0,
                    "duplicate_check_interval": 1800
                }
                for address in address_list:
                    requests.post(url=address, data=json.dumps(data), headers=headers)
            else:
                logger.info("未推送消息至企业微信:任务id-%s 报告id-%s" % (self.task_id, self.record_id))
        else:
            # 获取当前日期（不包含时间）
            report_date = self.date
            interface_compare_result_obj = InterfaceCompareResult.objects.filter(date=report_date)
            # 统计总数
            total = interface_compare_result_obj.count()
            # 统计变更
            change = interface_compare_result_obj.filter(compare_result=1).count()
            # 统计未覆盖
            not_covered = interface_compare_result_obj.filter(compare_result=2).count()
            # 统计新增
            add = interface_compare_result_obj.filter(compare_result=3).count()

            # 判断是否有待处理的接口，如没有则不展示报告地址
            if total == 0:
                report_address = "无"
            else:
                report_address = "http://" + service_ip + ":8000/api/task_info/interface_compare_report/?date=" + str(report_date)

            content = "接口比对差异报告：" + "\n任务名称：待处理接口清单" \
                      + "\n比对日期：" + str(report_date) \
                      + "\n待处理总数：" + str(total) \
                      + "\n入参变更接口：" + str(change) \
                      + "\n未实现自动化接口：" + str(not_covered) \
                      + "\n新需求增加接口：" + str(add) \
                      + "\n差异比对报告地址：" + str(report_address)
            data = {
                        "touser": "15986827962",
                        "toparty": "",
                        "totag": "",
                        "msgtype": "text",
                        "agentid": 1,
                        "text": {
                            "content": content
                        },
                        "safe": 0,
                        "enable_id_trans": 0,
                        "enable_duplicate_check": 0,
                        "duplicate_check_interval": 1800
                    }
            # 测试部监控群
            requests.post(url=address1, data=json.dumps(data), headers=headers)
            # 前端监控群
            requests.post(url="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2cb6e567-3b70-485d-a1e2-b06cf6ecd6f1",
                          data=json.dumps(data), headers=headers)


    def afterExecute(self):
        self.record_id = ""
