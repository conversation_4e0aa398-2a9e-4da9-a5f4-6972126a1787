# -*- coding:utf-8 -*-

import time
from core.commonUtils.commonUtil import async_call
from interface_test.models import *
from core.servers.caseRun import TestCase
import traceback
# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()
"""
场景执行器
"""


class SceneExecute:
    # 初始化函数
    def __init__(self, scene_id, env_id, person):
        self.scene_id = scene_id
        self.sceneInfo = SceneInfo.objects.get(scene_id=self.scene_id)
        self.env_id = env_id
        self.person = person
        if self.env_id == '1':
            self.environment = "测试环境"
        elif self.env_id == '2':
            self.environment = "预发环境"
        elif self.env_id == '3':
            self.environment = "生产环境"
        else:
            self.environment = "未知环境"
        self.param = {}
        self.api_result = {}
        self.log_content = []
        self.log_data = []
        self.log_dict = {}
        self.result_log = {}
        self.case_id = ""
        # 是否存在前置数据
        if self.sceneInfo.before_param:
            str_list = self.sceneInfo.before_param.split(";")
            str_list = [i for i in str_list if i != '']  # 去除空元素
            for x in str_list:
                param_str = x.split("=")
                # 写入到字典中
                self.param[param_str[0]] = param_str[1]

    # 场景运行主函数
    def debug(self):
        code = "0000"
        try:
            self.test()
        except Exception as e:
            error_info = str(e)
            logger.info("场景调试失败：%s" % traceback.format_exc())
            code = "1001"
            result = "出错"
        finally:
            if code == "0000":
                self.result_log = {"code": "0000",
                                   "msg": self.environment + " 场景：" + self.scene_id + " " + self.sceneInfo.scene_name,
                                   "data": self.log_data}
                result = "成功"
            else:
                self.result_log = {"code": code, "msg": self.case_id + "-" + self.caseInfo.case_name,
                                   "error": "场景调试失败，请检查: %s" % error_info, "data": self.log_data}
                result = "失败"
            # id = self.sceneLogRecord()
            # log_record = LogRecord.objects.get(log_id=id)
            # 将数据存入新参数，用于数据返回，不用查表
            log = self.result_log.copy()
            data = self.log_data.copy()
            log["data"] = data
            # 记录日志到数据库
            self.sceneLogRecord()
            # 任务执行完成后清除全局变量值
            self.afterDebug()
            create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            obj = ExecuteRecord(type="scene", env_id=self.env_id, run_id=self.scene_id, result=result,
                                creater=self.person, update_person=self.person,
                                create_time=create_time, update_time=create_time)
            obj.save()
            # return log_record.log_content
            return str(log)


    # 执行日志记录
    @async_call
    def sceneLogRecord(self):
        # 将数据存入新参数，用于异步数据存储
        log = self.result_log.copy()
        data = self.log_data.copy()
        log["data"] = data
        logRe = LogRecord(log_type='2', running_log_type='2', running_log_id=self.scene_id,
                          log_content=log, person=self.person,
                          temp_time=time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
        logRe.save()
        # return logRe.log_id

    # 案例执行完成之后的处理
    def afterDebug(self):
        self.param.clear()
        self.api_result.clear()
        self.log_data.clear()
        self.result_log.clear()
        self.log_dict.clear()
        self.log_content.clear()
        self.case_id = ""
        self.caseInfo = None

    # 场景执行逻辑函数
    def test(self):
        project_list = []
        for relation_id in self.sceneInfo.scene_relation_id.split("|"):
            self.api_result.clear()
            self.log_content = []
            self.log_dict = {}
            scene_relation = SceneRelation.objects.get(scene_relation_id=relation_id)

            #2022.02.25 增加禁用功能，启用执行案例才可执行
            if scene_relation.is_enable:
                try:
                    # 2022.03.21 修改案例运行逻辑 huangjiaojiao
                    self.case_id = scene_relation.relation_case_id
                    self.caseInfo = CaseInfo.objects.get(case_id=self.case_id)
                    if self.caseInfo.case_type == "API" or self.caseInfo.case_type == "UPLOAD":
                        interfaceInfo = InterfaceInfo.objects.get(interface_id=self.caseInfo.interface_id)
                        if interfaceInfo.project_id not in project_list:
                            project_list.append(interfaceInfo.project_id)
                            projectInfo = ProjectInfo.objects.get(project_id=interfaceInfo.project_id)
                            if projectInfo.project_mode:
                                if self.env_id == '1':
                                    pre_case = projectInfo.test_case
                                    pre_param = projectInfo.test_before_param
                                elif self.env_id == '2':
                                    pre_case = projectInfo.uat_case
                                    pre_param = projectInfo.uat_before_param
                                elif self.env_id == '3':
                                    pre_case = projectInfo.pro_case
                                    pre_param = projectInfo.pro_before_param
                                if pre_param:
                                    str_list = [i for i in pre_param.split(";") if i != '']  # 去除空元素
                                    for x in str_list:
                                        param_str = x.split("=")
                                        # 写入到字典中
                                        self.param[param_str[0]] = param_str[1]
                                if pre_case:
                                    for evn_relation_id in pre_case.split("|"):
                                        scene_relation = SceneRelation.objects.get(scene_relation_id=evn_relation_id)
                                        if scene_relation.is_enable:
                                            case_id = scene_relation.relation_case_id
                                            caseInfo = CaseInfo.objects.get(case_id=case_id)
                                            test_case = TestCase(self.env_id, self.person, evn_relation_id, self.param)
                                            case_data = test_case.scene_transform()
                                            api_result = test_case.case_run(case_data)
                                            param = test_case.get_param(api_result)
                                            if param:
                                                for key in param:
                                                    self.param[key] = param[key]
                                            test_case.scene_sleep()
                                            case_msg = test_case.report(case_id, caseInfo.case_name)
                                            self.log_data.append(case_msg)
                    target_case = TestCase(self.env_id, self.person, relation_id, self.param)
                    case_data = target_case.scene_transform()
                    self.api_result = target_case.case_run(case_data)
                    if self.api_result:
                        param = target_case.get_param(self.api_result)
                        if param:
                            for key in param:
                                self.param[key] = param[key]
                    target_case.scene_sleep()
                    target_msg = target_case.report(self.case_id, self.caseInfo.case_name)
                    self.log_data.append(target_msg)
                except Exception as e:
                    raise e

    # 组装成指定格式的场景日志
    def sceneLog(self):
        flag1 = [s for s in self.log_content if '无需断言' in s]
        if flag1:
            self.log_dict['assert'] = "1"
            self.log_dict['title'] = "案例ID：" + (self.case_id + " " + self.caseInfo.case_name) + " 无需断言"
            self.log_dict['detail'] = self.log_content
        else:
            flag2 = [s for s in self.log_content if '断言成功' in s]
            if flag2:
                self.log_dict['assert'] = "1"
                self.log_dict['title'] = "案例ID：" + (
                        self.case_id + " " + self.caseInfo.case_name) + " 断言成功"
                self.log_dict['detail'] = self.log_content
            else:
                self.log_dict['assert'] = "0"
                self.log_dict['title'] = "案例ID：" + (
                        self.case_id + " " + self.caseInfo.case_name) + " 断言失败"
                self.log_dict['detail'] = self.log_content
        # 将接口案例执行日志追加到日志data列表
        self.log_data.append(self.log_dict)

