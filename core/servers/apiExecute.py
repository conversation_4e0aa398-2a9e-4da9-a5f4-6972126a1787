#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import time
from interface_test.models import *
from core.servers.caseRun import TestCase

class ApiExecute:
    def __init__(self, case_id, env_id, operator):
        self.case_id = case_id
        self.env_id = env_id
        self.operator = operator
        self.caseInfo = CaseInfo.objects.get(case_id=self.case_id)
        self.param = {}
        self.log_data = []

    def run(self):
        if self.caseInfo.case_type == "API":
            interfaceInfo = InterfaceInfo.objects.get(interface_id=self.caseInfo.interface_id)
            projectInfo = ProjectInfo.objects.get(project_id=interfaceInfo.project_id)
            if projectInfo.project_mode:
                if self.env_id == '1':
                    pre_case = projectInfo.test_case
                    pre_param = projectInfo.test_before_param
                elif self.env_id == '2':
                    pre_case = projectInfo.uat_case
                    pre_param = projectInfo.uat_before_param
                elif self.env_id == '3':
                    pre_case = projectInfo.pro_case
                    pre_param = projectInfo.uat_before_param
                if pre_param:
                    str_list = [i for i in pre_param.split(";") if i != '']  # 去除空元素
                    for x in str_list:
                        param_str = x.split("=")
                        # 写入到字典中
                        self.param[param_str[0]] = param_str[1]
                if pre_case:
                    for relation_id in pre_case.split("|"):
                        scene_relation = SceneRelation.objects.get(scene_relation_id=relation_id)
                        if scene_relation.is_enable:
                            case_id = scene_relation.relation_case_id
                            caseInfo = CaseInfo.objects.get(case_id=case_id)
                            test_case = TestCase(self.env_id, self.operator, relation_id, self.param)
                            case_data = test_case.scene_transform()
                            api_result = test_case.case_run(case_data)
                            param = test_case.get_param(api_result)
                            if param:
                                for key in param:
                                    self.param[key] = param[key]
                            test_case.scene_sleep()
                            case_msg = test_case.report(case_id, caseInfo.case_name)
                            self.log_data.append(case_msg)
        target_case = TestCase(self.env_id, self.operator, param=self.param)
        target_case.case_run(self.caseInfo)
        case_msg = target_case.report(self.case_id, self.caseInfo.case_name)
        self.log_data.append(case_msg)

    def report(self):
        code = "0000"
        try:
            self.run()
        except Exception as e:
            self.log_data.append("案例调试失败：%s" % repr(e))
            code = "1001"
            result = "出错"
        finally:
            if code == "0000":
                result_log = {
                    "code": "0000",
                    "msg": "运行结果",
                    "data": self.log_data
                }
                result = "成功"
            else:
                result_log = {
                    "code": "1001",
                    "msg": "运行结果",
                    "data": [
                        {
                            "assert": "0",
                            "title": "运行出错",
                            "detail": self.log_data
                        }
                    ]
                }
                result = "失败"
            create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            obj = ExecuteRecord(type="case", env_id = self.env_id, run_id = self.case_id,result=result,
                               creater=self.operator, update_person=self.operator,
                               create_time=create_time, update_time=create_time)
            obj.save()
            return result_log



