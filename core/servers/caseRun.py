import re
import datetime
import time
import ast
import pandas as pd
import numpy as np
from openpyxl import load_workbook
import io

from decimal import Decimal
import urllib.parse
import os

from interface_test.models import *
from data_config.models import *
from core.commonUtils.commonUtil import CommonUtil
from core.commonUtils.getModelInfoUtil import GetModelInfo
from core.commonUtils.dbUtil import DBUtils
from core.commonUtils.aesCryptUtil import PrpCrypt
from core.commonUtils.apiTestUtil import APITest
import sympy as sp

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()

class TestCase:
    def __init__(self, env_id, operator,relation_id=0, param={}):
        self.env_id = env_id
        self.operator = operator
        self.log_content = []
        self.relation_id = relation_id
        self.param = param
        self.api_result = {}

    def add_log(self, msg):
        self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 " + msg)

    # 字符串比较函数，解决浮点精度问题
    # 支持：引号不一致  ["家有老年人", "血压高", "高血糖"] = ['家有老年人', '血压高', '高血糖'] 为 True
    # 支持：包含引号时，不判断运算， "01-01,05-01" = 01-01,05-01 为True
    # 支持：多引号嵌套和转义符处理  [""wechat-tweet""] = ['"wechat-tweet"'] ，["\"wechat-tweet\""] = ['"wechat-tweet"']  均为 True
    def smart_compare(self, a, b):
        # 统一转字符串并去除首尾空白
        str_a, str_b = str(a).strip(), str(b).strip()

        # 尝试转为数字比较
        try:
            num_a = float(str_a)
            num_b = float(str_b)
            return abs(num_a - num_b) < 1e-9
        except (ValueError, TypeError):
            pass

        # 特殊处理：直接比较去引号后的内容
        def remove_quotes(s):
            # 移除所有外层引号（单引号或双引号）
            while (s.startswith('"') and s.endswith('"')) or \
                    (s.startswith("'") and s.endswith("'")):
                s = s[1:-1]
            return s

        # 尝试解析为列表前，先统一引号处理
        try:
            # 先移除外层列表符号的引号
            clean_a = remove_quotes(str_a)
            clean_b = remove_quotes(str_b)

            # 尝试解析为列表
            list_a = ast.literal_eval(clean_a)
            list_b = ast.literal_eval(clean_b)

            if isinstance(list_a, list) and isinstance(list_b, list):
                if len(list_a) != len(list_b):
                    return False
                return all(self.smart_compare(x, y) for x, y in zip(list_a, list_b))
        except (SyntaxError, ValueError, TypeError):
            pass

        # 字符串比较：彻底规范化引号
        def normalize_string(s):
            # 1. 移除所有转义字符
            s = s.replace('\\"', '"').replace("\\'", "'")
            # 2. 统一将双引号转为单引号
            s = s.replace('"', "'")
            # 3. 移除多余的单引号对
            while len(s) >= 2 and s[0] == "'" and s[-1] == "'":
                s = s[1:-1]
            return s

        return normalize_string(str_a) == normalize_string(str_b)

    def assertResult(self, result, caseInfo):
        self.add_log("========================= 断言信息 =======================")
        if caseInfo.is_assert and caseInfo.asserts != " ":
            assertResult_list = []
            for assert_id in caseInfo.asserts.split('|'):
                assertResultInfo = AssertResultInfo.objects.get(assert_id=assert_id)
                if assertResultInfo.assert_type == '1':  # 模糊匹配断言
                    value_name = assertResultInfo.return_value
                    # 返回值参数化
                    assertResultInfo.return_value = str(
                        GetModelInfo().get_element_by_params(assertResultInfo.return_value, result))
                    # 预期值参数化
                    # 判断预期结果中包含${data}$.xx.xx
                    if "$." in assertResultInfo.expect_result:
                        # 判断预期结果中包含 round 函数
                        pattern = r'\bround\b\((.*?)\)'
                        # 使用findall方法找到所有匹配项
                        matches = re.findall(pattern, assertResultInfo.expect_result)
                        result_dict = {}
                        # 输出结果
                        if matches:
                            for match in matches:
                                r_match = (f"round({match})")
                                pattern = r'round\(([^,]+)(?:,\s*)?(\d+)\)'
                                round_match = re.search(pattern, r_match)
                                # 获取round(XXXX,X) 第一个参数
                                expect_result = round_match.group(1)
                                # 将参数进行转换处理，将${xx}$、${xx}$.xx.xx获取相应的值，并重新组装成字符，即${xx}$-${xx}$.xx.xx，替换参数后成为a-b
                                result = self.expect_result_handle(expect_result)
                                # 替换后的表达式存入新字典
                                result_dict[expect_result] = result
                            # 将替换后的参数，替换回原字符串中
                            expect_result = assertResultInfo.expect_result
                            for key in result_dict:
                                expect_result = expect_result.replace(key, result_dict[key])
                        # 没有round 则直接进行${xx}$.xx.xx参数预处理
                        else:
                            expect_result = self.expect_result_handle(assertResultInfo.expect_result)
                    # 没有${xx}$.xx格式的，则直接替换参数处理
                    else:
                        expect_result = self.parameterization(assertResultInfo.expect_result)

                    # 接下来，判断处理后的表达式中是否包含四则运算符，即a-b等
                    pattern = r'([+\-*/])'
                    match = re.search(pattern, expect_result)
                    if match and '"' not in expect_result and "'" not in expect_result:
                        assertResultInfo.expect_result = self.extract_params_operation(expect_result)
                    else:
                        assertResultInfo.expect_result = expect_result

                    # 判断数值比较方式
                    if assertResultInfo.assert_operator == '1':  # 等于
                        symbol = "="
                        if self.smart_compare(assertResultInfo.return_value, assertResultInfo.expect_result):
                            assertResult = "结果为真"
                        else:
                            assertResult = "结果为假"
                    elif assertResultInfo.assert_operator == '2':  # 不等于
                        symbol = "!="
                        if assertResultInfo.return_value != assertResultInfo.expect_result:
                            assertResult = "结果为真"
                        else:
                            assertResult = "结果为假"
                    elif assertResultInfo.assert_operator == '3':  # 包含
                        symbol = "包含"
                        if assertResultInfo.return_value in str(assertResultInfo.expect_result):
                            assertResult = "结果为真"
                        elif assertResultInfo.expect_result in str(assertResultInfo.return_value):
                            assertResult = "结果为真"
                        else:
                            assertResult = "结果为假"
                    elif assertResultInfo.assert_operator == '4':  # 不包含
                        symbol = "不包含"
                        if assertResultInfo.return_value not in str(assertResultInfo.expect_result):
                            assertResult = "结果为真"
                        elif assertResultInfo.expect_result not in str(assertResultInfo.return_value):
                            assertResult = "结果为真"
                        else:
                            assertResult = "结果为假"
                    else:
                        symbol = None
                        assertResult = None

                    assertMsg = "数据匹配，%s：%s %s %s，%s" % (
                        value_name, assertResultInfo.expect_result, symbol, assertResultInfo.return_value, assertResult)
                else:  # 四则运算
                    # 返回值参数化
                    assertResultInfo.return_value = str(
                        GetModelInfo().get_element_by_params(assertResultInfo.return_value, result))
                    assertResultInfo.return_value = self.parameterization(assertResultInfo.return_value, param=result)
                    assertResultInfo.operation_value = self.parameterization(assertResultInfo.operation_value, param=result)
                    assertResultInfo.expect_result = self.parameterization(assertResultInfo.expect_result, param=result)
                    temp_1 = Decimal(assertResultInfo.return_value).quantize(Decimal('0.00'))
                    temp_2 = Decimal(assertResultInfo.operation_value).quantize(Decimal('0.00'))
                    temp_result = Decimal(assertResultInfo.expect_result).quantize(Decimal('0.00'))
                    if assertResultInfo.assert_operator == '1':  # 加法
                        temp = temp_1 + temp_2
                        symbol = "+"
                        if temp == temp_result:
                            assertResult = "结果为真"
                        else:
                            assertResult = "结果为假"
                    elif assertResultInfo.assert_operator == '2':  # 减法
                        temp = temp_1 - temp_2
                        symbol = "-"
                        if temp == temp_result:
                            assertResult = "结果为真"
                        else:
                            assertResult = "结果为假"
                    elif assertResultInfo.assert_operator == '3':  # 乘法
                        temp = temp_1 * temp_2
                        symbol = "×"
                        if temp == temp_result:
                            assertResult = "结果为真"
                        else:
                            assertResult = "结果为假"
                    elif assertResultInfo.assert_operator == '4':  # 除法
                        temp = round(temp_1 / temp_2, 2)
                        symbol = "÷"
                        if temp == temp_result:
                            assertResult = "结果为真"
                        else:
                            assertResult = "结果为假"
                    else:
                        symbol = None
                        assertResult = None
                    assertMsg = "四则运算，%s %s %s = %s，%s" % (
                        temp_1, symbol, temp_2, temp_result, assertResult)
                self.add_log(assertMsg)
                if "结果为真" in assertMsg:
                    assertResult_list.append(True)
                else:
                    assertResult_list.append(False)
            if all(assertResult_list):
                assert_result_str = "断言成功"
            else:
                assert_result_str = "断言失败"
            self.add_log(assert_result_str)
        else:
            self.add_log("无需断言")

    # 导出解析案例断言
    def assertExportResult(self, file, caseInfo):
        self.add_log("========================= 断言信息 =======================")
        if caseInfo.is_assert and caseInfo.asserts != " ":
            assertResult_list = []
            assertid = caseInfo.asserts.split('|')
            is_row = AssertResultInfo.objects.filter(assert_id__in=assertid, return_value='ROW', is_active=True)
            row_not_empty = bool(is_row)
            if row_not_empty:
                rownumber = int(is_row[0].expect_result) - 1
            else:
                rownumber = 0
            result = GetModelInfo().read_excel_data(file, rownumber)

            for assert_id in caseInfo.asserts.split('|'):
                assertResultInfo = AssertResultInfo.objects.get(assert_id=assert_id)
                if assertResultInfo.return_value == 'ROW':
                    continue  # 不处理，跳过当前迭代
                else:
                    value_name = assertResultInfo.return_value
                    if assertResultInfo.return_value == 'total':
                        # 返回值参数化
                        assertResultInfo.return_value = str(
                            GetModelInfo().get_element_by_params(assertResultInfo.return_value, result))
                    else:
                        assertResultInfo.return_value = "{}{}".format('data.0.', assertResultInfo.return_value)
                        # 返回值参数化
                        assertResultInfo.return_value = str(
                            GetModelInfo().get_element_by_params(assertResultInfo.return_value, result))

                    # 判断预期结果是 ${data}$.xx.xx形式'
                    if "$." in assertResultInfo.expect_result:
                        # 截取第一个 . 前面的${}$参数
                        expect_result_before = assertResultInfo.expect_result.split('.')[0]
                        # 预期值参数化
                        expect_result = self.parameterization(expect_result_before)
                        expect_result = ast.literal_eval(expect_result)

                        # 判断预期结果是 ${data}$.xx.xx('+', '-', '*', '/')x 形式'
                        # 正则表达式模式，匹配第一个'.'和操作符之间的内容，以及操作符后面的内容
                        pattern = r'\.([^+\-*/\*]+)([+\-*/\*])(.*)'
                        # 使用re.search查找匹配的子字符串
                        match = re.search(pattern, assertResultInfo.expect_result)
                        if match:
                            # match.group(1) 包含第一个'.'和操作符之间的内容
                            expect_result_after = match.group(1)

                            # match.group(2) 包含找到的操作符
                            found_operator = match.group(2)
                            # match.group(3) 包含操作符后面的内容
                            value_after_operator = match.group(3)

                            # 调用获取参数方法，获取xx.xx的值
                            assertResultInfo.expect_result = str(
                                GetModelInfo().get_element_by_params(expect_result_after, expect_result))
                            # 调用四则运算，获取运算后的值
                            assertResultInfo.expect_result = str(
                                self.four_operations(assertResultInfo.expect_result, found_operator,
                                                     value_after_operator))
                        # 预期结果是 ${data}$.xx.xx形式'
                        else:
                            # 截取第一个 . 后面的xx.xx参数,生成list: ['xx','xx']
                            expect_result_list = assertResultInfo.expect_result.split('.')[1:]
                            # 将list 重新拼接成 xx.xx
                            expect_result_after = ".".join(expect_result_list[0:])

                            # 调用获取参数方法，获取xx.xx的值
                            assertResultInfo.expect_result = str(
                                GetModelInfo().get_element_by_params(expect_result_after, expect_result))

                    # 判断预期结果是 ${data}$('+', '-', '*', '/')x 形式'
                    elif "$" in assertResultInfo.expect_result and any(
                            op in assertResultInfo.expect_result for op in '+-*/'):
                        # 正则表达式模式，匹配操作符前后的值
                        pattern = r'(.*?)([+\-*/\*])(.*)'
                        # 使用re.search查找匹配的子字符串
                        match = re.search(pattern, assertResultInfo.expect_result)
                        # match.group(1) 包含第一个'.'和操作符之间的内容
                        expect_result_after = match.group(1)

                        # match.group(2) 包含找到的操作符
                        found_operator = match.group(2)
                        # match.group(3) 包含操作符后面的内容
                        value_after_operator = match.group(3)
                        assertResultInfo.expect_result = self.parameterization(expect_result_after)
                        # 调用四则运算，获取运算后的值
                        assertResultInfo.expect_result = str(self.four_operations(assertResultInfo.expect_result,
                                                                                  found_operator,
                                                                                  value_after_operator))
                    else:
                        # 预期值参数化
                        assertResultInfo.expect_result = self.parameterization(assertResultInfo.expect_result)

                    # 判断断言方式
                    if assertResultInfo.assert_operator == '1':  # 等于
                        symbol = "="
                        # 转换为字符串用于检查'****'
                        str_return = str(assertResultInfo.return_value)
                        str_expect = str(assertResultInfo.expect_result)

                        # 先检查'****'加密特殊情况,
                        if '****' in str_return or '****' in str_expect:
                            if self.check_string_consistent(str_expect, str_return):
                                assertResult = "结果为真"
                            else:
                                assertResult = "结果为假"
                        # 常规比较
                        else:
                            if self.smart_compare(assertResultInfo.return_value, assertResultInfo.expect_result):
                                assertResult = "结果为真"
                            else:
                                assertResult = "结果为假"

                    elif assertResultInfo.assert_operator == '2':  # 不等于
                        symbol = "!="
                        if assertResultInfo.return_value != assertResultInfo.expect_result:
                            assertResult = "结果为真"
                        else:
                            assertResult = "结果为假"
                    elif assertResultInfo.assert_operator == '3':  # 包含
                        symbol = "包含"
                        if assertResultInfo.return_value in str(assertResultInfo.expect_result):
                            assertResult = "结果为真"
                        elif assertResultInfo.expect_result in str(assertResultInfo.return_value):
                            assertResult = "结果为真"
                        else:
                            assertResult = "结果为假"
                    elif assertResultInfo.assert_operator == '4':  # 不包含
                        symbol = "不包含"
                        if assertResultInfo.return_value not in str(assertResultInfo.expect_result):
                            assertResult = "结果为真"
                        elif assertResultInfo.expect_result not in str(assertResultInfo.return_value):
                            assertResult = "结果为真"
                        else:
                            assertResult = "结果为假"
                    else:
                        symbol = None
                        assertResult = None

                    if value_name == 'total':
                        assertMsg = "数据匹配(总条数),%s：%s %s %s，%s" % (
                        value_name, assertResultInfo.expect_result, symbol,
                        assertResultInfo.return_value, assertResult)
                    else:
                        assertMsg = "数据匹配(第%s行),%s：%s %s %s，%s" % (
                        rownumber + 1, value_name, assertResultInfo.expect_result, symbol,
                        assertResultInfo.return_value, assertResult)
                self.add_log(assertMsg)
                if "结果为真" in assertMsg:
                    assertResult_list.append(True)
                else:
                    assertResult_list.append(False)
            if all(assertResult_list):
                assert_result_str = "断言成功"
            else:
                assert_result_str = "断言失败"
            self.add_log(assert_result_str)
        else:
            self.add_log("无需断言")

    # 针对加密字符串比对处理
    def check_string_consistent(self, expect_result, retrurn_value, prefix=3 ,suffix=4):
        # 确保输入是字符串
        if not isinstance(expect_result, str) or not isinstance(retrurn_value, str):
            raise ValueError("Both inputs must be strings.")

        # 提取前三位和最后四位
        expect_prefix = expect_result[:prefix]
        expect_suffix = expect_result[-suffix:]
        retrurn_prefix = retrurn_value[:prefix]
        retrurn_suffix = retrurn_value[-suffix:]
        # 最后两位
        expect_suffix_2 = expect_result[-2:]
        retrurn_suffix_2 = retrurn_value[-2:]

        # 比较前三位和最后四位是否相同
        if expect_prefix == retrurn_prefix and expect_suffix == retrurn_suffix:
            return True
        # 比较前三位和最后两位是否相同
        elif expect_prefix == retrurn_prefix and expect_suffix_2 == retrurn_suffix_2:
            return True
        else:
            return False

    # 数学计算
    def four_operations(self, expect_result, operator, value_after_operator):
        temp_1 = Decimal(expect_result).quantize(Decimal('0.00'))
        temp_2 = Decimal(value_after_operator).quantize(Decimal('0.00'))
        if operator == "+":
            result = temp_1 + temp_2
        elif operator == "-":
            result = temp_1 - temp_2
        elif operator == "*":
            result = temp_1 * temp_2
        elif operator == "/":
            result = round(temp_1 / temp_2, 2)

        # 如果小数位都是0，则转成整数展示
        if result == result.to_integral():
            # 如果小数部分为0，则转换为整数
            expect_result = int(result)
        else:
            # 如果小数部分不为0，则保持为 Decimal 对象
            expect_result = result
        return expect_result

    # 获取字符中包含的round函数,并计算数学公式
    def extract_params_operation(self, tempStr):
        # # 尝试从字符串中提取 round 函数的参数
        pattern = r'\bround\b\((.*?)\)'
        # 使用findall方法找到所有匹配项
        matches = re.findall(pattern, tempStr)
        result_dict = {}
        # 输出结果
        if matches:
            # 循环获取round参数
            for match in matches:
                r_match = (f"round({match})")
                pattern = r'round\(([^,]+)(?:,\s*)?(\d+)\)'
                round_match = re.search(pattern, r_match)
                expression_part = round_match.group(1)  # 提取 round 函数的第一个参数（表达式部分）
                precision = int(round_match.group(2))  # 提取 round 函数的第二个参数（精度）

                # 使用sympy的parse_expr函数解析表达式字符串
                expression = sp.parse_expr(expression_part)
                # 计算表达式
                result_after = expression.evalf()
                # 使用提取的精度来格式化结果
                result = f"{result_after:.{precision}f}"
                result_dict[r_match] = result
            # 重新赋值，用于for循环替换参数
            result = tempStr
            for key in result_dict:
                result = result.replace(key, result_dict[key])
        else:
            result = tempStr

        # 查找字符串中是否包含四则运算符
        operators = re.findall(r'[+\-*/]', result)
        # 如果表达式中存在四则运算，则再次进行计算
        if operators:
            # 使用正则表达式匹配日期格式:'xxxx-xx-xx,则不进行四则运算
            date_pattern = r'\d{4}-\d{2}-\d{2}'
            if re.match(date_pattern, result):
                return result
            # 使用正则表达式匹配中文字符
            elif re.search(r'[\u4e00-\u9fa5]', result):
                return result
            # 使用正则表达式匹配负整数和负小数
            elif re.match(r'^-\d+(\.\d+)?$', result):
                return result
            else:
                # 判断是否包含百分号%
                if '%' in result:
                    result = result.split('%')[0]
                    # 使用sympy的parse_expr函数解析表达式字符串
                    expression = sp.parse_expr(result)
                    # 计算表达式，默认保留两位小数
                    result = expression.evalf()

                    # 转换为百分比
                    percentage = float(result) * 100
                    formatted_percentage = f"{percentage:.2f}%"
                    return formatted_percentage
                else:
                    # 使用sympy的parse_expr函数解析表达式字符串
                    expression = sp.parse_expr(result)
                    # 计算表达式，默认保留两位小数
                    result = expression.evalf()
                    result = "{:.2f}".format(result)
                    return result
        else:
            return result

    # 判断预期结果中包含${}$.xx.xx并处理
    def expect_result_handle(self, expect_result):

        # 使用正则表达式找到所有的运算符
        operators = re.findall(r'[+\-*/]', expect_result)

        # 初始化一个空列表来存储结果
        values_list = []

        # 初始化一个变量来跟踪当前正在构建的字符串片段
        current_value = ''

        # 遍历字符串的每个字符
        i = 0
        while i < len(expect_result):
            # 如果当前字符是运算符，则检查并添加前一个片段（如果有的话）
            if expect_result[i] in operators:
                if current_value:
                    values_list.append(current_value)
                    current_value = ''
            else:
                # 否则，将当前字符添加到当前片段中
                current_value += expect_result[i]
            i += 1

            # 添加最后一个片段（如果有的话）
        if current_value:
            values_list.append(current_value)

        # 调用获取参数方法，获取xx.xx的值
        for result in values_list:
            if '$' in result:
                if '$.' in result:
                    # 截取第一个 . 前面的${}$参数
                    expect_result_before = result.split('.')[0]
                    # 预期值参数化
                    ex_result = self.parameterization(expect_result_before)
                    ex_result = ast.literal_eval(ex_result)

                    expect_result_list = result.split('.')[1:]
                    # 将list 重新拼接成 xx.xx
                    expect_result_after = ".".join(expect_result_list[0:])

                    # 调用获取参数方法，获取xx.xx的值
                    temp = str(GetModelInfo().get_element_by_params(expect_result_after, ex_result))
                    expect_result = expect_result.replace(result, str(temp))
                else:
                    temp = str(self.parameterization(result))
                    expect_result = expect_result.replace(result, str(temp))
            else:
                continue
        return expect_result

    # 包含加密解密判断
    def process_encrypt_decrypt(self, tempStr):
        def replace_calls(match, method):
            data = match.group(1)
            try:
                if method == 'encrypt':
                    return CommonUtil().get_encrypt(data)
                elif method == 'decrypt':
                    return CommonUtil().get_decrypt(data)
            except Exception as e:
                # 处理异常，可以选择记录日志或返回原始字符串
                print(f"Error processing {method}: {e}")
                return match.group(0)

        # 匹配 get_encrypt(...) 或 get_decrypt(...)
        encrypt_pattern = r'get_encrypt\(([^)]+)\)'
        decrypt_pattern = r'get_decrypt\(([^)]+)\)'

        if isinstance(tempStr, str):
            # 先处理 get_encrypt
            tempStr = re.sub(encrypt_pattern, lambda m: replace_calls(m, 'encrypt'), tempStr)
            # 再处理 get_decrypt
            tempStr = re.sub(decrypt_pattern, lambda m: replace_calls(m, 'decrypt'), tempStr)
            return tempStr
        elif isinstance(tempStr, dict):
            processed_dict = {}
            for key, value in tempStr.items():
                if isinstance(value, str):
                    # 对字符串值进行处理
                    processed_value = value
                    processed_value = re.sub(encrypt_pattern, lambda m: replace_calls(m, 'encrypt'), processed_value)
                    processed_value = re.sub(decrypt_pattern, lambda m: replace_calls(m, 'decrypt'), processed_value)
                    processed_dict[key] = processed_value
                else:
                    # 非字符串值直接添加到新字典中
                    processed_dict[key] = value
            return processed_dict
        else:
            raise ValueError("Input must be a string or a dictionary")
    def parameterization(self, tempStr, param={}):
        # param type:  @ : 测试参数模块获取  $: 从前置参数中或接口返回值中获取  #：断言中从返回参数中获取
        result = re.findall('@{(.*?)}@', tempStr)
        if result:
            for x in result:
                temp_result = GetModelInfo().getParameter(x, self.env_id)
                if isinstance(temp_result, tuple):
                    temp_result = temp_result[0]
                tempStr = tempStr.replace("@{" + x + "}@", str(temp_result))
        result = re.findall('\${(.*?)}\$', tempStr)
        if result:
            for y in result:
                # 获取返回值类型为list：[{key:value}]时，进行参数处理
                if isinstance(self.param[y], list):
                    # 处理list中存在value为None时，转换成 null,解决json.load报错
                    l = json.dumps(self.param[y], default=lambda b: None if b is None else b, ensure_ascii=False)
                    temp = tempStr.replace("${" + y + "}$", str(l))
                    # 将参数中单引号替换成双引号
                    tempStr = str(temp).replace("\'", "\"")
                tempStr = tempStr.replace("${" + y + "}$", str(self.param[y]))
                #     temp = str(self.param[y]).replace("\'", "\"")
                # #     tempStr = tempStr.replace("${" + y + "}$", temp)
                # # tempStr = tempStr.replace("${" + y + "}$", str(self.param[y]))
        result = re.findall('#{(.*?)}#', tempStr)
        if result:
            for y in result:
                tempStr = str(GetModelInfo().get_element_by_params(y, param))
        # 处理 get_encrypt(...) 形式的加密函数调用
        if  re.findall(r'get_encrypt\((\d+)\)', tempStr) or re.findall(r'get_decrypt\((\d+)\)', tempStr):
            tempStr = self.process_encrypt_decrypt(tempStr)

        return tempStr

    # 复制文件数据
    def copy_file_data(self, data_file, template_file, min_row=1, max_row=None):
        '''
        入参说明：
        data_file：数据文件,包含路径（xxx/xxx/data_file.xlsx）
        template_file:模版文件,包含路径（xxx/xxx/template_file.xlsx）
        min_row：复制数据文件的开始行，不包含标题
        max_row:复制数据文件的结束行，不包含标题
        按照表格里的行号传值，包含标题行，min_row =2 max_row =3 时，则表示复制第二行数据
        '''
        # 读取数据文件
        df_data = pd.read_excel(data_file, engine='openpyxl')
        # 对整个表格先过滤空数据
        df_data = df_data.fillna('')
        # 选择要复制的行
        if max_row:
            # 下标从0开始，max_row是开区间，不包含此行
            copy_row = df_data.iloc[int(min_row) - 2:int(max_row) - 1]
        else:
            copy_row = df_data.iloc[int(min_row) - 2:, :]

        # 剔除空行
        copy_row = copy_row.dropna(how='all')

        # 调用 parameterization 方法进行参数替换
        param_row = self.parameterization(copy_row.to_csv(index=False))
        # 将替换后的数据转成DataFrame类型
        copy_row_data = pd.read_csv(io.StringIO(param_row))

        # 定义一个函数来检查并转换浮点型为整数（如果小数位为0）
        def convert_value(value):
            if isinstance(value, float) and value.is_integer():
                return int(value)
            # 对于非数值类型（如字符串），我们直接返回它
            return value

        # 对DataFrame的每一列应用处理函数
        # 使用lambda函数来应用convert_value到每个元素上
        copy_row_data = copy_row_data.apply(lambda series: series.apply(convert_value))
        # 剔除空行和 NaN 值，并转成字符串，解决存值科学计数问题
        copy_row = copy_row_data.dropna(how='all').fillna('').astype(str)

        # 加载模板文件
        workbook = load_workbook(template_file)
        sheet = workbook.active  # 假设我们操作的是第一个工作表

        # 将数据复制到指定行
        for i, row in copy_row.iterrows():
            for j, value in enumerate(row):
                cell =  sheet.cell(row=int(min_row) + i, column=j + 1, value=value)
                cell.number_format = '@'  # '@' 表示文本格式

        # 保存模板文件
        workbook.save(template_file)
        return template_file
    def api_transform(self, caseInfo):
        interfaceInfo = InterfaceInfo.objects.get(interface_id = caseInfo.interface_id)
        projectInfo = ProjectInfo.objects.get(project_id=interfaceInfo.project_id)
        self.add_log("========================= 域名信息 =======================")
        self.add_log("域名ID：%s" % projectInfo.project_id)
        self.add_log("域名名称：%s" % projectInfo.project_name)
        host = ""
        if projectInfo.project_mode:
            if self.env_id == "1":
                self.add_log("域名/主机：%s" % projectInfo.test_host)
                host = projectInfo.test_host
            elif self.env_id == "2":
                self.add_log("域名/主机：%s" % projectInfo.uat_host)
                host = projectInfo.uat_host
            elif self.env_id == "3":
                self.add_log("域名/主机：%s" % projectInfo.pro_host)
                host = projectInfo.pro_host
        else:
            self.add_log("域名/主机：%s" % projectInfo.ip)
            host = projectInfo.ip
        self.add_log("========================= 接口信息 =======================")
        self.add_log("接口ID：%s" % interfaceInfo.interface_id)
        self.add_log("接口名称：%s" % interfaceInfo.interface_name)
        header = ""
        if interfaceInfo.header:
            self.add_log("请求头：%s" % interfaceInfo.header)
            header = interfaceInfo.header
        elif projectInfo.project_mode:
            if self.env_id == "1":
                self.add_log("请求头：%s" % projectInfo.test_header)
                header = projectInfo.test_header
            elif self.env_id == "2":
                self.add_log("请求头：%s" % projectInfo.test_header)
                header = projectInfo.test_header
            elif self.env_id == "3":
                self.add_log("请求头：%s" % projectInfo.test_header)
                header = projectInfo.test_header
        self.add_log("请求地址：%s" % interfaceInfo.interface_address)
        interface_address = self.parameterization(interfaceInfo.interface_address)
        interface_data = self.parameterization(caseInfo.interface_data)
        header = self.parameterization(header)
        ip = self.parameterization(host)
        # 生成案例运行预登记ID（自定义）
        pre_record_id = "preRecord" + datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')
        if projectInfo.port is None:
            projectInfo.port = ''
        # 没有参数，则给一个默认值：{}
        if interface_data is None or interface_data == '':
            interface_data = str({})
        if caseInfo.asserts is None:
            caseInfo.asserts = ""
        # 案例运行预登记
        caseRunPreRecord = CaseRunPreRecord(pre_record_id=pre_record_id, environment_id=self.env_id,
                                            run_type="sceneDebug", case_id=caseInfo.case_id,
                                            case_name=caseInfo.case_name,
                                            interface_agreement=interfaceInfo.interface_agreement,
                                            ip=ip, port=projectInfo.port, interface_way=interfaceInfo.interface_way,
                                            interface_address=interface_address, header=header,
                                            interface_data=interface_data, is_init=caseInfo.is_init,
                                            init_database=caseInfo.init_database, init_sql=caseInfo.init_sql,
                                            is_back=caseInfo.is_back, back_sql=caseInfo.back_sql, creater=self.operator,
                                            is_encrypt=caseInfo.is_encrypt, asserts=caseInfo.asserts,
                                            create_time=time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
        caseRunPreRecord.save()
        return caseRunPreRecord

    def case_run(self, caseInfo):
        if caseInfo.case_type == 'API' or caseInfo.case_type == 'EXPORT' or  caseInfo.case_type == 'UPLOAD':
            caseRunPreRecord = self.api_transform(caseInfo)
            if caseInfo.case_type == 'EXPORT':
                path, file_name = os.path.split(caseRunPreRecord.interface_address)
                # 解决导出路径中存在密钥导致鉴权失败问题
                if "OSSAccessKeyId" in  caseRunPreRecord.interface_address:
                    filename = file_name.split('?')[0]
                    caseRunPreRecord.interface_address = path + "/" + filename
                # 解决导出路径中存在"+"等字符，导致文件出错问题
                if "+" in  caseRunPreRecord.interface_address:
                    name = urllib.parse.quote(file_name,safe='')
                    caseRunPreRecord.interface_address = path + "/" + name
        self.add_log("========================= 案例信息 =======================")
        self.add_log("案例ID：%s" % caseInfo.case_id)
        self.add_log("案例名称：%s" % caseInfo.case_name)
        if caseInfo.case_type == 'SQL':
            interface_data = self.parameterization(caseInfo.interface_data)
            # 执行sql查询
            dbinfo = DataBaseInfo.objects.get(data_base_id=caseInfo.init_database)
            dbinfo.ip_address = self.parameterization(dbinfo.ip_address)
            dbinfo.data_base_port = self.parameterization(dbinfo.data_base_port)
            dbinfo.db_name = self.parameterization(dbinfo.db_name)
            dbinfo.db_account = self.parameterization(dbinfo.db_account)
            dbinfo.db_pwd = PrpCrypt('HP9lYhuDeeJjEnAo').decrypt(dbinfo.db_pwd)
            dbinfo.db_pwd = self.parameterization(dbinfo.db_pwd)
            self.add_log("数据库地址：%s:%s" % (dbinfo.ip_address, dbinfo.data_base_port))
            dbUtil = DBUtils(dbinfo.data_base_type, dbinfo.ip_address + ":" + dbinfo.data_base_port,
                             dbinfo.db_account, dbinfo.db_pwd, dbinfo.db_name)
            sql_list = interface_data.split(";")
            sql_list = [i for i in sql_list if i != '']  # 去除空元素
            query_result = {}
            for sql in sql_list:
                self.add_log("执行语句：%s" % sql)
                if sql and (sql.strip().upper().startswith('SELECT')):
                    # sql案例执行结果由数组类型修改为字典，key值为字段名 2022.02.08 huangjiaojiao
                    result = dbUtil.queryDB(sql)
                    for key in result:
                        query_result[key] = result[key]
                else:
                    result = dbUtil.update(sql)
                    query_result["msg"] = result
                self.add_log("执行结果：%s" % result)
            if len(query_result) > 1:
                self.add_log("案例结果：%s" % query_result)
            self.assertResult(query_result, caseInfo)
            self.api_result["response"] = query_result
        elif caseInfo.case_type == 'REDIS':
            interface_data = self.parameterization(caseInfo.interface_data)
            # 执行redis查询
            dbinfo = DataBaseInfo.objects.get(data_base_id=caseInfo.init_database)
            dbinfo.ip_address = self.parameterization(dbinfo.ip_address)
            dbinfo.data_base_port = self.parameterization(dbinfo.data_base_port)
            dbinfo.db_name = self.parameterization(dbinfo.db_name)
            dbinfo.db_account = self.parameterization(dbinfo.db_account)
            dbinfo.db_pwd = PrpCrypt('HP9lYhuDeeJjEnAo').decrypt(dbinfo.db_pwd)
            dbinfo.db_pwd = self.parameterization(dbinfo.db_pwd)
            self.add_log("redis地址：%s:%s" % (dbinfo.ip_address, dbinfo.data_base_port))
            dbUtil = DBUtils(dbinfo.data_base_type, dbinfo.ip_address + ":" + dbinfo.data_base_port,
                             dbinfo.db_account, dbinfo.db_pwd, dbinfo.db_name)
            key_list = interface_data.split(";")
            key_list = [i for i in key_list if i != '']  # 去除空元素
            result = {}
            for keys in key_list:
                self.add_log("查询key：%s" % keys)
                value = dbUtil.queryKey(keys)
                # 判断返回结果是否为字典
                if value.find("{") >= 0:
                    result = json.loads(value)
                    if isinstance(result, dict) == False:
                        result = json.loads(result)
                else:
                    result["data"] = value
                self.add_log("案例结果：%s" % result)
            self.assertResult(result, caseInfo)
            self.api_result["response"] = result
        else:
            if caseRunPreRecord.port:
                if caseRunPreRecord.interface_agreement == "HTTPS":
                    url = "https://" + caseRunPreRecord.ip + ":" + caseRunPreRecord.port + caseRunPreRecord.interface_address
                elif caseRunPreRecord.interface_agreement == "HTTP":
                    url = "http://" + caseRunPreRecord.ip + ":" + caseRunPreRecord.port + caseRunPreRecord.interface_address
                elif caseRunPreRecord.interface_agreement == "NULL":
                    url = caseRunPreRecord.ip + ":" + caseRunPreRecord.port + caseRunPreRecord.interface_address
            else:
                if caseRunPreRecord.interface_agreement == "HTTPS":
                    url = "https://" + caseRunPreRecord.ip + caseRunPreRecord.interface_address
                elif caseRunPreRecord.interface_agreement == "HTTP":
                    url = "http://" + caseRunPreRecord.ip + caseRunPreRecord.interface_address
                elif caseRunPreRecord.interface_agreement == "NULL":
                    url = caseRunPreRecord.ip + caseRunPreRecord.interface_address
            self.add_log("========================= 参数化后 =======================")
            self.add_log("请求地址：%s" % url)
            self.add_log("请求方式：%s" % caseRunPreRecord.interface_way)
            if caseRunPreRecord.header:
                self.add_log("请求头：%s" % caseRunPreRecord.header)
            else:
                caseRunPreRecord.header = None
                default_header = {
                    "Accept": "application/json, text/plain, */*",
                    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                    "Connection": "close",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36",
                    "Content-Type": "application/json;charset=UTF-8"
                }
                self.add_log("默认请求头：%s" % default_header)
            if caseInfo.case_type == 'UPLOAD':
                self.add_log("请求数据：%s" % json.loads(caseRunPreRecord.interface_data))
            else:
                self.add_log("请求数据：%s" % caseRunPreRecord.interface_data)
            logger.info("请求数据：%s" % caseRunPreRecord.interface_data)
            if caseInfo.case_type == 'UPLOAD':
                interface_data = json.loads(self.parameterization(caseRunPreRecord.interface_data))
                data_file_path = interface_data["file_path"]
                template_path = interface_data["template_file"]
                min_rows = interface_data["upload_min_rows"]
                max_rows = interface_data["upload_max_rows"]
                # 当模版存在且文件后缀为表格，则进行数据处理
                if template_path and template_path.endswith((".xls", ".xlsx")):
                    # 判断数据文件和模版文件列名是否一致
                    diff_columns =GetModelInfo().compare_column_names(data_file_path, template_path)
                    if diff_columns:
                      # 如果存在差异则直接返回错误，不继续执行
                      raise ValueError("模版与数据文件列名存在差异：", diff_columns['diff_columns'])
                    else:
                        # 读取文件内容，并返回json格式
                        data_list = GetModelInfo().read_excel_data(data_file_path, int(min_rows) - 2)
                        data = self.parameterization(str(data_list["data"]))
                        self.add_log("数据文件内容(首行)：%s" % data)
                        self.copy_file_data(data_file_path, template_path, min_rows, max_rows)
            # 执行接口请求
            test = APITest(caseRunPreRecord.interface_way, url, caseRunPreRecord.interface_data,
                           caseRunPreRecord.header)
            result = test.doAutoAPITest()
            if result:
                if caseInfo.case_type == 'EXPORT':
                    logger.info("案例ID：%s" % caseInfo.case_id)
                    logger.info("案例名称：%s" % caseInfo.case_name)
                    logger.info("接口返回结果：%s" % result)
                    self.add_log("========================= 请求结果 =======================")
                    self.add_log("响应码：%s" % result["status_code"])
                    self.add_log("请求耗时：%s秒" % result["time"])
                    self.add_log("实际请求头：%s" % result["request_header"])
                    self.add_log("响应头：%s" % result["header"])
                    file = GetModelInfo().download_excel(caseRunPreRecord.interface_address, result["response"], caseInfo.case_id)
                    logger.info("文件路径----：%s" % file)
                    # 读取文件内容，并返回json格式
                    data_list = GetModelInfo().read_excel_data(file)
                    # 给所有值加上引号
                    for key, value in data_list["data"][0].items():
                        data_list["data"][0][key] = str(value).replace(" ", "\"")  # 将所有值转换为带引号的字符串
                    self.add_log("EXCEL解析结果(总条数、首行数据)：%s" % data_list)
                    data_list = json.dumps(data_list)
                    if result["status_code"] == 200:
                        response_header = {"header": result["header"]}
                        self.api_result["response"] = {**(json.loads(data_list)), **response_header}
                        self.api_result["request"] = json.loads(caseRunPreRecord.interface_data, strict=False)

                        # 执行断言
                        self.assertExportResult(file, caseInfo)
                    else:
                        self.add_log("案例执行失败，结束运行")
                else:
                    logger.info("案例ID：%s" % caseInfo.case_id)
                    logger.info("案例名称：%s" % caseInfo.case_name)
                    logger.info("接口返回结果：%s" % result)
                    self.add_log("========================= 请求结果 =======================")
                    # self.add_log("result：%s" % result)
                    self.add_log("响应码：%s" % result["status_code"])
                    self.add_log("请求耗时：%s秒" % result["time"])
                    self.add_log("实际请求头：%s" % result["request_header"])
                    self.add_log("响应头：%s" % result["header"])
                    self.add_log("响应结果：%s" % result["result"])
                    if result["status_code"] == 200:
                        response_header = {"header": result["header"]}
                        self.api_result["response"] = {**(json.loads(result["result"])), **response_header}
                        if caseRunPreRecord.interface_way == "x-www-form-urlencoded_POST":
                            request = caseRunPreRecord.interface_data.split("&")
                            request_dict = {}
                            for item in request:
                                data = item.split("=")
                                request_dict[data[0]] = data[1]
                            self.api_result["request"] = request_dict
                        else:
                            if caseInfo.case_type == 'UPLOAD':
                                self.api_result["request"] = caseRunPreRecord.interface_data
                            else:
                                self.api_result["request"] = json.loads(caseRunPreRecord.interface_data, strict=False)
                        # 执行断言
                        self.assertResult(json.loads(result["result"], strict=False), caseInfo)
                    else:
                        self.add_log("案例执行失败，结束运行")
        return self.api_result

    def report(self, case_id, case_name):
        assert_dict = {}
        flag1 = [s for s in self.log_content if '无需断言' in s]
        flag2 = [s for s in self.log_content if '断言成功' in s]
        flag3 = [s for s in self.log_content if '断言失败' in s]
        if flag1:
            assert_str = " 无需断言"
            assert_flag = "1"
        elif flag2:
            assert_str = " 断言成功"
            assert_flag = "1"
        elif flag3:
            assert_str = " 断言失败"
            assert_flag = "0"
        else:
            assert_str = " 无断言内容"
            assert_flag = "0"
        assert_dict['assert'] = assert_flag
        assert_dict['title'] = "案例ID：" + str(case_id) + " " + case_name + assert_str
        assert_dict['detail'] = self.log_content
        return assert_dict

    def scene_transform(self):
        scene_relation = SceneRelation.objects.get(scene_relation_id=self.relation_id)
        case_id = scene_relation.relation_case_id
        caseInfo = CaseInfo.objects.get(case_id=case_id)
        # 2021.05.25 开启参数重置，则需要替换testcase中的请求参数与断言
        if scene_relation.reset_param:
            caseInfo.interface_data = scene_relation.interface_data
            caseInfo.is_assert = scene_relation.is_assert
            caseInfo.asserts = scene_relation.asserts
        return caseInfo

    def get_param(self, result):
        param = {}
        scene_relation = SceneRelation.objects.get(scene_relation_id=self.relation_id)
        respon_param = result['response']
        if scene_relation.is_get_param:
            self.add_log("========================= 提取参数 =======================")
            if scene_relation.request_param:
                str_list = scene_relation.request_param.split(";")
                str_list = [i for i in str_list if i != '']  # 去除空元素
                for x in str_list:
                    param[x] = GetModelInfo().get_element_by_params(x, result['request'])
                    self.add_log("请求参数，%s：%s" % (x, str(param[x])))
            if scene_relation.response_param:
                str_list = scene_relation.response_param.split(";")
                param_alias_list = scene_relation.response_param_alias.split(";")
                str_list = [i for i in str_list if i != '']  # 去除空元素
                i = 0
                for x in str_list:
                    param[param_alias_list[i]] = GetModelInfo().get_element_by_params(x, respon_param)
                    if param[param_alias_list[i]] is None:
                        self.add_log("接口返回值：%s：" %respon_param)
                        self.add_log("返回值中无参数【%s】，请检查：" % x)
                    else:
                        self.add_log("返回参数，%s：%s" % (x, str(param[param_alias_list[i]])))
                        i = i + 1
            if scene_relation.response_header_param:
                str_list = scene_relation.response_header_param.split(";")
                str_list = [i for i in str_list if i != '']  # 去除空元素
                for x in str_list:
                    param[x] = GetModelInfo().get_element_by_params(x, result['response']['header'])
                    self.log_content.append("【" + CommonUtil().get_current_ymdhmsf() + "】 "
                                            + "返回头部，%s：%s" % (x, str(param[x])))
            return param

    def scene_sleep(self):
        scene_relation = SceneRelation.objects.get(scene_relation_id=self.relation_id)
        if scene_relation.sleep_time != '0':
            time.sleep(int(scene_relation.sleep_time))
            self.add_log("========================= 程序休眠 =======================")
            self.add_log("休眠 %s 秒" % scene_relation.sleep_time)





