import re
from logging import getLogger
from types import ModuleType
from typing import Union, List

from django.conf.urls import url
from django.urls import include
from rest_framework.routers import DefaultRouter
from scaffold.apps.config import views as config_views
from scaffold.apps.media import views as media_views
from scaffold.restframework.utils import auto_collect_urls

import base.views
import data_config.views
import interface_test.views
import ui_test.views
import performance_test.views
import tools_help.views
import test_case.views
from . import views

logger = getLogger(__name__)

app_name = 'core'

# 添加路由数据
views_module_list = [
    views,
    media_views,
    config_views,
    base.views,
    data_config.views,
    interface_test.views,
    test_case.views,
    ui_test.views,
    performance_test.views,
    tools_help.views,
]

# 添加路由数据
urlpatterns = [
    url(r'^', include(auto_collect_urls(views_module_list))),
]

# # 为了解决 url后面不用添加“/"的问题
# def auto_collect_urls(modules: Union[ModuleType, List[ModuleType]]):
#     """ Automatically collect ViewSet classes to urls list for `urls.py`
#     :param modules: passing module(s)
#     :return:
#     """
#     # 默认情况下，由 DefaultRouter 创建的 URL 附加了尾部斜杠 "/"。在实例化路由器时，可以通过将 trailing_slash 参数设置为 False 来修改此行为
#     router = DefaultRouter(trailing_slash=False)
#
#     routers = []
#
#     # Fall-backs for single-module usage
#     if type(modules) == ModuleType:
#         modules = [modules]
#
#     for module in modules:
#         for key, item in module.__dict__.items():
#             # Catches name ends with ViewSet.
#             if key.endswith('ViewSet'):
#                 # Replacing ViewSet from uppercase to underscored naming as Rest resource name.
#                 name = key.replace('ViewSet', '')
#                 name = re.sub(r'([A-Z])', '_\\1', name)[1:].lower()
#                 if name:
#                     # 如果name设置成功，则添加到routers数组中
#                     routers.append((name, item))
#
#     for name, item in routers:
#         # print(name, item)
#         # 进行视图集路由注册 register(prefix(视图集的路由前缀),viewset(视图集),base_name(路由名称的前缀))
#         router.register(name, item)
#
#     # Return urls
#     return router.urls



