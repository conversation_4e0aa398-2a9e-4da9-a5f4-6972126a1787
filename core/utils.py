import csv
import hashlib
import logging
import os
import time
from django.http import JsonResponse

from base.models import UserInfo

logger = logging.getLogger(__name__)


class ResponseUtils(object):

    def result_json(self, status, msg, data=None):
        # 创建一个空字典
        result = {"data": data, "meta": {"msg": msg, "status": status},
                  "timestamp": int(round(time.time() * 1000))}
        return result

    @classmethod
    def return_success(cls, msg, data=None):
        return JsonResponse(data=cls().result_json(200, msg, data), json_dumps_params={'ensure_ascii': False})

    @classmethod
    def return_fail(cls, app_errors, error_detail=None):
        error_msg = app_errors.message
        if error_detail is not None:
            error_msg += ":" + error_detail
        return JsonResponse(data=cls().result_json(app_errors.code, error_msg, None),
                            json_dumps_params={'ensure_ascii': False})


class UserUtils(object):

    @classmethod
    def get_login_user(cls, request):
        user_id = request.META.get("HTTP_USERID")
        if user_id is not None and user_id.isdigit():
            return UserInfo.objects.get(user_id=user_id)
        else:
            return None


class DateUtils(object):
    @classmethod
    def get_current_time(cls):
        return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())


class CsvFileReader(object):

    @classmethod
    def csv_read_file(cls, file_path):
        csv_row = []
        if file_path is None or not os.path.isfile(file_path):
            raise Exception(f'{file_path}; 文件路径不存在或不是文件')
        if not file_path.endswith('.csv'):
            raise Exception(f'{file_path}; 不是csv文件')
        with open(file_path, 'r') as f:
            reader = csv.reader(f)
            result = list(reader)
            if result:
                header = result[0]
                del result[0]
                for row in result:
                    row_dict = {}
                    for col in row:
                        row_dict.update({header[row.index(col)]: col})
                    csv_row.append(row_dict)
        return csv_row

    @classmethod
    def md5_vaule(cls, path):
        if os.path.exists(path):
            hasher = hashlib.md5()
            afile = open(path, 'rb')
            buf = afile.read()
            hasher.update(buf)
            return str(hasher.hexdigest())
        else:
            return ''