Index: .idea/hydee_auto_server.iml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<module type=\"PYTHON_MODULE\" version=\"4\">\n  <component name=\"NewModuleRootManager\">\n    <content url=\"file://$MODULE_DIR$\" />\n    <orderEntry type=\"jdk\" jdkName=\"Python 3.8 (hydee_auto_server)\" jdkType=\"Python SDK\" />\n    <orderEntry type=\"sourceFolder\" forTests=\"false\" />\n  </component>\n  <component name=\"PyDocumentationSettings\">\n    <option name=\"format\" value=\"PLAIN\" />\n    <option name=\"myDocStringFormat\" value=\"Plain\" />\n  </component>\n  <component name=\"TestRunnerService\">\n    <option name=\"PROJECT_TEST_RUNNER\" value=\"pytest\" />\n  </component>\n</module>
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/hydee_auto_server.iml b/.idea/hydee_auto_server.iml
--- a/.idea/hydee_auto_server.iml	(revision d7c07b3c0de5d4e7bd526b5f312d5662af221fa0)
+++ b/.idea/hydee_auto_server.iml	(date 1749797098103)
@@ -2,7 +2,7 @@
 <module type="PYTHON_MODULE" version="4">
   <component name="NewModuleRootManager">
     <content url="file://$MODULE_DIR$" />
-    <orderEntry type="jdk" jdkName="Python 3.8 (hydee_auto_server)" jdkType="Python SDK" />
+    <orderEntry type="jdk" jdkName="Python 3.10 (hydee_auto_server)" jdkType="Python SDK" />
     <orderEntry type="sourceFolder" forTests="false" />
   </component>
   <component name="PyDocumentationSettings">
Index: hydee_auto_server/settings.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>\"\"\"\nDjango settings for hydee_auto_server project.\n\nGenerated by 'django-admin startproject' using Django 3.0.6.\n\nFor more information on this file, see\nhttps://docs.djangoproject.com/en/3.0/topics/settings/\n\nFor the full list of settings and their values, see\nhttps://docs.djangoproject.com/en/3.0/ref/settings/\n\"\"\"\n\nimport os\nfrom scaffold.settings import *\n\n# 服务器IP地址\nSERVICE_IP = '************'\n\n# IP 白名单   ************为生产HIM 调用IP\nALLOWED_IPS = ['************', '127.0.0.1', '**************', '**************', '************', '************']\n\nFILE_PATH = 'uploads/'\n\n# REDIS密码\nREDIS_PASSWORD = 'Hydee123!'\n\n# APP_ID\nWECHAT_APP_ID = \"ww408c023179829552\"\n# 应用ID\nAGENTID = \"1000189\"\n\n# 企业ID-测试\n# CROP_ID = \"ww408c023179829552\"\n# 企业ID-生产\nCROP_ID = \"ww408c023179829552\"\n\n# B端测试域名\n# ROBOT_DOMAIN = \"http://hydee-middle-robot.svc.k8s.dev.ydjia.cn\"\n# B端生产域名\nROBOT_DOMAIN = \"http://hydee-middle-robot.svc.k8s.test.ydjia.cn\"\n\n# 测试平台域名-测试\n# PLATFORM_DOMAIN = \"testplatform-dev.hydee.cn\"\n# 测试平台域名-生产\nPLATFORM_DOMAIN = \"testplatform.hydee.cn\"\n\n# ai-robot 服务域名-测试\n# AI_ROBOT_DOMAIN = \"https://middle-dev.ydjia.cn/\"\n# ai-robot 服务域名-生产\nAI_ROBOT_DOMAIN = \"https://middle.hydee.cn/\"\n\n# 获取句子状态接口\nROBOT_STATUS_URL = AI_ROBOT_DOMAIN+\"/robot/api/data/bot_status\"\n\n# 句子推送群消息接口\nROBOT_SEND_MSG_URL = AI_ROBOT_DOMAIN+\"/robot/api/data/send_message_data\"\n\n# 默认消息内容\nROBOT_SEND_DEFAULT_MSG = \"技术人员正在跟进中，请耐心等待\"\n\n# 售后问题消息推送接口\nQUESTION_MSG_URL = ROBOT_DOMAIN+\"/1.0/robot/api/push/group/msg\"\n\n# 售后问题消息识别接口\nQUESTION_CHECK_URL = ROBOT_DOMAIN+\"/1.0/robot/api/msg/prediction/batch\"\n\n# 值班表信息接口\nROTA_URL = ROBOT_DOMAIN+\"/1.0/robot/api/fetch/group\"\n\n# 企微个人推送地址\nNOTICE_URL = ROBOT_DOMAIN+\"/1.0/robot/api/notice/ta\"\n\n# callback地址\nCALLBACK_URL = ROBOT_DOMAIN+\"/1.0/robot/wx/api/callback\"\n\n# 文件上传接口\nUPLOAD_URL = ROBOT_DOMAIN+\"/1.0/robot/api/upload\"\n\n# 解绑合并消息接口\nUNBIND_MSG_URL = ROBOT_DOMAIN+\"/1.0/robot/api/lift/msg\"\n\n# AI回答接口\nAI_ANSWER_URL = ROBOT_DOMAIN+\"/1.0/robot/api/msg/ai/answer\"\n\n\n\n\n\n\n# Build paths inside the project like this: os.path.join(BASE_DIR, ...)\nBASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))\n\n\n# Quick-start development settings - unsuitable for production\n# See https://docs.djangoproject.com/en/3.0/howto/deployment/checklist/\n\n# SECURITY WARNING: keep the secret key used in production secret!\nSECRET_KEY = 'lr8sgcx-$)%tachlo3^q%uo%38wj_@3ey@egqybfh-rxh8-&#3'\n\n# 会员营销密钥\nMEMBER_KEY = \"hydeesoft0211122\"\nKEY_SIZE = 16\n\n# SECURITY WARNING: don't run with debug turned on in production!\nDEBUG = False\n\nALLOWED_HOSTS = ['*']\n\n\n# Application definition\n\nINSTALLED_APPS = [\n    'django.contrib.admin',\n    'django.contrib.auth',\n    'django.contrib.contenttypes',\n    'django.contrib.sessions',\n    'django.contrib.messages',\n    'django.contrib.staticfiles',\n    'scaffold.apps.media',\n    'scaffold.apps.config',\n    'corsheaders',#跨域访问的应用部分\n    'django_celery_beat',\n    'core',\n    'base',#自己建立的应用名称\n    'interface_test',\n    'ui_test',\n    'test_case',\n    'performance_test',\n    'tools_help',\n    'data_config',\n    'django_db_reconnect',\n]\n\n\nMIDDLEWARE = [\n    'corsheaders.middleware.CorsMiddleware',\n    'django.middleware.security.SecurityMiddleware',\n    'django.contrib.sessions.middleware.SessionMiddleware',\n    'django.middleware.common.CommonMiddleware',\n    #'django.middleware.csrf.CsrfViewMiddleware',\n    'django.contrib.auth.middleware.AuthenticationMiddleware',\n    'django.contrib.messages.middleware.MessageMiddleware',\n    'django.middleware.clickjacking.XFrameOptionsMiddleware',\n    'core.commonUtils.userRequestMiddleware.UserRequestMiddleware',\n]\n\nCORS_ORIGIN_WHITELIST = (\n    'http://127.0.0.1:8080',\n    'http://localhost:8080',\n    'http://*************:8080'\n)\nCORS_ALLOW_CREDENTIALS = True\n\nCORS_ALLOW_METHODS = (\n    'DELETE',\n    'GET',\n    'OPTIONS',\n    'PATCH',\n    'POST',\n    'PUT',\n    'VIEW',\n)\nCORS_ALLOW_HEADERS = (\n    'XMLHttpRequest',\n    'X_FILENAME',\n    'accept-encoding',\n    'authorization',\n    'content-type',\n    'dnt',\n    'origin',\n    'user-agent',\n    'x-csrftoken',\n    'x-requested-with',\n    'Pragma',\n    \"userId\",\n)\n\n\nROOT_URLCONF = 'hydee_auto_server.urls'\n\n\nTEMPLATES = [\n    {\n        'BACKEND': 'django.template.backends.django.DjangoTemplates',\n        'DIRS': [],\n        'APP_DIRS': True,\n        'OPTIONS': {\n            'context_processors': [\n                'django.template.context_processors.debug',\n                'django.template.context_processors.request',\n                'django.contrib.auth.context_processors.auth',\n                'django.contrib.messages.context_processors.messages',\n            ],\n        },\n    },\n]\n\nWSGI_APPLICATION = 'hydee_auto_server.wsgi.application'\n\n\n# Database\n# https://docs.djangoproject.com/en/3.0/ref/settings/#databases\n\nDATABASES = {\n    'default': {\n        'ENGINE': 'django.db.backends.mysql',\n        'HOST': SERVICE_IP,\n        'NAME': \"hydee_auto_test\",\n        'USER': \"hydee\",\n        'PASSWORD': \"AutoTest\",\n        'OPTIONS': {'charset': 'utf8mb4'},\n        'PORT': \"3306\"\n    }\n}\n\nLOGGING = {\n    'version': 1,\n    'disable_existing_loggers': False,\n    'handlers': {\n        'console': {\n            'class': 'logging.StreamHandler',\n        },\n    },\n    'root': {\n        'handlers': ['console'],\n        'level': 'INFO',\n    },\n}\n# Password validation\n# https://docs.djangoproject.com/en/3.0/ref/settings/#auth-password-validators\n\nAUTH_PASSWORD_VALIDATORS = [\n    {\n        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',\n    },\n    {\n        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',\n    },\n    {\n        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',\n    },\n    {\n        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',\n    },\n]\n\n# 屏蔽中间应用token验证，直接访问\nNO_TOKEN_LIST = [\n    \"/api/interface_info/interface_traffic_summary/\",\n    \"/api/interface_info/get_interface_daily_traffic/\",\n    \"/api/interface_info/export_interface_traffic_summary/\",\n    \"/api/interface_info/export_interface_daily_traffic/\",\n    \"/api/task_info/task_report/\",\n    \"/api/login\",\n    \"/api/user_info/login/\",\n    \"/api/user_info/user_password_reset/\",\n    \"/api/case_info/download_csv/\",\n    \"/api/task_info/task_list/\",\n    \"/api/task_info/get_daily_monitoring_list/\",\n    \"/api/task_info/task_run/\",\n    \"/api/task_info/hops_run_task/\",\n    \"/api/tools_help/new_release_list/\",\n    \"/api/performance/task_report/\",\n    \"/api/task_info/task_report/\",\n    \"/api/tools_help/runTaskForJenkins/\",\n    \"/api/tools_help/unifiedPay/\",\n    \"/api/tools_help/refund/\",\n    \"/api/task_info/task_report_detail/\",\n    \"/api/task_info/interface_compare_report/\",\n    \"/export/records/\",\n    \"/api/export/records/\",\n    \"/api/task_info/add_scene_to_more_task/\",\n    \"/api/task_info/delete_task_scene/\",\n    \"/api/case_info/upload_file/\",\n    \"/upload/\",\n    \"/api/question/question_list/\",\n    \"/api/question/question_push_message/\",\n    \"/api/question/get_emp_info/\",\n    \"/api/question/question_update/\",\n    \"/api/question/answer_update/\",\n    \"/api/question/get_question_statistics/\",\n    \"/api/test_case_info/import_xmind/\"\n    \"/api/user_info/get_authInfo/\",\n    \"/api/question/question_attachment_upload/\"\n\n]\n\n# 导出接口，单独处理\nEXPORT_API_LIST = [\n    \"/api/app_case/download/\",\n    \"/api/case_info/download_csv/\",\n    \"/export/records/\",\n    \"/api/interface_info/export_interface_traffic_summary/\",\n    \"/api/interface_info/export_interface_daily_traffic/\",\n    \"/upload/\"\n]\n\n# Internationalization\n# https://docs.djangoproject.com/en/3.0/topics/i18n/\n\nLANGUAGE_CODE = 'zh-hans'\n\nTIME_ZONE = 'Asia/Shanghai'\n\nUSE_I18N = True\n\nUSE_L10N = True\n\nUSE_TZ = False\n\n\n# Static files (CSS, JavaScript, Images)\n# https://docs.djangoproject.com/en/3.0/howto/static-files/\n\nSTATIC_URL = '/static/'\n\n# celery 配置\nCELERY_BROKER_URL = 'pyamqp://admin:admin@'+SERVICE_IP+':5672/hydee_auto_server'  # 使用 RabbitMQ 作为消息代理\nCELERY_RESULT_BACKEND = f'redis://default:{REDIS_PASSWORD}@{SERVICE_IP}:6379/0'  # 把任务结果存在了 Redis\n\n# 是否自动重连，默认是 True\nBROKER_CONNECTION_RETRY = True\n\n# 重连最大次数，默认是100\nBROKER_CONNECTION_MAX_RETRIES = 10\n\nCELERY_ENABLE_UTC = False\nCELERY_TIMEZONE = TIME_ZONE\nCELERY_ACCEPT_CONTENT = ['json']\n#指定序列化的方式\nCELERY_TASK_SERIALIZER = 'json'\nCELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'\nDJANGO_CELERY_BEAT_TZ_AWARE = False\n\n#并发的worker数量,避免任务堆积\nCELERYD_CONCURRENCY = 4\n\n#每个worker执行的最大任务数\nCELERYD_MAX_TASKS_PER_CHILD = 200\n\n#防止死锁\nCELERYD_FORCE_EXECV = True\n\n#celery worker 每次去redis取任务的数量\nCELERYD_PREFETCH_MULTIPLIER = 4\n\n#关闭限速\nCELERY_DISABLE_RATE_LIMITS = True\n\n#celery任务执行结果的超时时间\nCELERY_TASK_RESULT_EXPIRES = 1200\n\n#单个任务的运行时间限制\nCELERYD_TASK_SOFT_TIME_LIMIT = 300\n\n# CELERY_EVENT_QUEUE_TTL = 60\n# CELERY_EVENT_QUEUE_EXPIRES = 60\n\n#解决定时任务重复执行问题\nBROKER_TRANSPORT_OPTIONS = {'visibility_timeout': 43200}\n\n#用于解决启动项目告警\nDEFAULT_AUTO_FIELD = 'django.db.models.AutoField'\n\n# LLM 配置\nLLM_CONFIG = {\n    \"MODEL_NAME\": \"qwen-plus\",\n    \"TEMPERATURE\": 0.85,\n    \"MAX_TOKENS\": 1000,\n    \"API_KEY\": \"sk-d7e52de3ed4b4752936a291dad093da2\",\n    \"BASE_URL\": \"https://dashscope.aliyuncs.com/compatible-mode/v1\",\n    \"EMB_MODEL\": \"embedding-3\",\n    \"EMB_API_KEY\": \"sk-d7e52de3ed4b4752936a291dad093da2\",\n    \"EMB_BASE_URL\": \"https://dashscope.aliyuncs.com/compatible-mode/v1\"\n}\n\n# JENKINS配置\nJENKINS_CONFIG = {\n    \"JENKINS_URL\": \"https://jenkins-pt.hydee.cn\",\n    \"JENKINS_USER\": \"2908\",\n    \"JENKINS_API_TOKEN\": \"Hydeeldap798\",\n    \"JENKINS_VALUE\":\"ahqu7udoteih5iqu4U\",\n    \"REMOTE_DIR\": \"dist/build/mp-weixin\",\n    \"LOCAL_DOWNLOAD_DIR\": os.path.join(BASE_DIR, \"download\")\n}\n\n# 服务器的域名 ，注意发布线上需要修改，暂时为 **************:8000\n# server_domain = 'http://**************:8000'\nserver_domain = 'http://' + SERVICE_IP + ':8000'\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/hydee_auto_server/settings.py b/hydee_auto_server/settings.py
--- a/hydee_auto_server/settings.py	(revision d7c07b3c0de5d4e7bd526b5f312d5662af221fa0)
+++ b/hydee_auto_server/settings.py	(date 1750043840546)
@@ -14,7 +14,7 @@
 from scaffold.settings import *
 
 # 服务器IP地址
-SERVICE_IP = '************'
+SERVICE_IP = '************'
 
 # IP 白名单   ************为生产HIM 调用IP
 ALLOWED_IPS = ['************', '127.0.0.1', '**************', '**************', '************', '************']
@@ -129,6 +129,7 @@
     'tools_help',
     'data_config',
     'django_db_reconnect',
+    'ai_agent',
 ]
 
 
Index: ai_agent/apps.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/ai_agent/apps.py b/ai_agent/apps.py
new file mode 100644
--- /dev/null	(date 1746705428000)
+++ b/ai_agent/apps.py	(date 1746705428000)
@@ -0,0 +1,11 @@
+from django.apps import AppConfig
+from ai_agent.agents.tool_manager import default_tool_manager
+
+class AiAgentConfig(AppConfig):
+    name = 'ai_agent'
+    #
+    # def ready(self):
+    #     # 确保只在主进程运行，避免在Django的reloader中运行两次
+    #     import os
+    #     if os.environ.get('RUN_MAIN') == 'true':
+    #         default_tool_manager.auto_register()
\ No newline at end of file
Index: .idea/misc.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<project version=\"4\">\n  <component name=\"Black\">\n    <option name=\"sdkName\" value=\"Python 3.8 (hydee_auto_server)\" />\n  </component>\n  <component name=\"ProjectRootManager\" version=\"2\" project-jdk-name=\"Python 3.8 (hydee_auto_server)\" project-jdk-type=\"Python SDK\" />\n</project>
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/misc.xml b/.idea/misc.xml
--- a/.idea/misc.xml	(revision d7c07b3c0de5d4e7bd526b5f312d5662af221fa0)
+++ b/.idea/misc.xml	(date 1749797098108)
@@ -3,5 +3,5 @@
   <component name="Black">
     <option name="sdkName" value="Python 3.8 (hydee_auto_server)" />
   </component>
-  <component name="ProjectRootManager" version="2" project-jdk-name="Python 3.8 (hydee_auto_server)" project-jdk-type="Python SDK" />
+  <component name="ProjectRootManager" version="2" project-jdk-name="Python 3.10 (hydee_auto_server)" project-jdk-type="Python SDK" />
 </project>
\ No newline at end of file
Index: .idea/dataSources.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.idea/dataSources.xml b/.idea/dataSources.xml
new file mode 100644
--- /dev/null	(date 1749797098098)
+++ b/.idea/dataSources.xml	(date 1749797098098)
@@ -0,0 +1,20 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<project version="4">
+  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
+    <data-source source="LOCAL" name="chroma" uuid="6ae56cdb-9d70-49dd-bb18-58cfa0a5eb9d">
+      <driver-ref>sqlite.xerial</driver-ref>
+      <synchronize>true</synchronize>
+      <jdbc-driver>org.sqlite.JDBC</jdbc-driver>
+      <jdbc-url>*************************************************************</jdbc-url>
+      <working-dir>$ProjectFileDir$</working-dir>
+      <libraries>
+        <library>
+          <url>file://$APPLICATION_CONFIG_DIR$/jdbc-drivers/Xerial SQLiteJDBC/3.45.1/org/xerial/sqlite-jdbc/3.45.1.0/sqlite-jdbc-3.45.1.0.jar</url>
+        </library>
+        <library>
+          <url>file://$APPLICATION_CONFIG_DIR$/jdbc-drivers/Xerial SQLiteJDBC/3.45.1/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar</url>
+        </library>
+      </libraries>
+    </data-source>
+  </component>
+</project>
\ No newline at end of file
Index: ai_agent/__init__.py
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/ai_agent/__init__.py b/ai_agent/__init__.py
new file mode 100644
--- /dev/null	(date 1746705428000)
+++ b/ai_agent/__init__.py	(date 1746705428000)
@@ -0,0 +1,1 @@
+default_app_config = 'ai_agent.apps.AiAgentConfig'
\ No newline at end of file
diff --git a/ai_agent/agents/__init__.py b/ai_agent/agents/__init__.py
new file mode 100644
diff --git a/ai_agent/services/__init__.py b/ai_agent/services/__init__.py
new file mode 100644
