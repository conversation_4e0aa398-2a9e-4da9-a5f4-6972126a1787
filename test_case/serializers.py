import re

from django.db.models import Max
from rest_framework import serializers
from scaffold.restframework.utils import auto_declare_serializers

from . import models as m


class ImportCaseReportSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.ImportCaseReport
        fields = '__all__'


# # Auto declare serializer classes from models.
auto_declare_serializers(m, locals())
