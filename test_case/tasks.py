from test_case.servers.get_tapd_info import *
from tools_help.views import *
from hydee_auto_server.celery import app

@app.task(bind=True)
def get_tapd_info_task_job(self):
    save_bugs_to_db(100, 2)
    save_iterations_to_db()
    iterations_list = get_iterations()
    for iteration in iterations_list:
        save_requirement_to_db(iteration['Iteration']['id'])


if __name__ == '__main__':
    get_tapd_info_task_job()
