from datetime import datetime
import time
from django.utils.dateparse import parse_date, parse_datetime
import requests
from typing import Dict, List, Any
import os
import django
import json

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hydee_auto_server.settings')
django.setup()

from test_case.models import TapdIterationsInfo, TapdRequirementInfo, TapdHeadersConfig, TapdBugInfo


def get_iterations() -> List[Dict[str, Any]]:
    # 获取接口请求参数
    iteration_config = TapdHeadersConfig.objects.filter(type='iteration').first()
    # 第一次请求不带last_id参数
    body = iteration_config.get_body_dict()
    headers = iteration_config.get_headers_dict()
    url = iteration_config.url
    # 初始化合并后的迭代列表
    all_iterations = []
    """获取迭代列表数据"""
    try:
        while True:
            response = requests.post(url, headers=headers, json=body)
            response.raise_for_status()

            data = response.json()
            if not data.get('data', {}).get('result_tree'):
                break  # 如果没有数据就退出循环

            current_iterations = data['data']['result_tree']
            all_iterations.extend(current_iterations)

            # 获取最后一条迭代的ID作为下一页的查询条件
            last_id = current_iterations[-1].get('Iteration', {}).get('id')

            # 更新请求body，加入last_id参数
            body['last_id'] = last_id
            logger.info('last_id: %s', last_id)
        logger.info('共查询到%s条迭代数据', len(all_iterations))
        return all_iterations

    except requests.exceptions.RequestException as e:
        logger.error(f"请求tapd获取迭代接口失败: {e}")
        return []
    except ValueError as e:
        logger.error(f"参数问题: {e}")
        return []
    except Exception as e:
        logger.error(f"未知错误: {e}")
        return []


def get_requirements(iteration_id=None) -> List[Dict[str, Any]]:
    # 获取迭代名称
    try:
        # 查询数据库并获取第一个匹配的对象
        iteration_obj = TapdIterationsInfo.objects.filter(tapd_iteration_id=iteration_id).first()

        # 检查对象是否为空
        if iteration_obj is None:
            raise ValueError(f"未查询待迭代数据,迭代ID: {iteration_id}")

        # 获取 iteration_name 属性
        iteration_name = iteration_obj.iteration_name
    except Exception as e:
        # 捕获异常并记录日志或处理错误
        print(f"获取迭代名称时发生错误: {e}")

    # 获取接口请求参数
    iteration_config = TapdHeadersConfig.objects.filter(type='requirement').first()
    body = iteration_config.get_body_dict(iteration_id=iteration_id)
    headers = iteration_config.get_headers_dict(iteration_id=iteration_id)
    url = iteration_config.url

    """获取需求列表数据"""
    try:
        response = requests.post(url, headers=headers, json=body)

        response.raise_for_status()

        data = response.json()
        if not data.get('data', {}).get('iterations_workitem_list'):
            raise ValueError(f"无需求信息")

        requirements_list = data['data']['iterations_workitem_list']
        return requirements_list

    except requests.exceptions.RequestException as e:
        logger.error(f"迭代{iteration_name}请求tapd获取需求接口失败: {e}")
        return []
    except ValueError as e:
        logger.error(f"{iteration_name}查询结果为空: {e}")
        return []
    except Exception as e:
        logger.error(f"{iteration_name}未知错误: {e}")
        return []


def get_bugs(page_size=100, max_pages=None, timeout=30) -> List[Dict[str, Any]]:
    current_page = 1
    total_pages = 1  # 初始值，会在第一次请求后更新
    # 获取接口请求参数
    bug_config = TapdHeadersConfig.objects.filter(type='bug').first()
    headers = bug_config.get_headers_dict()
    url = bug_config.url

    # 初始化合并后的bug列表
    all_bugs_list = []

    """获取bug列表数据"""
    try:
        # 第一次请求获取总数据量
        logger.info('开始获取bug列表数据')
        body = bug_config.get_body_dict(page=current_page)
        response = requests.post(url, headers=headers, json=body)
        response.raise_for_status()

        data = response.json()
        total_count = data['data']['total_count']
        if not total_count:
            logger.info('无bug数据，总数为0')
            return []

        # 计算总页数
        total_pages = (int(total_count) + page_size - 1) // page_size
        logger.info(f'共有{total_count}条bug数据，分{total_pages}页获取，每页{page_size}条')

        # 处理第一页数据
        if data.get('data', {}).get('bugs_list'):
            origin_bugs_list = data['data']['bugs_list']
            bugs_list = _extract_bug_fields(origin_bugs_list)
            all_bugs_list.extend(bugs_list)
            logger.info(f'已获取第{current_page}/{total_pages}页，当前共{len(all_bugs_list)}条数据')

        # 如果设置了最大页数限制，调整总页数
        if max_pages and max_pages < total_pages:
            total_pages = max_pages
            logger.info(f'由于设置了最大页数限制，将只获取{max_pages}页数据')

        # 循环获取剩余页数据
        while current_page < total_pages:
            current_page += 1

            # 更新请求参数，添加分页信息
            body['page'] = current_page

            logger.info(f'正在获取第{current_page}/{total_pages}页bug数据')
            response = requests.post(url, headers=headers, json=body, timeout=timeout)
            response.raise_for_status()

            data = response.json()
            if not data.get('data', {}).get('bugs_list'):
                logger.warning(f'第{current_page}页无bug数据，提前结束获取')
                break

            origin_bugs_list = data['data']['bugs_list']
            bugs_list = _extract_bug_fields(origin_bugs_list)
            all_bugs_list.extend(bugs_list)
            logger.info(f'已获取第{current_page}/{total_pages}页，当前共{len(all_bugs_list)}条数据')

            # 可以添加短暂休眠，避免请求过于频繁
            time.sleep(0.5)

        logger.info(f'bug列表数据获取完成，共获取{len(all_bugs_list)}条数据')
        return all_bugs_list

    except requests.exceptions.Timeout:
        logger.error(f"请求tapd获取bug列表接口超时，已获取{len(all_bugs_list)}条数据")
        return all_bugs_list
    except requests.exceptions.RequestException as e:
        logger.error(f"请求tapd获取bug列表接口失败: {e}")
        return all_bugs_list
    except ValueError as e:
        logger.error(f"获取bug列表查询结果为空: {e}")
        return all_bugs_list
    except Exception as e:
        logger.error(f"获取bug列表获取未知错误: {e}")
        return all_bugs_list


def _extract_bug_fields(origin_bugs_list):
    """提取bug列表中的关键字段，缺失的字段返回空字符串"""
    return [
        {
            'tapd_bug_id': bug['Bug'].get('id', ''),
            'short_id': bug['Bug'].get('short_id', ''),
            'title': bug['Bug'].get('title', ''),
            'iteration_id': bug['Bug'].get('iteration_id', ''),
            'iteration_name': bug['Bug'].get('version_report', ''),
            'severity': bug['Bug'].get('severity', ''),
            'priority': bug['Bug'].get('priority', ''),
            'environment': bug['Bug'].get('custom_field_one', ''),
            'status': bug['Bug'].get('status', ''),
            'developer': bug['Bug'].get('de', ''),
            'current_owner': bug['Bug'].get('current_owner', ''),
            'creater': bug['Bug'].get('reporter', ''),
            'bug_create_time': bug['Bug'].get('created', ''),
            'project_id': bug['Bug'].get('project_id', ''),
            'requirement_short_id': bug['Bug'].get('BugStoryRelation_relative_id', '')
        }
        for bug in origin_bugs_list
    ]


def save_iterations_to_db():
    """将迭代数据保存到数据库"""
    iterations = get_iterations()
    saved_count = 0
    updated_count = 0

    for iteration in iterations:
        iteration_info = iteration.get('Iteration', {})
        if not iteration_info:
            continue

        try:
            # 解析日期
            start_date = parse_date(iteration_info['startdate']) if iteration_info.get(
                'startdate') else None
            end_date = datetime.strptime(iteration_info['enddate'], '%Y-%m-%d').date() if iteration_info.get(
                'enddate') else None
            nowtime = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

            # 准备更新数据
            update_data = {
                'iteration_name': iteration_info['name'],
                'start_date': start_date,
                'end_date': end_date,
                'status': iteration_info.get('status', 'open'),
                'workspace_id': iteration_info['workspace_id']
            }

            # 检查记录是否存在
            tapd_iteration_id = iteration_info['id']
            existing_iteration = TapdIterationsInfo.objects.filter(tapd_iteration_id=tapd_iteration_id).first()

            if existing_iteration:
                # 检查哪些字段需要更新
                needs_update = False
                fields_to_update = {}
                # 判断新旧数据是否一致,若不一致,则到needs_update设置为true
                for field, new_value in update_data.items():
                    old_value = getattr(existing_iteration, field)
                    if old_value != new_value:
                        needs_update = True
                        fields_to_update[field] = new_value

                if needs_update:
                    # 只更新有变化的字段
                    for field, value in fields_to_update.items():
                        setattr(existing_iteration, field, value)
                    # 更新'更新时间'
                    existing_iteration.update_time = nowtime
                    existing_iteration.save()
                    updated_count += 1
            else:
                # 不存在则创建插入新的迭代信息
                TapdIterationsInfo.objects.create(
                    tapd_iteration_id=tapd_iteration_id,
                    iteration_name=iteration_info['name'],
                    start_date=start_date,
                    end_date=end_date,
                    status=iteration_info.get('status', 'open'),
                    workspace_id=iteration_info['workspace_id'],
                    create_time=nowtime,
                    update_time=nowtime
                )
                saved_count += 1
        except Exception as e:
            logger.error(f"写入迭代信息失败 {iteration_info.get('name')}: {e}")

    logger.info('共新增%s条迭代数据', saved_count)
    logger.info('共更新%s条迭代数据', updated_count)

    return saved_count, updated_count


def save_requirement_to_db(iteration_id):
    """将需求数据保存到数据库"""
    requirements = get_requirements(iteration_id)
    saved_count = 0
    updated_count = 0
    # 获取迭代名称
    try:
        # 查询数据库并获取第一个匹配的对象
        iteration_obj = TapdIterationsInfo.objects.filter(tapd_iteration_id=iteration_id).first()

        # 检查对象是否为空
        if iteration_obj is None:
            raise ValueError(f"未查询待迭代数据,迭代ID: {iteration_id}")

        # 获取 iteration_name 属性
        iteration_name = iteration_obj.iteration_name

    except Exception as e:
        # 捕获异常并记录日志或处理错误
        print(f"获取迭代名称时发生错误: {e}")
        iteration_name = None  # 或者根据需求设置一个默认值
    for requirement in requirements:
        if not requirement:
            continue
        try:
            # 获取提测时间yyyy-mm-dd
            submit_test_time = datetime.strptime(requirement['custom_field_10'],
                                                 '%Y-%m-%d %H:%M').date() if requirement.get(
                'custom_field_10') else None
            # 获取测试完成时间yyyy-mm-dd
            completed_test_time = parse_date(requirement['custom_field_测试计划完成时间']) if requirement.get(
                'custom_field_测试计划完成时间') else None
            # 获取当前时间
            nowtime = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

            # 准备更新数据
            update_data = {
                'tapd_id': requirement['id'],
                'tapd_short_id': requirement.get('short_id'),
                'parent_tapd_id': requirement['parent_id'] if requirement['parent_id'] != '0' else None,
                'requirement_name': requirement.get('name'),
                'category': requirement.get('category_name'),
                'developer': requirement.get('developer', ''),
                'tester': requirement.get('te', ''),
                'producter': requirement.get('owner', ''),
                'submit_test_time': submit_test_time if submit_test_time else None,
                'completed_test_time': completed_test_time if completed_test_time else None,
                'status': requirement.get('status', ''),
                'priority': requirement.get('priority', '').lower(),
                'iteration_id': requirement.get('iteration_id')
            }

            # 检查记录是否存在
            requirement_id = requirement['id']
            existing_requirement = TapdRequirementInfo.objects.filter(tapd_id=requirement_id).first()

            if existing_requirement:
                # 检查哪些字段需要更新
                needs_update = False
                fields_to_update = {}
                # 判断新旧数据是否一致,若不一致,则到needs_update设置为true,并将更新字段添加到fields_to_update中
                for field, new_value in update_data.items():
                    old_value = getattr(existing_requirement, field)
                    if old_value != new_value:
                        needs_update = True
                        fields_to_update[field] = new_value
                if needs_update:
                    # 只更新有变化的字段
                    for field, value in fields_to_update.items():
                        setattr(existing_requirement, field, value)
                    # 更新'更新时间'
                    existing_requirement.update_time = nowtime
                    existing_requirement.save()
                    updated_count += 1

            else:
                # 不存在则创建插入新的迭代信息
                TapdRequirementInfo.objects.create(
                    iteration_id=requirement['iteration_id'],
                    tapd_id=requirement['id'],
                    tapd_short_id=requirement['short_id'],
                    parent_tapd_id=requirement['parent_id'] if requirement['parent_id'] != '0' else None,
                    requirement_name=requirement['name'],
                    developer=requirement['developer'],
                    tester=requirement['te'],
                    producter=requirement['owner'],
                    submit_test_time=submit_test_time if submit_test_time else None,
                    completed_test_time=completed_test_time if completed_test_time else None,
                    status=requirement['status'],
                    priority=requirement['priority'],
                    create_time=nowtime,
                    update_time=nowtime
                )
                saved_count += 1
        except Exception as e:
            logger.error(f"失败写入需求【{requirement.get('short_id')}】: {requirement.get('name')}: {e}")

    logger.info('%s共新增%s条需求数据', iteration_name, saved_count)
    logger.info('%s共更新%s条需求数据', iteration_name, updated_count)

    return saved_count, updated_count


def save_bugs_to_db(page_size, max_pages):
    """将bug数据保存到数据库，使用批量操作提高效率"""
    bugs = get_bugs(page_size, max_pages)
    saved_count = 0
    updated_count = 0

    if not bugs:
        logger.info('无bug数据需要保存')
        return saved_count, updated_count

    # 获取当前时间
    nowtime = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

    # 获取所有bug的ID列表
    bug_ids = [bug['tapd_bug_id'] for bug in bugs if bug]

    # 批量查询已存在的bug记录
    existing_bugs = {str(bug.tapd_bug_id): bug for bug in TapdBugInfo.objects.filter(tapd_bug_id__in=bug_ids)}

    # 准备批量创建和更新的列表
    bugs_to_create = []
    bugs_to_update = []

    # 处理每个bug
    for bug in bugs:
        if not bug:
            continue

        try:
            # 解析创建时间
            bug_create_time = datetime.strptime(bug['bug_create_time'], '%Y-%m-%d %H:%M:%S') if bug.get('bug_create_time') else None

            # 检查记录是否存在
            bug_id = bug['tapd_bug_id']

            if bug_id in existing_bugs:
                # 已存在的bug记录
                existing_bug = existing_bugs[bug_id]

                # 准备更新数据
                update_data = {
                    'title': bug['title'],
                    'iteration_id': bug.get('iteration_id', ''),
                    'iteration_name': bug.get('iteration_name', ''),
                    'severity': bug.get('severity', 'normal'),
                    'priority': bug.get('priority', ''),
                    'environment': bug.get('environment', ''),
                    'status': bug.get('status', ''),
                    'developer': bug.get('developer', ''),
                    'current_owner': bug.get('current_owner', ''),
                    'project_id': bug.get('project_id', ''),
                    'requirement_id': bug.get('requirement_short_id', '')
                }

                # 检查哪些字段需要更新
                needs_update = False
                for field, new_value in update_data.items():
                    old_value = getattr(existing_bug, field)
                    if old_value != new_value:
                        needs_update = True
                        setattr(existing_bug, field, new_value)

                if needs_update:
                    bugs_to_update.append(existing_bug)
                    updated_count += 1
            else:
                # 不存在则创建新的Bug记录
                new_bug = TapdBugInfo(
                    tapd_bug_id=bug_id,
                    bug_short_id=bug.get('short_id', ''),
                    title=bug['title'],
                    iteration_name=bug.get('iteration_name', ''),
                    iteration_id=bug.get('iteration_id', ''),
                    severity=bug.get('severity', 'normal'),
                    priority=bug.get('priority', ''),
                    environment=bug.get('environment', ''),
                    status=bug.get('status', ''),
                    developer=bug.get('developer', ''),
                    current_owner=bug.get('current_owner', ''),
                    creater=bug.get('creater', ''),
                    bug_create_time=bug_create_time,
                    project_id=bug.get('project_id', ''),
                    requirement_id=bug.get('requirement_short_id', ''),
                    create_time=nowtime,
                    update_time=nowtime
                )
                bugs_to_create.append(new_bug)
                saved_count += 1
        except Exception as e:
            logger.error(f"处理Bug数据失败 {bug.get('short_id')} - {bug.get('title')}: {e}")

    # 批量创建新记录
    if bugs_to_create:
        try:
            # 使用批量创建
            TapdBugInfo.objects.bulk_create(bugs_to_create)
            logger.info(f'批量创建成功，共{len(bugs_to_create)}条记录')
        except Exception as e:
            logger.error(f"批量创建Bug记录失败: {e}")
            saved_count = 0  # 重置计数，因为批量操作失败

    # 批量更新已有记录
    if bugs_to_update:
        for bug in bugs_to_update:
            bug.update_time = nowtime
        try:
            # 使用批量更新
            TapdBugInfo.objects.bulk_update(
                bugs_to_update,
                fields=[
                    'title', 'iteration_id', 'iteration_name', 'severity', 'priority',
                    'environment', 'status', 'developer', 'current_owner',
                    'creater', 'bug_create_time', 'project_id', 'requirement_id', 'update_time'
                ],
            )
            logger.info(f'批量更新成功，共{len(bugs_to_update)}条记录')
        except Exception as e:
            logger.error(f"批量更新Bug记录失败: {e}")
            updated_count = 0  # 重置计数，因为批量操作失败

    logger.info('共新增%s条Bug数据', saved_count)
    logger.info('共更新%s条Bug数据', updated_count)

    return saved_count, updated_count


if __name__ == '__main__':
    save_requirement_to_db("1161969829001002081")
