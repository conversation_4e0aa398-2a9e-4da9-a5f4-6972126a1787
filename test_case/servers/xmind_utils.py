# case_processor.py
import re
import time
from test_case.models import TapdIterationsInfo, TapdRequirementInfo, TapdHeadersConfig
import xmind
import zipfile
import tempfile
import os
import json
from django.http import HttpResponse

from test_case.models import TestCase, ImportCaseReport
# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()

def opera_dict_to_list(dict_data, _list=None):
    """将 XMind 字典数据解析为嵌套列表的生成器"""
    if _list is None:
        _list = []
    title_list = []
    topic_list = []
    try:
        topics = dict_data.get("topics")
        title = dict_data.get("title")
        title_list.append(title)
        title_list = _list + title_list

        if topics is None:
            yield title_list
        elif isinstance(topics, list):
            for topic in topics:
                topic_list.append(topic)
    except AttributeError:
        return

    for topic in topic_list:
        yield from opera_dict_to_list(topic, title_list)


def case_data_into_db(cases_list, business_line, title, import_report):
    try:
        for case in cases_list:
            # 初始化当前用例的字段
            system = ""
            requirement_id = ""
            requirement_name = ""
            module = ""
            scenario = ""
            precondition = ""
            steps = ""
            expected = ""
            developer = ""
            keyword = ""
            has_steps = False  # 标记是否有步骤
            has_expected = False  # 标记是否有预期
            step_start = step_end = 0

            if case[0] in ('文档合集', '文档集合') and '需求' in case[1]:
                ImportCaseReport.objects.filter(id=import_report.id).update(requirement_link=case[2])

            if case[0] in ('用例合集', '用例集合'):

                # 定位步骤和预期的索引位置
                for i, item in enumerate(case):
                    if isinstance(item, str):
                        if item.startswith("步骤"):
                            step_start = i
                            # 检查步骤内容是否为空（去除标题后的内容是否有效）
                            step_content = re.split(r'[：:]', item, 1)[-1].strip() if re.search(r'[：:]', item) else item[2:].strip()  # 去掉"步骤"后的内容
                            if step_content and step_content != "：":  # 确保不是空或只有冒号
                                has_steps = True
                        elif re.search(r'^(预期|预期结果)[：:]*', item):
                            step_end = i
                            # 检查预期内容是否为空
                            expected_content = re.split(r'[：:]', item, 1)[-1].strip() if re.search(r'[：:]', item) else item[2:].strip()
                            if expected_content:  # 确保预期有实际内容
                                has_expected = True
                            break  # 找到第一个预期即停止

                # 判断步骤内容类型：多条目列表 or 单字符串
                if step_end - step_start > 1:
                    has_steps = True
                    # 只有同时包含步骤和预期时，才处理该用例
                    if not (has_steps and has_expected):
                        continue  # 如果缺少步骤或预期，舍弃此用例
                    # 多层级步骤条目,拼接步骤,范围:步骤后~预期前
                    step_items = case[step_start + 1:step_end]
                    steps = '\n'.join(
                        f"{i + 1}、{str(item).strip()}"
                        for i, item in enumerate(step_items)
                        if str(item).strip()
                    )
                else:
                    # 新逻辑：单字符串复杂步骤
                    # 如果具体步骤写在一个步骤中，则需要处理为多条目,如:步骤：进入企微群发；新建群发消息给客户；群发内容勾选公众号二维码；查看任务发送详情
                    if step_start >= 0:  # 确保找到了步骤
                        step_item = case[step_start]
                        # 使用正则表达式提取步骤内容，处理各种格式
                        step_content = re.split(r'步骤[：:]*', step_item, 1)[-1].strip()
                        if not step_content:  # 如果分割后为空，尝试其他方式
                            step_content = re.sub(r'^步骤[：:]*', '', step_item).strip()
                    # 统一分隔符处理
                    step_content = re.sub(r'[；;]', '\n', step_content)  # 处理分号分隔符(中英文均需处理)
                    steps_split = re.split(r'\n|(?<=\D)(?=\d+[、.:：])', step_content)  # 将包含多个步骤的文本拆分成独立的步骤列表

                    # 清理步骤
                    clean_steps = []
                    for raw_step in steps_split:
                        step = raw_step.strip()
                        step = re.sub(r'^\d+[、.:：]?\s*', '', step)  # 移除开头序号
                        if step:
                            clean_steps.append(step)

                    # 重组序号
                    formatted_steps = []
                    for idx, step in enumerate(clean_steps, 1):
                        # 替换内部错误序号
                        step = re.sub(r'(?<!\n)\d+[、.:：]', f'{idx}、', step, count=1)
                        formatted_steps.append(f"{idx}、{step.lstrip('、.:： ')}")

                    steps = '\n'.join(formatted_steps)

                # 提取其他字段
                for item in case:
                    if isinstance(item, str):
                        if item.startswith(("需求ID", "需求id")):
                            match = re.search(r'需求[Ii][Dd][：:]\s*(\S+)', item)
                            if match:
                                requirement_id = match.group(1).strip()
                        # elif item.startswith("开发人员"):
                        #     developer = item.split("：", 1)[-1].strip()
                        elif item.startswith("系统"):
                            system = re.split(r'[：:]', item, 1)[-1].strip()
                        elif re.search(r'^模块', item):
                            module = re.split(r'[：:]', item, 1)[-1].strip()
                        
                        elif re.search(r'^(场景|回归场景)', item):
                            scenario = re.split(r'[：:]', item, 1)[-1].strip()
                        
                        elif re.search(r'^(前提|前置)', item):
                            precondition = re.split(r'[：:]', item, 1)[-1].strip()
                        
                        elif re.search(r'^(预期|预期结果)', item):
                            expected = re.split(r'[：:]', item, 1)[-1].strip()
                        
                        elif re.search(r'^(关键字|关键词)', item):
                            keyword = re.split(r'[：:]', item, 1)[-1].strip()

                requirement_info = TapdRequirementInfo.objects.filter(tapd_short_id=requirement_id).first()
                if requirement_info:
                    requirement_name = requirement_info.requirement_name
                    developer = requirement_info.developer

                # 用例类型映射
                case_type_mapping = {
                    "冒烟": 1,
                    "核心": 2,
                    "普通": 3
                }
                # 获取用例类型枚举值，默认为普通（3）
                case_type_value = 3  # 默认为普通
                if case and len(case) > 0:
                    last_item = case[-1]
                    if isinstance(last_item, str):
                        case_type_value = case_type_mapping.get(last_item.strip(), 3)
                create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                try:
                    # 将用例插入数据库
                    TestCase.objects.create(
                        case_scene=scenario,
                        business_line=business_line,
                        system=system,
                        module=module,
                        requirement_id=requirement_id,
                        requirement=requirement_name,
                        developer=developer,
                        premise=precondition,
                        test_steps=steps,
                        expected_result=expected,
                        iteration_name=title,
                        case_type=case_type_value,
                        operator_result=0,
                        create_time=create_time,
                        update_time=create_time,
                        case_report_id=import_report.id,
                        keyword=keyword,
                        is_active=True
                    )
                except Exception as e:
                    logger.error(f'插入数据库失败{e}')
                    continue
    except Exception as e:
        # 记录整体处理错误
        logger.error(f'处理用例数据失败{e}')
        # 可以选择重新抛出异常或返回错误状态
        raise
        


def export_to_xmind(test_case, requirement_info, iteration_info):
    iteration_name = iteration_info.iteration_name
    requirement_id = requirement_info.tapd_short_id
    developer = requirement_info.developer if requirement_info else None
    # 创建XMind文件
    # 使用tempfile创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建输出文件路径
        output_xmind_path = os.path.join(temp_dir, "召回用例导出.xmind")
        # 创建一个新的工作簿
        # 使用xmind.load创建一个新的工作簿，需要提供一个不存在的文件路径
        # 然后捕获异常并创建一个新的工作簿
        try:
            # 尝试创建一个新的工作簿
            workbook = xmind.load(output_xmind_path)
        except Exception as e:
            # 如果失败，我们使用另一种方式创建工作簿
            workbook = xmind.load("")  # 这会创建一个新的工作簿

        # 获取第一个sheet
        sheet = workbook.getPrimarySheet()
        sheet.setTitle("测试用例")  # 设置画布标题

        # 获取根主题
        root_topic = sheet.getRootTopic()
        root_topic.setTitle(iteration_name)  # 设置根主题标题
        # 右侧逻辑图,保证所有主题都显示在右侧
        root_topic.setStructureClass("org.xmind.ui.logic.right")

        # 创建文档合集主题
        doc_collection_topic = root_topic.addSubTopic()
        doc_collection_topic.setTitle("文档合集")

        # 添加文档合集的子主题
        req_doc_topic = doc_collection_topic.addSubTopic()
        req_doc_topic.setTitle("需求文档")

        ui_doc_topic = doc_collection_topic.addSubTopic()
        ui_doc_topic.setTitle("UI稿")

        tech_doc_topic = doc_collection_topic.addSubTopic()
        tech_doc_topic.setTitle("技术文档")

        tapd_topic = doc_collection_topic.addSubTopic()
        tapd_topic.setTitle("tapd")

        # 创建用例合集主题
        case_collection_topic = root_topic.addSubTopic()
        case_collection_topic.setTitle("用例合集")

        # 添加用例合集的子主题
        requirement_topic = case_collection_topic.addSubTopic()
        if test_case and len(test_case) > 0:
            # 将requirement_id转换为字符串，以防它是整数
            requirement_topic.setTitle("需求ID: " + requirement_id)
        else:
            requirement_topic.setTitle("需求ID: ")

        # 添加开发人员子主题
        developer_topic = requirement_topic.addSubTopic()
        if developer:
            developer_topic.setTitle(f"开发人员: {developer}")
        else:
            developer_topic.setTitle("开发人员: ")
        # 将开发人员主题作为后续用例场景的父主题
        parent_topic = developer_topic

        # 用于存储已创建的系统主题和模块主题
        system_topics = {}
        module_topics = {}

        for item in test_case:
            # 处理系统层级
            system_key = str(item.system) if item.system else ""
            if system_key not in system_topics:
                # 创建新的系统主题
                system_topic = parent_topic.addSubTopic()
                system_topic.setTitle(f"系统: {system_key}" if system_key else "系统:")
                system_topics[system_key] = system_topic
            current_topic = system_topics[system_key]

            # 处理模块层级
            module_key = f"{system_key}-{str(item.module)}" if item.module else f"{system_key}-"
            if module_key not in module_topics:
                # 创建新的模块主题
                module_topic = current_topic.addSubTopic()
                module_topic.setTitle(f"模块: {str(item.module)}" if item.module else "模块:")
                module_topics[module_key] = module_topic
            current_topic = module_topics[module_key]

            # 场景 - 无论是否为空都创建节点
            case_topic = current_topic.addSubTopic()
            if item.case_scene:
                case_topic.setTitle(f"场景: {str(item.case_scene)}")
            else:
                case_topic.setTitle("场景:")
            current_topic = case_topic

            # 前置条件 - 无论是否为空都创建节点
            premise_topic = current_topic.addSubTopic()
            if item.premise:
                premise_text = str(item.premise) if item.premise is not None else ""
                premise_topic.setTitle(f"前置条件：{premise_text}")
            else:
                premise_topic.setTitle("前置条件：")
            current_topic = premise_topic  # 更新当前主题

            # 测试步骤 - 无论是否为空都创建节点
            # 处理测试步骤的格式
            test_steps = item.test_steps
            try:
                if isinstance(test_steps, str) and (test_steps.startswith('[') or test_steps.startswith('{')):
                    test_steps_obj = json.loads(test_steps)
                    if isinstance(test_steps_obj, list):
                        test_steps = '\n'.join(test_steps_obj)
                    elif isinstance(test_steps_obj, dict):
                        test_steps = '\n'.join([f"{k}: {v}" for k, v in test_steps_obj.items()])
            except (json.JSONDecodeError, TypeError):
                pass  # 保持原样

            # 格式化测试步骤，确保每个步骤都有序号标注
            formatted_steps = ""
            if test_steps:
                steps_text = str(test_steps) if test_steps is not None else ""
                # 按换行符分割步骤
                steps_list = steps_text.split('\n')
                formatted_steps_list = []

                for i, step in enumerate(steps_list):
                    step = step.strip()
                    if not step:  # 跳过空行
                        continue

                    # 检查步骤是否已经有序号（格式如"1、"，"1."，"1："等）
                    if not re.match(r'^\d+[、.:：]\s*', step):
                        # 如果没有序号，添加序号
                        formatted_step = f"{i + 1}、{step}"
                    else:
                        # 如果已有序号，保持原样
                        formatted_step = step

                    formatted_steps_list.append(formatted_step)

                # 将格式化后的步骤合并为字符串，每个步骤独占一行
                formatted_steps = '\n'.join(formatted_steps_list)

            steps_topic = current_topic.addSubTopic()
            # 将测试步骤内容直接整合到标题中，保留换行符
            if formatted_steps:
                steps_topic.setTitle(f"测试步骤：\n{formatted_steps}")
            else:
                steps_topic.setTitle("测试步骤：")
            current_topic = steps_topic  # 更新当前主题

            # 预期结果 - 无论是否为空都创建节点
            result_topic = current_topic.addSubTopic()
            # 将预期结果内容直接整合到标题中
            if item.expected_result:
                result_text = str(item.expected_result) if item.expected_result is not None else ""
                result_topic.setTitle(f"预期结果：{result_text}")
            else:
                result_topic.setTitle("预期结果：")
            current_topic = result_topic  # 更新当前主题

            # 打标
            ai_topic = current_topic.addSubTopic()
            ai_topic.setTitle("AI生成")

        # 保存XMind文件
        xmind.save(workbook, output_xmind_path)

        # 添加META-INF文件夹和manifest.xml文件，使XMind 8可以打开
        # 创建manifest.xml内容
        manifest_content = """<?xml version="1.0" encoding="UTF-8" standalone="no"?>
                                        <manifest xmlns="urn:xmind:xmap:xmlns:manifest:1.0">
                                        <file-entry full-path="content.xml" media-type="text/xml"/>
                                        <file-entry full-path="META-INF/" media-type=""/>
                                        <file-entry full-path="META-INF/manifest.xml" media-type="text/xml"/>
                                        <file-entry full-path="styles.xml" media-type="text/xml"/>
                                        <file-entry full-path="Thumbnails/" media-type=""/>
                                        <file-entry full-path="Thumbnails/thumbnail.png" media-type="image/png"/>
                                        </manifest>"""

        # 使用zipfile打开xmind文件（本质上是zip文件）
        with zipfile.ZipFile(output_xmind_path, 'a') as zipf:
            # 添加META-INF文件夹（在zip中，文件夹以/结尾）
            try:
                zipf.getinfo('META-INF/')
            except KeyError:
                # 如果文件夹不存在，创建一个空的文件夹条目
                zipf.writestr('META-INF/', '')

            # 添加manifest.xml文件
            zipf.writestr('META-INF/manifest.xml', manifest_content)

        # 读取修改后的文件内容并返回响应
        with open(output_xmind_path, 'rb') as f:
            file_content = f.read()

            # 设置正确的MIME类型
            # 对于XMind文件，使用application/zip可能更通用，因为XMind本质上是ZIP文件
            response = HttpResponse(file_content, content_type='application/zip')

            # 设置文件名
            filename = "testcase_export.xmind"  # 使用英文文件名，避免编码问题

            # 设置Content-Disposition头，确保文件名正确
            # 使用双引号包围文件名，并添加UTF-8编码的文件名
            response['Content-Disposition'] = f'attachment; filename="{filename}"'

            # 设置Content-Length头，帮助客户端了解文件大小
            response['Content-Length'] = len(file_content)

            # 添加额外的头信息，帮助客户端识别文件类型
            response['X-Content-Type-Options'] = 'nosniff'
    return response
