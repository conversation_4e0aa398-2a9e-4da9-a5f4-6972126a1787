"""hydee_auto_server URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from data_config.views import export_fail_result
from ai_agent.views import chat_api, chat_view
from test_case.views import TestCaseInfoViewSet
from ai_agent.tools.jenkins_downloader import download_specific_file
import core.urls

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/', include(core.urls)),
    path('api/export/records/', export_fail_result, name='export_records_to_excel'),
    path('api/ai-agent/chat/', chat_api, name='ai_agent_chat'),  # API端点
    path('agent/', chat_view, name='agent_chat'),
    path('export/xmind/requirement/', TestCaseInfoViewSet.export_to_xmind_by_requirement, name='export_to_xmind_by_requirement'),
    path('export/xmind/record/', TestCaseInfoViewSet.export_to_xmind_by_record, name='export_to_xmind_by_record'),
    path('api/download-specific/', download_specific_file, name='download_specific_file'),
]



# urlpatterns = [
#     path('admin/', admin.site.urls),
#     #登陆接口
#     path('login', views.login),
#     #登录之后获取左侧菜单栏接口
#     path('menu',views.getMenu),
#     # 首页数据
#     path('index',views.getIndex),
 # 统一接口：通过参数type获取指定数据
#     path('info', views.getInfo),
#     #用户列表及查询接口
#     path('users',views.userList),
#     #新增用户接口
#     path('user-add',views.userAdd),
#     #用户变更或删除，传用户ID
#     path('user/<int:userId>', views.userUpdate),


#     #用户分配角色，中间传用户ID
#     path('user/<int:userId>/role', views.userRole),
#     # 新增角色接口
#     path('role-add', views.roleAdd),
#     # 角色列表及查询接口
#     path('roles', views.roleList),
#     # 角色编辑或删除，传角色ID
#     path('role/<int:roleId>', views.roleUpdate),
#     # 角色授权，中间传角色ID
#     path('role/<int:roleId>/rights', views.roleRights),
#     # 权限列表
#     path('rights', views.rightList),
#     #数据源新增
#     path('database-add', views.dbAdd),
#     # 数据源测试连接
#     path('database-connect', views.dbConnect),
#     # 数据源编辑和删除
#     path('database/<int:databaseId>', views.dbUpdate),
#     # 数据源列表及查询
#     path('databases', views.dbList),
#     # 参数列表及查询
#     path('parameters', views.parameterList),
#     # 参数新增
#     path('parameter-add', views.parameterAdd),
#     # 参数编辑和删除
#     path('parameter/<int:parameterId>', views.parameterUpdate),
#     # 任务列表及查询
#     path('tasks', views.taskList),
#     # 任务列表及查询
#     path('task-add', views.taskAdd),
#     # 任务运行
#     path('task-run', views.taskRun),
#     # 任务编辑和删除
#     path('task/<int:taskId>', views.taskUpdate),
#     # 任务编辑和删除
#     path('task/report', views.taskReport),
#     # 邮件模板列表及查询
#     path('emails', views.emailList),
#     # 邮件模板新增
#     path('email-add', views.emailAdd),
#     # 邮件模板编辑和删除
#     path('email/<int:emailId>', views.emailUpdate),
#     # 项目列表及查询
#     path('projects', views.projectList),
#     # 项目新增
#     path('project-add', views.projectAdd),
#     # 项目编辑和删除
#     path('project/<int:projectId>', views.projectUpdate),
#     # 接口列表及查询
#     path('interfaces', views.interfaceList),
#     # 接口新增
#     path('interface-add', views.interfaceAdd),
#     # 接口编辑和删除
#     path('interface/<int:interfaceId>', views.interfaceUpdate),
#     # 案例列表及查询
#     path('cases', views.caseList),
#     # 案例新增
#     path('case-add', views.caseAdd),
#     # 案例编辑和删除
#     path('case/<int:caseId>', views.caseUpdate),
#     # 案例运行
#     path('case-run/<int:caseId>', views.caseRun),
#     # 三层架构的案例列表信息/两层的项目-接口信息
#     path('api/<str:apiType>', views.getApiInfo),
#     # 案例、场景等复制
#     path('copy/<int:id>', views.infoCopy),
#
#     # 统一接口：通过类型和ID获取 编辑页面时传递的数据
#     path('modify/info', views.getOnlyInfo),
#     # UI自动化案例脚本导出
#     path('export/<int:caseId>', views.caseExport),
#     # 场景列表及查询
#     path('scenes', views.sceneList),
#     # 场景新增
#     path('scene-add', views.sceneAdd),
#     # 场景编辑和删除
#     path('scene/<int:sceneId>', views.sceneUpdate),
#     # 场景运行
#     path('scene-run/<int:sceneId>', views.sceneRun),
#     # 页面列表及查询
#     path('pages', views.pageList),
#     # 页面新增
#     path('page-add', views.pageAdd),
#     # 页面编辑和删除
#     path('page/<int:pageId>', views.pageUpdate),
#     # web元素列表及查询
#     path('web/elements', views.webElementList),
#     #  web元素新增
#     path('web/element-add', views.webElementAdd),
#     #  web元素编辑和删除
#     path('web/element/<int:webElementId>', views.webElementUpdate),
#     # web案例列表及查询
#     path('web/cases', views.webCaseList),
#     #  web案例新增
#     path('web/case-add', views.webCaseAdd),
#     #  web元素编辑和删除
#     path('web/case/<int:webCaseId>', views.webCaseUpdate),
#     # web案例运行
#     path('web/case-run/<int:webCaseId>', views.webCaseRun),
#     # 块列表及查询
#     path('chunks', views.chunkList),
#     #  块新增
#     path('chunk-add', views.chunkAdd),
#     #  块编辑和删除
#     path('chunk/<int:chunkId>', views.chunkUpdate),
#     # 设备列表及查询
#     path('devices', views.deviceList),
#     #  设备新增
#     path('device-add', views.deviceAdd),
#     #  设备编辑和删除
#     path('device/<int:deviceId>', views.deviceUpdate),
#     #  设备测试连接
#     path('device-connect', views.deviceTestConnect),
#     # 应用列表及查询
#     path('applications', views.applicationList),
#     #  应用新增
#     path('application-add', views.applicationAdd),
#     #  应用编辑和删除
#     path('application/<int:applicationId>', views.applicationUpdate),
#     # 安卓元素列表及查询
#     path('app/elements', views.appElementList),
#     #  安卓元素新增
#     path('app/element-add', views.appElementAdd),
#     #  安卓元素编辑和删除
#     path('app/element/<int:appElementId>', views.appElementUpdate),
#     # 安卓案例列表及查询
#     path('app/cases', views.appCaseList),
#     #  安卓案例新增
#     path('app/case-add', views.appCaseAdd),
#     #  安卓案例编辑和删除
#     path('app/case/<int:appCaseId>', views.appCaseUpdate),
#     # 安卓案例运行
#     path('app/case-run/<int:appCaseId>', views.appCaseRun),
#     # 性能测试任务列表及查询
#     path('performances', views.performanceList),
#     #  性能测试任务新增
#     path('performance-add', views.performanceAdd),
#     #  性能测试任务编辑和删除
#     path('performance/<int:locustTaskId>', views.performanceUpdate),
#     # 性能测试任务运行
#     path('performance-run/<int:locustTaskId>', views.performanceRun),
#     # 性能测试报告查看
#     path('performance/report', views.performanceReport),
#     # 发版列表及查询
#     path('releases', views.releaseList),
#     # 金额计算工具
#     path('money-calculate', views.moneyCalculate),
#     # 日志列表及查询
#     path('logs', views.logList),
    # 发版任务执行
    # path('runTaskForJenkins', views.runTaskForJenkins),
#
#     path('download', views.download),
# ]
