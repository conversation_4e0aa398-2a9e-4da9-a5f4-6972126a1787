from __future__ import absolute_import, unicode_literals

import os

from celery import Celery

# set the default Django settings module for the 'celery' program.
from celery.signals import setup_logging



os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hydee_auto_server.settings')

app = Celery('hydee_auto_server')

from django.conf import settings

# 配置自动路由
# https://docs.celeryproject.org/en/latest/userguide/routing.html
app.conf.task_routes = {}

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
# - namespace='CELERY' means all celery-related configuration keys
#   should have a `CELERY_` prefix.
#加载配置模块
app.config_from_object('django.conf:settings', namespace='CELERY')

# 读取配置文件，服务接口IP
SERVICE_IP = settings.SERVICE_IP
REDIS_PASSWORD = settings.REDIS_PASSWORD
REDIS_URL = f'redis://default:{REDIS_PASSWORD}@{SERVICE_IP}:6379/0'

# 配置celery_once 解决任务重复执行问题
app.conf.ONCE = {
  'backend': 'celery_once.backends.Redis',
  'settings': {
    'url': REDIS_URL,
    'default_timeout': 60 * 60
  }
}

# 将 Django 的 LOGGING 设置同步到 Celery 中
@setup_logging.connect
def config_loggers(*args, **kwargs):
    from logging.config import dictConfig
    from django.conf import settings
    dictConfig(settings.LOGGING)


# Load task modules from all registered Django app configs.
app.autodiscover_tasks()

