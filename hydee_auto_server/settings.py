"""
Django settings for hydee_auto_server project.

Generated by 'django-admin startproject' using Django 3.0.6.

For more information on this file, see
https://docs.djangoproject.com/en/3.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.0/ref/settings/
"""

import os
from typing import Dict

from scaffold.settings import *

# 服务器IP地址
SERVICE_IP = '************'

# IP 白名单   ************为生产HIM 调用IP
ALLOWED_IPS = ['************', '127.0.0.1', '**************', '**************', '************', '************', '*************']

FILE_PATH = 'uploads/'

# REDIS密码
REDIS_PASSWORD = 'Hydee123!'

# APP_ID
WECHAT_APP_ID = "ww408c023179829552"
# 应用ID
AGENTID = "1000189"

# 企业ID-测试
# CROP_ID = "ww408c023179829552"
# 企业ID-生产
CROP_ID = "ww408c023179829552"

# B端测试域名
ROBOT_DOMAIN = "http://hydee-middle-robot.svc.k8s.dev.ydjia.cn"
# B端生产域名
# ROBOT_DOMAIN = "http://hydee-middle-robot.svc.k8s.test.ydjia.cn"

# 测试平台域名-测试
PLATFORM_DOMAIN = "testplatform-dev.hydee.cn"
# 测试平台域名-生产
# PLATFORM_DOMAIN = "testplatform.hydee.cn"

# ai-robot 服务域名-测试
AI_ROBOT_DOMAIN = "https://middle-dev.ydjia.cn/"
# ai-robot 服务域名-生产
# AI_ROBOT_DOMAIN = "https://middle.hydee.cn/"

# 获取句子状态接口
ROBOT_STATUS_URL = AI_ROBOT_DOMAIN+"/robot/api/data/bot_status"

# 句子推送群消息接口
ROBOT_SEND_MSG_URL = AI_ROBOT_DOMAIN+"/robot/api/data/send_message_data"

# 默认消息内容
ROBOT_SEND_DEFAULT_MSG = "技术人员正在跟进中，请耐心等待"

# 售后问题消息推送接口
QUESTION_MSG_URL = ROBOT_DOMAIN+"/1.0/robot/api/push/group/msg"

# 售后问题消息识别接口
QUESTION_CHECK_URL = ROBOT_DOMAIN+"/1.0/robot/api/msg/prediction/batch"

# 值班表信息接口
ROTA_URL = ROBOT_DOMAIN+"/1.0/robot/api/fetch/group"

# 企微个人推送地址
NOTICE_URL = ROBOT_DOMAIN+"/1.0/robot/api/notice/ta"

# callback地址
CALLBACK_URL = ROBOT_DOMAIN+"/1.0/robot/wx/api/callback"

# 文件上传接口
UPLOAD_URL = ROBOT_DOMAIN+"/1.0/robot/api/upload"

# 解绑合并消息接口
UNBIND_MSG_URL = ROBOT_DOMAIN+"/1.0/robot/api/lift/msg"

# AI回答接口
AI_ANSWER_URL = ROBOT_DOMAIN+"/1.0/robot/api/msg/ai/answer"

# 业务线到webhook地址的映射表
WEBHOOK_MAP: Dict[str, str] = {
    "中台": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=88e37d12-b0e7-4359-99cb-d2527b5314b9",
    "四季蝉": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=595f0d78-79d8-4035-a512-99451203f3b7",
    "药事云": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=9fcc7b37-4f78-44f4-91fb-d1c382856f86",
    "微商城": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=5a34793c-741e-49bd-abd1-f57a5d558ae5",
    "海川": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8380db4c-4df4-4142-b755-d2cc4665c0d3"
}

# 测试报告模板地址
TEMPLATE_URL = '/usr/local/autotest/hydee_auto_server/uploads/template_path/testreport.xlsx'







# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'lr8sgcx-$)%tachlo3^q%uo%38wj_@3ey@egqybfh-rxh8-&#3'

# 会员营销密钥
MEMBER_KEY = "hydeesoft0211122"
KEY_SIZE = 16

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False

ALLOWED_HOSTS = ['*']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'scaffold.apps.media',
    'scaffold.apps.config',
    'corsheaders',#跨域访问的应用部分
    'django_celery_beat',
    'core',
    'base',#自己建立的应用名称
    'interface_test',
    'ui_test',
    'test_case',
    'performance_test',
    'tools_help',
    'data_config',
    'django_db_reconnect',
    'ai_agent',
    'gunicorn'
]


MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    #'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'core.commonUtils.userRequestMiddleware.UserRequestMiddleware',
]

CORS_ORIGIN_WHITELIST = (
    'http://127.0.0.1:8080',
    'http://localhost:8080',
    'http://*************:8080'
)
CORS_ALLOW_CREDENTIALS = True

CORS_ALLOW_METHODS = (
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
    'VIEW',
)
CORS_ALLOW_HEADERS = (
    'XMLHttpRequest',
    'X_FILENAME',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'Pragma',
    "userId",
)


ROOT_URLCONF = 'hydee_auto_server.urls'


TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'hydee_auto_server.wsgi.application'


# Database
# https://docs.djangoproject.com/en/3.0/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'HOST': SERVICE_IP,
        'NAME': "hydee_auto_test",
        'USER': "hydee",
        'PASSWORD': "AutoTest",
        'OPTIONS': {'charset': 'utf8mb4'},
        'PORT': "3306"
    }
}

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
}
# Password validation
# https://docs.djangoproject.com/en/3.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# 屏蔽中间应用token验证，直接访问
NO_TOKEN_LIST = [
    "/api/interface_info/interface_traffic_summary/",
    "/api/interface_info/get_interface_daily_traffic/",
    "/api/interface_info/export_interface_traffic_summary/",
    "/api/interface_info/export_interface_daily_traffic/",
    "/api/task_info/task_report/",
    "/api/login",
    "/api/user_info/login/",
    "/api/user_info/user_password_reset/",
    "/api/case_info/download_csv/",
    "/api/task_info/task_list/",
    "/api/task_info/get_daily_monitoring_list/",
    "/api/task_info/task_run/",
    "/api/task_info/hops_run_task/",
    "/api/tools_help/new_release_list/",
    "/api/performance/task_report/",
    "/api/task_info/task_report/",
    "/api/tools_help/runTaskForJenkins/",
    "/api/tools_help/unifiedPay/",
    "/api/tools_help/refund/",
    "/api/task_info/task_report_detail/",
    "/api/task_info/interface_compare_report/",
    "/export/records/",
    "/api/export/records/",
    "/api/task_info/add_scene_to_more_task/",
    "/api/task_info/delete_task_scene/",
    "/api/case_info/upload_file/",
    "/upload/",
    "/api/question/question_list/",
    "/api/question/question_push_message/",
    "/api/question/get_emp_info/",
    "/api/question/question_update/",
    "/api/question/answer_update/",
    "/api/question/get_question_statistics/",
    "/api/test_case_info/import_xmind/"
    "/api/user_info/get_authInfo/",
    "/api/question/question_attachment_upload/",
    "/api/ai-agent/chat/",
    "/api/download-specific/",
    "/api/user_info/upload_file/"

]

# 导出接口，单独处理
EXPORT_API_LIST = [
    "/api/app_case/download/",
    "/api/case_info/download_csv/",
    "/export/records/",
    "/api/interface_info/export_interface_traffic_summary/",
    "/api/interface_info/export_interface_daily_traffic/",
    "/upload/"
]

# Internationalization
# https://docs.djangoproject.com/en/3.0/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = False


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.0/howto/static-files/

STATIC_URL = '/static/'

# celery 配置
CELERY_BROKER_URL = 'pyamqp://admin:admin@'+SERVICE_IP+':5672/hydee_auto_server'  # 使用 RabbitMQ 作为消息代理
CELERY_RESULT_BACKEND = f'redis://default:{REDIS_PASSWORD}@{SERVICE_IP}:6379/0'  # 把任务结果存在了 Redis

# 是否自动重连，默认是 True
BROKER_CONNECTION_RETRY = True

# 重连最大次数，默认是100
BROKER_CONNECTION_MAX_RETRIES = 10

CELERY_ENABLE_UTC = False
CELERY_TIMEZONE = TIME_ZONE
CELERY_ACCEPT_CONTENT = ['json']
#指定序列化的方式
CELERY_TASK_SERIALIZER = 'json'
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'
DJANGO_CELERY_BEAT_TZ_AWARE = False

#并发的worker数量,避免任务堆积
CELERYD_CONCURRENCY = 4

#每个worker执行的最大任务数
CELERYD_MAX_TASKS_PER_CHILD = 200

#防止死锁
CELERYD_FORCE_EXECV = True

#celery worker 每次去redis取任务的数量
CELERYD_PREFETCH_MULTIPLIER = 4

#关闭限速
CELERY_DISABLE_RATE_LIMITS = True

#celery任务执行结果的超时时间
CELERY_TASK_RESULT_EXPIRES = 1200

#单个任务的运行时间限制
CELERYD_TASK_SOFT_TIME_LIMIT = 300

# CELERY_EVENT_QUEUE_TTL = 60
# CELERY_EVENT_QUEUE_EXPIRES = 60

#解决定时任务重复执行问题
BROKER_TRANSPORT_OPTIONS = {'visibility_timeout': 43200}

#用于解决启动项目告警
DEFAULT_AUTO_FIELD = 'django.db.models.AutoField'

# LLM 配置
LLM_CONFIG = {
    "MODEL_NAME": "qwen-max",
    "TEMPERATURE": 0.85,
    "MAX_TOKENS": 1000,
    "API_KEY": "sk-d7e52de3ed4b4752936a291dad093da2",
    "BASE_URL": "https://dashscope.aliyuncs.com/compatible-mode/v1",
    "EMB_MODEL": "embedding-3",
    "EMB_API_KEY": "sk-d7e52de3ed4b4752936a291dad093da2",
    "EMB_BASE_URL": "https://dashscope.aliyuncs.com/compatible-mode/v1"
}

# JENKINS配置
JENKINS_CONFIG = {
    "JENKINS_URL": "https://jenkins-pt.hydee.cn",
    "JENKINS_USER": "2908",
    "JENKINS_API_TOKEN": "Hydeeldap798",
    "JENKINS_VALUE":"ahqu7udoteih5iqu4U",
    "REMOTE_DIR": "dist/build/mp-weixin",
    "LOCAL_DOWNLOAD_DIR": os.path.join(BASE_DIR, "download")
}

# 服务器的域名 ，注意发布线上需要修改，暂时为 **************:8000
# server_domain = 'http://**************:8000'
server_domain = 'http://' + SERVICE_IP + ':8000'
