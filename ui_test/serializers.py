import re

from django.db.models import Max
from rest_framework import serializers
from scaffold.restframework.utils import auto_declare_serializers

from . import models as m


class ChunkInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.ChunkInfo
        fields = '__all__'


class PageInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.PageInfo
        fields = '__all__'


class WebElementInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.ElementInfo
        fields = '__all__'


class WebCaseInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.WebCaseInfo
        fields = '__all__'


class DeviceSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.DeviceInfo
        fields = '__all__'


class AndroidElementInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.AndroidElementInfo
        fields = '__all__'


class AndroidCaseInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.AndroidCaseInfo
        fields = '__all__'

# # Auto declare serializer classes from models.
auto_declare_serializers(m, locals())
