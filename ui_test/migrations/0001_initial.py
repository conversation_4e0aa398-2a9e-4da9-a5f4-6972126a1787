# Generated by Django 3.0.6 on 2021-05-13 18:47

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AndroidCaseInfo',
            fields=[
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('creater', models.Char<PERSON><PERSON>(max_length=128, verbose_name='创建人')),
                ('update_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='修改时间')),
                ('update_person', models.Char<PERSON>ield(max_length=128, verbose_name='修改人')),
                ('androidCase_id', models.AutoField(primary_key=True, serialize=False)),
                ('androidCase_name', models.Char<PERSON>ield(max_length=256)),
                ('androidCase_describe', models.TextField()),
                ('androidCase_level', models.Char<PERSON><PERSON>(max_length=32)),
                ('test_device', models.Char<PERSON><PERSON>(max_length=32)),
                ('test_application', models.Char<PERSON><PERSON>(max_length=32)),
                ('androidCase_formation', models.TextField()),
                ('is_assert', models.Boolean<PERSON>ield()),
                ('asserts', models.TextField()),
                ('recent_img_url', models.CharField(default='', max_length=512)),
            ],
            options={
                'verbose_name': '安卓案例信息表',
                'db_table': 'android_case_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='AndroidElementInfo',
            fields=[
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('creater', models.CharField(max_length=128, verbose_name='创建人')),
                ('update_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='修改时间')),
                ('update_person', models.CharField(max_length=128, verbose_name='修改人')),
                ('appElement_id', models.AutoField(primary_key=True, serialize=False)),
                ('appElement_name', models.CharField(max_length=256)),
                ('find_appElement_type', models.CharField(max_length=32)),
                ('appElement_value', models.TextField()),
                ('operate_appElement_type', models.CharField(max_length=32)),
                ('send_value', models.CharField(max_length=512, null=True)),
                ('appElement_exists', models.CharField(max_length=32, null=True)),
                ('appElement_not_exists', models.CharField(max_length=32, null=True)),
                ('sleep_time', models.CharField(default='0', max_length=8)),
                ('sweep_page', models.CharField(default='0', max_length=8)),
                ('remark', models.TextField(blank=True, null=True)),
            ],
            options={
                'verbose_name': '安卓元素操作表',
                'db_table': 'android_element_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='AppCaseRunningRecord',
            fields=[
                ('app_case_record_id', models.AutoField(primary_key=True, serialize=False)),
                ('app_case_id', models.CharField(max_length=128)),
                ('app_case_name', models.CharField(max_length=256)),
                ('assert_result', models.BooleanField()),
                ('total_time', models.CharField(max_length=128)),
                ('run_result', models.CharField(max_length=128)),
                ('parent_id', models.CharField(default='', max_length=256)),
                ('running_log', models.TextField()),
            ],
            options={
                'verbose_name': '安卓案例执行记录表',
                'db_table': 'app_case_running_record',
            },
        ),
        migrations.CreateModel(
            name='ApplicationInfo',
            fields=[
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('creater', models.CharField(max_length=128, verbose_name='创建人')),
                ('update_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='修改时间')),
                ('update_person', models.CharField(max_length=128, verbose_name='修改人')),
                ('application_id', models.AutoField(primary_key=True, serialize=False)),
                ('application_name', models.CharField(max_length=256)),
                ('application_packageName', models.CharField(max_length=512)),
                ('application_activityName', models.CharField(max_length=512)),
                ('remark', models.TextField(blank=True, null=True)),
            ],
            options={
                'verbose_name': '待测应用信息表',
                'db_table': 'application_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='CaseFormationInfo',
            fields=[
                ('case_formation_id', models.AutoField(primary_key=True, serialize=False)),
                ('formation_type', models.CharField(max_length=1)),
                ('element_id', models.CharField(max_length=128, null=True)),
                ('chunk_id', models.CharField(max_length=128, null=True)),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
            ],
            options={
                'db_table': 'case_formation_info',
            },
        ),
        migrations.CreateModel(
            name='CaseRunningRecord',
            fields=[
                ('case_record_id', models.AutoField(primary_key=True, serialize=False)),
                ('case_id', models.CharField(max_length=128)),
                ('case_name', models.CharField(max_length=256)),
                ('assert_result', models.BooleanField()),
                ('belong_task', models.BooleanField()),
                ('total_time', models.CharField(max_length=128)),
                ('run_result', models.CharField(max_length=128)),
                ('parent_id', models.CharField(default='', max_length=256)),
                ('running_log', models.TextField()),
            ],
            options={
                'verbose_name': '案例执行记录表',
                'db_table': 'case_running_record',
            },
        ),
        migrations.CreateModel(
            name='ChunkInfo',
            fields=[
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('creater', models.CharField(max_length=128, verbose_name='创建人')),
                ('update_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='修改时间')),
                ('update_person', models.CharField(max_length=128, verbose_name='修改人')),
                ('chunk_id', models.AutoField(primary_key=True, serialize=False)),
                ('chunk_name', models.CharField(max_length=256)),
                ('chunk_describe', models.TextField()),
                ('element_type', models.CharField(default='WEB', max_length=32)),
                ('element_pool', models.TextField()),
            ],
            options={
                'verbose_name': 'UI块信息表',
                'db_table': 'chunk_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='DeviceInfo',
            fields=[
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('creater', models.CharField(max_length=128, verbose_name='创建人')),
                ('update_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='修改时间')),
                ('update_person', models.CharField(max_length=128, verbose_name='修改人')),
                ('device_id', models.AutoField(primary_key=True, serialize=False)),
                ('device_name', models.CharField(max_length=256)),
                ('platform_version', models.CharField(max_length=32)),
                ('device_udid', models.CharField(max_length=64)),
                ('connect_way', models.CharField(max_length=64)),
                ('remark', models.TextField(blank=True, null=True)),
            ],
            options={
                'verbose_name': '设备信息表',
                'db_table': 'device_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='ElementInfo',
            fields=[
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('creater', models.CharField(max_length=128, verbose_name='创建人')),
                ('update_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='修改时间')),
                ('update_person', models.CharField(max_length=128, verbose_name='修改人')),
                ('element_id', models.AutoField(primary_key=True, serialize=False)),
                ('page_id', models.CharField(max_length=32)),
                ('element_name', models.CharField(max_length=256)),
                ('find_element_type', models.CharField(max_length=32)),
                ('element_value', models.TextField()),
                ('element_num', models.CharField(default='0', max_length=4)),
                ('operate_element_type', models.CharField(max_length=32)),
                ('send_value', models.CharField(max_length=128)),
                ('remark', models.TextField(blank=True, null=True)),
                ('label_name', models.CharField(default='', max_length=128)),
                ('sleep_time', models.CharField(default='0', max_length=4)),
                ('sweep_page', models.CharField(default='0', max_length=4)),
                ('is_switch_window', models.CharField(default='0', max_length=1)),
                ('window_value', models.CharField(default='', max_length=512)),
            ],
            options={
                'verbose_name': 'WEB元素操作信息表',
                'db_table': 'element_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='PageInfo',
            fields=[
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('creater', models.CharField(max_length=128, verbose_name='创建人')),
                ('update_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='修改时间')),
                ('update_person', models.CharField(max_length=128, verbose_name='修改人')),
                ('page_id', models.AutoField(primary_key=True, serialize=False)),
                ('page_name', models.CharField(max_length=256)),
                ('page_type', models.CharField(max_length=16)),
                ('page_url', models.TextField()),
                ('remark', models.TextField()),
            ],
            options={
                'verbose_name': '页面信息表',
                'db_table': 'page_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='SceneRunningRecord',
            fields=[
                ('scene_record_id', models.AutoField(primary_key=True, serialize=False)),
                ('scene_id', models.CharField(max_length=128)),
                ('scene_name', models.CharField(max_length=256)),
                ('assert_result', models.BooleanField()),
                ('total_time', models.CharField(max_length=128)),
                ('run_result', models.CharField(max_length=128)),
                ('parent_id', models.CharField(default='', max_length=256)),
                ('running_log', models.TextField(default='')),
            ],
            options={
                'verbose_name': '场景执行记录表',
                'db_table': 'scene_running_record',
            },
        ),
        migrations.CreateModel(
            name='WebCaseInfo',
            fields=[
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('creater', models.CharField(max_length=128, verbose_name='创建人')),
                ('update_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='修改时间')),
                ('update_person', models.CharField(max_length=128, verbose_name='修改人')),
                ('web_case_id', models.AutoField(primary_key=True, serialize=False)),
                ('web_case_name', models.CharField(max_length=256)),
                ('web_case_describe', models.TextField()),
                ('web_case_formation', models.TextField()),
                ('is_assert', models.BooleanField()),
                ('asserts', models.TextField()),
                ('web_case_level', models.CharField(default='低', max_length=64)),
                ('recent_img_url', models.CharField(max_length=256, null=True)),
            ],
            options={
                'verbose_name': 'WEB案例信息表',
                'db_table': 'web_case_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='WebCaseRunningRecord',
            fields=[
                ('web_case_record_id', models.AutoField(primary_key=True, serialize=False)),
                ('web_case_id', models.CharField(max_length=128)),
                ('web_case_name', models.CharField(max_length=256)),
                ('assert_result', models.BooleanField()),
                ('total_time', models.CharField(max_length=128)),
                ('run_result', models.CharField(max_length=128)),
                ('parent_id', models.CharField(default='', max_length=256)),
                ('running_log', models.TextField()),
            ],
            options={
                'verbose_name': 'WEB案例执行记录表',
                'db_table': 'web_case_running_record',
            },
        ),
    ]
