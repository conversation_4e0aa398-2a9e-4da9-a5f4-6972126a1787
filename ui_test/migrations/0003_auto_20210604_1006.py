# Generated by Django 3.0.6 on 2021-06-04 10:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ui_test', '0002_auto_20210514_1437'),
    ]

    operations = [
        migrations.AddField(
            model_name='androidcaseinfo',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True, verbose_name='是否可用'),
        ),
        migrations.AddField(
            model_name='androidelementinfo',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True, verbose_name='是否可用'),
        ),
        migrations.AddField(
            model_name='applicationinfo',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True, verbose_name='是否可用'),
        ),
        migrations.AddField(
            model_name='caseformationinfo',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True, verbose_name='是否可用'),
        ),
        migrations.AddField(
            model_name='chunkinfo',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True, verbose_name='是否可用'),
        ),
        migrations.AddField(
            model_name='deviceinfo',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True, verbose_name='是否可用'),
        ),
        migrations.AddField(
            model_name='elementinfo',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True, verbose_name='是否可用'),
        ),
        migrations.AddField(
            model_name='pageinfo',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True, verbose_name='是否可用'),
        ),
        migrations.AddField(
            model_name='webcaseinfo',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True, verbose_name='是否可用'),
        ),
    ]
