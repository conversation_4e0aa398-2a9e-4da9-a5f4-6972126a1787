from django.db import models
from scaffold.models.abstract.meta import ActiveModel, HierarchicalModel

from core.models import DateModel


# 块信息-->积木拼图    自行组建模块
class ChunkInfo(DateModel, ActiveModel):
    chunk_id = models.AutoField(primary_key=True)
    chunk_name = models.CharField(max_length=256)
    chunk_describe = models.TextField()
    element_type = models.CharField(max_length=32, default='WEB')
    element_pool = models.TextField()  # 元素池，存放元素ID

    def __str__(self):
        return self.chunk_name

    class Meta(object):
        # 定义表名
        db_table = "chunk_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = 'UI块信息表'


# 页面信息表
class PageInfo(DateModel, ActiveModel):
    page_id = models.AutoField(primary_key=True)
    page_name = models.Char<PERSON><PERSON>(max_length=256)
    page_type = models.Char<PERSON>ield(max_length=16)
    page_url = models.TextField()
    remark = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.page_name

    class Meta(object):
        # 定义表名
        db_table = "page_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = '页面信息表'


# 元素操作表
class ElementInfo(DateModel, ActiveModel):
    element_id = models.AutoField(primary_key=True)
    page_id = models.CharField(max_length=32)
    element_name = models.CharField(max_length=256)
    find_element_type = models.CharField(max_length=32)
    element_value = models.TextField()
    element_num = models.CharField(max_length=4, default='0')
    operate_element_type = models.CharField(max_length=32)
    send_value = models.CharField(max_length=128)
    remark = models.TextField(blank=True, null=True)
    label_name = models.CharField(max_length=128, default='')
    sleep_time = models.CharField(max_length=4, default='0')
    sweep_page = models.CharField(max_length=4, default='0')
    is_switch_window = models.CharField(max_length=1, default='0')
    window_value = models.CharField(max_length=512, default='')

    def __str__(self):
        return self.element_name

    class Meta(object):
        # 定义表名
        db_table = "element_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = 'WEB元素操作信息表'


# 案例数据表
class WebCaseInfo(DateModel, ActiveModel):
    web_case_id = models.AutoField(primary_key=True)
    web_case_name = models.CharField(max_length=256)
    web_case_describe = models.TextField()
    web_case_formation = models.TextField()
    is_assert = models.BooleanField()
    asserts = models.TextField()
    web_case_level = models.CharField(max_length=64, default='低')
    recent_img_url = models.CharField(max_length=256, null=True)

    def __str__(self):
        return self.web_case_name

    class Meta(object):
        # 定义表名
        db_table = "web_case_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = 'WEB案例信息表'


# 案例组成信息表
class CaseFormationInfo(ActiveModel):
    case_formation_id = models.AutoField(primary_key=True)
    formation_type = models.CharField(max_length=1)  # 0表示元素，1表示块
    element_id = models.CharField(max_length=128, null=True)
    chunk_id = models.CharField(max_length=128, null=True)
    create_time = models.DateTimeField()
    creater = models.CharField(max_length=128)

    class Meta(object):
        # 定义表名
        db_table = "case_formation_info"


# 设备信息表
class DeviceInfo(DateModel, ActiveModel):
    device_id = models.AutoField(primary_key=True)
    device_name = models.CharField(max_length=256)
    platform_version = models.CharField(max_length=32)
    device_udid = models.CharField(max_length=64)
    connect_way = models.CharField(max_length=64)
    remark = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.device_name

    class Meta(object):
        # 定义表名
        db_table = "device_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = '设备信息表'


# 应用信息表
class ApplicationInfo(DateModel, ActiveModel):
    application_id = models.AutoField(primary_key=True)
    application_name = models.CharField(max_length=256)
    application_packageName = models.CharField(max_length=512)
    application_activityName = models.CharField(max_length=512)
    remark = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.application_name

    class Meta(object):
        # 定义表名
        db_table = "application_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = '待测应用信息表'


# 安卓元素操作表
class AndroidElementInfo(DateModel, ActiveModel):
    appElement_id = models.AutoField(primary_key=True)
    appElement_name = models.CharField(max_length=256)
    find_appElement_type = models.CharField(max_length=32)
    appElement_value = models.TextField()
    operate_appElement_type = models.CharField(max_length=32)
    send_value = models.CharField(max_length=512, null=True)
    appElement_exists = models.CharField(max_length=32, null=True)
    appElement_not_exists = models.CharField(max_length=32, null=True)
    sleep_time = models.CharField(max_length=8, default='0')
    sweep_page = models.CharField(max_length=8, default='0')
    remark = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.appElement_name

    class Meta(object):
        # 定义表名
        db_table = "android_element_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = '安卓元素操作表'


# 安卓案例信息表
class AndroidCaseInfo(DateModel, ActiveModel):
    androidCase_id = models.AutoField(primary_key=True)
    androidCase_name = models.CharField(max_length=256)
    androidCase_describe = models.TextField()
    androidCase_level = models.CharField(max_length=32)
    test_device = models.CharField(max_length=32)
    test_application = models.CharField(max_length=32)
    androidCase_formation = models.TextField()
    is_assert = models.BooleanField()
    asserts = models.TextField()
    recent_img_url = models.CharField(max_length=512, default='')

    def __str__(self):
        return self.androidCase_name

    class Meta(object):
        # 定义表名
        db_table = "android_case_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = '安卓案例信息表'


# 场景执行记录表
class SceneRunningRecord(models.Model):
    scene_record_id = models.AutoField(primary_key=True)
    scene_id = models.CharField(max_length=128)  # 场景ID
    scene_name = models.CharField(max_length=256)  # 场景名称
    assert_result = models.BooleanField()  # 断言结果
    total_time = models.CharField(max_length=128)  # 场景运行时长
    run_result = models.CharField(max_length=128)  # 场景运行结果 成功/失败/错误
    parent_id = models.CharField(max_length=256, default='')  # 归属ID，默认为空，不为空时表示归属的任务执行记录ID
    running_log = models.TextField(default='')  # 执行日志
    business_id = models.IntegerField(verbose_name='业务ID', default=0)

    class Meta(object):
        # 定义表名
        db_table = "scene_running_record"
        # 表别名
        verbose_name = '场景执行记录表'


# 案例执行记录表
class CaseRunningRecord(models.Model):
    case_record_id = models.AutoField(primary_key=True)
    case_id = models.CharField(max_length=128)  # 场景ID
    case_name = models.CharField(max_length=256)  # 场景名称
    assert_result = models.BooleanField()  # 断言结果
    belong_task = models.BooleanField()  # 是否直接归属任务
    total_time = models.CharField(max_length=128)  # 案例运行时长
    run_result = models.CharField(max_length=128)  # 案例运行结果 成功/失败/错误
    parent_id = models.CharField(max_length=256, default='')  # 归属ID，默认为空，不为空时表示归属的任务执行记录ID或场景执行记录ID
    running_log = models.TextField()  # 执行日志

    class Meta(object):
        # 定义表名
        db_table = "case_running_record"
        # 表别名
        verbose_name = '案例执行记录表'


# WEB案例执行记录表
class WebCaseRunningRecord(models.Model):
    web_case_record_id = models.AutoField(primary_key=True)
    web_case_id = models.CharField(max_length=128)  # web案例ID
    web_case_name = models.CharField(max_length=256)  # web案例名称
    assert_result = models.BooleanField()  # 断言结果
    total_time = models.CharField(max_length=128)  # 案例运行时长
    run_result = models.CharField(max_length=128)  # 案例运行结果 成功/失败/错误
    parent_id = models.CharField(max_length=256, default='')  # 归属ID，默认为空，不为空时表示归属的任务执行记录ID
    running_log = models.TextField()  # 执行日志

    class Meta(object):
        # 定义表名
        db_table = "web_case_running_record"
        # 表别名
        verbose_name = 'WEB案例执行记录表'


# 安卓案例执行记录表
class AppCaseRunningRecord(models.Model):
    app_case_record_id = models.AutoField(primary_key=True)
    app_case_id = models.CharField(max_length=128)  # app案例ID
    app_case_name = models.CharField(max_length=256)  # app案例名称
    assert_result = models.BooleanField()  # 断言结果
    total_time = models.CharField(max_length=128)  # 案例运行时长
    run_result = models.CharField(max_length=128)  # 案例运行结果 成功/失败/错误
    parent_id = models.CharField(max_length=256, default='')  # 归属ID，默认为空，不为空时表示归属的任务执行记录ID
    running_log = models.TextField()  # 执行日志

    class Meta(object):
        # 定义表名
        db_table = "app_case_running_record"
        # 表别名
        verbose_name = '安卓案例执行记录表'
