import json
import os
import time
import traceback

from django.core.paginator import Paginator
from django.forms import model_to_dict
from django.http import FileResponse
from rest_framework.decorators import action

from core.commonUtils.commonUtil import CommonUtil
from core.commonUtils.getModelInfoUtil import GetModelInfo
from core.commonUtils.jsonUtil import isContainsDDL
from core.servers.androidAutoTest import AndroidAutoTest
from core.servers.apiAutoTest import APIAutoTest
from core.servers.sceneExecute import SceneExecute
from core.servers.webAutoTest import WebAutoTest
from core.utils import UserUtils, ResponseUtils, DateUtils
from data_config.models import TaskInfo
from interface_test.models import AssertInfo
from .exceptions import AppErrors
from rest_framework import viewsets
from .models import *
from . import serializers as s

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()


class ChunkInfoViewSet(viewsets.ModelViewSet):
    queryset = ChunkInfo.objects.all()
    serializer_class = s.ChunkInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def chunk_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
            chunk_id = request.GET.get('search_chunk_id', '')
            chunk_name = request.GET.get('search_chunk_name', '')
            creater = request.GET.get('creater', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询UI块信息，查询条件：块编号=%s，块名称=%s" \
                  % (username, chunk_id, chunk_name)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = ChunkInfo.objects.filter(is_active=True)
        if chunk_id:
            obj = obj.filter(chunk_id = chunk_id)
        if chunk_name:
            obj = obj.filter(chunk_name__contains = chunk_name)
        if creater:
            obj = obj.filter(creater__contains = creater)
        obj = obj.order_by('-update_time')
        paginator = Paginator(obj, limit)
        data = paginator.page(page)
        data_list = list(data.object_list.values())
        response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count, 'search_chunk_id': chunk_id,
                         'search_chunk_name': chunk_name, 'chunks': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def chunk_add(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        if request.method == 'POST':
            # 提取参数
            temp = json.loads(request.body)
            try:
                chunk_name = temp['chunk_name']
                chunk_describe = temp['chunk_describe']
                element_type = temp['element_type']
                temp_pool = list(map(str, temp['element_pool']))
                element_pool = "|".join(temp_pool)
            except Exception as e:
                error_info = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{error_info}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
            create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            chunk = ChunkInfo(chunk_name=chunk_name, chunk_describe=chunk_describe,
                              creater=username, element_pool=element_pool, element_type=element_type,
                              update_person=username, create_time=create_time, update_time=create_time)
            chunk.save()
            content = "用户 %s 新增UI块成功，块信息：%s" % (username, chunk.__dict__)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("块新增成功")

    @action(methods=['PUT'], detail=True)
    def chunk_update(self, request, pk):
        chunk_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            chunk_name = temp['chunk_name']
            chunk_describe = temp['chunk_describe']
            element_type = temp['element_type']
            temp_pool = list(map(str, temp['element_pool']))
            element_pool = "|".join(temp_pool)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        chunk_info_before = ChunkInfo.objects.get(chunk_id=chunk_id).__dict__
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        ChunkInfo.objects.filter(chunk_id=chunk_id).update(chunk_name=chunk_name, chunk_describe=chunk_describe,
                                                           element_pool=element_pool, update_person=username,
                                                           update_time=update_time, element_type=element_type)
        chunk_info = ChunkInfo.objects.get(chunk_id=chunk_id).__dict__
        content = "用户 %s 修改UI块信息成功，修改前信息：%s，修改后信息：%s" % (username, chunk_info_before, chunk_info)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("块编辑成功")

    @action(methods=['DELETE'], detail=True)
    def chunk_delete(self, request, pk):
        chunk_id = pk
        username = UserUtils.get_login_user(request).username
        if request.method == 'DELETE':
            chunk_info = ChunkInfo.objects.get(chunk_id=chunk_id)
            case_formation = CaseFormationInfo.objects.filter(chunk_id=str(chunk_id), is_active=True)
            case_id_list = []
            if case_formation:
                for formation in case_formation:
                    if chunk_info.element_type == "WEB":
                        web_case_id = WebCaseInfo.objects.get(
                            web_case_formation__contains=formation.case_formation_id, is_active=True).web_case_id
                        case_id_list.append(web_case_id)
                        case_id_list = list(set(case_id_list))
                        return ResponseUtils.return_fail(AppErrors.ERROR_CHUNK_CASE_IS_QUOTE_BY_WEB,
                                                         '案例id= %s' % str(case_id_list))
                    else:
                        androidCaseId = AndroidCaseInfo.objects.get(
                            androidCase_formation__contains=formation.case_formation_id, is_active=True).androidCase_id
                        case_id_list.append(androidCaseId)
                        case_id_list = list(set(case_id_list))
                        return ResponseUtils.return_fail(AppErrors.ERROR_CHUNK_CASE_IS_QUOTE_BY_ANDROID,
                                                         '案例id= %s' % str(case_id_list))
            ChunkInfo.objects.filter(chunk_id=chunk_id).delete()
            content = "用户 %s 删除了块：%s" % (username, chunk_info.__dict__)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("块删除成功")


class PageInfoViewSet(viewsets.ModelViewSet):
    queryset = PageInfo.objects.all()
    serializer_class = s.PageInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def page_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
            page_id = request.GET.get('search_page_id', '')
            page_name = request.GET.get('search_page_name', '')
            creater = request.GET.get('creater', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

        content = "用户 %s 查询页面信息，查询条件：页面编号=%s，页面名称=%s" \
                  % (username, page_id, page_name)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = PageInfo.objects.filter(is_active=True)
        if page_id:
            obj = obj.filter(page_id = page_id)
        if page_name:
            obj = obj.filter(page_name__contains = page_name)
        if creater:
            obj = obj.filter(creater__contains = creater)
        obj = obj.order_by('-update_time')
        paginator = Paginator(obj, limit)
        data = paginator.page(page)
        data_list = list(data.object_list.values())
        response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count, 'search_page_id': page_id,
                         'search_page_name': page_name, 'pages': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def page_add(self, request):
        username = UserUtils.get_login_user(request).username
        if request.method == 'POST':
            temp = json.loads(request.body)
            try:
                page_name = temp['page_name']
                page_type = temp['page_type']
                page_url = temp['page_url']
                remark = temp.get('remark', '')
            except Exception as e:
                error_info = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{error_info}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        page = PageInfo(page_name=page_name, page_type=page_type,
                        page_url=page_url, remark=remark, creater=username,
                        update_person=username, create_time=create_time, update_time=create_time)
        page.save()
        content = "用户 %s 新增页面成功，页面信息：%s" % (username, page.__dict__)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("页面新增成功")

    @action(methods=['PUT'], detail=True)
    def page_update(self, request, pk):
        page_id = pk
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            page_name = temp['page_name']
            page_type = temp['page_type']
            page_url = temp['page_url']
            remark = temp.get('remark')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        page_before = PageInfo.objects.get(page_id=page_id).__dict__
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        PageInfo.objects.filter(page_id=page_id).update(page_name=page_name, page_type=page_type,
                                                        page_url=page_url, remark=remark,
                                                        update_person=username, update_time=update_time)
        pageInfo = PageInfo.objects.get(page_id=page_id).__dict__
        content = "用户 %s 修改页面信息成功，修改前信息：%s，修改后信息：%s" % (username, page_before, pageInfo)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("页面编辑成功")

    @action(methods=['DELETE'], detail=True)
    def page_delete(self, request, pk):
        page_id = pk
        username = UserUtils.get_login_user(request).username
        if request.method == 'DELETE':
            page = PageInfo.objects.get(page_id=page_id).__dict__
            # 判断web元素是否被引用
            element_info = ElementInfo.objects.filter(page_id=page_id, is_active=True)
            if element_info:
                return ResponseUtils.return_fail(AppErrors.ERROR_WEB_IS_EXISTS_CHILDREN)
            else:
                PageInfo.objects.filter(page_id=page_id, is_active=True).update(is_active=False, update_person=username,
                                                                                update_time=DateUtils.get_current_time())
                content = "用户 %s 删除了页面信息：%s" % (username, page)
                # 日志记录
                CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
                return ResponseUtils.return_success("页面删除成功")


class WebElementViewSet(viewsets.ModelViewSet):
    queryset = ElementInfo.objects.all()
    serializer_class = s.WebElementInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def web_element_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
            element_id = request.GET.get('search_element_id', '')
            element_name = request.GET.get('search_element_name', '')
            creater = request.GET.get('creater', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询元素信息，查询条件：元素编号=%s，元素名称=%s" \
                  % (username, element_id, element_name)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = ElementInfo.objects.filter(is_active=True)
        if element_id:
            obj = obj.filter(element_id = element_id)
        if element_name:
            obj = obj.filter(element_name__contains = element_name)
        if creater:
            obj = obj.filter(creater__contains=creater)
        obj = obj.order_by('-update_time')
        temp_list = []
        for element in obj:
            element_dict = model_to_dict(element)
            element_dict["page_id"] = int(element.page_id)
            temp_list.append(element_dict)
        paginator = Paginator(temp_list, limit)
        data = paginator.page(page)
        response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count, 'search_element_id': element_id,
                         'search_element_name': element_name, 'webElements': data.object_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def web_element_add(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        if request.method == 'POST':
            temp = json.loads(request.body)
            try:
                element_name = temp['element_name']
                page_id = temp['page_id']
                find_element_type = temp['find_element_type']
                element_value = temp['element_value']
                element_num = temp.get('element_num', 0)
                operate_element_type = temp['operate_element_type']
                send_value = temp['send_value']
                remark = temp.get('remark', '')
                label_name = temp['label_name']
                sleep_time = temp.get('sleep_time', '0')
                sweep_page = temp.get('sweep_page', '0')
                is_switch_window = temp['is_switch_window']
                window_value = temp['window_value']
            except Exception as e:
                error_info = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{error_info}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
            create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            element = ElementInfo(element_name=element_name, page_id=page_id, label_name=label_name,
                                  find_element_type=find_element_type, element_value=element_value,
                                  operate_element_type=operate_element_type, send_value=send_value,
                                  remark=remark, creater=username, update_person=username, sleep_time=sleep_time,
                                  sweep_page=sweep_page, is_switch_window=is_switch_window, element_num=element_num,
                                  create_time=create_time, update_time=create_time, window_value=window_value)

            element.save()
            content = "用户 %s 新增web元素成功，元素信息：%s" % (username, element.__dict__)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("web元素新增成功")

    @action(methods=['PUT'], detail=True)
    def web_element_update(self, request, pk):
        web_element_id = pk
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            element_name = temp['element_name']
            page_id = temp['page_id']
            find_element_type = temp['find_element_type']
            element_value = temp['element_value']
            element_num = temp['element_num']
            operate_element_type = temp['operate_element_type']
            send_value = temp['send_value']
            remark = temp.get('remark')
            label_name = temp['label_name']
            sleep_time = temp['sleep_time']
            sweep_page = temp['sweep_page']
            is_switch_window = temp['is_switch_window']
            window_value = temp['window_value']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        element_before = ElementInfo.objects.get(element_id=web_element_id).__dict__
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        ElementInfo.objects.filter(element_id=web_element_id).update(element_name=element_name, remark=remark,
                                                                     window_value=window_value, element_num=element_num,
                                                                     find_element_type=find_element_type,
                                                                     label_name=label_name, page_id=page_id,
                                                                     sleep_time=sleep_time, sweep_page=sweep_page,
                                                                     is_switch_window=is_switch_window,
                                                                     operate_element_type=operate_element_type,
                                                                     update_person=username,
                                                                     element_value=element_value,
                                                                     update_time=update_time, send_value=send_value)
        element_info = ElementInfo.objects.get(element_id=web_element_id).__dict__
        content = "用户 %s 修改WEB元素信息成功，修改前信息：%s，修改后信息：%s" % (username, element_before, element_info)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("web元素编辑成功")

    @action(methods=['DELETE'], detail=True)
    def web_element_delete(self, request, pk):
        web_element_id = pk
        username = UserUtils.get_login_user(request).username
        element = ElementInfo.objects.get(element_id=web_element_id).__dict__
        # 判断元素操作是否被引用
        caseformation_info = CaseFormationInfo.objects.filter(element_id=web_element_id, is_active=True)
        chunk_info = ChunkInfo.objects.filter(element_type='WEB', element_pool__contains=web_element_id, is_active=True)
        if chunk_info:
            chunk_id_list = []
            for chunk in chunk_info:
                chunk_id_list.append(str(chunk.chunk_id))
            return ResponseUtils.return_fail(AppErrors.ERROR_WEB_IS_EXISTS_QUOTE, 'chunk_id: %s' % chunk_id_list)
        if caseformation_info:
            web_case_id_list = []
            for caseformation in caseformation_info:
                web_case = WebCaseInfo.objects.filter(
                    web_case_formation__icontains=caseformation.case_formation_id, is_active=True)
                for case in web_case:
                    web_case_id_list.append(str(case.web_case_id))
            web_case_id_list = list(set(web_case_id_list))
            return ResponseUtils.return_fail(AppErrors.ERROR_CHUNK_CASE_IS_QUOTE_BY_WEB,
                                             'web_id: %s' % web_case_id_list)
        ElementInfo.objects.filter(element_id=web_element_id).update(is_active=False, update_person=username,
                                                                     update_time=DateUtils.get_current_time())
        content = "用户 %s 删除了WEB元素：%s" % (username, element)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("web元素删除成功")


class WebCaseViewSet(viewsets.ModelViewSet):
    queryset = WebCaseInfo.objects.all()
    serializer_class = s.WebCaseInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def web_case_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
            web_case_id = request.GET.get('search_web_case_id', '')
            web_case_name = request.GET['search_web_case_name']
            creater = request.GET.get('creater', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询web案例信息，查询条件：案例编号=%s，案例名称=%s" \
                  % (username, web_case_id, web_case_name)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = WebCaseInfo.objects.filter(is_active=True)
        if web_case_id:
            obj = obj.filter(web_case_id = web_case_id)
        if web_case_name:
            obj = obj.filter(web_case_name__contains = web_case_name)
        if creater:
            obj = obj.filter(creater__contains = creater)
        obj = obj.order_by('-update_time')
        paginator = Paginator(obj, limit)
        data = paginator.page(page)
        data_list = list(data.object_list.values())
        response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count,
                         'search_web_case_id': web_case_id,
                         'search_web_case_name': web_case_name, 'webCases': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def web_case_add(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        temp = json.loads(request.body)
        try:
            web_case_name = temp['web_case_name']
            web_case_describe = temp['web_case_describe']
            web_case_level = temp['web_case_level']
            formation_str = temp["formation"]
            is_assert = temp["is_assert"]
            assert_str = temp["asserts"]
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        # 案例组成信息
        web_case_formation = CommonUtil().insertCaseFormation(formation_str, username)
        # 断言信息
        if is_assert:
            assert_id = CommonUtil().insertOldAssertInfo(assert_str, username)
        else:
            assert_id = ''
        web_case = WebCaseInfo(web_case_name=web_case_name, web_case_describe=web_case_describe,
                               web_case_level=web_case_level,
                               create_time=temp_time, update_time=temp_time, is_assert=is_assert, creater=username,
                               web_case_formation=web_case_formation, update_person=username, asserts=assert_id)
        web_case.save()
        content = "用户 %s 新增web案例成功，案例信息：%s" % (username, web_case.__dict__)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("WEB案例新增成功")

    @action(methods=['PUT'], detail=True)
    def web_case_update(self, request, pk):
        web_case_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        temp = json.loads(request.body)
        try:
            web_case_name = temp['web_case_name']
            web_case_describe = temp['web_case_describe']
            web_case_level = temp['web_case_level']
            formation_str = temp["formation"]
            is_assert = temp["is_assert"]
            assert_str = temp["asserts"]
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        web_case_before = WebCaseInfo.objects.get(web_case_id=web_case_id).__dict__
        # 删除原来的案例组成数据
        web_case_info = WebCaseInfo.objects.get(web_case_id=web_case_id)
        old_formation = web_case_info.web_case_formation.split('|')
        for item in old_formation:
            CaseFormationInfo.objects.filter(case_formation_id=item).update(is_active=False)
        # 获取新的案例组成数据
        web_case_formation = CommonUtil().insertCaseFormation(formation_str, username)
        # 删除原来的断言
        if web_case_info.is_assert:
            old_asserts = web_case_info.asserts.split('|')
            for item in old_asserts:
                AssertInfo.objects.filter(assert_id=item).update(is_active=False, update_person=username,
                                                                 update_time=DateUtils.get_current_time())
        # 新的断言
        if is_assert:
            assert_id = CommonUtil().insertOldAssertInfo(assert_str, username)
        else:
            assert_id = ''
        WebCaseInfo.objects.filter(web_case_id=web_case_id).update(web_case_name=web_case_name,
                                                                   web_case_describe=web_case_describe,
                                                                   web_case_level=web_case_level,
                                                                   update_time=temp_time, is_assert=is_assert,
                                                                   web_case_formation=web_case_formation,
                                                                   update_person=username, asserts=assert_id)
        info = WebCaseInfo.objects.get(web_case_id=web_case_id).__dict__
        content = "用户 %s 修改web案例信息成功，修改前信息：%s，修改后信息：%s" % (username, web_case_before, info)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("WEB案例编辑成功")

    @action(methods=['DELETE'], detail=True)
    def web_case_delete(self, request, pk):
        web_case_id = pk
        username = UserUtils.get_login_user(request).username
        web_case = WebCaseInfo.objects.get(web_case_id=web_case_id)
        # 判断案例是否被引用在任务中
        task_info = TaskInfo.objects.filter(task_type="UI", case_pool__icontains=str(web_case_id), is_active=True)
        if task_info:
            task_id_list = []
            for task in task_info:
                task_id_list.append(task.task_id)
            task_id_list = list(set(task_id_list))
            msg = "该WEB案例已被任务 %s 引用，请先解除引用后再试！" % str(task_id_list)
            return ResponseUtils.return_fail(AppErrors.ERROR_WEB_TASK_IS_EXISTS_QUOTE,
                                             'task_ids: %s' % str(task_id_list))
        else:
            formation = web_case.web_case_formation.split('|')
            # 先删除案例组成
            for item in formation:
                CaseFormationInfo.objects.filter(case_formation_id=item).update(is_active=False)
            # 再删除断言数据
            if web_case.is_assert:
                asserts = web_case.asserts.split('|')
                AssertInfo.objects.filter(assert_id__in=asserts).update(is_active=False, update_person=username,
                                                                     update_time=DateUtils.get_current_time())
            # 删除案例本身
            WebCaseInfo.objects.filter(web_case_id=web_case_id).update(is_active=False, update_person=username,
                                                                       update_time=DateUtils.get_current_time())
            content = "用户 %s 删除了web案例：%s" % (username, web_case.__dict__)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("WEB案例删除成功")

    @action(methods=['POST'], detail=True)
    def web_case_run(self, request, pk):
        web_case_valid_id = pk
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            web_case_id = temp['web_case_id']
            env_type = temp['env_type']
            if str(web_case_id) != web_case_valid_id:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        # 获取用户IP
        if 'HTTP_X_FORWARDED_FOR' in request.META:
            ipaddress = request.META['HTTP_X_FORWARDED_FOR']
        else:
            # ipaddress = request.META['REMOTE_ADDR']
            ipaddress = request.META.get('REMOTE_ADDR')
        # 运行UI测试
        web_test = WebAutoTest(web_case_id, env_type, username, ipaddress)
        web_case_info = str(web_case_id) + "-" + WebCaseInfo.objects.get(web_case_id=web_case_id).web_case_name
        content = "用户 %s 开始调试案例：%s" % (username, web_case_info)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        log = web_test.debugWebCase()
        return ResponseUtils.return_success("WEB案例执行成功", log)

    # 案例脚本文件导出
    @action(methods=['POST'], detail=True)
    def case_export(self, request, pk):
        case_id_str = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            env_type = temp['env_type']
            case_type = temp['case_type']
            case_id = str(case_id_str)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        if case_type == "WEB":
            web_test = WebAutoTest(case_id, env_type, username, "")
            web_case_info = str(case_id) + "-" + WebCaseInfo.objects.get(web_case_id=case_id).web_case_name
            content = "用户 %s 导出WEB案例：%s 为脚本文件" % (username, web_case_info)
            # 执行案例导出为脚本操作 返回脚本文件路径
            file_path = web_test.transforScript()
        elif case_type == "Android":
            android_test = AndroidAutoTest(case_id, env_type, username, "")
            android_case_info = str(case_id) + "-" + AndroidCaseInfo.objects.get(
                androidCase_id=case_id).androidCase_name
            content = "用户 %s 导出Android案例：%s 为脚本文件" % (username, android_case_info)
            # 执行案例导出为脚本操作 返回脚本文件路径
            file_path = android_test.transforScript()
        else:
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, "case_type参数异常：%s" % case_type)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        # 截取文件名
        file_name = file_path.split("Scripts")[1].replace("\\", "/")
        return ResponseUtils.return_success("导出成功", file_name)


class DeviceViewSet(viewsets.ModelViewSet):
    queryset = DeviceInfo.objects.all()
    serializer_class = s.DeviceSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def device_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
            device_id = request.GET.get('search_device_id', '')
            device_name = request.GET.get('search_device_name', '')
            creater = request.GET.get('creater', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询设备信息，查询条件：设备编号=%s，设备名称=%s" \
                  % (username, device_id, device_name)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = DeviceInfo.objects.filter(is_active=True)
        if device_id:
            obj = obj.filter(device_id = device_id)
        if device_name:
            obj = obj.filter(device_name__contains = device_name)
        if creater:
            obj = obj.filter(creater__contains = creater)
        obj = obj.order_by('-update_time')
        paginator = Paginator(obj, limit)
        data = paginator.page(page)
        data_list = list(data.object_list.values())
        response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count, 'search_device_id': device_id,
                         'search_device_name': device_name, 'devices': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def device_add(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        if request.method == 'POST':
            # 提取参数
            temp = json.loads(request.body)
            try:
                device_name = temp['device_name']
                platform_version = temp['platform_version']
                device_udid = temp['device_udid']
                connect_way = temp['connect_way']
                remark = temp.get('remark')
            except Exception as e:
                error_info = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{error_info}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
            create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            device = DeviceInfo(device_name=device_name, platform_version=platform_version, device_udid=device_udid,
                                creater=username, connect_way=connect_way, remark=remark,
                                update_person=username, create_time=create_time, update_time=create_time)
            device.save()
            content = "用户 %s 新增设备成功，设备信息：%s" % (username, device.__dict__)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("设备新增成功")

    @action(methods=['PUT'], detail=True)
    def device_update(self, request, pk):
        device_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            device_name = temp['device_name']
            platform_version = temp['platform_version']
            device_udid = temp['device_udid']
            connect_way = temp['connect_way']
            remark = temp.get('remark')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        device_info_before = DeviceInfo.objects.get(device_id=device_id).__dict__
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        DeviceInfo.objects.filter(device_id=device_id).update(device_name=device_name, connect_way=connect_way,
                                                              platform_version=platform_version,
                                                              device_udid=device_udid, update_person=username,
                                                              update_time=update_time, remark=remark)
        device_info = DeviceInfo.objects.get(device_id=device_id).__dict__
        content = "用户 %s 修改设备信息成功，修改前信息：%s，修改后信息：%s" % (username, device_info_before, device_info)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("设备编辑成功")

    @action(methods=['DELETE'], detail=True)
    def device_delete(self, request, pk):
        device_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        device_info = DeviceInfo.objects.get(device_id=device_id).__dict__
        android_case_info = AndroidCaseInfo.objects.filter(test_device=device_id, is_active=True)
        if android_case_info:
            case_id_list = []
            for info in android_case_info:
                case_id_list.append(info.androidCase_id)
            case_id_list = list(set(case_id_list))
            return ResponseUtils.return_fail(AppErrors.ERROR_DEVICE_IS_QUOTE_BY_ANDORID, 'caseId: %s' % case_id_list)
        DeviceInfo.objects.filter(device_id=device_id).update(is_active=False, update_person=username,
                                                              update_time=DateUtils.get_current_time())
        content = "用户 %s 删除了设备：%s" % (username, device_info)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("设备删除成功")

    @action(methods=['POST'], detail=False)
    def device_test_connect(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            device_udid = temp['device_udid']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        information = os.popen('adb devices')
        text = information.read()
        information.close()
        if device_udid in text:
            result = "当前设备连接成功"
        else:
            result = "当前设备连接失败，errorInfo:\r\n%s" % text
        content = "用户 %s 正在测试连接设备 %s ，连接结果为：%s" % (username, device_udid, result)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success(result)


class ApplicationViewSet(viewsets.ModelViewSet):
    queryset = DeviceInfo.objects.all()
    serializer_class = s.DeviceSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def application_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
            application_id = request.GET.get('search_application_id', '')
            application_name = request.GET.get('search_application_name', '')
            creater = request.GET.get('creater', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询应用信息，查询条件：应用编号=%s，应用名称=%s" \
                  % (username, application_id, application_name)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = ApplicationInfo.objects.filter(is_active=True)
        if application_id:
            obj = obj.filter(application_id = application_id)
        if application_name:
            obj = obj.filter(application_name__contains = application_name)
        if creater:
            obj = obj.filter(creater__contains = creater)
        obj = obj.order_by('-update_time')
        paginator = Paginator(obj, limit)
        data = paginator.page(page)
        data_list = list(data.object_list.values())
        response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count,
                         'search_application_id': application_id, 'search_application_name': application_name,
                         'applications': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def application_add(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            application_name = temp['application_name']
            application_packageName = temp['application_packageName']
            application_activityName = temp['application_activityName']
            remark = temp.get('remark')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        application = ApplicationInfo(application_name=application_name,
                                      application_packageName=application_packageName,
                                      creater=username, application_activityName=application_activityName,
                                      remark=remark,
                                      update_person=username, create_time=create_time, update_time=create_time)
        application.save()
        content = "用户 %s 新增应用成功，应用信息：%s" % (username, application.__dict__)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("应用添加成功")

    @action(methods=['PUT'], detail=True)
    def application_update(self, request, pk):
        application_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            application_name = temp['application_name']
            application_packageName = temp['application_packageName']
            application_activityName = temp['application_activityName']
            remark = temp.get('remark')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        application_info_before = ApplicationInfo.objects.get(application_id=application_id).__dict__
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        ApplicationInfo.objects.filter(application_id=application_id).update(application_name=application_name,
                                                                             application_packageName=application_packageName,
                                                                             application_activityName=application_activityName,
                                                                             update_person=username,
                                                                             update_time=update_time, remark=remark)
        application_info = ApplicationInfo.objects.get(application_id=application_id).__dict__
        content = "用户 %s 修改应用信息成功，修改前信息：%s，修改后信息：%s" % (username, application_info_before, application_info)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("应用编辑成功")

    @action(methods=['DELETE'], detail=True)
    def application_delete(self, request, pk):
        application_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        application_info = ApplicationInfo.objects.get(application_id=application_id).__dict__
        androidCaseInfo = AndroidCaseInfo.objects.filter(test_application=application_id, is_active=True)
        if androidCaseInfo:
            case_id_list = []
            for info in androidCaseInfo:
                case_id_list.append(info.androidCase_id)
            case_id_list = list(set(case_id_list))
            msg = "该应用已被Android案例 %s 引用，请先解除引用后再试" % case_id_list
            return ResponseUtils.return_fail(AppErrors.ERROR_APPLICATION_IS_QUOTE_BY_ANDORID,
                                             'caseIds: %s' % case_id_list)
        ApplicationInfo.objects.filter(application_id=application_id).update(is_active=False, update_person=username,
                                                                             update_time=DateUtils.get_current_time())
        content = "用户 %s 删除了应用：%s" % (username, application_info)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success('应用删除成功')


class AppElementViewSet(viewsets.ModelViewSet):
    queryset = AndroidElementInfo.objects.all()
    serializer_class = s.AndroidElementInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def element_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
            app_element_id = request.GET.get('search_appElement_id', '')
            app_element_name = request.GET.get('search_appElement_name', '')
            creater = request.GET.get('creater', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询安卓元素信息，查询条件：安卓元素编号=%s，安卓元素名称=%s" \
                  % (username, app_element_id, app_element_name)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = AndroidElementInfo.objects.filter(is_active=True)
        if app_element_id:
            obj = obj.filter(appElement_id = app_element_id)
        if app_element_name:
            obj = obj.filter(appElement_name__contains = app_element_name)
        if creater:
            obj = obj.filter(creater__contains = creater)
        obj = obj.order_by('-update_time')
        paginator = Paginator(obj, limit)
        data = paginator.page(page)
        data_list = list(data.object_list.values())
        response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count,
                         'search_appElement_id': app_element_id, 'search_appElement_name': app_element_name,
                         'appElements': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def element_add(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            app_element_name = temp['appElement_name']
            find_app_element_type = temp['find_appElement_type']
            app_element_value = temp['appElement_value']
            operate_app_element_type = temp['operate_appElement_type']
            send_value = temp['send_value']
            app_element_exists = temp['appElement_exists']
            app_element_not_exists = temp['appElement_not_exists']
            sleep_time = temp.get('sleep_time','0')
            sweep_page = temp['sweep_page']
            remark = temp.get('remark', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        android_element_info = AndroidElementInfo(appElement_name=app_element_name,
                                                  find_appElement_type=find_app_element_type,
                                                  creater=username, appElement_value=app_element_value, remark=remark,
                                                  send_value=send_value,
                                                  operate_appElement_type=operate_app_element_type,
                                                  appElement_exists=app_element_exists,
                                                  appElement_not_exists=app_element_not_exists, sleep_time=sleep_time,
                                                  sweep_page=sweep_page,
                                                  update_person=username, create_time=create_time,
                                                  update_time=create_time)
        android_element_info.save()
        content = "用户 %s 新增Android元素成功，元素信息：%s" % (username, android_element_info.__dict__)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("安卓元素添加成功")

    @action(methods=['PUT'], detail=True)
    def element_update(self, request, pk):
        app_element_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            app_element_name = temp['appElement_name']
            find_app_element_type = temp['find_appElement_type']
            app_element_value = temp['appElement_value']
            operate_app_element_type = temp['operate_appElement_type']
            send_value = temp['send_value']
            app_element_exists = temp['appElement_exists']
            app_element_not_exists = temp['appElement_not_exists']
            sleep_time = temp['sleep_time']
            sweep_page = temp['sweep_page']
            remark = temp.get('remark')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        app_element_info_before = AndroidElementInfo.objects.get(appElement_id=app_element_id).__dict__
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        AndroidElementInfo.objects.filter(appElement_id=app_element_id).update(appElement_name=app_element_name,
                                                                               find_appElement_type=find_app_element_type,
                                                                               appElement_value=app_element_value,
                                                                               remark=remark, send_value=send_value,
                                                                               operate_appElement_type=operate_app_element_type,
                                                                               appElement_exists=app_element_exists,
                                                                               appElement_not_exists=app_element_not_exists,
                                                                               sleep_time=sleep_time,
                                                                               sweep_page=sweep_page,
                                                                               update_person=username,
                                                                               update_time=update_time)
        app_element_info = AndroidElementInfo.objects.get(appElement_id=app_element_id).__dict__
        content = "用户 %s 修改Android元素信息成功，修改前信息：%s，修改后信息：%s" % (username, app_element_info_before, app_element_info)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success('安卓元素编辑成功')

    @action(methods=['DELETE'], detail=True)
    def element_delete(self, request, pk):
        app_element_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        if request.method == 'DELETE':
            app_element_info = AndroidElementInfo.objects.get(appElement_id=app_element_id).__dict__
            # 元素被其他元素在  元素存在时引用
            element_info = AndroidElementInfo.objects.filter(appElement_exists=app_element_id)
            if element_info:
                app_element_id_list = []
                for element in element_info:
                    app_element_id_list.append(str(element.appElement_id))
                return ResponseUtils.return_fail(AppErrors.ERROR_APP_ELEMENT_IS_QUOTE_BY_ANDORID,
                                                 'ids: %s' % app_element_id_list)
            # 元素被其他元素在  元素不存在时引用
            element_info2 = AndroidElementInfo.objects.filter(appElement_not_exists=app_element_id)
            if element_info2:
                app_element_id_list = []
                for element in element_info2:
                    app_element_id_list.append(str(element.appElement_id))
                return ResponseUtils.return_fail(AppErrors.ERROR_APP_ELEMENT_IS_QUOTE_BY_ANDORID,
                                                 'ids: %s' % app_element_id_list)
            # 元素在块中被引用
            chunk_info = ChunkInfo.objects.filter(element_type='Android', element_pool__icontains=app_element_id,
                                                  is_active=True)
            if chunk_info:
                chunk_id_list = []
                for chunk in chunk_info:
                    chunk_id_list.append(str(chunk.chunk_id))
                return ResponseUtils.return_fail(AppErrors.ERROR_APP_ELEMENT_IS_QUOTE_BY_UI,
                                                 'chunkIds: %s' % chunk_id_list)
            # 元素在案例中被引用
            caseformation_info = CaseFormationInfo.objects.filter(element_id=app_element_id, is_active=True)
            if caseformation_info:
                android_case_id_list = []
                for caseformation in caseformation_info:
                    android_case = AndroidCaseInfo.objects.filter(
                        androidCase_formation__icontains=caseformation.case_formation_id)
                    for case in android_case:
                        android_case_id_list.append(str(case.androidCase_id))
                android_case_id_list = list(set(android_case_id_list))
                return ResponseUtils.return_fail(AppErrors.ERROR_APP_ELEMENT_IS_QUOTE_BY_CASE,
                                                 'caseIds: %s' % android_case_id_list)
            AndroidElementInfo.objects.filter(appElement_id=app_element_id).update(is_active=False,
                                                                                   update_person=username,
                                                                                   update_time=DateUtils.get_current_time())
            content = "用户 %s 删除了安卓元素：%s" % (username, app_element_info)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("安卓元素删除成功")


class AppCaseViewSet(viewsets.ModelViewSet):
    queryset = AndroidCaseInfo.objects.all()
    serializer_class = s.AndroidCaseInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def case_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
            android_case_id = request.GET.get('search_androidCase_id', '')
            android_case_name = request.GET['search_androidCase_name']
            creater = request.GET.get('creater', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询安卓元素信息，查询条件：安卓案例编号=%s，安卓案例名称=%s" \
                  % (username, android_case_id, android_case_name)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = AndroidCaseInfo.objects.filter(is_active=True)
        if android_case_id:
            obj = obj.filter(androidCase_id = android_case_id)
        if android_case_name:
            obj = obj.filter(androidCase_name__contains = email_model_name)
        if creater:
            obj = obj.filter(creater__contains = creater)
        obj = obj.order_by('-update_time')
        paginator = Paginator(obj, limit)
        data = paginator.page(page)
        data_list = list(data.object_list.values())
        for caseDict in data_list:
            caseDict['test_device'] = DeviceInfo.objects.get(device_id=caseDict['test_device'],
                                                             is_active=True).device_name
            caseDict['test_application'] = ApplicationInfo.objects.get(
                application_id=caseDict['test_application'], is_active=True).application_name
        response_json = {'pagenum': page, 'pagesize': limit, 'count': paginator.count,
                         'search_androidCase_id': android_case_id, 'search_androidCase_name': android_case_name,
                         'appCases': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def case_add(self, request):
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            androidCase_name = temp['androidCase_name']
            androidCase_describe = temp['androidCase_describe']
            androidCase_level = temp['androidCase_level']
            test_device = temp['test_device']
            test_application = temp['test_application']
            formation_str = temp["formation"]
            is_assert = temp["is_assert"]
            assert_str = temp["asserts"]
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        # 案例组成信息，插入案例组成表
        android_case_formation = CommonUtil().insertCaseFormation(formation_str, username)
        # 断言数据
        if is_assert:
            assert_id = CommonUtil().insertOldAssertInfo(assert_str, username)
        else:
            assert_id = ''
        create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        android_case_info = AndroidCaseInfo(androidCase_name=androidCase_name,
                                            androidCase_describe=androidCase_describe,
                                            creater=username, androidCase_level=androidCase_level,
                                            test_device=test_device, update_person=username,
                                            test_application=test_application,
                                            androidCase_formation=android_case_formation,
                                            is_assert=is_assert, asserts=assert_id, recent_img_url='',
                                            create_time=create_time, update_time=create_time)
        android_case_info.save()
        content = "用户 %s 新增Android案例成功，元素信息：%s" % (username, android_case_info.__dict__)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("Android案例添加成功")

    @action(methods=['PUT'], detail=True)
    def case_update(self, request, pk):
        app_case_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            androidCase_name = temp['androidCase_name']
            androidCase_describe = temp['androidCase_describe']
            androidCase_level = temp['androidCase_level']
            test_device = temp['test_device']
            test_application = temp['test_application']
            formation_str = temp["formation"]
            is_assert = temp["is_assert"]
            assert_str = temp["asserts"]
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        android_case_info_before = AndroidCaseInfo.objects.get(androidCase_id=app_case_id).__dict__
        # 删除原来的案例组成数据
        android_case_info = AndroidCaseInfo.objects.get(androidCase_id=app_case_id)
        old_formation = android_case_info.androidCase_formation.split('|')
        for item in old_formation:
            CaseFormationInfo.objects.filter(case_formation_id=item).update(is_active=False)
        # 获取新的案例组成数据
        android_case_formation = CommonUtil().insertCaseFormation(formation_str, username)
        # 删除原来的断言
        if android_case_info.is_assert:
            old_asserts = android_case_info.asserts.split('|')
            for item in old_asserts:
                AssertInfo.objects.filter(assert_id=item).delete()
        # 新的断言
        if is_assert:
            assert_id = CommonUtil().insertOldAssertInfo(assert_str, username)
        else:
            assert_id = ''
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        AndroidCaseInfo.objects.filter(androidCase_id=app_case_id).update(androidCase_name=androidCase_name,
                                                                          androidCase_describe=androidCase_describe,
                                                                          androidCase_level=androidCase_level,
                                                                          test_device=test_device,
                                                                          update_person=username,
                                                                          test_application=test_application,
                                                                          androidCase_formation=android_case_formation,
                                                                          is_assert=is_assert, asserts=assert_id,
                                                                          update_time=update_time)
        info = AndroidCaseInfo.objects.get(androidCase_id=app_case_id).__dict__
        content = "用户 %s 修改Android案例信息成功，修改前信息：%s，修改后信息：%s" % (username, android_case_info_before, info)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("Android案例编辑成功")

    @action(methods=['DELETE'], detail=True)
    def case_delete(self, request, pk):
        app_case_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        android_case = AndroidCaseInfo.objects.get(androidCase_id=app_case_id)
        case_info = android_case.__dict__
        # 判断案例是否被引用在任务中
        task_info = TaskInfo.objects.filter(task_type="UI", api_type="Android", case_pool__icontains=app_case_id,
                                            is_active=True)
        if task_info:
            task_id_list = []
            for task in task_info:
                task_id_list.append(task.task_id)
            task_id_list = list(set(task_id_list))
            msg = "该Android案例已被任务 %s 引用，请先解除引用后再试" % str(task_id_list)
            return ResponseUtils.return_fail(AppErrors.ERROR_APP_CASE_IS_QUOTE_BY_TASK,
                                             'taskIds: %s' % str(task_id_list))
        # 删除
        formation = android_case.androidCase_formation.split('|')
        # 删除案例组成
        for item in formation:
            CaseFormationInfo.objects.filter(case_formation_id=item).update(is_active=False)
        # 删除案例断言
        if android_case.is_assert:
            asserts = android_case.asserts.split('|')
            AssertInfo.objects.filter(assert_id__in=asserts).update(is_active=False, update_person=username,
                                                                 update_time=DateUtils.get_current_time())
        AndroidCaseInfo.objects.filter(androidCase_id=app_case_id).update(is_active=False, update_person=username,
                                                                          update_time=DateUtils.get_current_time())
        content = "用户 %s 删除了Android案例：%s" % (username, case_info)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("Android案例删除成功")

    @action(methods=['POST'], detail=True)
    def app_case_run(self, request, pk):
        app_case_valid_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        if request.method == 'POST':
            # 提取参数
            temp = json.loads(request.body)
            try:
                app_case_id = temp['androidCase_id']
                env_type = temp['env_type']
                if app_case_valid_id != str(app_case_id):
                    return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION)
            except Exception as e:
                error_info = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{error_info}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
            if 'HTTP_X_FORWARDED_FOR' in request.META:
                ipaddress = request.META['HTTP_X_FORWARDED_FOR']
            else:
                ipaddress = request.META['REMOTE_ADDR']
            android_test = AndroidAutoTest(app_case_id, env_type, username, ipaddress)
            android_case_info = str(app_case_id) + "-" + AndroidCaseInfo.objects.get(
                androidCase_id=app_case_id).androidCase_name
            content = "用户 %s 开始调试安卓案例：%s" % (username, android_case_info)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            log = android_test.debugAndroidCase()
            return ResponseUtils.return_success("APP案例执行成功", log)

    @action(methods=['GET'], detail=False)
    def download(self, request):
        file_name = request.GET['name']
        file_path = os.path.abspath('./resource/Scripts/' + file_name)
        file = open(file_path, 'rb')
        response = FileResponse(file)
        response['Content-Type'] = 'application/octet-stream'
        response['Content-Disposition'] = 'attachment;filename="web.py"'
        return response
