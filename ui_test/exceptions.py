""" Current project exception classes.
"""
from django.http import Http404

from rest_framework import exceptions
from rest_framework.exceptions import PermissionDenied, ErrorDetail
from rest_framework.response import Response
from rest_framework.views import set_rollback

from scaffold.exceptions.exceptions import AppError

import core.exceptions


class AppErrors(core.exceptions.AppErrors):
    ERROR_WEB_IS_EXISTS_CHILDREN = AppError("HD5001", "存在下级WEB元素数据，请删除WEB元素后再试！")
    ERROR_WEB_IS_EXISTS_QUOTE = AppError("HD5002", "该元素已被块引用，请先解除引用后再试！")
    ERROR_WEB_TASK_IS_EXISTS_QUOTE = AppError("HD5005", "该WEB案例已被任务引用，请先解除引用后再试！")
    ERROR_CHUNK_CASE_IS_QUOTE_BY_WEB = AppError("HD5006", "该块已被WEB案例引用，请先解除引用后再试！")
    ERROR_DEVICE_IS_QUOTE_BY_ANDORID = AppError("HD5007", "该设备已被Android案例引用，请先解除引用后再试！")
    ERROR_APPLICATION_IS_QUOTE_BY_ANDORID = AppError("HD5008", "该应用已被Android案例引用，请先解除引用后再试！")
    ERROR_APP_ELEMENT_IS_QUOTE_BY_ANDORID = AppError("HD5009", "该元素已被Android元素引用，请先解除引用后再试！")
    ERROR_APP_ELEMENT_IS_QUOTE_BY_UI = AppError("HD5010", "该元素已被UI块引用，请先解除引用后再试！")
    ERROR_APP_ELEMENT_IS_QUOTE_BY_CASE = AppError("HD5011", "该元素已被Android案例引用，请先解除引用后再试！")
    ERROR_APP_CASE_IS_QUOTE_BY_TASK = AppError("HD5012", "该Android案例已被任务引用，请先解除引用后再试！")
    ERROR_CHUNK_CASE_IS_QUOTE_BY_ANDROID = AppError("HD5013", "该块已被Android案例引用，请先解除引用后再试！")


# pylint: disable=unused-argument
def exception_handler(exc, context):
    """
    Returns the response that should be used for any given exception.

    By default we handle the REST framework `APIException`, and also
    Django's built-in `Http404` and `PermissionDenied` exceptions.

    Any unhandled exceptions may return `None`, which will cause a 500 error
    to be raised.
    """
    if isinstance(exc, Http404):
        exc = exceptions.NotFound()
    elif isinstance(exc, PermissionDenied):
        exc = exceptions.PermissionDenied()

    if isinstance(exc, exceptions.APIException):
        headers = {}
        if getattr(exc, 'auth_header', None):
            headers['WWW-Authenticate'] = exc.auth_header
        if getattr(exc, 'wait', None):
            headers['Retry-After'] = '%d' % exc.wait

        if isinstance(exc.detail, ErrorDetail):
            if exc.detail.code == 'not_authenticated':
                exc.status_code = 401
                data = dict(
                    ok=False,
                    msg=str(exc.detail),
                    errcode=10006
                )
            else:
                data = dict(
                    ok=False,
                    msg=exc.detail
                )
        elif isinstance(exc.detail, (list, dict)):
            data = dict(
                ok=False,
                msg=str(exc.detail),
                data=exc.detail
            )
        else:
            data = dict(
                ok=False,
                msg=exc.detail
            )

        set_rollback()
        return Response(data, status=exc.status_code, headers=headers)

    return None
