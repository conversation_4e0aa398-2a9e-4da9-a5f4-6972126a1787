{"name": "hydee_auto_web", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@riophae/vue-treeselect": "^0.4.0", "axios": "^0.21.1", "codemirror": "^5.65.16", "core-js": "^3.6.5", "diff-match-patch": "^1.0.5", "dompurify": "^3.2.6", "echarts": "^5.1.1", "el-table-virtual-scroll": "^1.4.3", "el-tree-transfer": "^2.4.7", "element-ui": "^2.15.14", "github-markdown-css": "^5.8.1", "lodash": "^4.17.21", "marked": "^4.3.0", "nprogress": "^0.2.0", "sass": "^1.32.13", "sass-loader": "^10.2.0", "sha1": "^1.1.1", "sortablejs": "^1.13.0", "viewerjs": "^1.11.7", "vue": "^2.6.11", "vue-codemirror": "^4.0.6", "vue-router": "^3.2.0", "vue-virtual-scroller": "^1.1.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "less": "^3.13.1", "less-loader": "^5.0.0", "vue-cli-plugin-element": "^1.0.1", "vue-template-compiler": "^2.6.11"}}