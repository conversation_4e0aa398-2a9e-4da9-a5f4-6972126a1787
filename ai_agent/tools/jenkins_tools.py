import os
import requests
import django
# 设置 Django 环境（关键配置）
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hydee_auto_server.settings')  # 注意这里是项目目录名
django.setup()  # 初始化Django
from hydee_auto_server import settings
from ai_agent .agents .tool_manager import tool

# Jenkins 配置
JENKINS_URL = settings.JENKINS_CONFIG['JENKINS_URL']  # Jenkins 地址
JENKINS_USER = settings.JENKINS_CONFIG['JENKINS_USER']  # Jenkins 用户名
JENKINS_API_TOKEN = settings.JENKINS_CONFIG['JENKINS_API_TOKEN']      # Jenkins API Token
JENKINS_VALUE = settings.JENKINS_CONFIG['JENKINS_VALUE']

@tool(
    name="jenkins构建",
    description="根据输入的服务名称name,环境env与分支branch，触发Jenkins项目构建，未传环境默认test,未传分支默认release，环境传入pro则提示：生产环境不允许构建",
    require_auth=False,
    permission=None,
    need_llm_process=False  # 工具返回的结果是否需要LLM处理
)
def trigger_build(name: str, env: str = "test", branch: str = "release"):
    """触发Jenkins构建任务"""
    try:
        session = requests.Session()
        session.auth = (JENKINS_USER, JENKINS_API_TOKEN)

        # 获取 CSRF Crumb
        crumb_response = session.get(f"{JENKINS_URL}/crumbIssuer/api/json")
        if crumb_response.status_code != 200:
            return f"获取CSRF Crumb失败，状态码: {crumb_response.status_code}"
        crumb_data = crumb_response.json()
        headers = {crumb_data['crumbRequestField']: crumb_data['crumb'],
                   "content-type": "application/x-www-form-urlencoded"}
        # 参数处理
        if env == '' or env == None or env == [] or env == 'pro' or env not in ('dev', 'test', 'uat', 'grey'):
            env = 'test'
        # 参数处理
        if branch == '' or branch == None or branch == [] or branch not in ('develop', 'release', 'master'):
            branch = 'release'
        data = {
            # "name": "env",
            # "value": env,
            # "name": "hydee_git_branch",
            # "value": branch,
            "json": '{"parameter": [{"name": "env", "value": "%s"}, '
                    '{"name": "hydee_git_branch", "value": "%s"}]}' % (env, branch)
        }
        # 触发构建（使用表单数据传递参数）
        resp = session.post(
            f"{JENKINS_URL}/job/{name}/build?delay=0sec",
            headers=headers,
            # params={'branch': branch,  'env': env},
            data=data,
            timeout=10
        )

        if resp.status_code == 201:
            return f"构建已触发: {name}服务/{branch}分支/{env}环境"
        return f"构建失败，状态码: {resp.status_code}, 响应内容: {resp.text}"
    except Exception as e:
        return f"请求出错: {str(e)}"




print(f"Jenkins工具装饰器生效: {trigger_build._is_tool}")

# if __name__ == '__main__':
#      print(trigger_build("test"))