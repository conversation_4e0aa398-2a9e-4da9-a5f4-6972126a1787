# get_sql_in_skywalking.py
import os
import django
import requests
from ai_agent.agents.tool_manager import tool
# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()

# 设置 Django 环境（关键配置）
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hydee_auto_server.settings')  # 注意这里是项目目录名
django.setup()  # 初始化Django


# SkyWalking配置
SKYWALKING_BASE_URL = "http://skywalking.dev.pt.hydee.cn"
BASIC_AUTH = "ZGV2OnRoYWhraXF1NmllbmdlaVZpZA=="


def get_common_headers():
    """获取通用请求头"""
    return {
        "Accept": "application/json, text/plain, */*",
        "Authorization": f"Basic {BASIC_AUTH}",
        "Content-Type": "application/json",
    }


def clean_sql(sql_text):
    """清理SQL格式，去除多余空白"""
    # 按行分割
    lines = sql_text.split('\n')
    # 清理每行，去除首尾空白
    cleaned_lines = [line.strip() for line in lines]
    # 过滤掉空行
    non_empty_lines = [line for line in cleaned_lines if line]
    # 重新组合成字符串
    return '\n'.join(non_empty_lines)


def _format_database_operations(database_operations, traceid):
    """
    格式化数据库操作列表为可读的字符串

    Args:
        database_operations: 数据库操作列表
        traceid: 追踪ID

    Returns:
        str: 格式化后的字符串
    """
    if not database_operations:
        return f"在traceid: {traceid} 中未找到数据库相关的SQL语句"

    # 构建输出内容
    result_lines = []
    result_lines.append("\n")
    result_lines.append(f"TraceID: {traceid} 中找到 {len(database_operations)} 个数据库操作:")
    result_lines.append("\n")

    for i, operation in enumerate(database_operations, 1):
        result_lines.append("\n")
        result_lines.append(f"=== SQL {i} ===")
        result_lines.append(f"数据库类型: {operation['db_type']}")
        result_lines.append("\n")
        result_lines.append(f"数据库实例: {operation['db_instance']}")
        result_lines.append("\n")
        result_lines.append(f"服务名称: {operation['service_name']}")
        result_lines.append("\n")

        if operation.get('error', False):
            result_lines.append(f"状态: ❌ 参数解析失败")
            result_lines.append(f"原始SQL: {operation['db_sql']}")
            result_lines.append(f"错误信息: {operation['full_sql']}")
        else:
            result_lines.append(f"完整SQL: {operation['full_sql']}")
            result_lines.append("\n")

    return '\n'.join(result_lines)


@tool(
    name="从skywalking获取sql",
    description="传入traceid,根据traceid到skywalking中获取接口相关的SQL语句",
    require_auth=False,
    permission=None,
    need_llm_process=False  # 工具返回的结果是否需要LLM处理
)
def get_sql_in_skywalking(traceid: str = None, **kwargs) -> str:
    """
    根据traceid从skywalking获取SQL语句

    Args:
        traceid: 追踪ID
        **kwargs: 其他参数

    Returns:
        str: SQL查询结果或错误信息
    """
    # 参数处理：优先使用直接传入的traceid，否则从kwargs中获取
    if not traceid and 'traceid' in kwargs:
        traceid = kwargs['traceid']

    if not traceid:
        return "错误：缺少必需的traceid参数"

    # 构建GraphQL查询
    query_body = {
        "query": "query queryTrace($traceId: ID!) {\n  trace: queryTrace(traceId: $traceId) {\n    spans {\n      traceId\n      segmentId\n      spanId\n      parentSpanId\n      refs {\n        traceId\n        parentSegmentId\n        parentSpanId\n        type\n      }\n      serviceCode\n      serviceInstanceName\n      startTime\n      endTime\n      endpointName\n      type\n      peer\n      component\n      isError\n      layer\n      tags {\n        key\n        value\n      }\n      logs {\n        time\n        data {\n          key\n          value\n        }\n      }\n      attachedEvents {\n        startTime {\n          seconds\n          nanos\n        }\n        event\n        endTime {\n          seconds\n          nanos\n        }\n        tags {\n          key\n          value\n        }\n        summary {\n          key\n          value\n        }\n      }\n    }\n  }\n  }",
        "variables": {"traceId": traceid}
    }

    # 发送请求
    try:
        headers = get_common_headers()
        response = requests.post(
            f"{SKYWALKING_BASE_URL}/graphql",
            headers=headers,
            json=query_body,
        )
        response.raise_for_status()
        data = response.json()

        # 检查响应数据
        if not data.get('data') or not data['data'].get('trace') or not data['data']['trace'].get('spans'):
            return f"未找到traceid: {traceid} 对应的trace信息"

        # 获取返参
        span_list = data['data']['trace']['spans']
        database_operations = []  # 存储所有数据库操作信息

        # 遍历所有span，收集数据库相关的操作
        for span in span_list:
            if span['layer'] == 'Database':
                # 获取数据库类型、数据库实例、SQL语句、SQL参数
                tags = span.get('tags', [])
                if len(tags) < 4:
                    continue

                db_type = tags[0]['value']
                db_instance = tags[1]['value']
                db_sql = tags[2]['value']
                db_sql_param = tags[3]['value']

                # 获取span的其他信息用于标识
                span_id = span.get('spanId', 'unknown')
                service_name = span.get('serviceCode', 'unknown')
                start_time = span.get('startTime', 'unknown')

                # 解析SQL参数列表
                try:
                    # 将字符串转换为列表格式
                    db_sql_param = db_sql_param.strip()
                    if db_sql_param.startswith('[') and db_sql_param.endswith(']'):
                        # 移除首尾的方括号
                        param_str = db_sql_param[1:-1]
                        # 分割参数
                        param_parts = param_str.split(',')
                        # 处理参数
                        params = []
                        for part in param_parts:
                            part = part.strip()
                            if part == '':
                                params.append(None)  # 空值转为None
                            elif part.isdigit():
                                params.append(int(part))  # 数字转为整数
                            else:
                                params.append(part)  # 其他保持字符串
                    else:
                        raise ValueError("参数格式不正确")
                except Exception as e:
                    # 参数解析失败时，仍然记录这个数据库操作
                    database_operations.append({
                        'db_type': db_type,
                        'db_instance': db_instance,
                        'db_sql': db_sql,
                        'full_sql': f"SQL参数解析失败：{db_sql_param}，解析错误：{str(e)}",
                        'service_name': service_name,
                        'error': True
                    })
                    continue

                # 替换SQL中的问号
                full_sql = db_sql
                for param in params:
                    # 处理空值
                    if param is None:
                        param_str = "''"
                    # 处理非空值
                    else:
                        param_str = f"'{param}'"  # 字符串类型加引号
                    full_sql = full_sql.replace('?', param_str, 1)

                # 将数据库操作信息添加到列表中
                database_operations.append({
                    'db_type': db_type,
                    'db_instance': db_instance,
                    'db_sql': db_sql,
                    'full_sql': full_sql,
                    'service_name': service_name,
                    'error': False
                })

        # 如果没有找到任何数据库操作
        if not database_operations:
            return f"在traceid: {traceid} 中未找到数据库相关的SQL语句"

        # 格式化输出所有数据库操作
        result_content = _format_database_operations(database_operations, traceid)
        result = clean_sql(result_content)
        logger.info(f"SkyWalking SQL查询结果: {result}")
        return result

    except requests.exceptions.RequestException as e:
        return f"请求SkyWalking服务失败: {str(e)}"
    except Exception as e:
        return f"处理SkyWalking数据时发生错误: {str(e)}"
