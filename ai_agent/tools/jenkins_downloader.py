import os
import shutil
import requests
from tqdm import tqdm
from pathlib import Path
import django
# 设置 Django 环境（关键配置）
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hydee_auto_server.settings')  # 注意这里是项目目录名
django.setup()  # 初始化Django
from django.conf import settings
from rest_framework.response import Response
from django.http import FileResponse, JsonResponse
from ai_agent.agents.tool_manager import tool
from rest_framework.decorators import action
from core.utils import ResponseUtils


# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()


@tool(
    name="下载小程序代码",
    description="根据输入的项目名称project_name,下载Jenkins工作空间里的小程序代码文件",
    require_auth=False,
    permission=None,
    need_llm_process=False  # 工具返回的结果是否需要LLM处理
)
def download_jenkins_workspace_files(project_name):
    """小程序包下载"""
    download_dir = Path(settings.JENKINS_CONFIG['LOCAL_DOWNLOAD_DIR']) / project_name
    try:
        # 1. 准备下载路径
        zip_path = download_dir / "dist.zip"

        # 清理旧文件
        if download_dir.exists():
            shutil.rmtree(download_dir)
        download_dir.mkdir(parents=True)

        # 2. 下载文件
        url = f"{settings.JENKINS_CONFIG['JENKINS_URL']}/job/{project_name}/ws/dist/*zip*/dist.zip"
        auth = (settings.JENKINS_CONFIG['JENKINS_USER'], settings.JENKINS_CONFIG['JENKINS_API_TOKEN'])

        with requests.get(url, stream=True, auth=auth) as r:
            r.raise_for_status()
            total_size = int(r.headers.get('content-length', 0))

            with open(zip_path, 'wb') as f, tqdm(
                    total=total_size, unit='B', unit_scale=True,
                    desc="下载进度", ncols=80
            ) as bar:
                for chunk in r.iter_content(chunk_size=8192):
                    f.write(chunk)
                    bar.update(len(chunk))

        # 3. 返回结果
        return  "下载成功，download_url:" f'/download-specific/?project_name={project_name}'

    except Exception as e:
        logger.error(f"下载失败: {e}")
        if 'download_dir' in locals():
            shutil.rmtree(download_dir, ignore_errors=True)
        return "下载失败, 错误信息:", str(e)

@action(methods=['GET'], detail=False)
def download_specific_file(request):
    """
    从本地下载小程序包，调用改方法后，自动下载小程序包
    示例：/api/download-specific/?project_name=smart-bi-miniprogram-mp
    project_name ：要下载的小程序包的项目名称 ，比如随心看project_name=smart-bi-miniprogram-mp，药事云：project_name=pharmacy-mp-ui
    """
    project_name = request.GET.get('project_name')
    if not project_name:
        return ResponseUtils.return_fail("参数异常")

    try:
        # 通过project_name 拼接需要下载的文件路径
        file_path = Path(settings.JENKINS_CONFIG['LOCAL_DOWNLOAD_DIR']) / project_name / "dist.zip"
        # allowed_root是项目的基目录；拼接 "download" 文件夹，表示只允许从该项目下的 download 目录中下载文件；
        allowed_root = Path(settings.BASE_DIR) / "download"
        #  判断文件路径是否在允许的根目录下，判断解析后的文件路径是否在允许的根目录内。若不在，则返回错误响应“非法路径访问”。
        file_path_str = str(file_path.resolve())
        allowed_root_str = str(allowed_root.resolve())
        if os.path.commonpath([file_path_str, allowed_root_str]) != allowed_root_str:
            return ResponseUtils.return_fail("非法路径访问")

        # 判断文件是否存在，若不存在，则返回错误响应“文件不存在”。
        if not file_path.exists():
            return ResponseUtils.return_fail("文件不存在")

        #  返回文件响应  download_name 表示下载的文件名，默认为文件名，若指定，则使用指定的文件名。
        download_name = "dist.zip"
        return FileResponse(
            open(file_path, 'rb'),  # 打开文件并以二进制模式读取
            filename=download_name,  # 指定下载时的文件名
            as_attachment=True,  # 表明应将文件作为附件下载
            headers={
                'Content-Type': 'application/zip',  # 设置响应的Content-Type为application/zip
                'Cache-Control': 'no-store'  # 设置缓存控制头，指示浏览器不存储此响
            }
        )

    except Exception as e:
        return ResponseUtils.return_fail("参数异常", e)


if __name__ == '__main__':
     download_jenkins_workspace_files("pharmacy-mp-ui")