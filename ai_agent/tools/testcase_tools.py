# testcase_tools.py
import os
import re

import django

# 设置 Django 环境（关键配置）
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hydee_auto_server.settings')  # 注意这里是项目目录名
django.setup()  # 初始化Django
from langchain_core.documents import Document
from langchain_chroma import Chroma
from openai import OpenAI
import json
from typing import Dict, Any, List
import PyPDF2
from ai_agent.agents.tool_manager import tool
from ai_agent.services.llm_service import LLMService
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from django.conf import settings

from typing import List
from langchain.embeddings.base import Embeddings

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()

# 初始化LLM服务
llm_service = LLMService()
# 定义Chroma数据库存储路径和名称
CHROMA_DB_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                             "chroma_db/test_cases")
COLLECTION_NAME = "test_cases"


# 读取PDF文件内容
def read_pdf(file_path: str) -> str:
    """读取PDF文件内容"""
    with open(file_path, 'rb') as file:
        reader = PyPDF2.PdfReader(file)
        text = ''
        for page in reader.pages:
            text += page.extract_text()
    return text


# 获取需求分析内容
def get_prd_content(prd_path: str = None) -> str:
    """获取需求分析内容"""
    # 如果指定了PRD路径，直接读取
    if prd_path and os.path.exists(prd_path):
        if prd_path.endswith('.pdf'):
            return read_pdf(prd_path)
        with open(prd_path, 'r', encoding='utf-8') as f:
            return f.read()

    # 否则从默认data目录查找
    base_dir = r'./data'
    print(f"当前工作目录: {os.getcwd()}")
    if not os.path.exists(base_dir):
        return "请提供PRD文档路径或将文档放入data目录"

    for file in os.listdir(base_dir):
        file_path = os.path.join(base_dir, file)
        if file.endswith('.pdf'):
            return read_pdf(file_path)
        elif file.endswith('.txt'):
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()

    return "未找到PRD文档，请提供文档路径或将文档放入data目录"


# 生成需求分析
def get_analyze_prd(content: str = None) -> Dict[str, Any]:
    prompt_template = """

      # 角色与任务
    你是一位资深需求分析师，现在需要基于提供的产品需求文档(PRD)生成一份简洁、结构化的需求分析报告。

    # 输出要求
    1. 仅输出与需求分析直接相关的内容
    2. 严格按照以下结构输出，不要添加额外章节
    3. 每个分析维度必须基于PRD中的具体内容，不要臆测
    4. 保持分析简洁明了，避免冗余

    # 分析维度（必须按此顺序输出）
    ## 1. 功能需求分析
    ### 1.1 核心业务流程
    - **主流程**：描述端到端完整业务流程，标明关键决策点和状态转换
    - **分支流程**：识别重要分支场景（正常分支、异常分支、回退流程）
    - **流程节点**：列出关键的输入节点、处理节点、输出节点和存储节点

    ### 1.2 功能模块拆解
    - **核心模块**：列出主要功能模块及其职责边界
    - **输入输出**：每个模块的关键输入参数和输出结果
    - **模块接口**：模块间的调用关系和数据传递方式

    ### 1.3 业务规则提取
    - **核心规则**：影响业务流程的关键规则和约束条件
    - **验证规则**：数据验证、业务验证的具体要求
    - **计算逻辑**：涉及的重要计算和处理逻辑


    ## 2. 用户体验需求分析
    - 交互流程流畅性要求
    - 异常提示友好性要求
    - 角色权限差异

    ## 3. 异常场景分析
    - 输入异常处理
    - 操作异常处理
    - 外部依赖异常处理
    - PRD未明确的隐性异常场景

    ## 4. 兼容性需求分析
    - PRD明确的兼容范围

    ## 5. 安全性需求分析
    - 数据安全要求
    - 权限控制要求
    - 业务安全要求

    ## 6. 性能需求分析
    - PRD明确的性能指标
    - 高并发场景要求

    # 执行约束
    - 仅基于提供的PRD内容进行分析，不要假设未提及的需求
    - 如PRD中某维度信息不足，请标注"PRD中未提供相关信息"，不要臆测
    - 确保分析与PRD高度相关，避免生成通用性内容
    - 输出格式必须结构化，便于阅读和理解
    - 不要包含任何测试策略、测试用例或测试建模相关内容

    # 内容控制要求
    - **每个分析维度限制3-5个要点，每个要点1-2句话表达核心内容**
    - **使用要点式表达，避免冗长段落描述**
    - **突出关键信息，去除重复和冗余内容**
    - **重点关注对测试用例设计有指导意义的内容**


    产品PRD：{context}

    """
    prompt = ChatPromptTemplate.from_template(prompt_template)

    rag_chain = (prompt | llm_service.llm | StrOutputParser())
    output_content = rag_chain.invoke({"context": content, "question": "请根据上下文内容分析需求"})
    return output_content


@tool(
    name="生成测试用例",
    description="根据PRD文档生成测试用例内容",
    require_auth=False,
    permission=None
)
def generate_testcases(prd_path: str = None, content: str = None) -> Dict[str, Any]:
    """
    根据PRD文档生成测试用例内容

    Args:
        prd_path: PRD文档路径（可选）
        content: 需求内容（可选）

    Returns:
        {
            "status": "success|error",
            "message": "操作结果描述",
            "test_cases": List[Dict]  # 生成的测试用例内容
        }
    """
    try:

        # 获取PRD内容
        if content:
            context = content
        else:
            context = get_prd_content(prd_path)

        if "未找到PRD文档" in context or "请提供PRD文档" in context:
            return {"status": "error", "message": context}

        print(context)
        # 构造提示词
        prompt = """你是一位资深测试专家，请根据以下PRD内容生成测试用例：
                要求：
                1. 测试用例设计要求：
                   - 必须包含以下测试类型，且每次生成的用例数不少于10条：
                     * 正向测试用例（占80%）：验证功能正常流程
                     * 异常测试用例（占15%）：验证错误处理、边界值、无效输入
                     * 安全测试用例（占5%）：验证权限控制、数据安全等
                   - 每个测试用例必须包含以下结构化字段：
                     * 需求名称：七位数字的编号+具体需求名称
                     * 系统：抓取所属系统名称[系统名]
                     * 模块：抓取模块名称[模块名]
                     * 场景：[模块名]+[测试场景]+[预期状态]
                     * 前提：列出执行前的系统状态、数据准备和环境要求
                     * 步骤：用有序数组描述，每个步骤包含操作和预期中间结果,并且每个步骤都标有序号,每个步骤之间需要换行,如1、
                     * 预期结果：必须可验证，包含系统响应、界面变化、数据变更
                     * 关键词：关键词提取，如无则默认取值模块名称
                     
                2. 测试覆盖要求：
                   - 必须覆盖PRD中所有需求项（标注对应需求ID）
                   - 包含正常业务流程和备选业务流程
                   - 包含界面交互、数据验证、业务规则验证
                   - 考虑不同用户角色（如有）的权限差异
                   
                3.测试用例设计原则：
                   - 覆盖性：覆盖功能需求、边界条件、异常场景。
                    
                   - 可维护性：用例清晰、模块化，便于后期更新。
                    
                   - 独立性：每个用例聚焦单一功能点，避免依赖。
                    
                   - 可重复性：用例在不同环境下执行结果一致
                   
                4.功能测试场景设计要求：
                
                正向场景：
                
                    设计最小化成功路径用例（包含所有必填项有效数据）
                    场景化测试方法：此方法为主要方法，其余方法为辅；
                        
                        定义：模拟用户实际使用流程（如用户从登录到下单的全流程）。
                        
                        适用场景：端到端业务流程验证（电商购物、银行转账）。
                        
                        示例：用户登录→搜索商品→加入购物车→支付→查看订单状态。
                        
                        用户故事测试（User Story Testing）
                        
                        定义：根据用户故事（User Story）的验收标准设计用例。
                        
                        适用场景：敏捷开发中的功能验收。
                        
                        示例：用户故事：“作为用户，我希望可以重置密码”，测试用例包括：
                        
                        输入正确邮箱→接收重置链接→修改密码成功。
                        
                        输入错误邮箱→提示“邮箱不存在”
                    
                    判定表（多条件组合的复杂业务逻辑（如优惠券使用规则），示例：电商优惠券使用条件（是否登录、是否过期、金额是否满足），组合生成测试用例。

                    设计边界值有效用例（最大值/最小值/临界值,每个选取3-5个代表值测试）
                    
                    设计等价类划分（定义：将输入数据划分为有效等价类（合法输入）和无效等价类（非法输入），每个等价类选取3-5个代表值测试）
                                        
                    状态迁移法（适用场景：状态驱动的系统（订单系统、工作流系统，示例：订单状态从“待支付”到“已支付”需触发支付接口，从“已支付”到“已发货”需校验物流信息。))
                    
                    设计特殊字符兼容用例（如姓名包含特殊符号的情况）
                
                异常场景：
                
                    必填项缺失验证（逐个遗漏必填字段）
                    
                    无效格式验证（错误数据类型/格式错误/超出范围）
                    
                    并发冲突测试（如同时提交相同票据）
                    
                    失败事务回滚验证（支付失败后的数据一致性）
                    
                    状态转换测试：
                    
                    列出所有可能的状态机变迁路径（如订单状态：待支付→已支付→发货中→已完成）
                    
                    验证非法状态跳转的拦截机制（如从"已完成"直接修改为"待支付"）
                
                5. 输出规范要求：
                   - 严格遵循JSON数组格式
                   - 字段名称使用中文（与需求文档保持一致）
                   - 每个用例为独立JSON对象
                   - 禁止包含注释、解释文本或代码标记
                   - 禁止有多余换行和空格
                   - 布尔值使用true/false
                   - 空值使用null
                6. 以严格JSON数组格式返回无多余换行或注释**
                7. 不要包含```json或```标记

                PRD内容：
                {context}

                返回示例：
                [{{
                  "用例编号":"TC-LOGIN-001",
                  "系统":"商户后台",
                  "模块":"会员管理",
                  "场景":"登录模块-正确凭证登录-成功",
                  "前提":"有资源权限",
                  "步骤":[
                    "1. 访问/login页面，响应码200",
                    "2. 输入用户名testuser，输入框显示*******",
                    "3. 输入密码Test@123，显示为密文",
                    "4. 勾选记住登录选项",
                    "5. 点击登录按钮"
                  ],
                  "预期结果":[
                    "1. 跳转到/dashboard页面",
                    "2. 响应头包含auth-token",
                    "3. 本地存储记住登录标记为true",
                    "4. 用户菜单显示欢迎testuser"
                  ]
                   "关键字":"会员登录", 
                }}]
                """.format(context=context)

        # 调用LLM服务
        messages = [
            {"role": "system", "content": "你是一个专业的测试工程师"},
            {"role": "user", "content": prompt}
        ]

        try:
            # 1. 获取原始响应
            raw_response = llm_service.chat_completion(messages)
            logger.info(f'原始响应 (类型: {type(raw_response)}): {raw_response}')

            if not raw_response:
                logger.error('LLM服务返回空响应')
                return {"status": "error", "message": "LLM 返回空响应"}

            # 2. 统一响应处理逻辑
            if isinstance(raw_response, str):
                # 情况A：LLM返回的是字符串（需提取JSON）
                cleaned_str = clean_json_response(raw_response)
                try:
                    test_cases = json.loads(cleaned_str)
                except json.JSONDecodeError as e:
                    logger.error(f'JSON解析失败 (内容: {cleaned_str}): {str(e)}')
                    return {"status": "error", "message": "测试用例格式解析失败"}
            else:
                # 情况B：LLM直接返回Python对象（如List/Dict）
                test_cases = raw_response

            # 3. 验证数据格式
            if not isinstance(test_cases, list):
                logger.error(f'响应不是列表 (类型: {type(test_cases)})')
                return {"status": "error", "message": "测试用例应为JSON数组"}

            # 4. 过滤有效用例
            valid_cases = [
                case for case in test_cases
                if isinstance(case, dict) and all(case.values())
            ]

            if not valid_cases:
                logger.error('无有效测试用例')
                return {"status": "error", "message": "未能生成有效的测试用例"}

            return {
                "status": "success",
                "total": len(valid_cases),
                "test_cases": valid_cases
            }

        except Exception as e:
            logger.error(f'处理失败: {str(e)}', exc_info=True)
            return {"status": "error", "message": f"系统错误: {str(e)}"}
    except json.JSONDecodeError:
        return {"status": "error", "message": "测试用例格式解析失败"}
    except Exception as e:
        return {"status": "error", "message": f"生成测试用例时出错: {str(e)}"}


# 提取有效的JSON部分
def clean_json_response(response: str) -> str:
    """安全提取JSON内容（兼容Markdown/不规则格式）"""
    try:
        # 尝试直接解析（应对标准JSON）
        return json.dumps(json.loads(response), ensure_ascii=False)
    except json.JSONDecodeError:
        # 提取最长的 {...} 或 [...] 块
        json_candidates = re.findall(
            r'(?s)(\[.*?\]|\{.*?\})',
            response.replace('\n', '')
        )
        if json_candidates:
            for candidate in sorted(json_candidates, key=len, reverse=True):
                try:
                    return json.dumps(json.loads(candidate), ensure_ascii=False)
                except json.JSONDecodeError:
                    continue
        return response  # 保底返回原始内容


class AliyunEmbeddings(Embeddings):
    # 初始化向量化模型
    def __init__(self, model_name="text-embedding-v4", dimensions=1024):
        self.model_name = model_name
        self.dimensions = dimensions
        # 初始化OpenAI客户端，使用通义的兼容接口
        self.client = OpenAI(
            api_key=settings.LLM_CONFIG["EMB_API_KEY"],
            base_url=settings.LLM_CONFIG["EMB_BASE_URL"]
        )
        logger.info(f"初始化通义向量化模型: {model_name}, 维度: {dimensions}")

    # 批量文本向量化
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """批量文本向量化"""
        try:
            response = self.client.embeddings.create(
                model=self.model_name,
                input=texts,
                dimensions=self.dimensions,
                encoding_format="float"
            )
            # 提取向量数据
            embeddings = [item.embedding for item in response.data]
            return embeddings
        except Exception as e:
            logger.error(f"通义向量化API调用失败: {str(e)}")
            raise ValueError(f"向量化失败: {str(e)}")

    # 单条文本向量化
    def embed_query(self, text: str) -> List[float]:
        """单条文本向量化"""
        return self.embed_documents([text])[0]


# 将测试用例向量化并存储到Chroma数据库
def vectorize_test_cases(test_cases):
    """
    将测试用例向量化并存储到Chroma数据库

    接收测试用例结果集，将每个测试用例的文本内容（包括测试场景、步骤和预期结果）转换为向量，并存储到Chroma向量数据库中。
    为了避免一次处理过多数据导致内存问题，采用批量处理的方式。

    Args:
        test_cases (QuerySet): 测试用例对象，每个对象应包含case_scene、
                          test_steps和expected_result属性

    Returns:
        Dict: 包含处理状态和消息的字典，格式为:
              {"status": "success|error", "message": "处理结果描述"}

    Raises:
        Exception: 向量化过程中的任何错误都会被捕获并记录，但不会中断处理
        {"status": "success", "message": "成功向量化 120 条测试用例"}
    """

    # 初始化通义向量化模型
    emb = AliyunEmbeddings()

    # # 从数据库查询测试用例
    # test_cases = NormTestCase.objects.filter(is_active=True)
    # logger.info(f"找到 {len(test_cases)} 条测试用例")

    # 准备文档列表
    chunks = []
    for case in test_cases:
        # 合并测试场景、步骤和预期结果作为向量化文本
        text_content = f"测试场景: {case.case_scene}\n测试步骤: {case.test_steps}\n预期结果: {case.expected_result}"
        chunks.append(
            Document(
                # 核心文本内容
                page_content=text_content,
                # 附加信息,不参与向量化计算,仅用于后续的筛选和展示
                metadata={
                    "test_case_id": case.id,
                    "case_scene": case.case_scene,
                    "test_step": case.test_steps,
                    "expected_result": case.expected_result
                }
            )
        )

    # 批量处理，避免一次处理过多数据,通义text-embedding-v3模型,一次最多支持批量处理10条
    batch_size = 10
    total_data = len(chunks)

    # 如果目录不存在，创建目录
    os.makedirs(CHROMA_DB_DIR, exist_ok=True)

    # 分批处理
    for start in range(0, total_data, batch_size):
        end = min(start + batch_size, total_data)
        logger.info(f"处理批次: {start} 到 {end}")

        try:
            Chroma.from_documents(
                documents=chunks[start:end],
                persist_directory=CHROMA_DB_DIR,
                collection_name=COLLECTION_NAME,
                embedding=emb
            )
            logger.info(f"成功向量化并存储 {end - start} 条测试用例")
        except Exception as e:
            logger.error(f"向量化过程中出错: {str(e)}")

    logger.info(f"向量化完成，共处理 {total_data} 条测试用例")
    return {"status": "success", "message": f"成功向量化 {total_data} 条测试用例"}


# 查询与输入文本相似的测试用例
def query_test_cases(query_text: str):
    """
    查询与输入文本相似的测试用例

    该函数使用通义向量化模型将查询文本转换为向量，然后在Chroma向量数据库中
    查找最相似的测试用例文档。它利用语义相似度而非关键词匹配，能够找到
    表达方式不同但含义相近的测试用例。

    Args:
        query_text (str): 查询文本，可以是测试场景描述、测试步骤或预期结果

    Returns:
            List[int]: 返回匹配到的测试用例ID列表

    Raises:
        ValueError: 如果向量化过程失败
        Exception: 如果数据库查询失败
    """
    # 初始化通义向量化模型
    emb = AliyunEmbeddings()

    vectorstore = Chroma(
        persist_directory=CHROMA_DB_DIR,
        collection_name=COLLECTION_NAME,
        embedding_function=emb
    )
    # 统计向量数据库中有多少条数据
    count = vectorstore._collection.count()
    logger.info(f"当前向量数据库中共有 {count} 条测试用例数据")
    # 执行相似度搜索，返回前5个最相似的文档
    results = vectorstore.similarity_search(query_text, k=5)
    case_list = []

    logger.info("\n最相似的测试用例:")
    for i, doc in enumerate(results):
        logger.info(f"\n排名: {i + 1}")
        logger.info(f"测试用例ID: {doc.metadata.get('test_case_id')}")
        logger.info(f"测试场景: {doc.metadata.get('case_scene')}")
        logger.info(f"测试步骤: {doc.metadata.get('test_step')}")
        logger.info(f"预期结果: {doc.metadata.get('expected_result')}")
        logger.info("-" * 50)
        case_list.append(doc.metadata.get('test_case_id'))
    logger.info(case_list)
    return case_list


def ai_merge_case(content: str = None) -> Dict[str, Any]:
    prompt_template = """
        # 角色
        你是一位资深的测试专家，在软件测试领域经验丰富，能够分析两个用例之间的关联，并通过合并操作构建高聚合度的测试场景。
        任务目标：分析两个测试用例的关联性，合并为一个完整用例
        
        输入格式要求：
        请提供以下两个用例的完整信息 {context}
        处理逻辑要求：
        1.关联性分析维度：
        --检查场景(case_scene)是否存在上下游关系
        --比对前提条件(premise)是否互补或冲突
        --分析测试步骤(test_steps)是否存在：
            可合并的连续操作
            相同操作的不同参数
            前置条件步骤与主流程步骤
        --验证预期结果(expected_result)是否：
            存在递进关系
            覆盖不同验证维度
        2.合并优先级规则：
        --保留更具体的场景描述
        --合并前提条件时用序号标记
        --按操作时间线排序测试步骤
        --去重后合并预期结果
        3.输出格式要求,此点非常重要,如果你输出的格式错误将会受到非常严重的惩罚:
        -- 严格遵循JSON数组格式
           - 禁止包含注释、解释文本或代码标记
           - 禁止有多余换行和空格
           - 布尔值使用true/false
           - 空值使用null
        -- 以严格JSON数组格式返回无多余换行或注释**
        -- 只需返回一个场景的用例，不要用列表形式
        -- 不要包含```json或```标记
        示例:
        {{
          "case_scene": "登录模块-凭证验证-成功登录与记住密码",
          "premise": "1.有资源权限,2.记住密码功能可用",
          "test_steps": "1. 访问/login页面，响应码200,2. 输入用户名，输入框显示*******,3. 输入密码，显示为密文,4. 勾选记住登录选项,5. 点击登录按钮",
          "expected_result": "1. 跳转到/dashboard页面,2. 响应头包含auth-token,3. 本地存储记住登录标记为true,4. 用户菜单显示欢迎"
        }}
    """

    prompt = ChatPromptTemplate.from_template(prompt_template)
    rag_chain = (prompt | llm_service.llm | StrOutputParser())
    response = rag_chain.invoke({"context": content})
    logger.info(f"用例合并结果: {response}")
    return response

