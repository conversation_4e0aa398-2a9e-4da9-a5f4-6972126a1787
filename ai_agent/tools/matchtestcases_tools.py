# 标准库导入
from typing import Dict, List, Optional, Tuple,Union
from datetime import datetime
import numpy as np
import json

# 第三方库导入
from sklearn.metrics.pairwise import cosine_similarity
from sentence_transformers import SentenceTransformer
from pydantic import BaseModel,Field
from ai_agent.agents.tool_manager import tool

# ------------------- 系统提示词模板 -------------------
AI_PROMPT_TEMPLATE = """
# 角色
你是一名专业的高级测试工程师，擅长将当前迭代的核心测试用例与历史用例进行整合。

# 核心工作原则
1. 优先级策略：
   - 第一优先级：业务->模块->场景->关键词的精确匹配
   - 第二优先级：语义相似度>0.7的用例
   - 时间权重：优先匹配最近3个月内的活跃用例

2. 用例整合标准：
   /case_merge_criteria/
   - 相同业务线且模块匹配度≥80%
   - 关键步骤重叠率≥60%
   - 预期结果无冲突

3. 输出规范：
   - 必须包含匹配依据说明
   - 差异点需明确标注
   - 保留原始用例关键信息
"""


# ------------------- 数据模型定义 -------------------
class NormTestCase(BaseModel):
    """
    标准测试用例池表模型
    """
    id: Optional[int] = Field(None, description='自增主键')
    business_line: str = Field(..., max_length=2, description='所属业务线')
    module: str = Field(..., max_length=50, description='所属模块')
    keywords: str = Field(...,alias="keywords",max_length=50, description='关键字')
    requirement: Optional[str] = Field(None, max_length=1000, description='需求标题')
    case_scene: str = Field(..., alias="scenario", max_length=500, description='场景名称')
    premise: Optional[str] = Field(None, max_length=500, description='前置条件')
    test_steps: str = Field(..., description='操作步骤')
    expected_result: str = Field(..., max_length=500, description='预期结果')
    iteration_name: Optional[str] = Field(None, max_length=500, description='迭代名称')
    case_scene_id: Optional[str] = Field(None, max_length=100, description='自动化场景ID')
    creator: str = Field(..., max_length=50, description='创建人')
    create_time: datetime = Field(default_factory=datetime.now, description='创建时间')
    modifier: Optional[str] = Field(None, max_length=50, description='修改人')
    modify_time: datetime = Field(default_factory=datetime.now, description='修改时间')
    class Config:
        populate_by_name = True
        allow_population_by_field_name = True

class MergeIntermediate(BaseModel):
    """
    用例合并中间表模型
    """
    id: Optional[int] = Field(None, description='主键ID')
    business_line: Optional[str] = Field(None, max_length=20, description='所属业务线')
    module: Optional[str] = Field(None, max_length=50, description='所属模块')
    keywords: Optional[str] = Field(None, max_length=1000, description='关键字')
    requirement: Optional[str] = Field(None, max_length=500, description='需求标题')
    case_scene: Optional[str] = Field(None, max_length=500, description='场景名称')
    premise: Optional[str] = Field(None, description='前置条件')
    test_steps: Optional[str] = Field(None, description='操作步骤')
    expected: Optional[str] = Field(None, description='预期结果')
    iteration_name: Optional[str] = Field(None, max_length=100, description='迭代名称')
    creator: Optional[str] = Field(None, max_length=50, description='创建人')
    create_time: Optional[datetime] = Field(None, description='创建时间')
    normtestcase_id: int = Field(..., description='原始用例池ID')
    is_delete: bool = Field(False, description='是否删除')


class CaseMapping(BaseModel):
    """
    优化后的用例匹配表模型
    """
    id: Optional[int] = Field(None, description='主键ID')
    normtestcase_id: int = Field(..., description='标准用例池ID')
    case_id: str = Field(..., max_length=50, description='迭代核心用例ID')
    mapping_type: str = Field('auto', max_length=20, description='关联类型(auto/manual)')
    relation_type: str = Field(..., max_length=20, description='关系类型(merge/obsolete/refactor)')
    created_time: datetime = Field(default_factory=datetime.now, description='创建时间')
    status: str = Field('active', max_length=20, description='映射状态')
    operation_log: Optional[str] = Field(None, max_length=500, description='操作日志')


# ------------------- AI语义分析组件 -------------------
class AIAnalyzer:
    """
    AI语义分析引擎
    功能：
    - 文本特征向量化
    - 语义相似度计算
    模型：paraphrase-multilingual-MiniLM-L12-v2（多语言轻量模型）
    """

    def __init__(self):
        """
        初始化语义分析模型
        模型特点：
        - 生成768维语义向量
        - 支持中英文混合文本
        - 内存占用约500MB
        """
        self.model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
        self.case_embeddings: Dict[str, np.ndarray] = {}  # 用例向量缓存

    def get_embedding(self, text: str) -> np.ndarray:
        """
        生成文本语义向量
        Args:
            text: 输入文本（支持中英文，建议长度50-500字）
        Returns:
            768维语义向量（numpy数组）
        """
        return self.model.encode(text, convert_to_tensor=False)


# ------------------- 用例管理核心类 -------------------
class CaseManager:
    """
    测试用例基础管理类
    功能：
    - 用例存储管理
    - 基础相似度计算
    - 用例合并/废弃操作
    """

    def __init__(self):
        """初始化用例存储池和相关日志"""
        self.norm_case_pool = self._load_sample_cases()  # 加载示例数据
        self.norm_case_pool: List[NormTestCase] = []  # 标准用例池
        self.merge_records: List[MergeIntermediate] = []  # 合并中间表
        self.mapping_records: List[CaseMapping] = []  # 匹配关系表
        self.analyzer = AIAnalyzer()  # AI分析器实例

    def _load_sample_cases(self):
        """加载示例测试用例（实际应连接数据库）"""
        return [
            NormTestCase(
                id=1001,
                business_line="02",
                module="O2O订单核销",
                keywords="支付回调,区块链验证",
                case_scene="支付成功后的自动化核销流程",
                test_steps="1. 监听支付回调\n2. 验证区块链信息...",
                expected_result="生成核销凭证",
                creator="system"
            ),
        ]

    def _map_fields(self, source: Union[NormTestCase, dict]) -> Dict:
        """字段映射处理器"""
        field_mapping = {
            'business': 'business_line',
            'module': 'module',
            'keywords': lambda x: ','.join(x) if isinstance(x, list) else x,
            'scenario': 'case_scene',
            'steps': 'test_steps',
            'expected': 'expected_result'
        }

        mapped = {}
        for target_field, source_field in field_mapping.items():
            if isinstance(source_field, str):
                mapped[target_field] = getattr(source, source_field, None)
            elif callable(source_field):
                mapped[target_field] = source_field(getattr(source, target_field, None))
        return mapped

    def _calculate_similarity(self, target: NormTestCase, candidate: NormTestCase) -> float:
        """
        基于规则的相似度计算（0-1范围）
        评分策略：
        - 业务模块匹配（40%+30%）
        - 关键词重合度（20%）
        - 场景文本匹配（10%）
        """
        target_keywords = target.keywords.split(',')
        candidate_keywords = candidate.keywords.split(',')
        score = 0.0

        # 业务模块匹配（共70%）
        if target.business_line  == candidate.business_line:
            score += 0.4  # 业务线基础分
            if target.module == candidate.module:
                score += 0.3  # 模块匹配附加分

        # 关键词匹配度（20%）
        common  = set(target.keywords) & set(candidate.keywords)
        score += 0.2 * (len(common ) / max(len(target.keywords), 1))  # 防止除零

        # 场景描述匹配（10%）
        if target.case_scene  and candidate.case_scene:
            if target.case_scene  == candidate.case_scene:
                score += 0.1  # 完全匹配
            elif target.case_scene  in candidate.case_scene  or candidate.case_scene  in target.case_scene :
                score += 0.05  # 部分包含

        return round(min(score, 1.0), 2)  # 确保不超过1


class EnhancedCaseManager(CaseManager):
    """
    增强版用例管理器（AI驱动）
    新增功能：
    - 混合相似度计算（规则+语义）
    - 智能合并建议生成
    - 提示词驱动的决策流程
    """

    def _enhanced_similarity(self, target: NormTestCase, candidate: NormTestCase) -> Tuple[float, dict]:
        """
        增强版相似度计算（应用提示词策略）
        综合评分 = 规则相似度(60%) + 场景语义(30%) + 步骤语义(10%) + 时间权重(0-0.1)
        """
        # 基础规则计算
        base_score = super()._calculate_similarity(target, candidate)

        # 语义相似度计算
        scenario_sim = cosine_similarity(
            [self.analyzer.get_embedding(target.case_scene)],
            [self.analyzer.get_embedding(candidate.case_scene)]
        )[0][0]

        steps_sim = cosine_similarity(
            [self.analyzer.get_embedding(target.test_steps)],
            [self.analyzer.get_embedding(candidate.test_steps)]
        )[0][0]

        # 应用提示词中的时间权重策略
        time_weight = 0.1 if (datetime.now() - candidate.create_time).days < 90 else 0

        # 综合加权计算
        enhanced_score = (
                base_score * 0.6  # 规则权重（保证可解释性）
                + scenario_sim * 0.3  # 场景语义权重
                + steps_sim * 0.1  # 步骤语义权重
                + time_weight  # 时间衰减因子
        )

        return round(enhanced_score, 2), {
            "structure_similarity": base_score,
            "scenario_semantic": round(scenario_sim, 2),
            "steps_semantic": round(steps_sim, 2),
            "time_weight": time_weight,
            "combined_score": round(enhanced_score, 2)
        }

    def ai_identify_candidates(self, target_case: NormTestCase, threshold: float = 0.7) -> List[dict]:
        """
        AI驱动的候选用例识别
        Args:
            target_case: 目标用例对象
            threshold: 相似度阈值（默认0.7）
        Returns:
            排序后的候选列表，包含：
            - case: 用例对象
            - score: 综合得分
            - analysis: 详细指标
            - match_reason: 可解释的匹配原因
        """
        candidates = []
        for case in self.norm_case_pool:
            # 过滤非活跃用例和自身
            if case.id == target_case.id:
                continue

            # 计算增强相似度
            score, analysis = self._enhanced_similarity(target_case, case)

            if score >= threshold:
                candidates.append({
                    "case": case,
                    "score": score,
                    "analysis": analysis,
                    "match_reason": self._generate_match_reason(target_case, case, analysis)
                })

        # 按综合得分降序排列
        return sorted(candidates, key=lambda x: -x['score'])

    def _generate_match_reason(self, target: NormTestCase, candidate: NormTestCase, analysis: dict) -> str:
        """
        生成自然语言解释（遵循提示词规范）
        策略：规则匹配优先，语义相似补充
        """
        reasons = []

        # 规则匹配部分
        if target.business == candidate.business:
            reasons.append(f"同业务域【{target.business}】")
            if target.module == candidate.module:
                reasons.append(f"同模块【{target.module}】")

        common_keywords = set(target.keywords) & set(candidate.keywords)
        if common_keywords:
            reasons.append(f"共有关键词：{', '.join(common_keywords)}")

        # 语义补充说明
        if analysis['scenario_semantic'] > 0.8:
            reasons.append("场景描述高度相似")
        elif analysis['scenario_semantic'] > 0.6:
            reasons.append("场景描述部分相似")

        # 时间因素提示
        if analysis['time_weight'] > 0:
            reasons.append("近期活跃用例")

        return "；".join(reasons) if reasons else "综合相似度匹配"

    def generate_merge_suggestion(self, target_case: NormTestCase, top_n: int = 3) -> dict:
        """
        生成智能合并建议（符合提示词规范）
        Returns:
            {
                "system_prompt": 使用的提示词,
                "target_case": 目标用例信息,
                "suggestions": 候选建议列表,
                "recommended_action": 最终操作建议
            }
        """
        candidates = self.ai_identify_candidates(target_case)

        suggestions = []
        for candidate in candidates[:top_n]:
            suggestions.append({
                "candidate_id": candidate['case'].case_id,
                "similarity_score": candidate['score'],
                "merge_strategy": self._recommend_strategy(target_case, candidate['case']),
                "confidence": self._calculate_confidence(candidate['score']),
                "reason": candidate['match_reason'],
                "analysis_details": candidate['analysis']
            })

        return {
            "system_prompt": AI_PROMPT_TEMPLATE,
            "target_case": target_case.dict(),
            "suggestions": suggestions,
            "recommended_action": self._recommend_action(suggestions)
        }

    def _recommend_strategy(self, target: NormTestCase, candidate: NormTestCase) -> str:
        """
        推荐合并策略（遵循提示词标准）
        Business Rules:
        - 匹配字段≥2时建议更新字段
        - 否则保留原用例
        """
        field_match = sum([
            target.business == candidate.business,
            target.module == candidate.module,
            len(set(target.keywords) & set(candidate.keywords)) > 0
        ])
        return "update_fields" if field_match >= 2 else "preserve_original"

    def _calculate_confidence(self, score: float) -> str:
        """置信度分级（高/中/低）"""
        if score >= 0.9:
            return "high"
        if score >= 0.7:
            return "medium"
        return "low"

    def _recommend_action(self, suggestions: list) -> str:
        """生成最终操作建议（符合提示词决策逻辑）"""
        if not suggestions:
            return "建议新增用例（无相似候选）"

        if any(s['confidence'] == 'high' for s in suggestions):
            top_case = max(suggestions, key=lambda x: x['similarity_score'])
            return f"建议合并用例：{top_case['candidate_id']}（置信度：高）"

        return "建议人工复核候选用例"

    class EnhancedCaseManager(CaseManager):
        def generate_top_matches(self, target_case: NormTestCase, top_n: int = 5) -> dict:
            """生成前N个匹配用例（阈值调整为0.4以获取更多候选）"""
            candidates = self.ai_identify_candidates(target_case, threshold=0.4)

            # 添加分数过滤和排序保障
            filtered = sorted([c for c in candidates if c['score'] > 0.4],
                              key=lambda x: -x['score'])[:top_n]

            return {
                "match_results": {
                    "target_case": self._format_case(target_case),
                    "top_matches": [self._format_match(c, idx)
                                    for idx, c in enumerate(filtered, 1)],
                    "statistics": self._generate_stats(filtered)
                }
            }

        def _format_case(self, case: NormTestCase) -> dict:
            """格式化目标用例信息"""
            return {
                "id": case.id,
                "business_line": case.business_line,
                "module": case.module,
                "scenario_abbr": case.case_scene[:150] + "..."
            }

        def _format_match(self, candidate: dict, rank: int) -> dict:
            """格式化匹配结果"""
            case = candidate['case']
            return {
                "rank": rank,
                "id": case.id,
                "similarity": candidate['score'],
                "module": case.module,
                "key_steps": case.test_steps[:200] + "...",
                "match_reasons": candidate['match_reason'].split('；'),
                "last_update": case.modify_time.strftime("%Y-%m-%d %H:%M")
            }

        def _generate_stats(self, candidates: list) -> dict:
            """生成统计信息"""
            return {
                "total_matches": len(candidates),
                "average_score": round(sum(c['score'] for c in candidates) / len(candidates), 2),
                "score_distribution": {
                    "0.8+": sum(1 for c in candidates if c['score'] >= 0.8),
                    "0.6-0.8": sum(1 for c in candidates if 0.6 <= c['score'] < 0.8),
                    "<0.6": sum(1 for c in candidates if c['score'] < 0.6)
                }
            }

# ------------------- 工具函数 -------------------
@tool(
    name="handle_testcase_matching",
    description="测试用例匹配与整合操作入口",
    args_schema={
        "type": "object",
        "properties": {
            "business_line": {"type": "string", "alias": "业务线"},
            "module": {"type": "string", "alias": "模块"},
            "keywords": {"type": "string", "alias": "关键词"},
            "scenario": {"type": "string", "alias": ["测试场景", "案例场景"]},  # 添加多别名支持
            "steps": {"type": "string", "alias": "测试步骤"},
            "expected_result": {"type": "string", "alias": "预期结果"},
            "creator": {"type": "string", "alias": "创建者"},
            "operation": {"type": "string", "alias": ["操作类型", "operation_type"]}
        },
        "required": ["business_line", "module", "keywords", "scenario", "operation"],
        "_position_args": {
            "type": "array",
            "description": "位置参数依次为：业务线, 模块, 关键词, 场景, 操作类型",
            "items": {"type": "string"}
        }
    }
)
def convert_parameter_names(params: dict) -> dict:
    """将中文参数名转换为英文参数名"""
    name_mapping = {
        '业务线': 'business_line',
        '模块': 'module',
        '功能': 'function',
        '场景': 'scenarios',
        '关键词': 'keywords',
        '测试场景': 'scenario',
        '操作类型': 'operation'
    }
    return {name_mapping.get(k, k): v for k, v in params.items()}
def handle_case_operations(**kwargs) -> Dict:
    try:
        # 参数标准化处理
        processed = {
            "business_line": str(kwargs.get('business_line', '')).strip(),
            "module": kwargs.get('module', ''),
            "keywords": ','.join(kwargs['keywords']) if isinstance(kwargs.get('keywords'), list)
                      else kwargs.get('keywords', ''),
            "scenario": kwargs.get('scenario', ''),
            "operation": kwargs.get('operation', 'ai_analyze').lower()
        }

        # 必填字段验证
        required_fields = ['business_line', 'module', 'keywords', 'scenario']
        if any(not processed[f] for f in required_fields):
            missing = [f for f in required_fields if not processed[f]]
            raise ValueError(f"缺失必填字段: {', '.join(missing)}")

        # 构建测试用例对象
        test_case = NormTestCase(
            business_line=processed['business_line'],
            module=processed['module'],
            keywords=processed['keywords'],
            case_scene=processed['scenario'],  # 使用别名映射
            test_steps=kwargs.get('steps', ''),
            expected_result=kwargs.get('expected_result', ''),
            creator=kwargs.get('creator', 'system_auto')
        )

        # 执行匹配分析
        manager = EnhancedCaseManager()
        if processed['operation'] == 'ai_analyze':
            result = manager.generate_top_matches(test_case)
            return {
                "status": "success",
                "data": result
            }

        return {"status": "error", "message": "不支持的操作类型"}

    except Exception as e:
        return {
            "status": "error",
            "message": f"处理失败: {str(e)}",
            "solution": "请检查参数格式是否符合要求"
        }


    # 参数预处理管道
    def process_arguments(args):
        processed = {}

        # 处理位置参数（兼容旧版传参方式）
        if 'args' in args and isinstance(args['args'], list):
            args_list = args.pop('args')

            # 定义位置参数映射顺序
            position_mapping = [
                'business_line',  # 第0位
                'module',  # 第1位
                'keywords',  # 第2位
                'scenario',  # 第3位
                'operation'  # 第4位
            ]

            # 将位置参数转换为字典
            for idx, value in enumerate(args_list):
                if idx < len(position_mapping):
                    field = position_mapping[idx]
                    processed[field] = str(value)

        # 合并关键字参数
        processed.update(args)
        return processed

    try:
        # 执行参数处理管道
        processed_args = process_arguments(kwargs.copy())

        # 必填字段校验
        required_fields = ['business_line', 'module', 'keywords', 'scenario', 'operation']
        missing = [f for f in required_fields if f not in processed_args]
        if missing:
            cn_names = {
                'business_line': '业务线',
                'module': '模块',
                'keywords': '关键词',
                'scenario': '测试场景',
                'operation': '操作类型'
            }
            raise ValueError(f"缺少必填字段: {', '.join([cn_names[f] for f in missing])}")

        # 构建用例对象
        case_data = {
            "business_line": processed_args['business_line'],
            "module": processed_args['module'],
            "keywords": processed_args['keywords'],
            "scenario": processed_args['scenario'],  # 直接使用别名
            "test_steps": json.dumps([processed_args.get('steps', '')]),
            "expected_result": processed_args.get('expected_result', ''),
            "creator": processed_args.get('creator', 'system')
        }

        current_case_obj = NormTestCase(**case_data)
        manager = EnhancedCaseManager()

        if processed_args.get('operation') == "ai_analyze":
            analysis = manager.generate_merge_suggestion(current_case_obj)
            return {"status": "success", "data": analysis}

        return {"status": "success", "data": {}}

    except Exception as e:
        return {
            "status": "error",
            "message": f"参数处理失败: {str(e)}",
            "solution": [
                "请使用以下参数结构（不要嵌套任何层级）:",
                {
                    "business_line": "业务线编码（字符串）",
                    "module": "模块名称",
                    "keywords": "逗号分隔的关键词",
                    "scenario": "测试场景描述",
                    "operation": "操作类型（ai_analyze）"
                }
            ]
        }

    def _apply_prompt_constraints(analysis: dict) -> dict:
        """解析提示词中的业务规则"""
        return {
            "priority_rules": AI_PROMPT_TEMPLATE.split("优先级策略：")[1].split("\n2.")[0].strip(),
            "merge_criteria": AI_PROMPT_TEMPLATE.split("/case_merge_criteria/")[1].split("\n3.")[0].strip()
        }

    try:
        # 构造参数对象
        case_data = {
            "business_line": business_line,
            "module": module,
            "keywords": keywords,
            "case_scene": scenario,  # 字段别名处理
            "test_steps": steps,
            "expected_result": expected_result,
            "creator": creator
        }
        current_case_obj = NormTestCase(**case_data)

    except Exception as e:
        return {
            "status": "error",
            "message": f"用例构建失败：{str(e)}",
            "constraints": {}
        }

    manager = EnhancedCaseManager()

    try:
        if operation == "ai_analyze":
            analysis = manager.generate_merge_suggestion(current_case_obj)
            return {
                "status": "success",
                "data": analysis,
                "constraints": _apply_prompt_constraints(analysis)
            }
        # 其他操作处理逻辑...
        else:
            return {
                "status": "error",
                "message": "不支持的操作类型",
                "constraints": _apply_prompt_constraints({})
            }

    except Exception as e:
        return {
            "status": "error",
            "message": f"操作失败：{str(e)}",
            "constraints": _apply_prompt_constraints({})
        }

    def _apply_prompt_constraints(analysis: dict) -> dict:
        """解析提示词中的业务规则"""
        return {
            "priority_rules": AI_PROMPT_TEMPLATE.split("优先级策略：")[1].split("\n2.")[0].strip(),
            "merge_criteria": AI_PROMPT_TEMPLATE.split("/case_merge_criteria/")[1].split("\n3.")[0].strip()
        }

    manager = EnhancedCaseManager()
    try:
        current_case_obj = NormTestCase(**current_case)

        if operation == "ai_analyze":
            analysis = manager.generate_merge_suggestion(current_case_obj)
            return {
                "status": "success",
                "data": analysis,
                "constraints": _apply_prompt_constraints(analysis)
            }
        # 其他操作处理逻辑...

    except Exception as e:
        return {
            "status": "error",
            "message": f"操作失败：{str(e)}",
            "constraints": _apply_prompt_constraints({})
        }


def generate_visual_report(analysis: dict) -> str:
    """
    生成Markdown格式可视化报告
    结构：
    - 系统提示词摘要
    - 核心建议
    - 相似用例矩阵
    - 详细分析数据
    """
    report = [
        "# 测试用例智能分析报告",
        "## 系统配置",
        f"```prompt\n{AI_PROMPT_TEMPLATE}\n```",
        "## 核心建议",
        f"**{analysis['recommended_action']}**"
    ]

    if analysis['suggestions']:
        report.append("## 相似用例推荐")
        for idx, s in enumerate(analysis['suggestions'], 1):
            report.extend([
                f"### 候选 {idx}",
                f"- **ID**: {s['candidate_id']}",
                f"- **综合评分**: {s['similarity_score']:.2f}",
                f"- **置信度**: {s['confidence'].upper()}",
                f"- **合并策略**: `{s['merge_strategy']}`",
                f"- **匹配原因**: {s['reason']}",
                "```analysis",
                json.dumps(s['analysis_details'], indent=2, ensure_ascii=False),
                "```"
            ])

    report.append("## 原始数据")
    report.append(f"```json\n{json.dumps(analysis, indent=2, ensure_ascii=False)}\n```")

    return "\n".join(report)