import os
import django
import requests
from ai_agent.agents.tool_manager import tool
from datetime import datetime, timedelta
from base64 import b64encode

# 设置 Django 环境（关键配置）
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hydee_auto_server.settings')  # 注意这里是项目目录名
django.setup()  # 初始化Django

# Hops配置
HOPS_BASE_URL = "http://hops.pt.hydee.cn"
# 用户密码通过通过base64编码加密得到认证密文，跳过登录验证
HOPS_USER = 'Basic MzE4NDpZdWUxOTk1MTAxOQ=='

# 所有服务名称
service_list = ["h3-sharding-config", "h3-basic-setting", "hydee-api-gateway", "hydee-freedom-gateway",
                "basedata-manager-server", "hydee-data-center-api-gateway", "h3-operate-service", "h3-pay-finance",
                "upgrade-platform-cloud", "hydee-seal", "hydee-middle-sdp", "hydee-middle-touch-center",
                "hydee-data-center-report", "hydee-prescription-api", "hydee-cache-center",
                "hydee-data-center-business", "hydee-middle-payment", "h3-ware", "ydjia-merchant-platform",
                "hydee-medipurchase-platform", "ydjia-merchant-manager", "hydee-middle-third",
                "hydee-middle-account-center", "h3-sql-engine", "hydee-middle-member-data", "h3-report-base",
                "hydee-ewx-erp", "h3-sms-server", "middle-datasync-message", "oauth", "hydee-business-order-ext",
                "ybcloud-monitor-server", "basedata-monitor", "hydee-middle-goods", "hydee-middle-data-sync",
                "hydee-xxl-job", "h3-pay-core", "ydjia-merchant-customer", "hydee-business-order",
                "hydee-business-order-platform-portal", "h3-gateway", "hydee-data-center-transmit",
                "hydee-middle-baseinfo", "hydee-mobile-order", "hydee-middle-member", "ydjia-adaptation",
                "hydee-mobile-reexamine", "hydee-middle-order", "ydjia-operate", "hydee-middle-syncerp",
                "hydee-middle-merchandise", "erp-monitor-server", "hydee-middle-market", "hydee-data-center-store",
                "hydee-invoice-helper", "hydee-sp-platform", "businesses-gateway", "ydjia-merchant-promote",
                "ydjia-srm-goods", "hydee-data-center-smart-model", "hydee-ewx-honey", "hydee-es-sync",
                "hydee-freedom-sale", "hydee-middle-balance", "hydee-data-center-sale",
                "hydee-business-order-b2c-third", "ydjia-srm-bills", "hydee-im-server", "hydee-message-server",
                "hydee-business-order-web", "hydee-ewx-service", "middle-id", "hydee-medipurchase-order",
                "hydee-business-order", "hydee-business-order-web", "hydee-middle-reward",
                "hydee-data-center-store-extract", "ydjia-merchant-manager", "hydee-middle-payment",
                "ydjia-merchant-platform", "hydee-middle-third", "ydjia-merchant-customer",
                "hydee-business-order-platform-portal", "hydee-middle-baseinfo", "hydee-middle-member",
                "hydee-middle-merchandise", "hydee-middle-market", "hydee-sp-platform", "ydjia-merchant-promote",
                "hydee-ewx-honey"]


def get_common_headers():
    """获取通用请求头"""
    return {
        "Accept": "application/json, text/plain, */*",
        "Authorization": HOPS_USER,
        "Content-Type": "application/json",
        "Content-Length": "375",
        "Host": "hops.pt.hydee.cn"
    }


def format_exception_logs(logs):
    """格式化异常日志列表为可读的字符串"""
    # 根据hops返回的异常日志格式，设计格式化逻辑
    result_lines = []  # 创建一个空列表result_lines，用于存储格式化后的每行日志文本
    for log in logs:  # 遍历日志列表
        result_lines.append("\n")
        ts = format_timestamp(log['log_time'])
        result_lines.append(f"异常时间: {ts}")
        result_lines.append(f"服务名称: {log['service_name']}")
        log_content = log['log_content']
        lines = log_content.split('\n')[:20]  # 分割为行并取前20行
        truncated_content = '\n'.join(lines)  # 重新组合为字符串
        result_lines.append(f"异常信息: {truncated_content}")
        # result_lines.append(f"异常信息: {log['log_content']}")
        result_lines.append("")
    return "\n".join(result_lines)  # 返回结果，用换行符\n连接所有格式化后的行


def format_timestamp(ts):
    # 将时间戳转换为字符串
    ts_str = str(ts)

    # 提取各时间部分（去除最后3位）
    year = ts_str[0:4]
    month = ts_str[4:6]
    day = ts_str[6:8]
    hour = ts_str[8:10]
    minute = ts_str[10:12]
    second = ts_str[12:14]

    # 组合成目标格式
    return f"{year}-{month}-{day} {hour}:{minute}:{second}"


def parse_time_range(time_str: str = None):
    """
    解析时间范围字符串为Hops所需格式

    Args:
        time_str: 时间范围描述(如"近3天"/"最近30分钟")
                 若为None则默认返回近3天范围

    Returns:
        list[str]: ["开始时间戳", "结束时间戳"]格式的列表
    """
    now = datetime.now()

    # 处理默认值
    if not time_str:
        start_time = now - timedelta(days=3)
    else:
        # 解析相对时间
        if "近" in time_str or "最近" in time_str:
            num = int(''.join(filter(str.isdigit, time_str)))
            unit = time_str[-1]

            if unit == "天":
                delta = timedelta(days=num)
            elif unit == "小时":
                delta = timedelta(hours=num)
            elif unit == "分钟":
                delta = timedelta(minutes=num)
            else:
                raise ValueError(f"不支持的时间单位: {unit}")

            start_time = now - delta
        else:
            raise ValueError("仅支持'近X天/小时/分钟'格式")

    # 格式化为17位时间戳(14位时间+3位毫秒)
    return [
        start_time.strftime("%Y%m%d%H%M%S") + "000",
        now.strftime("%Y%m%d%H%M%S") + "000"
    ]


@tool(
    name="从hops获取异常日志",
    description="传入traceid和环境,根据traceid和环境到hops中获取相关服务的异常日志，未传环境默认test;开发环境：dev;测试环境：test;预发环境：uat;生产环境：pro",
    require_auth=False,
    permission=None,
    need_llm_process=False  # 工具返回的结果是否需要LLM处理
)
def get_exception_logs_from_hops(env: str = 'test', service_name: str = None, date_range: str = None,
                                 traceid: str = None,
                                 **kwargs):
    """
    从hops中获取异常日志

    Args:
        env : 环境默认传test（测试环境）
        service_name (str): 服务名称（可选）
        date_range(str)：查询时间范围
        traceid(str): traceID（必填）
        **kwargs: 其他参数（备用）

    Returns:
        str: 异常日志结果或错误信息
    """

    # 参数处理
    if env == '' or env == None or env == [] or env not in ('dev', 'test', 'uat', 'pro'):
        env = 'test'
    request_params = {
        "env": env,
        "service_list": service_list,
        "options": {
            "扩1": "",
            "扩2": "",
            "扩3": "",
            "扩4": "",
            "扩5": ""
        },
        "grey_option": "",
        "key_word": "",
        "link_id": traceid,
        "example_name": "",
        "log_level": "ERROR",
        "date_range": parse_time_range(date_range),  # 调用时间解析函数
        "log_class": "",
        "thread": "",
        "search_after": "",
        "ordering": "asc",
        "size": 10
    }

    # # 构建请求
    url = f"{HOPS_BASE_URL}/api/rest/jenkins/query_es_log_content"  # hops接口请求地址
    headers = get_common_headers()
    # print("请求参数:", request_params)
    # print("请求头:", headers)
    try:
        response = requests.post(url=url, headers=headers, json=request_params)
        response.raise_for_status()
        data = response.json()
        print(data)
        # 检查响应数据
        if not data.get('data') or not data['data'].get('hits') or not data['data']['hits'].get('hits'):
            return f"未找到traceid: {traceid} 对应的异常日志信息"

        # 获取返参
        hits = data.get('data', {}).get('hits', {}).get('hits', {})
        service_logs = [
            {
                'service_name': hit['_source'].get('服务名称', '未知服务'),
                'log_time': hit['_source'].get('时间', '无时间记录'),
                'log_content': hit['_source'].get('日志内容', '无内容记录')
            }
            for hit in hits
            if isinstance(hit, dict) and '_source' in hit
        ]

        print("错误日志列表:", service_logs)

        # # 获取并格式化异常日志
        formatted_logs = format_exception_logs(service_logs)
        # print(f"Hops异常日志查询结果: {formatted_logs}")
        return formatted_logs

    except requests.exceptions.RequestException as e:
        return f"请求Hops服务失败: {str(e)}"
    except Exception as e:
        return f"处理Hops数据时发生错误: {str(e)}"


if __name__ == "__main__":
    get_exception_logs_from_hops(traceid="447a6464748b4812bea655b1e3885c50.269506.17503035304820047")
