import os
from pathlib import Path
import django

from django.conf import settings

# 设置 Django 环境（关键配置）
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hydee_auto_server.settings')  # 注意这里是项目目录名
django.setup()  # 初始化Django

import smtplib
from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import Dict, Any
import requests
import json
from typing import Optional
from io import BytesIO
from django.db.models import Min, Max
from ai_agent.agents.tool_manager import tool
from ai_agent.services.llm_service import LLMService
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from test_case.models import TapdIterationsInfo, TapdRequirementInfo, TapdBugInfo
from openpyxl import load_workbook
from openpyxl.styles import Alignment
from django.db.models import Count, Case, When, Value, IntegerField

# 日志对象实例化
from core.commonUtils.logger import Logger

logger = Logger.get_logger()
WEBHOOK_MAP = settings.WEBHOOK_MAP
template_url = settings.TEMPLATE_URL
# 初始化LLM服务
llm_service = LLMService()



# 根据迭代ID查询数




def check_iteration_info(tapd_iteration_id: str):
    # 通过迭代id查询迭代名称
    iteration_name = TapdIterationsInfo.objects.filter(
        tapd_iteration_id=tapd_iteration_id
    ).values_list("iteration_name", flat=True).first()
    # 查询所有的需求名称
    requirement_names = TapdRequirementInfo.objects.filter(
        iteration_id=tapd_iteration_id
    ).values_list("requirement_name", flat=True)
    # 查询出开发人员
    developers = TapdRequirementInfo.objects.filter(
        iteration_id=tapd_iteration_id
    ).values_list("developer", flat=True)
    # 查询提测时间以及测试完成时间
    result = TapdRequirementInfo.objects.filter(
        iteration_id=tapd_iteration_id
    ).aggregate(
        earliest_submit=Min('submit_test_time'),  # 最早提测时间
        latest_completed=Max('completed_test_time')  # 最晚测试完成时间
    )
    
    # 统计bug数
    # 定义状态分类条件
    OPEN_STATUS = ['new', 'in_progress', 'resolved', 'unconfirmed', 'rejected', 'suspended', 'feedback', 'reopened']
    CLOSED_STATUS = ['closed','rejected']
    all_severities = ['fatal', 'serious', 'normal', 'prompt', 'advice']

    # 获取所有符合条件的bug记录
    bugs = TapdBugInfo.objects.filter(iteration_id=tapd_iteration_id)

    # 按严重级别统计总数
    severity_stats = bugs.values('severity').annotate(
        total_count=Count('id'),
        open_count=Count(
            Case(
                When(status__in=OPEN_STATUS, then=1),
                output_field=IntegerField()
            )
        ),
        closed_count=Count(
            Case(
                When(status__in=CLOSED_STATUS, then=1),
                output_field=IntegerField()
            )
        )
    ).order_by('severity')
    print(severity_stats)
    # 初始化 bug_result 字典，所有计数设为 0
    bug_result = {severity: {'total': 0, 'open': 0, 'closed': 0} for severity in all_severities}

    for item in severity_stats:
        severity = item['severity']
        bug_result[severity]['total'] = item['total_count']
        bug_result[severity]['open'] = item['open_count']
        bug_result[severity]['closed'] = item['closed_count']

    bug_result = json.dumps(bug_result, indent=4, ensure_ascii=False)

    print(bug_result)

    # 提取结果
    earliest_submit_time = result['earliest_submit']
    latest_completed_time = result['latest_completed']
    return iteration_name,requirement_names, developers, earliest_submit_time, latest_completed_time,bug_result


# 对所有的需求标题进行总结为测试范围
def requirement_summary(content: str = None) -> Dict[str, Any]:
    prompt_template = """
           # 角色
           你是一位资深的测试专家，在软件测试领域经验丰富，能够分析两个用例之间的关联，并通过合并操作构建高聚合度的测试场景。
           任务目标：将每一个需求去掉和功能无关的描述，精简总结，输出一份测试范围
           
           输入格式要求：
           请提供以下迭代需求标题的完整信息 {context}
           处理逻辑要求：
           1、简化要求：
           - 去掉空格
           - 去掉【】
           - 去掉客户信息，例如：【510273贵州一品】
           - 要保留模块已经功能的描述
           - 总结后的话语清晰易懂
           3.输出格式要求:
           - 禁止包含注释、解释文本或代码标记
           - 禁止有多余换行和空格
           - 给需求排序，第一个需求前面开头1.XXX ,第二个需求则为 2.xxx
           
           示例：
            1、B2C订单相关页面每页显示条数支持记忆
            2、待发商品统计增加商品的生产企业
            3、B2C订单处理：售后单待处理界面，支持增加备注并在界面展示
            4、商品库的商品可以支持批量修改商品分类
            5、商品库：批量修改商品分组功能优化
    """
    prompt = ChatPromptTemplate.from_template(prompt_template)
    rag_chain = (prompt | llm_service.llm | StrOutputParser())
    response = rag_chain.invoke({"context": content})
    return response


# 生成测试报告文件
@tool(
    name="生成测试报告",
    description="通过迭代id:tapd_iteration_id自动生成测试报告并且发送邮件给发出指令的人员，然后通过业务线:business_line找到企微群通知迭代测试完成可上预发",
    require_auth=False,
    permission=None,
    need_llm_process=False
)
def generate_report(tapd_iteration_id: str = None, business_line: str = None,email: str = None):
    business_line = business_line
    user_email = email
    # === 查询测试报告需要的数据内容 ===
    iteration = check_iteration_info(tapd_iteration_id)
    if not iteration or len(iteration) < 6:
        raise ValueError("获取迭代数据不完整")
    iteration_name, requirement_names, developers, starttime, endtime,  bug_result = iteration[:6]
    logger.info('迭代相关信息查询完成')

    # 将所有需求排序放入一个文本
    requirement_text = ""
    for i, req in enumerate(requirement_names, start=1):
        requirement_text += f"{i}、{req}\n"

    # 加载excel模板
    # 1. 从URL下载模板文件
    try:
        with open(template_url, "rb") as f:
            excel_data = BytesIO(f.read())
        wb = load_workbook(excel_data)
        ws = wb.active

    # 设置全局单元格居中对齐
        for row in ws.iter_rows():
          for cell in row:
            cell.alignment = Alignment(horizontal='center', vertical='center')

        # === 写入新数据）===
        ws['E1'] = str(iteration_name)  # 写入迭代名称
        ws.merge_cells('E1:F1')  # 合并EF列
        ws['B2'] = str(starttime)  # 开始时间
        ws.merge_cells('B2:C2')
        ws['E2'] = str(endtime)  # 完成时间
        ws.merge_cells('E2:F2')
        ws['B6'] = requirement_text  # 填充需求
        ws.merge_cells('B6:F6')
        # 设置自动换行
        ws['B6'].alignment = Alignment(
            horizontal='left',
            vertical='center',
            wrap_text=True  # 关键：允许文本换行
        )
        # 填充bug数
        row_mapping = {
            "fatal": 15,
            "serious": 16,
            "normal": 17,
            "prompt": 18,
            "advice": 19
        }
        if isinstance(bug_result, str):
            try:
                bug_result = json.loads(bug_result)
            except json.JSONDecodeError:
                return "生成报告失败: bug_result 不是有效的 JSON"
        # 写入数据
        for category, values in bug_result.items():
            row = row_mapping[category]
            ws[f'C{row}'] = values['total']
            ws[f'D{row}'] = values['closed']
            ws[f'E{row}'] = values['open']
        # bug合计
        ws['C20'] = "=SUM(C15:C19)"
        ws['D20'] = "=SUM(D15:D19)"
        ws['E20'] = "=SUM(E15:E19)"
        # ===  保存输出文件 ===
        current_dir = Path(__file__).parent
        upload_dir = current_dir / "uploads"
        upload_dir.mkdir(exist_ok=True, parents=True)
        output_path = upload_dir / "生成的测试报告.xlsx"
        wb.save(output_path)
        logger.info(f"报告已生成: {excel_data}")

    except Exception as e:
      logger.error(f"生成报告失败: {str(e)}")
      raise

# === 发送邮件 ===
    try:
        send_email(output_path,user_email,iteration_name)
    except Exception as e:
            logger.error(f"报告生成成功，但邮件发送失败: {str(e)}")
            raise
    # 发送企微通知
    pushTestMessage(iteration_name, business_line)
    return f"测试报告生成成功: {output_path}"



def send_email(attachment_path: str,  email_to: str = None,iteration_name: str = None):
    email_to = email_to or ''
    my_addr = '<EMAIL>'
    my_pwd = 'QdeJ8eS9KcBA72Df'
    msg = MIMEMultipart()
    msg['Subject'] = f"【测试报告】- {iteration_name} 迭代测试完成通知"
    msg['From'] = '海典自动化测试平台<%s>' % my_addr
    msg['To'] = ("<EMAIL>;")
    to_list = [email_to]
    # 固定邮件正文（按您要求的格式）
    email_body = """Dear all:

        本次迭代测试完成，可以发布预发，请产品及时验收并回复邮件"""
    msg.attach(MIMEText(email_body, 'plain'))
    # 添加附件
    with open(attachment_path, "rb") as f:
        attach = MIMEApplication(f.read(), _subtype="xlsx")
        attach.add_header('Content-Disposition', 'attachment',
                          filename=os.path.basename(attachment_path))
        msg.attach(attach)

    try:
        logger.info(f'开始发送测试报告邮件')
        with smtplib.SMTP_SSL("smtp.exmail.qq.com", 465) as server:
            server.login(my_addr, my_pwd)
            logger.info(f'发送邮件列表: {to_list}')
            server.sendmail(my_addr, to_list, msg.as_string())
            logger.info('成功发送邮件')
    except Exception as e:
        logger.error(f'发送邮件失败原因：{e}')
        raise e

def pushTestMessage(iteration_name: str,  business_line: str):
    headers = {'Content-Type': 'application/json'}

    # 根据业务线选择地址（默认使用第一个地址）
    address = WEBHOOK_MAP.get(business_line, next(iter(WEBHOOK_MAP.values())))
    message = {
        "msgtype": "text",
        "text": {
            "content": f"@所有人\n{iteration_name}迭代测试完成，可以合并代码上预发了，"
                       f"发布完成后通知测试及产品进行验收",
            "mentioned_mobile_list": ["@all"]  # @所有人功能
        }
    }
    response = requests.post(
        url=address,
        headers=headers,
        data=json.dumps(message)
    )
    if response.status_code == 200 and response.json().get("errcode") == 0:
        logger.info(f"企微消息推送成功：{iteration_name}")
        return True
    else:
        logger.info(f"企微消息推送失败：{response.text}")
        return False


# if __name__ == "__main__":
#     generate_report(tapd_iteration_id='1161969829001002033', business_line="中台",email="<EMAIL>")
