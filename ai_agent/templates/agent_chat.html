<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>AI助手</title>
    <style>
        #chat-container { width: 800px; margin: 0 auto; }
        #chat-history {
            height: 500px; overflow-y: auto;
            border: 1px solid #ddd; padding: 15px;
            margin-bottom: 15px; background: #f9f9f9;
        }
        .message {
            margin: 10px 0; padding: 10px;
            border-radius: 5px; max-width: 80%;
            white-space: pre-wrap; /* 保留换行 */
        }
        .user-message {
            background: #e3f2fd; margin-left: 20%;
        }
        .bot-message {
            background: #f0f0f0; margin-right: 20%;
        }
        #input-area { display: flex; margin-top: 15px; }
        #message-input {
            flex: 1; padding: 10px;
            border: 1px solid #ddd; border-radius: 4px;
        }
        #send-button {
            margin-left: 10px; padding: 0 20px;
            background: #4CAF50; color: white;
            border: none; border-radius: 4px; cursor: pointer;
        }
        #typing-indicator {
            display: none; color: #888; font-style: italic;
            margin: 10px 0; padding: 5px;
        }
        .tool-info {
            color: #666; font-size: 0.9em;
            margin: 5px 0; padding-left: 10px;
        }
    </style>
</head>
<body>
    <div id="chat-container">
        <h2>AI助手</h2>
        <div id="chat-history"></div>
        <div id="typing-indicator">助手正在输入...</div>
        <div id="input-area">
            <input type="text" id="message-input" placeholder="输入消息..." autofocus>
            <button id="send-button">发送</button>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const chatHistory = document.getElementById('chat-history');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        const typingIndicator = document.getElementById('typing-indicator');
        let currentBotMessageDiv = null;
        let isStreaming = false;

        // 添加消息到聊天历史
        function addMessage(role, content) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}-message`;

            const roleName = role === 'user' ? '您' : '助手';
            messageDiv.innerHTML = `<strong>${roleName}:</strong> <span class="content">${content}</span>`;

            chatHistory.appendChild(messageDiv);
            chatHistory.scrollTop = chatHistory.scrollHeight;

            if (role === 'bot') {
                currentBotMessageDiv = messageDiv.querySelector('.content');
            }

            return messageDiv;
        }

        // 发送消息到服务器
        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || isStreaming) return;

            // 添加用户消息
            addMessage('user', message);
            messageInput.value = '';
            sendButton.disabled = true;
            isStreaming = true;

            // 显示"正在输入"指示器
            typingIndicator.style.display = 'block';

            // 创建新的bot消息容器
            addMessage('bot', '');

            const formData = new FormData();
            formData.append('message', message);

            // 使用EventSource代替fetch
            const eventSource = new EventSource(`/api/ai-agent/chat/?message=${encodeURIComponent(message)}`);

            eventSource.onmessage = function(e) {
                if (e.data === '{}' && e.event === 'complete') {
                    // 流结束
                    eventSource.close();
                    typingIndicator.style.display = 'none';
                    sendButton.disabled = false;
                    isStreaming = false;
                    return;
                }

                try {
                    const data = JSON.parse(e.data);
                    if (data.text) {
                        currentBotMessageDiv.textContent += data.text;
                        chatHistory.scrollTop = chatHistory.scrollHeight;
                    }
                    if (data.tool_used) {
                        const toolDiv = document.createElement('div');
                        toolDiv.className = 'tool-info';
                        toolDiv.textContent = `[使用工具: ${data.tool_used}]`;
                        chatHistory.appendChild(toolDiv);
                        chatHistory.scrollTop = chatHistory.scrollHeight;
                    }
                } catch (error) {
                    console.error('解析错误:', error);
                }
            };

            eventSource.onerror = function() {
                if (currentBotMessageDiv.textContent.trim() === '') {
                    currentBotMessageDiv.innerHTML = `<span style="color:red">连接出错，请重试</span>`;
                }
                eventSource.close();
                typingIndicator.style.display = 'none';
                sendButton.disabled = false;
                isStreaming = false;
            };
        }

        // 事件监听
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') sendMessage();
        });

        // 初始消息
        addMessage('bot', '您好！我是AI助手，可以帮您：\n1. 触发构建\n2. 查询日志\n3. 生成测试用例\n\n快来提问吧！');
    });
    </script>
</body>
</html>