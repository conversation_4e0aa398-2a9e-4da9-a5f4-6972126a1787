from django.http import JsonResponse, StreamingHttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.shortcuts import render
from .services.llm_service import LLMService
from .agents.tool_manager import default_tool_manager
from .agents.base_agent import BaseAgent
import json

from base.models import UserInfo


# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()

# 初始化服务
llm = LLMService()
default_tool_manager.auto_register()
agent = BaseAgent(llm, default_tool_manager)


@csrf_exempt
def chat_api(request):
    if request.method not in ('POST', 'GET'):
        return JsonResponse({'error': '仅支持POST和GET请求'}, status=405)

    # 从POST或GET中获取message参数
    message = json.loads(request.body)['message'] if request.method == 'POST' else request.GET.get('message')
    user_id = json.loads(request.body)['userId'] if request.method == 'POST' else request.GET.get('userId')

    if not message or not message.strip():
        return JsonResponse({'error': '消息不能为空'}, status=400)

    if not hasattr(request, 'session') or not request.session.session_key:
        request.session.create()

    session_id = request.session.session_key
    if not user_id:
        user = 'api'
    else:
        user = UserInfo.objects.get(user_id=user_id)

    def event_stream():
        try:
            for chunk in agent.process(
                    user_input=message,
                    session_id=session_id,
                    user=user,
                    stream=True
            ):
                # 在每个chunk中添加status字段
                chunk['status'] = 200
                yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
            yield "event: complete\ndata: {}\n\n"
        except Exception as e:
            logger.exception("流式处理崩溃")
            yield f"data: {json.dumps({'error': str(e), 'status': 500}, ensure_ascii=False)}\n\n"

    return StreamingHttpResponse(
        event_stream(),
        content_type='text/event-stream',
        headers={'X-Accel-Buffering': 'no'}
    )


def chat_view(request):
    """渲染聊天页面"""
    return render(request, 'agent_chat.html')
