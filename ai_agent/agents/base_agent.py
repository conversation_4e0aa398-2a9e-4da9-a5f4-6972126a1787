import inspect
from typing import Dict, Any, List, Optional, Union, Iterator
from django.contrib.auth.models import User
from ..services.llm_service import LLMService
from ..agents.tool_manager import ToolManager, Tool
from langchain.memory import ConversationBufferMemory
from langchain.agents import AgentExecutor, create_tool_calling_agent
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser, StrOutputParser
import json
# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()



class BaseAgent:
    """
    智能体基类
    功能：具备意图识别、输入扩写、记忆管理和流式输出能力的AI助手
    """

    # 系统级提示词模板
    SYSTEM_PROMPT = """你是一个有帮助的AI助手，可以访问以下工具:
    {tools}

    当用户询问你的身份时，请这样回答：
    "您好！我是AI助手，可以帮您[列出主要功能]。请问有什么可以帮您的？"

    请根据用户需求选择合适的工具，如果没有合适工具，直接回答用户问题。
    在回答问题时，请参考历史对话内容，提供更准确的回答。"""

    # 意图分析提示词模板
    INTENT_PROMPT = """你是一个专业的意图分析器，负责判断用户是否需要调用工具。
    当前可用工具：{tools}

    请严格按以下规则分析：
    1. 必须返回有效的JSON对象，否则会受到惩罚
    2. 如果用户询问工具列表或能做什么，设置special_response
    3. 如果明确需要工具，设置is_tool_call=true并提供工具名
    4. 调用工具时必须提供所有必要参数，从用户输入的中获取，不可凭空臆想，如果缺少参数，请向用户询问具体信息
    5. 当工具执行失败时，请不要自动生成错误消息，直接返回原始错误信息
    6. 当询问我是谁时，则结合memory去回复，其他情况直接对话
    7. 回复尽量简洁，并拟人化,可以适当使用一些emoji
    8. 重要：parameters字段格式示例：{{"kwargs": {{{{"traceid": "12345"}}}}}}"""

    # 输入扩写提示词模板
    EXTEND_PROMPT = """
    # 角色
    你是一位意图改写专家，擅长将用户的自然语言问题进行补充和改写，以提升大模型对意图识别的准确性。

    ## 技能
    ### 技能1：解析用户指令
    - **任务**：理解用户输入的自然语言指令，并将其改写为更具体、更明确的表达形式。

    ### 技能2：生成改写后的意图
    - 根据用户输入的内容，补充必要的信息，确保意图清晰且易于被大模型理解。
    - 示例：
        - 输入："构建服务"
          输出："帮我调用你的工具，构建相关服务"
        - 输入："V1.3测试报告"
          输出："帮我生成迭代编号为V1.3的测试报告"
        - 输入："测试环境获取2342342394209错误日志"
          输出："获取测试环境，TraceID为2342342394209的错误日志"


    ### 技能3：保持语义一致性
    - 在改写过程中，确保不改变用户输入的原始语义。
    - 如果需要补充信息，可以通过合理的假设或默认值来完成。

    ### 输出示例
    - 输入："创建一个视频播放列表"
      输出："请帮我创建一个包含多个视频的播放列表"
    - 输入："查询天气"
      输出："查询当前所在城市的实时天气情况"

    ## 限制
    - 只处理与意图改写相关的任务。
    - 输出内容必须是完整的句子，且语义清晰。
    - 不需要输出其他思考过程，直接返回改写后的结果。

    ### 用户输入
    {user_input}

    ### 改写后的意图
    """

    def __init__(self, llm_service: LLMService, tool_manager: ToolManager):
        """
        初始化智能体
        :param llm_service: LLM服务实例
        :param tool_manager: 工具管理实例
        """
        self.llm_service = llm_service  # 大语言模型服务
        self.tool_manager = tool_manager  # 工具管理器
        self.sessions: Dict[str, Dict] = {}  # 会话存储 {session_id: {memory: ConversationBufferMemory}}

    def _get_session_memory(self, session_id: str) -> ConversationBufferMemory:
        """
        获取或创建会话记忆
        :param session_id: 会话ID
        :return: 该会话的记忆对象
        """
        if session_id not in self.sessions:
            # 创建新的会话记忆
            self.sessions[session_id] = {
                "memory": ConversationBufferMemory(
                    memory_key="chat_history",  # 记忆键名
                    return_messages=True,  # 返回消息列表
                    output_key="output"  # 输出键名
                )
            }
        return self.sessions[session_id]["memory"]

    def extend_input(self, user_input: str) -> str:
        """
        扩写用户输入以提升意图识别准确率
        :param user_input: 原始用户输入
        :return: 扩写后的用户输入
        """
        # 构建输入扩写处理链
        chain = (
                ChatPromptTemplate.from_template(self.EXTEND_PROMPT)  # 加载提示词模板
                | self.llm_service.llm  # 连接大模型
                | StrOutputParser()  # 使用字符串输出解析器
        )
        return chain.invoke({"user_input": user_input})

    def _create_agent_executor(self, session_id: str, user: User) -> AgentExecutor:
        """
        创建带工具和记忆的Agent执行器
        :param session_id: 会话ID
        :param user: 用户对象
        :return: Agent执行器实例
        """
        # 获取会话记忆
        memory = self._get_session_memory(session_id)
        print("当前记忆内容:", memory.load_memory_variables({}))
        # 准备可用工具
        tools = self._prepare_tools(user)

        # 构建Agent提示词
        prompt = ChatPromptTemplate.from_messages([
            ("system", self.SYSTEM_PROMPT.format(
                tools="\n".join(f"{t.name}: {t.description}" for t in tools)  # 工具列表格式化
            )),
            ("placeholder", "{chat_history}"),  # 聊天历史占位符
            ("human", "{input}"),  # 用户输入占位符
            ("placeholder", "{agent_scratchpad}")  # Agent草稿占位符
        ])

        # 创建工具调用Agent
        agent = create_tool_calling_agent(
            llm=self.llm_service.llm,  # 大语言模型
            prompt=prompt,  # 提示词模板
            tools=tools  # 可用工具
        )

        # 返回Agent执行器
        return AgentExecutor(
            agent=agent,  # Agent实例
            tools=tools,  # 工具列表
            memory=memory,  # 记忆对象
            verbose=True,  # 详细日志
            handle_parsing_errors=True,  # 处理解析错误
            return_intermediate_steps=True  # 返回中间步骤
        )

    def _prepare_tools(self, user: User) -> List[Tool]:
        """
        准备带用户上下文环境的工具列表
        :param user: 用户对象
        :return: 工具列表
        """
        # 获取用户可用的工具
        available_tools = self.tool_manager.get_available_tools(user).values()

        def create_tool_func(tool_name):
            """创建工具调用函数闭包"""

            def tool_func(**kwargs):
                """实际工具调用函数"""
                # 统一参数格式处理
                params = self._normalize_tool_params(kwargs)
                return self._execute_tool(tool_name, params, user)  # 执行工具

            return tool_func

        # 生成工具列表
        return [
            Tool(
                name=tool.name,  # 工具名称
                description=tool.description,  # 工具描述
                func=create_tool_func(tool.name)  # 工具调用函数
            )
            for tool in available_tools  # 遍历可用工具
        ]

    def _normalize_tool_params(self, params: Union[str, dict]) -> dict:
        """
        统一工具参数格式，确保无论输入格式如何都能正确提取参数
        支持的参数格式：
        1. 字符串（JSON或普通文本）
        2. {'kwargs': {'traceid': 'xxx'}}
        3. {'args': {'traceid': 'xxx'}}
        4. {'args': [{'traceid': 'xxx'}]}
        5. {'parameters': {'traceid': 'xxx'}}
        6. {'traceid': 'xxx'}

        :param params: 原始参数（字符串或字典）
        :return: 标准化后的参数字典
        """
        # 处理字符串参数
        if isinstance(params, str):
            try:
                params = json.loads(params)  # 尝试解析JSON字符串
            except json.JSONDecodeError:
                return {"input": params}  # 失败则作为普通输入

        # 处理非字典参数
        if not isinstance(params, dict):
            return {"input": str(params)}  # 非字典参数转为字典

        # 如果参数中包含 'kwargs' 键，优先使用其值
        if 'kwargs' in params:
            kwargs_value = params['kwargs']
            if isinstance(kwargs_value, dict):
                return kwargs_value
            elif isinstance(kwargs_value, list):
                return self._extract_dict_from_list(kwargs_value)

        # 如果参数中包含 'args' 键，使用其值
        if 'args' in params:
            args_value = params['args']
            if isinstance(args_value, dict):
                return args_value
            elif isinstance(args_value, list):
                # 处理 args 为列表的情况
                return self._map_positional_args(args_value)

        # 如果参数中包含 'parameters' 键，使用其值
        if 'parameters' in params:
            parameters_value = params['parameters']
            if isinstance(parameters_value, dict):
                return parameters_value
            elif isinstance(parameters_value, list) and len(parameters_value) > 0 and isinstance(parameters_value[0],
                                                                                                 dict):
                return parameters_value[0]

        # 过滤掉常见的非业务参数键
        filtered_params = {}
        exclude_keys = {'kwargs', 'args', 'parameters', 'self', 'cls'}

        for key, value in params.items():
            if key not in exclude_keys:
                filtered_params[key] = value

        # 如果过滤后有参数，返回过滤后的参数
        if filtered_params:
            return filtered_params

        # 如果没有找到合适的参数，返回原始params（去除排除的键）
        return {k: v for k, v in params.items() if k not in exclude_keys}

    def _extract_dict_from_list(self, items_list: list) -> dict:
        """
        从列表中提取字典参数
        1. 如果列表为空，返回空字典
        2. 如果第一个元素是字典，直接返回
        3. 否则，将列表元素映射到预定义参数名

        :param items_list: 项目列表
        :return: 提取的字典
        """
        if not items_list:
            return {}

        # 如果第一个元素是字典，直接返回
        if isinstance(items_list[0], dict):
            return items_list[0]

        # 其他情况按位置映射到预定义参数名
        return self._map_positional_args(items_list)

    def _map_positional_args(self, args_list: list) -> dict:
        """
        将位置参数列表映射为字典格式
        支持的格式：
        1. [{'traceid': 'xxx'}] -> {'traceid': 'xxx'}
        2. ['traceid_value'] -> {'traceid': 'traceid_value'}  # 假设第一个参数是traceid
        3. ['p1', 'p2', 'p3'] -> {'traceid': 'p1', 'id': 'p2', 'input': 'p3'}
        4. [] -> {}

        :param args_list: 位置参数列表
        :return: 映射后的参数字典
        """
        if not args_list:
            return {}

        # 如果第一个元素是字典，直接返回
        if isinstance(args_list[0], dict):
            return args_list[0]

        # 如果是字符串或其他类型，按位置映射到预定义参数名
        result = {}
        common_param_names = ['traceid', 'id', 'input', 'query', 'text']

        for i, arg in enumerate(args_list):
            if i < len(common_param_names):
                result[common_param_names[i]] = arg
            else:
                result[f'arg_{i}'] = arg

        return result

    def _execute_tool(self, tool_name: str, params: Union[str, dict], user: User) -> str:
        """
        执行工具并返回标准化结果
        :param tool_name: 工具名称
        :param params: 参数(字符串或字典)
        :param user: 用户对象
        :return: JSON格式的执行结果
        """
        try:
            # 统一参数标准化处理
            params = self._normalize_tool_params(params)

            # 获取工具对象
            tool_obj = self.tool_manager.get_tool(tool_name)

            # 如果工具存在且用户对象不为空，自动注入用户信息
            if tool_obj and user:
                # 获取工具函数的参数列表
                tool_func = tool_obj.func
                sig = inspect.signature(tool_func)
                param_names = list(sig.parameters.keys())

            # 检查工具函数是否需要用户相关参数
            user_related_params = {
                'user_id': user.user_id if hasattr(user, 'user_id') else None,
                'username': user.username if hasattr(user, 'username') else None,
                'chinese_name': user.chinese_name if hasattr(user, 'chinese_name') else None,
                'email': user.email if hasattr(user, 'email') else None,
                'mobile': user.mobile if hasattr(user, 'mobile') else None,
                'role_id': user.role_id if hasattr(user, 'role_id') else None
            }

            # params.update(user_related_params)
            
            # 将用户相关参数添加到工具参数中（仅添加工具函数需要的参数）
            for param_name, param_value in user_related_params.items():
                if param_name in param_names and param_name not in params:
                    params[param_name] = param_value
            
            logger.info(f"工具执行 - 工具名: {tool_name}, 标准化后参数: {params}")

            # 通过工具管理器执行工具
            result = self.tool_manager.execute(tool_name, params, user)

            # 返回标准化JSON结果
            return json.dumps({
                "success": result["success"],  # 是否成功
                "content": str(result["result"]) if result["success"] else result["error"],  # 成功结果或错误信息
                "is_raw_error": not result["success"]  # 是否为原始错误
            })

        except Exception as e:
            logger.error(f"工具执行错误 ({tool_name}): {str(e)}")
            return json.dumps({
                "success": False,  # 执行失败
                "content": str(e),  # 错误信息
                "is_raw_error": True  # 原始错误标志
            })

    def process(self, user_input: str, session_id: str, user: User = None, stream: bool = True) -> Union[
        Dict[str, Any], Iterator[Dict[str, Any]]]:
        """
        处理用户输入(带意图识别和流式输出选项)
        :param user_input: 用户输入文本
        :param session_id: 会话ID
        :param user: 用户对象(可选)
        :param stream: 是否使用流式输出
        :return: 同步返回字典或流式迭代器
        """
        # 第一步：输入扩写
        extended_input = self.extend_input(user_input)
        logger.info(f"输入处理 - 原始输入: {user_input}, 扩写后: {extended_input}")

        # 第二步：创建Agent执行器
        agent = self._create_agent_executor(session_id, user)
        # 根据stream参数选择处理方式
        return self._stream_processing(agent, extended_input, user) if stream else self._sync_processing(agent, extended_input)

    def _stream_processing(self, agent: AgentExecutor, user_input: str, user: User) -> Iterator[Dict[str, Any]]:
        """支持工具调用的流式输出方法"""
        try:
            tool_used = None
            tool_response = None
            need_llm_process = True

            for chunk in agent.stream({"input": user_input}):
                # 1. 检测工具调用动作 - 立即流式返回工具调用提示
                if "actions" in chunk:
                    for action in chunk["actions"]:
                        tool_used = action.tool
                        # 获取工具是否需要LLM处理
                        tool_obj = self.tool_manager.get_tool(tool_used)
                        need_llm_process = getattr(tool_obj, 'need_llm_process', True) if tool_obj else True
                        # 立即yield工具调用消息，确保流式输出
                        tool_call_message = {
                            "text": f"调用工具: {tool_used}",
                            "finish": False,
                            "is_tool_call": True,
                            "tool_used": tool_used
                        }
                        yield tool_call_message
                        # 继续处理当前chunk的其他部分，不跳过

                # 2. 处理工具执行结果
                if "steps" in chunk:
                    for step in chunk["steps"]:
                        if hasattr(step, 'observation'):
                            try:
                                obs_data = json.loads(step.observation)
                                tool_response = obs_data.get("content", "")
                                # 如果是工具错误，直接返回错误信息
                                if not obs_data.get("success", True):
                                    yield {
                                        "text": tool_response,
                                        "finish": True,
                                        "is_tool_call": True,
                                        # "tool_used": tool_used,
                                        "is_error": True
                                    }
                                    return
                                # 如果工具不需要LLM处理，直接返回工具结果
                                elif not need_llm_process and tool_response:
                                    yield {
                                        "text": tool_response,
                                        "finish": True,
                                        "is_tool_call": True,
                                        # "tool_used": tool_used
                                    }
                                    return
                            except json.JSONDecodeError:
                                pass

                # 3. 处理最终输出
                if "output" in chunk:
                    # 如果有工具响应，优先使用工具响应
                    response = tool_response if tool_response else chunk["output"]
                    yield {
                        "text": response,
                        "finish": True,
                        "is_tool_call": bool(tool_used)}
                    return

                # 4. 处理中间消息
                if "messages" in chunk:
                    for message in chunk["messages"]:
                        if isinstance(message, AIMessage):
                            yield {
                                "text": message.content,
                                "finish": False,
                                "is_tool_call": False
                            }

        except Exception as e:
            logger.error(f"流式处理异常: {str(e)}")
            yield {
                "text": f"处理请求时出错: {str(e)}",
                "finish": True,
                "is_tool_call": False,
                "is_error": True
            }

    def _sync_processing(self, agent: AgentExecutor, user_input: str) -> Dict[str, Any]:
        """同步处理"""
        try:
            result = agent.invoke({"input": user_input})
            return {
                "response": result["output"],
                "is_tool_call": len(result.get("intermediate_steps", [])) > 0,
                "tool_used": result["intermediate_steps"][0][0].tool if result.get("intermediate_steps") else None
            }
        except Exception as e:
            logger.error(f"同步处理异常: {str(e)}")
            return {"response": "处理请求时出错", "is_tool_call": False}