from langchain.tools import BaseTool
from pydantic import BaseModel, Field
from typing import Optional, Callable, Dict, List
import inspect
from functools import wraps
from django.apps import apps

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()


class Tool(BaseTool):
    """工具类"""

    func: Callable = Field(..., exclude=True)
    require_auth: bool = Field(False, exclude=True)
    permission: Optional[str] = Field(None, exclude=True)
    need_llm_process: bool = Field(True, exclude=True)

    class Config:
        arbitrary_types_allowed = True

    def _run(self, *args, **kwargs):
        return self.func(*args, **kwargs)

    async def _arun(self, *args, **kwargs):
        return self._run(*args, **kwargs)


def tool(
        name: Optional[str] = None,
        description: Optional[str] = None,
        require_auth: bool = False,
        permission: Optional[str] = None,
        args_schema: Optional[BaseModel] = None,
        need_llm_process: bool = True
):


    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)

        wrapper._is_tool = True
        wrapper._tool_name = name or func.__name__
        wrapper._tool_desc = description or func.__doc__ or "无描述"
        wrapper.require_auth = require_auth
        wrapper.permission = permission
        wrapper.args_schema = args_schema
        wrapper.need_llm_process = need_llm_process

        return wrapper

    return decorator


class ToolManager:
    """工具管理器"""

    def __init__(self):
        self._tools: Dict[str, Tool] = {}

    def register(self, tool: Tool):
        """注册工具"""
        if tool.name in self._tools:
            logger.warning(f"工具 {tool.name} 已存在，将被覆盖")
        self._tools[tool.name] = tool
        logger.info(f"工具注册成功: {tool.name}")

    def auto_register(self):
        """自动注册所有被@tool装饰的工具"""
        logger.info("开始自动注册工具...")

        for app_config in apps.get_app_configs():
            try:
                module = __import__(f"{app_config.name}.tools", fromlist=['*'])
                for _, obj in inspect.getmembers(module, inspect.isfunction):
                    if getattr(obj, '_is_tool', False):
                        self.register(Tool(
                            name=obj._tool_name,
                            description=obj._tool_desc,
                            func=obj,
                            require_auth=getattr(obj, 'require_auth', False),
                            permission=getattr(obj, 'permission', None),
                            args_schema=getattr(obj, 'args_schema', None),
                            need_llm_process=getattr(obj, 'need_llm_process', True)
                        ))
            except ImportError as e:
                logger.debug(f"跳过应用 {app_config.name}: {str(e)}")
                continue

        logger.info(f"完成工具注册，总数: {len(self._tools)}")

    def get_tool(self, name: str) -> Optional[Tool]:
        return self._tools.get(name)

    def get_available_tools(self, user=None) -> Dict[str, Tool]:
        return {
            name: tool for name, tool in self._tools.items()
            if not tool.require_auth or (user and (
                    not tool.permission or user.has_perm(tool.permission)
            ))
        }

    def execute(self, tool_name: str, params: dict, user=None) -> dict:
        tool = self.get_tool(tool_name)
        if not tool:
            return {"success": False, "error": "工具不存在"}

        if tool.require_auth and (not user or not user.is_authenticated):
            return {"success": False, "error": "需要登录"}

        if tool.permission and (not user or not user.has_perm(tool.permission)):
            return {"success": False, "error": "权限不足"}

        try:
            result = tool._run(**params)
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"工具执行失败: {tool_name}, 错误: {str(e)}")
            return {"success": False, "error": str(e)}

    def as_langchain_tools(self, user=None) -> List[Tool]:
        """获取工具列表"""
        return list(self.get_available_tools(user).values())


default_tool_manager = ToolManager()