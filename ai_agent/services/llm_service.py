from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from typing import Dict, List
from django.conf import settings

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()


class LLMService:
    """仅负责大模型实例化和基础同步调用"""

    def __init__(self):
        self.llm = ChatOpenAI(
            api_key=settings.LLM_CONFIG['API_KEY'],
            base_url=settings.LLM_CONFIG['BASE_URL'],
            model=settings.LLM_CONFIG['MODEL_NAME'],
            temperature=settings.LLM_CONFIG['TEMPERATURE'],
            streaming=False  # 关闭流式，由agent统一处理
        )
        logger.info(f"LangChain LLM服务初始化完成，模型: {self.llm.model_name}")

    def chat_completion(
            self,
            messages: List[Dict[str, str]],
            **kwargs
    ) -> str:
        """基础同步聊天功能"""
        try:
            # 转换消息格式为LangChain格式
            lc_messages = []
            for msg in messages:
                if msg["role"] == "system":
                    lc_messages.append(SystemMessage(content=msg["content"]))
                else:
                    lc_messages.append(HumanMessage(content=msg["content"]))

            return self.llm.invoke(lc_messages).content

        except Exception as e:
            logger.error(f"LLM调用失败: {str(e)}")
            return "抱歉，服务暂时不可用"