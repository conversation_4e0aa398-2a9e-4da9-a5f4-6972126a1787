from django.db import models
from scaffold.models.abstract.meta import ActiveModel, HierarchicalModel

from core.models import DateModel


# 性能测试任务记录表
class LocustTaskRecord(DateModel, ActiveModel):
    locust_task_id = models.AutoField(primary_key=True)
    locust_task_name = models.CharField(max_length=256)  # 性能测试任务任务名称
    locust_task_type = models.CharField(max_length=1)  # 1为案例，2为场景
    case_id = models.CharField(max_length=512)  # 待压测案例或场景ID
    number_of_user = models.CharField(max_length=16)  # 模拟用户数
    user_spawned_second = models.CharField(max_length=16)  # 每秒启动用户
    run_time = models.CharField(max_length=16)  # 运行时间
    locust_task_status = models.CharField(max_length=1, default='B')  # S成功，R运行中，F失败，B初始状态
    remark = models.TextField(blank=True, null=True)  # 备注
    locust_report_id = models.CharField(max_length=32, default='')  # 报告内容所指向ID

    class Meta(object):
        # 定义表名
        db_table = "locust_task_record"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = '性能测试任务记录表'


# 性能测试报告
class LocustReport(models.Model):
    record_id = models.AutoField(primary_key=True)
    locust_file_name = models.CharField(max_length=256)  # 性能测试脚本文件名称
    report_content = models.TextField()  # 性能测试报告内容
    create_time = models.DateTimeField()
    creater = models.CharField(max_length=16)

    class Meta(object):
        # 定义表名
        db_table = "locust_report"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-create_time']
        # 表别名
        verbose_name = '性能测试任务报告表'
