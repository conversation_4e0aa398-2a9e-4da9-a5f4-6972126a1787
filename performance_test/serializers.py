import re

from django.db.models import Max
from rest_framework import serializers
from scaffold.restframework.utils import auto_declare_serializers

from . import models as m


class LocustTaskRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.LocustTaskRecord
        fields = '__all__'


# # Auto declare serializer classes from models.
auto_declare_serializers(m, locals())
