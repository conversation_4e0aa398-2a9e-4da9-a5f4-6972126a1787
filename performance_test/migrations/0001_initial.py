# Generated by Django 3.0.6 on 2021-05-13 18:47

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='LocustReport',
            fields=[
                ('record_id', models.AutoField(primary_key=True, serialize=False)),
                ('locust_file_name', models.CharField(max_length=256)),
                ('report_content', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.Char<PERSON>ield(max_length=16)),
            ],
            options={
                'verbose_name': '性能测试任务报告表',
                'db_table': 'locust_report',
                'ordering': ['-create_time'],
            },
        ),
        migrations.CreateModel(
            name='LocustTaskRecord',
            fields=[
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('creater', models.Char<PERSON><PERSON>(max_length=128, verbose_name='创建人')),
                ('update_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='修改时间')),
                ('update_person', models.CharField(max_length=128, verbose_name='修改人')),
                ('locust_task_id', models.AutoField(primary_key=True, serialize=False)),
                ('locust_task_name', models.CharField(max_length=256)),
                ('locust_task_type', models.CharField(max_length=1)),
                ('case_id', models.CharField(max_length=512)),
                ('number_of_user', models.CharField(max_length=16)),
                ('user_spawned_second', models.CharField(max_length=16)),
                ('run_time', models.CharField(max_length=16)),
                ('locust_task_status', models.CharField(default='B', max_length=1)),
                ('remark', models.TextField(blank=True, null=True)),
                ('locust_report_id', models.CharField(default='', max_length=32)),
            ],
            options={
                'verbose_name': '性能测试任务记录表',
                'db_table': 'locust_task_record',
                'ordering': ['-update_time'],
            },
        ),
    ]
