import json
import time
import traceback

from django.core.paginator import Paginator
from django.http import HttpResponse
from rest_framework import viewsets
from rest_framework.decorators import action

from core.commonUtils.commonUtil import CommonUtil
from core.servers.performanceExecute import PerfomanceExecute
from core.utils import UserUtils, ResponseUtils, DateUtils
from . import serializers as s
from .exceptions import AppErrors
from .models import *
# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()


class PerformanceViewSet(viewsets.ModelViewSet):
    queryset = LocustTaskRecord.objects.all()
    serializer_class = s.LocustTaskRecordSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def task_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
            locust_task_id = request.GET.get('search_locust_task_id', '')
            locust_task_name = request.GET['search_locust_task_name']
            creater = request.GET.get('creater', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询Locust任务，查询条件：Locust任务编号=%s，Locust任务名称=%s" % (username, locust_task_id, locust_task_name)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = LocustTaskRecord.objects.filter(is_active=True)
        if locust_task_id:
            obj = obj.filter(locust_task_id = locust_task_id)
        if locust_task_name:
            obj = obj.filter(locust_task_name__contains = locust_task_name)
        if creater:
            obj = obj.filter(creater__contains=creater)
        obj = obj.order_by('-update_time')
        paginator = Paginator(obj, limit)
        data = paginator.page(page)
        data_list = list(data.object_list.values())
        response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count,
                         'search_locust_task_id': locust_task_id, 'search_locust_task_name': locust_task_name,
                         'performances': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def task_add(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        if request.method == 'POST':
            # 提取参数
            temp = json.loads(request.body)
            try:
                locust_task_name = temp['locust_task_name']
                locust_task_type = temp['locust_task_type']
                case_id = temp['case_id']
                number_of_user = temp['number_of_user']
                user_spawned_second = temp['user_spawned_second']
                run_time = temp['run_time']
                remark = temp.get('remark', "")
            except Exception as e:
                error_info = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{error_info}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
            create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            locust_task_record = LocustTaskRecord(locust_task_name=locust_task_name, locust_task_type=locust_task_type,
                                                  creater=username, case_id=case_id, number_of_user=number_of_user,
                                                  update_person=username, user_spawned_second=user_spawned_second,
                                                  run_time=run_time, remark=remark, locust_task_status="B",
                                                  create_time=create_time, update_time=create_time)

            locust_task_record.save()
            content = "用户 %s 新增Locust任务成功，详细信息：%s" % (username, locust_task_record.__dict__)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("性能测试任务新增成功")

    @action(methods=['PUT'], detail=True)
    def task_update(self, request, pk):
        locust_task_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            locust_task_name = temp['locust_task_name']
            locust_task_type = temp['locust_task_type']
            case_id = temp['case_id']
            number_of_user = temp['number_of_user']
            user_spawned_second = temp['user_spawned_second']
            run_time = temp['run_time']
            remark = temp.get('remark')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        locust_task_record = LocustTaskRecord.objects.get(locust_task_id=locust_task_id)
        locust_before = locust_task_record.__dict__
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        LocustTaskRecord.objects.filter(locust_task_id=locust_task_id).update(locust_task_name=locust_task_name,
                                                                              locust_task_type=locust_task_type,
                                                                              case_id=case_id,
                                                                              number_of_user=number_of_user,
                                                                              update_person=username,
                                                                              user_spawned_second=user_spawned_second,
                                                                              run_time=run_time, remark=remark,
                                                                              update_time=update_time)
        locust_after = LocustTaskRecord.objects.get(locust_task_id=locust_task_id).__dict__
        content = "用户 %s 修改Locust任务信息成功，修改前信息：%s，修改后信息：%s" % (username, locust_before, locust_after)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("性能测试任务编辑成功")

    @action(methods=['DELETE'], detail=True)
    def task_delete(self, request, pk):
        locust_task_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        if request.method == 'DELETE':
            locust_task = LocustTaskRecord.objects.get(locust_task_id=locust_task_id)
            locust_info = locust_task.__dict__
            locust_task.is_active = False
            locust_task.update_time = DateUtils.get_current_time()
            locust_task.update_person = username
            locust_task.save()
            content = "用户 %s 删除了Locust任务：%s" % (username, locust_info)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("性能测试任务删除成功")

    @action(methods=['POST'], detail=True)
    def task_run(self, request, pk):
        locust_task_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        if request.method == 'POST':
            # 提取参数
            temp = json.loads(request.body)
            try:
                locust_task_id = temp['locust_task_id']
                ltr = LocustTaskRecord.objects.get(locust_task_id=locust_task_id)
                if ltr.locust_task_status == "R":
                    return ResponseUtils.return_fail(AppErrors.ERROR_TASK_IS_RUNNING)
                env_type = temp['env_type']
            except Exception as e:
                error_info = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{error_info}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
            LocustTaskRecord.objects.filter(locust_task_id=locust_task_id).update(locust_task_status='R')
            exe = PerfomanceExecute(locust_task_id, env_type, username)
            exe.runLocust()
            case_info = str(locust_task_id) + "-" + LocustTaskRecord.objects.get(
                locust_task_id=locust_task_id).locust_task_name
            content = "用户 %s 开始执行性能测试：%s" % (username, case_info)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            time.sleep(1)  # 让前端有个缓冲时间再展示弹窗提示
            return ResponseUtils.return_success("已在后台异步执行压测任务，别忘了稍后回来查看报告哦")

    @action(methods=['GET'], detail=False)
    def task_report(self, request):
        locust_task_id = request.GET['id']
        locust_report_id = LocustTaskRecord.objects.get(locust_task_id=locust_task_id).locust_report_id
        if locust_report_id:
            report_content = LocustReport.objects.get(record_id=locust_report_id).report_content
            return HttpResponse(report_content)
        else:
            return HttpResponse('暂无测试报告')
