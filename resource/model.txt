# -*- coding:utf-8 -*-
from selenium import webdriver
import time,datetime
import os,subprocess

driver= webdriver.Chrome()
driver.maximize_window()
driver.implicitly_wait(20)

try:
	$
except Exception as e:
	raise e
finally:
    time.sleep(5)
    screenShots_dir = os.path.abspath("resource/ScreenShots/%s/" % time.strftime("%Y%m%d"))
    os.makedirs(screenShots_dir, exist_ok=True)
    name = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')+".png"
    file_name = os.path.join(screenShots_dir, name)
    driver.save_screenshot(file_name)
    driver.quit()