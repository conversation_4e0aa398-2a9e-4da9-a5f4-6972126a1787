﻿# -*- coding:utf-8 -*-
from locust import HttpUser,task,between,TaskSet
import requests,os,json,traceback
import cx_Oracle,pymysql,pyodbc
from requests.packages.urllib3.exceptions import InsecureRequestWarning
# 禁用安全请求警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

class DBUtils:
	coon = None
	cur = None
	def __init__(self, dbType,host, account, pwd,dbName):
		self.dbType = dbType
		self.host = host
		self.account = account
		self.pwd = pwd
		self.dbName = dbName

	#数据库连接方法，当前仅支持Oracle MySQL SQLServer
	def getConnect(self):
		try:
			if self.dbType == 'MySQL':
				port = 3306
				host = self.host
				if self.host.split(":")[1]:
					port = int(self.host.split(":")[1])
					host = self.host.split(":")[0]
				self.coon = pymysql.connect(host=host, port=port, user=self.account, passwd=self.pwd,
										  db=self.dbName, charset='utf8')
				return self.coon
			elif self.dbType == 'Oracle':
				parameter = self.account + '/' + self.pwd + '@' + self.host + '/' + self.dbName
				self.coon = cx_Oracle.connect(parameter)
				return self.coon
			elif self.dbType == 'SQLServer':
				self.host = self.host.split(":")[0]
				self.coon = pyodbc.connect(SERVER=self.host, UID=self.account, PWD=self.pwd, DATABASE=self.dbName, DRIVER='{SQL Server}')
				return self.coon
			else:
				return self.coon
		except Exception as e:
			return traceback.print_exc()

	def excuteSQL(self,sql):
		self.coon = self.getConnect()
		# 创建游标
		self.cur = self.coon.cursor()
		# 执行sql
		try:
			self.cur.execute(sql)
			self.coon.commit()
			return self.cur.fetchall()
		except Exception as e:
			raise e


	def query(self,sql):
		self.coon = self.getConnect()
		# 创建游标
		self.cur = self.coon.cursor()
		self.cur.execute(sql)
		result = self.cur.fetchall()
		self.closeDB()
		return result


	def closeDB(self):
		self.cur.close()
		self.coon.close()


class HydeeMiddleUser(HttpUser):
	wait_time = between(1, 3)
	#{}#

	@task
	class WeightForTasks(TaskSet):

		${}$