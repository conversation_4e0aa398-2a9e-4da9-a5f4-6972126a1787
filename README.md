#### 1、Pycharm中启动celery
  在项目根目录下执行：
  - 启动生产者：celery -A hydee_auto_server.celery beat >beat.out 2>&1 
  - 启动消费者：celery -A hydee_auto_server worker --loglevel=info -c 2 >worker.out 2>&1 

#### 2、项目启动
  - 启动项目： python manage.py runserver 0.0.0.0:8000

#### 3、Windows服务器中启动
  直接运行项目下的 start.bat 文件

  注意：需要不定期清除 beat.out与worker.out中的日志文件，防止文件太大影响执行效率

#### 4、按照rabbitmq
  - 安装后访问：localhost:15672 账号：guest/guest 如果失败，则需要开启管理模块的插件，在安装目录的sbin目录下执行，注意需要使用管理员权限运行：
```
    # 开启RabbitMQ节点
    rabbitmqctl start_app
    # 开启RabbitMQ管理模块的插件，并配置到RabbitMQ节点上
    rabbitmq-plugins enable rabbitmq_management
```

 - 安装好rabbitmq后，需要新增虚拟目录，在 localhost:15672 登录后的界面，点击“admin” ，然后添加 ‘hydee_auto_server’,如下图：
  
   ![img.png](img.png)