import logging

import requests
from django.test import TestCase

# Create your tests here.
from base.models import UserInfo


class TestAuto(TestCase):

    def pre_login(self):
        #  self.user = UserInfo.objects.create(username='test', password='11223344')
        #  # 通过force_authenticate函数来执行用户
        # # self.client.force_authenticate(self.user)
        resp = requests.post(
            r'http://127.0.0.1:8000/api/user_info/login/',
            json=dict(username="test", password="11223344"))
        data = resp.json()
        token = data.get('data').get('token')
        self.assertIsNotNone(token)
        print(token)
        return token

    def test_01_scene_add(self):
        token = self.pre_login()
        header = dict(Authorization=token, userId='9')
        asserts = "[{\"assert_type\":\"1\",\"return_value\":\"msg\",\"assert_operator\":\"1\"," \
                  "\"operation_value\":\"\",\"expect_result\":\"操作成功\"}] "
        relation_list = [{"reset_param": True, "interface_data": "{\"a\":1}", "is_assert": True, "asserts": asserts,
                          "relation_case_id": "502", "is_get_param": False, "request_param": "", "response_param": "",
                          "response_header_param": "", "sleep_time": "3", "response_param_alias": "username;password"}]
        resp = requests.post(
            r'http://127.0.0.1:8000/api/scene_info/scene_add/',
            json=dict(
                scene_name='xiaoxiao_test',
                scene_describe='this is scene_describe',
                before_param='',
                relation=relation_list
            ), headers=header)
        data = resp.json()
        print(data)
        self.assertEqual(data.get('meta').get('status'), 200)

    def test_01_scene_update(self):
        token = self.pre_login()
        header = dict(Authorization=token, userId='9')
        asserts = "[{\"assert_type\":\"1\",\"return_value\":\"msg\",\"assert_operator\":\"1\"," \
                  "\"operation_value\":\"\",\"expect_result\":\"操作成功22\"}] "
        relation_list = [{"reset_param": True, "interface_data": "{\"a\":1,\"b\":2}", "is_assert": True, "asserts": asserts,
                          "relation_case_id": "502", "is_get_param": False, "request_param": "", "response_param": "",
                          "response_header_param": "", "sleep_time": "3", "response_param_alias": "username;password"}]
        resp = requests.put(
            r'http://127.0.0.1:8000/api/scene_info/232/scene_update/',
            json=dict(
                scene_name='xiaoxiao_test3',
                scene_describe='this is scene_describe2',
                before_param='',
                relation=relation_list
            ), headers=header)
        data = resp.json()
        print(data)
        self.assertEqual(data.get('meta').get('status'), 200)

