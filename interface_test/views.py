import hashlib
import json
import os
import time
import traceback
import uuid
import re


from django.conf import settings
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import Sum
from django.http import FileResponse
from django.utils.encoding import escape_uri_path
from rest_framework import viewsets
from rest_framework.decorators import action
from scaffold.exceptions.exceptions import AppError

import interface_test.tasks as t
from base.models import UserInfo
from core.commonUtils.commonUtil import CommonUtil
from core.commonUtils.getModelInfoUtil import GetModelInfo
from core.commonUtils.jsonUtil import isContainsDDL
from core.servers.apiExecute import ApiExecute
from core.utils import UserUtils, ResponseUtils, DateUtils
from data_config.models import TaskInfo
from . import serializers as s
from .exceptions import AppErrors
from .models import *

from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl import Workbook
from django.http import HttpResponse
from urllib.parse import quote
from io import BytesIO
from django.db.models import Q
from tools_help.models import ServiceConfig

from interface_test.weekly import WeekReport as weekly, WeekReport

from rest_framework import viewsets, status
from rest_framework.decorators import action
import uuid
import os
from django.conf import settings
from django.utils import timezone

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()


class ProjectInfoViewSet(viewsets.ModelViewSet):
    queryset = ProjectInfo.objects.all()
    serializer_class = s.ProjectInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']
    @action(methods=['GET'], detail=False)
    def project_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            project_id = request.GET.get('search_project_id', '')
            project_name = request.GET.get('search_project_name', '')
            creater = request.GET.get('creater', '')
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询项目，查询条件：项目编号=%s，项目名关键字=%s" % (username, project_id, project_name)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = ProjectInfo.objects.filter(is_active=True)
        if project_id:
            obj = obj.filter(project_id = project_id)
        if project_name:
            obj = obj.filter(project_name__contains = project_name)
        if creater:
            obj = obj.filter(creater__contains = creater)
        obj = obj.order_by('-update_time')
        paginator = Paginator(obj, limit)
        data = paginator.page(page)
        data_list = list(data.object_list.values())
        response_json = {'pagesize': limit, 'pagenum': page, 'total': paginator.count, 'search_project_id': project_id,
                         'search_project_name': project_name, 'projects': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def project_add(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            project_name = temp['project_name']
            remark = temp.get('remark')
            project_mode = temp['project_mode']
            test_case = temp.get('test_case', [])
            uat_case = temp.get('uat_case', [])
            pro_case = temp.get('pro_case', [])
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        test_relation = []
        uat_relation = []
        pro_relation = []
        case_list = [*test_case, *uat_case, *pro_case]
        # 显式的开启一个事务
        with transaction.atomic():
            # 创建事务保存点
            save_id = transaction.savepoint()
            try:
                for i, item in enumerate(case_list):
                    assert_ids = None
                    index = item.get('index', 1)
                    is_enable = item.get('is_enable', 1)
                    if item.get('reset_param'):
                        interface_data = item.get('interface_data')
                        is_assert = item.get('is_assert')
                        asserts = item.get('asserts')
                        case_id = item.get('relation_case_id')
                        if is_assert:  # 是否断言
                            assert_ids = CommonUtil().insertAssertResultInfo(asserts, username, case_id, is_case_assert=False)
                    else:
                        interface_data = None
                        is_assert = False
                        item['reset_param'] = False
                    if item.get('response_param_alias') is None:
                        item['response_param_alias'] = item.get('response_param')
                    else:
                        response_param_num = len(item.get('response_param').split(';'))
                        response_param_alias_num = len(item.get('response_param_alias').split(';'))
                        if response_param_num != response_param_alias_num:
                            transaction.savepoint_rollback(save_id)
                            return ResponseUtils.return_fail(AppErrors.ERROR_SCENE_PARAM_ARGS,
                                                             'case_id: %s' % item['relation_case_id'].split("-")[0])
                    cr = SceneRelation.objects.create(relation_case_id=item['relation_case_id'],
                                                      is_get_param=item['is_get_param'],
                                                      request_param=item.get('request_param', ''),
                                                      response_param=item.get('response_param', ''),
                                                      response_param_alias=item.get('response_param_alias', ''),
                                                      sleep_time=item['sleep_time'],
                                                      response_header_param=item.get('response_header_param', ''),
                                                      reset_param=item.get('reset_param'),
                                                      instructions=item['instructions'],
                                                      interface_data=interface_data,
                                                      is_assert=is_assert,
                                                      asserts=assert_ids, index=index,is_enable=is_enable,
                                                      creater=username, create_time=create_time)
                    if i < len(test_case):
                        test_relation.append(str(cr.scene_relation_id))
                    elif i < len(test_case) + len(uat_case):
                        uat_relation.append(str(cr.scene_relation_id))
                    else:
                        pro_relation.append(str(cr.scene_relation_id))
                test_relation = '|'.join(test_relation)
                uat_relation = '|'.join(uat_relation)
                pro_relation = '|'.join(pro_relation)
                prj = ProjectInfo(project_name=project_name, ip=temp.get('ip', ''), port=temp.get('port', ''), remark=remark,
                                  creater=username, update_person=username,
                                  create_time=create_time, update_time=create_time,project_mode=project_mode,
                                  test_host=temp.get('test_host', ''), test_header=temp.get('test_header', ''),
                                  test_before_param=temp.get('test_before_param', ''),
                                  uat_host=temp.get('uat_host', ''), uat_header=temp.get('uat_header', ''),
                                  uat_before_param=temp.get('uat_before_param', ''), pro_host=temp.get('pro_host', ''),
                                  pro_header=temp.get('pro_header', ''),
                                  pro_before_param=temp.get('pro_before_param', ''),
                                  test_case=test_relation,uat_case=uat_relation,pro_case=pro_relation)
                prj.save()
                content = "用户 %s 新增项目成功，项目信息：%s" % (username, prj.__dict__)
                # 日志记录
                CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            except Exception as e:
                logger.error(e)
                transaction.savepoint_rollback(save_id)
                return ResponseUtils.return_fail(AppErrors.ERROR_SAVE_SCENE)
            # 提交订单成功，显式的提交一次事务
            transaction.savepoint_commit(save_id)
        return ResponseUtils.return_success("项目新增成功")

    @action(methods=['PUT'], detail=True)
    def project_update(self, request, pk):
        project_id = pk
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            project_name = temp['project_name']
            remark = temp.get('remark')
            project_mode = temp['project_mode']
            test_case = temp.get('test_case', [])
            uat_case = temp.get('uat_case', [])
            pro_case = temp.get('pro_case', [])
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        project_before = ProjectInfo.objects.get(project_id=project_id).__dict__
        test_relation = []
        uat_relation = []
        pro_relation = []
        case_list = [*test_case, *uat_case, *pro_case]
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        # 显式的开启一个事务
        with transaction.atomic():
            # 创建事务保存点
            save_id = transaction.savepoint()
            try:
                # 删除原有关联案例
                old_relation = []
                if project_before["test_case"]:
                    old_relation.extend(project_before["test_case"].split('|'))
                if project_before["uat_case"]:
                    old_relation.extend(project_before["uat_case"].split('|'))
                if project_before["pro_case"]:
                    old_relation.extend(project_before["pro_case"].split('|'))
                for item in old_relation:
                    SceneRelation.objects.filter(scene_relation_id=item).update(is_active=False)
                for i, item in enumerate(case_list):
                    assert_ids = None
                    index = item.get('index', 1)
                    is_enable = item.get('is_enable', 1)
                    if item.get('reset_param'):
                        interface_data = item.get('interface_data')
                        is_assert = item.get('is_assert')
                        asserts = item.get('asserts')
                        case_id = item.get('relation_case_id')
                        if is_assert:  # 是否断言
                            assert_ids = CommonUtil().insertAssertResultInfo(asserts, username, case_id, is_case_assert=False)
                    else:
                        interface_data = None
                        is_assert = False
                        item['reset_param'] = False
                    if item.get('response_param_alias') is None:
                        item['response_param_alias'] = item.get('response_param')
                    else:
                        response_param_num = len(item.get('response_param').split(';'))
                        response_param_alias_num = len(item.get('response_param_alias').split(';'))
                        if response_param_num != response_param_alias_num:
                            transaction.savepoint_rollback(save_id)
                            return ResponseUtils.return_fail(AppErrors.ERROR_SCENE_PARAM_ARGS,
                                                             'case_id: %s' % item['relation_case_id'].split("-")[0])
                    cr = SceneRelation.objects.create(relation_case_id=item['relation_case_id'],
                                                      is_get_param=item['is_get_param'],
                                                      request_param=item.get('request_param', ''),
                                                      response_param=item.get('response_param', ''),
                                                      response_param_alias=item.get('response_param_alias', ''),
                                                      sleep_time=item['sleep_time'],
                                                      response_header_param=item.get('response_header_param', ''),
                                                      reset_param=item.get('reset_param'),
                                                      instructions=item['instructions'],
                                                      interface_data=interface_data,
                                                      is_assert=is_assert,
                                                      asserts=assert_ids, index=index,is_enable=is_enable,
                                                      creater=username, create_time=update_time)
                    if i < len(test_case):
                        test_relation.append(str(cr.scene_relation_id))
                    elif i < len(test_case) + len(uat_case):
                        uat_relation.append(str(cr.scene_relation_id))
                    else:
                        pro_relation.append(str(cr.scene_relation_id))
                test_relation = '|'.join(test_relation)
                uat_relation = '|'.join(uat_relation)
                pro_relation = '|'.join(pro_relation)

                ProjectInfo.objects.filter(project_id=project_id).update(project_name=project_name, ip=temp.get('ip', ''), port=temp.get('port', ''), remark=remark,
                                          update_person=username,update_time=update_time,project_mode=project_mode,
                                          test_host=temp.get('test_host', ''), test_header=temp.get('test_header', ''),
                                          test_before_param=temp.get('test_before_param', ''),
                                          uat_host=temp.get('uat_host', ''), uat_header=temp.get('uat_header', ''),
                                          uat_before_param=temp.get('uat_before_param', ''), pro_host=temp.get('pro_host', ''),
                                          pro_header=temp.get('pro_header', ''),
                                          pro_before_param=temp.get('pro_before_param', ''),
                                          test_case=test_relation,uat_case=uat_relation,pro_case=pro_relation)
                project = ProjectInfo.objects.get(project_id=project_id).__dict__
                content = "用户 %s 修改项目信息成功，修改前信息：%s，修改后信息：%s" % (username, project_before, project)
                # 日志记录
                CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            except Exception as e:
                logger.error(e)
                transaction.savepoint_rollback(save_id)
                return ResponseUtils.return_fail(AppErrors.ERROR_SAVE_SCENE)
            # 提交订单成功，显式的提交一次事务
            transaction.savepoint_commit(save_id)
        return ResponseUtils.return_success("项目编辑成功")

    @action(methods=['DELETE'], detail=True)
    def project_delete(self, request, pk):
        project_id = pk
        username = UserUtils.get_login_user(request).username
        project = ProjectInfo.objects.get(project_id=project_id).__dict__
        # 判断是否存在下级接口
        interface_info = InterfaceInfo.objects.filter(project_id=project_id, is_active=True)
        if interface_info:
            return ResponseUtils.return_fail(AppErrors.ERROR_PROJECT_IS_EXISTS_INTERFACE)
        else:
            ProjectInfo.objects.filter(project_id=project_id).update(is_active=False, update_person=username,
                                                                     update_time=DateUtils.get_current_time())
            content = "用户 %s 删除了项目：%s" % (username, project)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("项目删除成功")


class BusinessInfoViewSet(viewsets.ModelViewSet):
    queryset = BusinessInfo.objects.all()
    serializer_class = s.BusinessInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def business_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            business_id = request.GET.get('business_id', '')
            business_name = request.GET.get('business_name', '')
            creater = request.GET.get('creater', '')
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询业务线，查询条件：业务ID=%s，业务名称关键字=%s" % (username, business_id, business_name)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = BusinessInfo.objects.filter(is_active=True)
        if business_id:
            obj = obj.filter(business_id = business_id)
        if business_name:
            obj = obj.filter(business_name__contains = business_name)
        if creater:
            obj = obj.filter(creater__contains=creater)
        obj = obj.order_by('-update_time')
        paginator = Paginator(obj, limit)
        data = paginator.page(page)
        data_list = list(data.object_list.values())
        response_json = {'pagesize': limit, 'pagenum': page, 'total': paginator.count, 'business_id': business_id,
                         'business_name': business_name, 'data': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def business_add(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        if request.method == 'POST':
            # 提取参数
            temp = json.loads(request.body)
            try:
                business_name = temp['business_name']
                tester = temp.get('tester')
                remark = temp.get('remark')
            except Exception as e:
                error_info = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{error_info}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
            create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            obj = BusinessInfo(business_name = business_name, tester = tester, remark=remark,
                              creater=username, update_person=username,
                              create_time=create_time, update_time=create_time)
            obj.save()
            content = "用户 %s 新增业务线成功，项目信息：%s" % (username, obj.__dict__)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("业务线新增成功")

    @action(methods=['PUT'], detail=True)
    def business_update(self, request, pk):
        business_id = pk
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            business_name = temp['business_name']
            tester = temp.get('tester')
            remark = temp.get('remark')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        business_before = BusinessInfo.objects.get(business_id = business_id).__dict__
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        BusinessInfo.objects.filter(business_id = business_id).update(business_name = business_name, tester = tester,
                                                                 remark=remark, update_person=username,
                                                                 update_time=update_time)
        business = BusinessInfo.objects.get(business_id = business_id).__dict__
        content = "用户 %s 修改业务线信息成功，修改前信息：%s，修改后信息：%s" % (username, business_before, business)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("业务线编辑成功")

    @action(methods=['DELETE'], detail=True)
    def business_delete(self, request, pk):
        business_id = pk
        username = UserUtils.get_login_user(request).username
        business = BusinessInfo.objects.get(business_id = business_id).__dict__
        # 判断是否存在下级场景
        scene_info = SceneInfo.objects.filter(business_id = business_id, is_active=True)
        if scene_info:
            return ResponseUtils.return_fail(AppErrors.ERROR_BUSINESS_IS_EXISTS_INTERFACE)
        else:
            BusinessInfo.objects.filter(business_id = business_id).update(is_active=False, update_person=username,
                                                                     update_time=DateUtils.get_current_time())
            content = "用户 %s 删除了业务线：%s" % (username, business)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("业务线删除成功")


class InterfaceInfoViewSet(viewsets.ModelViewSet):
    queryset = InterfaceInfo.objects.all()
    serializer_class = s.InterfaceInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def interface_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            project_id = request.GET.get('search_project_id', '')
            interface_id = request.GET.get('search_interface_id', '')
            interface_address = request.GET.get('search_interface_address', '')
            is_related = request.GET.get('is_related', '')
            creater = request.GET.get('creater', '')
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询接口信息，查询条件：接口信息=%s, 接口地址=%s" % (username, interface_id, interface_address)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = InterfaceInfo.objects.filter(is_active=True, is_cover=1)
        if interface_id:
            obj = obj.filter(interface_id=interface_id)
        if project_id:
            obj = obj.filter(project_id=project_id)
        if interface_address:
            obj = obj.filter(interface_address__contains=interface_address)
        if is_related:
            obj = obj.filter(is_related=is_related)
        if creater:
            obj = obj.filter(creater__contains=creater)
        itf_list = obj.order_by('-update_time')
        paginator = Paginator(itf_list, limit)
        data = paginator.page(page)
        data_list = list(data.object_list.values())
        for itf in data_list:
            itf['project_name'] = ProjectInfo.objects.get(project_id=itf['project_id']).project_name
            itf['project_id'] = int(itf['project_id'])
            if itf['response_param'] and len(itf['response_param'].strip()) > 0:
                itf['response_param'] = json.loads(itf['response_param'])
            if itf['request_param'] and len(itf['request_param'].strip()) > 0:
                itf['request_param'] = json.loads(itf['request_param'])
        response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count,
                         'search_interface_id': interface_id, 'data': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def interface_add(self, request):
        """
         添加接口
        :param request:
          interface_name  必填  接口名称
          project_id      必填  项目id
          interface_agreement  必填  协议方式
          interface_way         必填  请求类型
          request_param   非必填  请求参数
          response_param  非必填  返回参数
          header          非必填  请求头
          remark          非必填  请求头
        :return:
        """
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            interface_name = temp['interface_name']
            project_id = temp['project_id']
            interface_agreement = temp['interface_agreement']
            interface_way = temp['interface_way']
            interface_address = temp['interface_address']
            header = temp.get('header')
            request_param = json.dumps(temp.get('request_param', []))
            response_param = json.dumps(temp.get('response_param', []))
            remark = temp.get('remark')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        itf = InterfaceInfo(project_id=project_id, interface_name=interface_name,
                            interface_agreement=interface_agreement,
                            interface_way=interface_way, interface_address=interface_address, header=header,
                            remark=remark, creater=username, update_person=username, response_param=response_param,
                            create_time=create_time, update_time=create_time, request_param=request_param, is_cover =1,
                            is_handle=1, compare_result=0)
        itf.save()
        content = "用户 %s 新增接口成功，接口信息：%s" % (username, itf.__dict__)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("接口新增成功")

    @action(methods=['PUT'], detail=True)
    def interface_update(self, request, pk, df=1):
        interface_id = pk
        username = UserUtils.get_login_user(request).username
        # 判断入口是接口管理/待处理清单，1为接口管理，其他为待处理清单
        if df == 1:
            # 提取参数
            temp = json.loads(request.body)
        else:
            # 提取参数，获取接口更新的入参
            temp1 = json.loads(request.body)
            temp = temp1['interfaceForm']
        try:
            interface_name = temp['interface_name']
            project_id = temp['project_id']
            interface_agreement = temp['interface_agreement']
            interface_way = temp['interface_way']
            interface_address = temp['interface_address']
            remark = temp.get('remark')
            header = temp['header']
            request_param = json.dumps(temp.get('request_param', []))
            response_param = json.dumps(temp.get('response_param', []))
            type = temp.get('type', 1)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        interface_before = InterfaceInfo.objects.get(interface_id=interface_id).__dict__
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        # 从接口管理页面修改
        if type == 1:
            InterfaceInfo.objects.filter(interface_id=interface_id).update(project_id=project_id, header=header,
                                                                           interface_name=interface_name,
                                                                           interface_agreement=interface_agreement,
                                                                           interface_way=interface_way,
                                                                           interface_address=interface_address,
                                                                           remark=remark, request_param=request_param,
                                                                           update_person=username,
                                                                           update_time=update_time,
                                                                           response_param=response_param)
        else:
            # 从待处理清单页面新增接口
            InterfaceInfo.objects.filter(interface_id=interface_id).update(project_id=project_id, header=header,
                                                                           interface_name=interface_name,
                                                                           interface_agreement=interface_agreement,
                                                                           interface_way=interface_way,
                                                                           interface_address=interface_address,
                                                                           remark=remark, request_param=[],
                                                                           creater=username,
                                                                           update_person=username,
                                                                           update_time=update_time,
                                                                           response_param=[],
                                                                           is_cover=1,
                                                                           is_handle=1)
        interface = InterfaceInfo.objects.get(interface_id=interface_id).__dict__
        content = "用户 %s 修改接口信息成功，修改前信息：%s，修改后信息：%s" % (username, interface_before, interface)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("接口编辑成功")

    @action(methods=['DELETE'], detail=True)
    def interface_delete(self, request, pk):
        interface_id = pk
        username = UserUtils.get_login_user(request).username
        interface = InterfaceInfo.objects.get(interface_id=interface_id).__dict__
        # 判断是否存在下级案例数据
        case_info = CaseInfo.objects.filter(interface_id = interface_id, is_active = True)
        case_list = []
        if case_info:
            for case in case_info:
                case_list.append(str(case.case_id) + "-" + case.case_name)
            return ResponseUtils.return_fail(AppErrors.ERROR_INTERFACE_IS_EXISTS_CHILDERN)
        else:
            InterfaceInfo.objects.filter(interface_id=interface_id).update(is_active=False, update_person=username,
                                                                           update_time=DateUtils.get_current_time())
            content = "用户 %s 删除了接口：%s" % (username, interface)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("接口删除成功")

    @action(methods=['POST'], detail=False)
    def interface_update_add(self, request):
        # 提取参数
        temp = json.loads(request.body)
        username = UserUtils.get_login_user(request).username
        interface = temp['interfaceForm']
        interface_id = interface['interface_id']
        # 数据库事务一致，某一个失败，全部回滚
        try:
            with transaction.atomic():
                # 调用接口更新方法
                interface_res = InterfaceInfoViewSet.interface_update(self, request, interface_id, 2)
                # 调用案例新增方法
                case_res = CaseInfoViewSet.case_add(self, request, 2)
                if interface_res.status_code == 200 and case_res.status_code == 200:
                    content = "用户 %s 新增了接口：%s的案例" % (username, interface_id)
                    # 日志记录，以系统日志类型记录到数据库
                    CommonUtil().recordSystemLog(username, content)
                    return ResponseUtils.return_success("接口案例新增成功")
                else:
                    return ResponseUtils.return_fail(AppErrors.ERROR_INTERFACE_ADD_FAIL)
        except Exception as e:
            logger.error(f'参数异常：{e}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, e)

    # ，数据大，延时有点长 /api/cases : getApiInfo (修改了参数形式，通过？api_type=**)
    @action(methods=['GET'], detail=False)
    def get_api_list(self, request):
        """
            获取全部接口案例信息的接口
        :param request:
           api_type  必填  类型 cases  案例  interfaces 接口
        :return:
        """
        api_type = request.GET['api_type']
        username = UserUtils.get_login_user(request).username
        if 'HTTP_X_FORWARDED_FOR' in request.META:
            ipaddress = request.META['HTTP_X_FORWARDED_FOR']
        else:
            ipaddress = request.META['REMOTE_ADDR']
        if api_type == "cases":
            data = GetModelInfo().getApiCases()
        elif api_type == "interfaces":
            data = GetModelInfo().getApiInterface()
        elif api_type == "scenes":
            data = GetModelInfo().getApiScenes()
        else:
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, "apiType参数异常：%s" % api_type)
        content = "来自IP：%s的用户 %s 请求获取全部接口案例信息" % (ipaddress, username)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("查询成功", data)


    @action(methods=['GET'], detail=False)
    def pendinginterface_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        # userinfo = UserUtils.get_login_user(request)
        # if userinfo:
        #     username = userinfo.username
        # else:
        #     username = request.GET.get('username')
        try:
            pendinginterface_id = request.GET.get('search_pendinginterface_id', '')
            pendinginterface_name = request.GET.get('search_pendinginterface_name', '')
            pendinginterface_address = request.GET.get('search_pendinginterface_address', '')
            compare_result = request.GET.get('compare_result', '')
            creater = request.GET.get('creater', '')
            is_cover = request.GET.get('is_cover', '')
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询待处理接口信息，查询条件：接口编号=%s, 接口名称=%s, 接口地址=%s, 变更状态=%s, 创建人=%s" % (username, pendinginterface_id, pendinginterface_name, pendinginterface_address,compare_result, creater)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = InterfaceInfo.objects.filter(is_active=True, is_handle=0)
        if pendinginterface_id:
            obj = obj.filter(interface_id=pendinginterface_id)
        if pendinginterface_name:
            obj = obj.filter(interface_name__contains=pendinginterface_name)
        if pendinginterface_address:
            obj = obj.filter(interface_address__contains=pendinginterface_address)
        if compare_result:
            obj = obj.filter(compare_result=compare_result)
        if creater:
            obj = obj.filter(creater__contains=creater)
        if is_cover:
            obj = obj.filter(is_cover=is_cover)
        pitf_list = obj.order_by('-interface_id')
        # 处理分页条数
        paginator = Paginator(pitf_list, limit)
        data = paginator.page(page)
        data_list = list(data.object_list.values())
        for itf in data_list:
            itf['project_name'] = ProjectInfo.objects.get(project_id=itf['project_id']).project_name
            itf['project_id'] = int(itf['project_id'])
        response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count, 'search_pendinginterface_id': pendinginterface_id,
                         'search_pendinginterface_name': pendinginterface_name, 'search_pendinginterface_address': pendinginterface_address, 'data': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['PUT'], detail=True)
    def pendinginterface_update(self, request, pk):
        pendinginterface_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        pendinginterface_before = InterfaceInfo.objects.get(interface_id=pendinginterface_id).__dict__
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        InterfaceInfo.objects.filter(interface_id=pendinginterface_id).update(update_person=username,
                                                                       update_time=update_time,
                                                                       is_handle=1)
        pendinginterface = InterfaceInfo.objects.get(interface_id=pendinginterface_id).__dict__
        content = "用户 %s 手动处理待处理接口成功，修改前信息：%s，修改后信息：%s" % (username, pendinginterface_before, pendinginterface)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("手动处理成功")

    @action(methods=['POST'], detail=False)
    def interface_traffic_summary(self, request):
        """
         topN流量接口
            start_date  开始时间 必填
            end_date  结束时间  必填
            top_num   取前几   非必填，默认False,不传取时间范围内全部数据
           interface_id 接口id   非必填
           interface_name   接口名称  非必填
           interface_address  接口地址  非必填
           service   服务名  非必填
           source    访问来源  非必填
           pagenum   页码   必填
           pagesize  条数   必填
        :return:
        """
        temp = json.loads(request.body)
        # 从请求头中取操作人的用户ID
        userinfo = UserUtils.get_login_user(request)
        if userinfo:
            username = userinfo.username
        else:
            username = 'api'
        try:
            start_date = temp['start_date']
            end_date = temp['end_date']
            top_num = temp['top_num']
            interface_name = temp['interface_name']
            interface_address = temp['interface_address']
            service = temp['service']
            # source = temp['source']
            page = temp['pagenum']
            limit = temp['pagesize']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询生产访问量top接口信息，查询条件：开始时间=%s, 结束时间=%s, TOP=%s" % (
        username, start_date, end_date, top_num)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库

        traffic_obj = InterfaceTrafficStatistics.objects.filter(statistics_date__range=(start_date, end_date))

        # 使用 in_bulk 方法来一次性获取，避免在for循环中再查表
        interface_ids = [obj.interface_id for obj in traffic_obj]
        interface_infos = InterfaceDetail.objects.in_bulk(interface_ids)

        # 获取service_condigs
        service_configs = ServiceConfig.objects.in_bulk(
            set(interface_info.service_config_id for interface_id, interface_info in interface_infos.items() if
                interface_info.service_config_id is not None)
        )

        # 初始化一个空的Q对象列表
        q_objects = Q()

        # 根据interface_name构建Q对象（如果提供）
        if interface_name:
            q_objects &= Q(interface_description__icontains=interface_name)

        # 根据interface_address构建Q对象（如果提供）
        if interface_address:
            q_objects &= Q(interface_address__icontains=interface_address)

            # 根据service构建Q对象（如果提供）
        if service:
            service_id = ServiceConfig.objects.filter(skywalking_service__icontains=service).values_list('id',
                                                                                                         flat=True)
            q_objects &= Q(service_config_id__in=service_id)

        #     # 根据source构建Q对象（如果提供）
        # if source:
        #     q_objects &= Q(source__icontains=source)

        # 使用Q对象来过滤interfacedetail_obj
        interface_id = InterfaceDetail.objects.filter(q_objects).values_list('id', flat=True)

        # 传入top_num时，则取top
        if top_num:
            # 聚合每个接口的访问量,按降序排序
            aggregated_obj = traffic_obj.values('interface_id').annotate(
                total_access_count=Sum('access_count')).order_by(
                '-total_access_count')[:int(top_num)]
        else:
            # 使用这些接口ID来过滤traffic_obj并聚合
            aggregated_obj = traffic_obj.filter(interface_id__in=interface_id).values(
                'interface_id').annotate(
                total_access_count=Sum('access_count')).order_by('-total_access_count')

        interface_list = []
        for interface in aggregated_obj:
            interface_id = interface['interface_id']
            if interface_id in interface_infos:
                interface_info = interface_infos[interface_id]
                # 使用 in_bulk 获取的 service_configs 字典来避免额外的数据库查询
                if interface_info.service_config_id in service_configs:
                    skywalking_service = service_configs[interface_info.service_config_id].skywalking_service
                else:
                    skywalking_service = None

                interface_dict = {
                    'interface_id': interface['interface_id'],
                    'interface_name': interface_info.interface_description,
                    'interface_address': interface_info.interface_address,
                    'interface_way': interface_info.interface_way,
                    'service': skywalking_service,
                    'total_access_count': interface['total_access_count']
                }
                interface_list.append(interface_dict)

        paginator = Paginator(interface_list, limit)
        data = paginator.page(page)
        data_list = list(data.object_list)
        response_json = {'pagenum': page, 'pagesize': limit, 'total': len(interface_list), 'data': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def export_interface_traffic_summary(self, request):
        """
        导出topN流量接口
            start_date  开始时间 必填
            end_date  结束时间  必填
            top_num   取前几   非必填，默认False,不传取时间范围内全部数据
           interface_id 接口id   非必填
           interface_name   接口名称  非必填
           interface_address  接口地址  非必填
           service   服务名  非必填
           source    访问来源  非必填
        :return:
        """
        response = self.interface_traffic_summary(request)
        records = json.loads(response.content.decode('utf-8'))
        data = records['data']['data']
        # 创建一个新的工作簿和工作表
        workbook = Workbook()
        sheet = workbook.active
        sheet.title = "TOP接口流量统计"
        # 写入表头
        headers = ["接口描述", "接口地址", "请求方式", "服务名", "总访问量"]
        sheet.append(headers)
        # 设置表头样式（居中文本、加粗字体、添加边框、底色灰色、字体调大）
        bold_font = Font(bold=True, size=15)  # 字体调大
        center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        thin_border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'),
                             bottom=Side(style='thin'))
        grey_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')  # 灰色底色

        for cell in sheet[1]:
            cell.font = bold_font
            cell.alignment = center_alignment
            cell.border = thin_border
            cell.fill = grey_fill  # 底色灰色
        # # 设置列宽
        sheet.column_dimensions['A'].width = 30
        sheet.column_dimensions['B'].width = 60
        sheet.column_dimensions['D'].width = 20
        sheet.column_dimensions['E'].width = 20

        for record in data:
            id = record["interface_id"]
            name = record["interface_name"]
            combined_data = f"{id}\n{name}"
            row = [combined_data, record["interface_address"], record["interface_way"], record["service"], record["total_access_count"]]
            sheet.append(row)
        # 设置单元格样式，使换行符生效
        for row in sheet.iter_rows(min_row=2, max_col=sheet.max_column, max_row=sheet.max_row):
            for cell in row:
                cell.alignment = Alignment(wrap_text=True, horizontal='center', vertical='center')
                cell.font = Font(size=10)

        # 将工作簿保存到字节流
        output = BytesIO()
        workbook.save(output)
        # 创建一个HTTP响应对象
        response = HttpResponse(output.getvalue(),
                                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = 'TOP接口流量统计.xlsx'
        response['Content-Disposition'] = 'attachment; filename="%s"' % quote(filename)

        return response

    @action(methods=['GET'], detail=False)
    def get_interface_daily_traffic(self, request):
        """
            获取接口每日流量统计
           :param request:
           start_date  开始时间 必填
           end_date  结束时间  必填
           interface_id 接口id   必填
           :return:
                  """
        # temp = json.loads(request.body)
        # 从请求头中取操作人的用户ID
        userinfo = UserUtils.get_login_user(request)
        print("userinfo:", userinfo)
        if userinfo:
            username = userinfo.username
        else:
            username = 'api'
        try:
            start_date = request.GET.get('start_date')
            end_date = request.GET.get('end_date')
            interface_id = request.GET.get('interface_id', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询每日流量统计接口信息，查询条件：接口编号=%s, 开始时间=%s, 结束时间=%s" % (
        username, interface_id, start_date, end_date)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        traffic_obj = InterfaceTrafficStatistics.objects.filter(statistics_date__range=(start_date, end_date))
        # interfacedetail_obj = InterfaceDetail.objects.all()
        # 使用 in_bulk 方法来一次性获取，避免在for循环中再查表
        interface_ids = [obj.interface_id for obj in traffic_obj]
        interface_infos = InterfaceDetail.objects.in_bulk(interface_ids)

        # 获取service_condigs
        service_configs = ServiceConfig.objects.in_bulk(
            set(interface_info.service_config_id for interface_id, interface_info in interface_infos.items() if
                interface_info.service_config_id is not None)
        )

        if interface_id:
            traffic_obj = traffic_obj.filter(interface_id=interface_id).order_by('-statistics_date')

        interface_list = []
        for interface in traffic_obj:
            interface_id = interface.interface_id
            if interface_id in interface_infos:
                interface_info = interface_infos[interface_id]
                # 使用 in_bulk 获取的 service_configs 字典来避免额外的数据库查询
                if interface_info.service_config_id in service_configs:
                    skywalking_service = service_configs[interface_info.service_config_id].skywalking_service
                else:
                    skywalking_service = None

                interface_dict = {
                    'interface_id': interface.interface_id,
                    'interface_name': interface_info.interface_description,
                    'interface_address': interface_info.interface_address,
                    'interface_way': interface_info.interface_way,
                    'service': skywalking_service,
                    # 'source': interface_info.source,
                    'access_count': interface.access_count,
                    'statistics_date': interface.statistics_date
                }
                interface_list.append(interface_dict)
        response_json = {'total': len(interface_list), 'data': interface_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['GET'], detail=False)
    def export_interface_daily_traffic(self, request):
        """
               导出接口每日流量统计
              :param request:
              start_date  开始时间 必填
              end_date  结束时间  必填
              interface_id 接口id   必填
              :return:
        """
        response = self.get_interface_daily_traffic(request)
        records = json.loads(response.content.decode('utf-8'))
        data = records['data']['data']
        print("data:", data)
        print("type(data):", type(data))
        # 创建一个新的工作簿和工作表
        workbook = Workbook()
        sheet = workbook.active
        sheet.title = "接口每日流量统计"
        # 写入表头
        headers = ["接口描述", "接口地址", "请求方式", "服务名", "访问量", "统计日期"]
        sheet.append(headers)
        # 设置表头样式（居中文本、加粗字体、添加边框、底色灰色、字体调大）
        bold_font = Font(bold=True, size=15)  # 字体调大
        center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        thin_border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'),
                             bottom=Side(style='thin'))
        grey_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')  # 灰色底色

        for cell in sheet[1]:
            cell.font = bold_font
            cell.alignment = center_alignment
            cell.border = thin_border
            cell.fill = grey_fill  # 底色灰色
        # # 设置列宽
        sheet.column_dimensions['A'].width = 30
        sheet.column_dimensions['B'].width = 60
        sheet.column_dimensions['D'].width = 20
        sheet.column_dimensions['E'].width = 20

        for record in data:
            id = record["interface_id"]
            name = record["interface_name"]
            combined_data = f"{id}\n{name}"
            row = [combined_data, record["interface_address"], record["interface_way"], record["service"], record["access_count"], record["statistics_date"]]
            sheet.append(row)
        # 设置单元格样式，使换行符生效
        for row in sheet.iter_rows(min_row=2, max_col=sheet.max_column, max_row=sheet.max_row):
            for cell in row:
                cell.alignment = Alignment(wrap_text=True, horizontal='center', vertical='center')
                cell.font = Font(size=10)
                # cell.border = thin_border
        # 将工作簿保存到字节流
        output = BytesIO()
        workbook.save(output)

        # 创建一个HTTP响应对象
        response = HttpResponse(output.getvalue(),
                                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        filename = '接口每日流量统计.xlsx'
        response['Content-Disposition'] = 'attachment; filename="%s"' % quote(filename)

        return response


class CaseInfoViewSet(viewsets.ModelViewSet):
    queryset = CaseInfo.objects.all()
    serializer_class = s.CaseInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def case_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            case_id = request.GET.get("search_case_id", "")
            interface_id = request.GET.get("search_interface_id", "")
            case_name = request.GET.get("search_case_name", "")
            case_uri = request.GET.get("search_case_uri", "")
            is_related = request.GET.get("is_related", "")
            creater = request.GET.get('creater', "")
            type = request.GET["type"]
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询案例信息，查询条件：案例编号=%s，案例名称=%s" % (username, case_id, case_name)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        if type =="EXPORT":
            types ={'EXPORT','UPLOAD'}
            obj = CaseInfo.objects.filter(case_type__in=types, is_active=True)
        else:
            obj = CaseInfo.objects.filter(case_type=type, is_active=True)
        if case_id:
            obj = obj.filter(case_id=case_id)
        if interface_id:
            obj = obj.filter(interface_id=interface_id)
        if case_name:
            obj = obj.filter(case_name__icontains=case_name)
        if case_uri:
            obj = obj.filter(interface_api__interface_address__icontains=case_uri)
        if is_related:
            obj = obj.filter(is_related=is_related)
        if creater:
            obj = obj.filter(creater__contains=creater)
        case_list = obj.order_by('-update_time')
        # 处理分页条数
        self.pagination_class.page_size = limit
        self.pagination_class.page_query_param = 'pagenum'
        data_list = self.paginate_queryset(case_list)
        if data_list is not None:
            data_list = s.CaseInfoSerializer(instance=data_list, many=True).data
        for cas in data_list:
            if cas["case_type"] == "API":
                cas['interface_name'] = InterfaceInfo.objects.get(interface_id=cas['interface_id']).interface_name
            if cas["case_type"] == "UPLOAD":
                cas['file_path'] = json.loads(obj.get(case_id=cas['case_id']).interface_data)['file_path']
                cas['upload_min_rows'] = json.loads(obj.get(case_id=cas['case_id']).interface_data)['upload_min_rows']
                cas['upload_max_rows'] = json.loads(obj.get(case_id=cas['case_id']).interface_data)['upload_max_rows']
        response_json = {'pagenum': page, 'pagesize': limit, 'total': len(case_list), 'search_case_id': case_id,
                         'search_case_name': case_name, 'cases': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def case_add(self, request, df=1) -> object:
        """
          添加案例
        :param request:
           case_name  必填  案例名称
           interface_data 必填  请求数据
           case_type    必填  案例类型：SQL sql案例  API 接口案例
           csv_file     非必填  外部csv文件路径
           is_assert    必填    是否断言
           asserts      非必填   断言数据
        :return:
        """
        create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        username = UserUtils.get_login_user(request).username
        # 判断入口是案例管理/待处理清单，1为案例管理，其他为待处理清单
        if df == 1:
            temp = json.loads(request.body)
            case_type = temp['case_type']
        else:
            # 提取参数，获取案例新增入参
            temp1 = json.loads(request.body)
            temp = temp1['caseForm']
            case_type = temp['case_type']
        # 判断案例类型
        # 若案例类型为SQL或者REDIS
        if case_type == "SQL" or case_type == "REDIS":
            try:
                # 获取入参中的数据
                case_describe = temp['case_describe']
                case_name = temp['case_name']
                init_database = temp['init_database']
                # SQL语句
                interface_data = temp['interface_data']
                # # 导出解析
                # interface_id = temp['interface_id']
                # interface_address = temp['interface_address']
                # 判断文本是否包含DDL关键字,若包含则抛错
                if isContainsDDL(interface_data):
                    return ResponseUtils.return_fail(AppErrors.ERROR_CASE_RUN_SQL_IS_ILLEGAL, interface_data)
                # 是否断言
                is_assert = temp["is_assert"]
                asserts = temp["asserts"]
            except Exception as e:
                error_info = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{error_info}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
            # 保存案例信息至数据库
            case = CaseInfo(case_name=case_name, case_type=case_type, interface_id="",
                            is_init=False, init_database=init_database, init_sql="", is_back=False,
                            back_sql="", is_encrypt=False, case_describe=case_describe,
                            interface_data=interface_data, creater=username, is_assert=is_assert,
                            update_person=username, create_time=create_time, update_time=create_time)
            case.save()
            case_id = case.case_id
            if is_assert:
                assert_id = CommonUtil().insertAssertResultInfo(asserts, username, case_id)
            else:
                assert_id = ''
            CaseInfo.objects.filter(case_id=case_id).update(asserts=assert_id)
            content = "用户 %s 新增SQL案例成功，案例信息：%s" % (username, case.__dict__)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("案例新增成功")
        # 若案例类型为导出解析
        elif case_type == "EXPORT":
            try:
                # 获取入参中的数据
                case_describe = temp['case_describe']
                case_name = temp['case_name']
                interface_list = temp['interface_id'].split('-')
                interface_id = interface_list[len(interface_list) - 1]
                # 是否断言
                is_assert = temp["is_assert"]
                asserts = temp["asserts"]
                header = temp.get('header', '')
            except Exception as e:
                error_info = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{error_info}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
            # 保存案例信息至数据库
            case = CaseInfo(case_name=case_name, case_type=case_type, interface_id=interface_id,
                            is_init=False, init_sql="", is_back=False,
                            back_sql="", is_encrypt=False, case_describe=case_describe,
                            creater=username, is_assert=is_assert,
                            update_person=username, create_time=create_time, update_time=create_time, header=header,
                            interface_api_id=interface_id)
            case.save()
            # 更新案例中所属接口在interface_info表中的is_related字段为1
            try:
                InterfaceInfo.objects.filter(interface_id=interface_id).update(is_related=1)
                logger.info("接口%s更新is_related字段为1成功" % interface_id)
            except Exception as e:
                logger.error("接口%s更新is_related字段为1失败" % interface_id)
            # 调用新增断言方法,将案例中的断言信息存至数据库
            case_id = case.case_id
            if is_assert:
                assert_id = CommonUtil().insertAssertResultInfo(asserts, username, case_id)
            else:
                assert_id = ''
            CaseInfo.objects.filter(case_id=case_id).update(asserts=assert_id)
            content = "用户 %s 新增导出案例成功，案例信息：%s" % (username, case.__dict__)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("案例新增成功")
        # 若案例类型为导入解析
        elif case_type == "UPLOAD":
            try:
                # 获取入参中的数据
                case_describe = temp['case_describe']
                case_name = temp['case_name']
                interface_list = temp['interface_id'].split('-')
                interface_id = interface_list[len(interface_list) - 1]
                # 是否断言
                is_assert = temp["is_assert"]
                asserts = temp["asserts"]
                header = temp.get('header', '')
                interface_data = temp['interface_data']
                file_path = temp['file_path']
                upload_min_rows = temp['upload_min_rows']
                upload_max_rows = temp.get('upload_max_rows','')
                template_file = temp.get('temp_file', '')
                upload_param = temp.get('upload_param','')
            except Exception as e:
                error_info = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{error_info}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
            # 保存案例信息至数据库
            case = CaseInfo(case_name=case_name, case_type=case_type, interface_id=interface_id,
                            is_init=False, init_sql="", is_back=False,
                            back_sql="", is_encrypt=False, case_describe=case_describe,interface_data=interface_data,
                            creater=username, is_assert=is_assert,
                            update_person=username, create_time=create_time, update_time=create_time, header=header,
                            interface_api_id=interface_id)
            case.save()
            # 更新案例中所属接口在interface_info表中的is_related字段为1
            try:
                InterfaceInfo.objects.filter(interface_id=interface_id).update(is_related=1)
                logger.info("接口%s更新is_related字段为1成功" % interface_id)
            except Exception as e:
                logger.error("接口%s更新is_related字段为1失败" % interface_id)
            # 调用新增断言方法,将案例中的断言信息存至数据库
            case_id = case.case_id
            # 如果文件路径存在，则移动文件
            if file_path:
                new_path = os.path.join(settings.FILE_PATH, 'case', str(case_id))
                print("new_path:", new_path)
                #将参数文件移动到案例目录下
                target_file_path = GetModelInfo().move_file(file_path, new_path)

                # 更新上传文件路径interface_data
                new_interface_data = json.dumps(
                    {"file_path": target_file_path,"template_file": template_file, "upload_min_rows": upload_min_rows, "upload_max_rows": upload_max_rows, "upload_param": upload_param})
                CaseInfo.objects.filter(case_id=case_id).update(interface_data=new_interface_data)
            if is_assert:
                assert_id = CommonUtil().insertAssertResultInfo(asserts, username, case_id)
            else:
                assert_id = ''
            CaseInfo.objects.filter(case_id=case_id).update(asserts=assert_id)
            content = "用户 %s 新增导入案例成功，案例信息：%s" % (username, case.__dict__)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("导入案例新增成功")
        elif case_type == "API":
            try:
                case_describe = temp['case_describe']
                case_name = temp['case_name']
                interface_list = temp['interface_id'].split('-')
                interface_id = interface_list[len(interface_list) - 1]
                is_init = temp["is_init"]
                init_database = temp['init_database']
                init_sql = temp['init_sql']
                is_back = temp["is_back"]
                back_sql = temp['back_sql']
                csv_file = temp.get('csv_file', '')
                header = temp.get('header', '')
                if is_init:  # 是否初始化
                    if isContainsDDL(init_sql):  # 初始化语句安全校验
                        return ResponseUtils.return_fail(AppErrors.ERROR_CASE_INIT_SQL_IS_ILLEGAL, init_sql)
                    if is_back:  # 回退语句安全校验
                        if isContainsDDL(back_sql):
                            return ResponseUtils.return_fail(AppErrors.ERROR_CASE_BACK_SQL_IS_ILLEGAL, back_sql)
                is_encrypt = temp["is_encrypt"]
                # 后续加解密逻辑待实际处理
                interface_data = temp['interface_data']
                is_assert = temp["is_assert"]
                asserts = temp["asserts"]
                case_level = temp['case_level']
            except Exception as e:
                error_info = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{error_info}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
            case = CaseInfo(case_name=case_name, interface_id=interface_id, case_describe=case_describe,
                            is_init=is_init, init_database=init_database, init_sql=init_sql, is_back=is_back,
                            back_sql=back_sql, is_encrypt=is_encrypt, case_level=case_level,
                            interface_data=interface_data, creater=username, is_assert=is_assert,
                            update_person=username, create_time=create_time, update_time=create_time,
                            interface_api_id=interface_id, csv_file=csv_file, header=header)
            case.save()
            # 更新案例中所属接口在interface_info表中的is_related字段为1
            try:
                InterfaceInfo.objects.filter(interface_id=interface_id).update(is_related=1)
                logger.info("接口%s更新is_related字段为1成功" % interface_id)
            except Exception as e:
                logger.error("接口%s更新is_related字段为1失败" % interface_id)
            case_id = case.case_id
            if is_assert:  # 是否断言
                assert_id = CommonUtil().insertAssertResultInfo(asserts, username, case_id)
            else:
                assert_id = ''
            CaseInfo.objects.filter(case_id=case_id).update(asserts=assert_id)
            content = "用户 %s 新增API案例成功，案例信息：%s" % (username, case.__dict__)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("案例新增成功")
        else:
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, 'case_type=' + case_type)

    @action(methods=['PUT'], detail=True)
    def case_update(self, request, pk):
        case_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        # 修改前信息
        case = CaseInfo.objects.get(case_id=case_id)
        case_before = case.__dict__
        # 定义一个修改时间
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        case_type = temp['case_type']
        if case_type == "SQL" or case_type == "REDIS":
            try:
                case_describe = temp['case_describe']
                case_name = temp['case_name']
                init_database = temp['init_database']
                # SQL语句
                interface_data = temp['interface_data']
                if isContainsDDL(interface_data):
                    return ResponseUtils.return_fail(AppErrors.ERROR_CASE_RUN_SQL_IS_ILLEGAL, interface_data)
                # 是否断言
                is_assert = temp["is_assert"]
                asserts = temp["asserts"]
            except Exception as e:
                error_info = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{error_info}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
            with transaction.atomic():
                # 创建事务保存点
                save_id = transaction.savepoint()
                try:
                    # 删除原来的断言
                    if case.is_assert:
                        old_asserts = case.asserts.split('|')
                        AssertResultInfo.objects.filter(assert_id__in=old_asserts).update(is_active=False)
                    if is_assert:
                        assert_id = CommonUtil().insertAssertResultInfo(asserts, username, case_id)
                    else:
                        assert_id = ''
                    CaseInfo.objects.filter(case_id=case_id).update(case_name=case_name, interface_id="",
                                                                    case_type=case_type,
                                                                    case_describe=case_describe, is_init=False,
                                                                    init_database=init_database,
                                                                    update_time=update_time,
                                                                    init_sql="", is_back=False, back_sql="",
                                                                    is_encrypt=False,
                                                                    interface_data=interface_data, asserts=assert_id,
                                                                    is_assert=is_assert, update_person=username)
                    case_after = CaseInfo.objects.get(case_id=case_id).__dict__
                    content = "用户 %s 修改SQL案例信息成功，修改前信息：%s，修改后信息：%s" % (username, case_before, case_after)
                    # 日志记录
                    CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
                except Exception as e:
                    logger.error(e)
                    transaction.savepoint_rollback(save_id)
                    return ResponseUtils.return_fail(AppErrors.ERROR_SAVE_SCENE)
                # 提交订单成功，显式的提交一次事务
                transaction.savepoint_commit(save_id)
            return ResponseUtils.return_success("案例编辑成功")
        elif case_type == "API" or case_type == "EXPORT" or case_type == "UPLOAD":
            try:
                case_describe = temp['case_describe']
                case_name = temp['case_name']
                interface_list = temp['interface_id'].split('-')
                interface_id = interface_list[len(interface_list) - 1]
                is_init = temp["is_init"]
                init_database = temp['init_database']
                init_sql = temp['init_sql']
                is_back = temp["is_back"]
                back_sql = temp['back_sql']
                csv_file = temp.get('csv_file', '')
                header = temp.get('header', '')
                if is_init:  # 是否初始化
                    if isContainsDDL(init_sql):  # 初始化语句安全校验
                        return ResponseUtils.return_fail(AppErrors.ERROR_CASE_INIT_SQL_IS_ILLEGAL, init_sql)
                    if is_back:  # 回退语句安全校验
                        if isContainsDDL(back_sql):
                            return ResponseUtils.return_fail(AppErrors.ERROR_CASE_BACK_SQL_IS_ILLEGAL, back_sql)
                is_encrypt = temp["is_encrypt"]
                #  后续加解密逻辑待实际处理
                interface_data = temp['interface_data']
                is_assert = temp["is_assert"]
                asserts = temp["asserts"]
                case_level = temp['case_level']
                # 上传参数
                if case_type =="UPLOAD":
                    file_path = temp.get('file_path','')
                    upload_min_rows = temp.get('upload_min_rows')
                    upload_max_rows = temp.get('upload_max_rows', '')
                    template_file = temp.get('temp_file', '')
                    upload_param = temp.get('upload_param', '')
                    interface_data = json.loads(interface_data)
                    # 更新 upload_min_rows、upload_max_rows、template_file的值
                    interface_data["upload_min_rows"] = upload_min_rows
                    interface_data["upload_max_rows"] = upload_max_rows
                    interface_data["template_file"] = template_file
                    interface_data["upload_param"] = upload_param
                    # file_path不为空，表示文件更新，需移动文件
                    if file_path:
                        new_path = os.path.join(settings.FILE_PATH, 'case', str(case_id))
                        # 移动文件至指定目录
                        target_file_path = GetModelInfo().move_file(file_path, new_path)
                        # 更新数据文件地址
                        interface_data["file_path"] = target_file_path
                    interface_data = json.dumps(interface_data)
            except Exception as e:
                error_info = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{error_info}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
            with transaction.atomic():
                # 创建事务保存点
                save_id = transaction.savepoint()
                try:
                    # 删除原来的断言
                    if case.is_assert:
                        old_asserts = case.asserts.split('|')
                        AssertResultInfo.objects.filter(assert_id__in=old_asserts).update(is_active=False)
                    if is_assert:  # 是否断言
                        assert_id = CommonUtil().insertAssertResultInfo(asserts, username, case_id)
                    else:
                        assert_id = ''
                    CaseInfo.objects.filter(case_id=case_id).update(case_name=case_name, interface_id=interface_id,
                                                                    case_describe=case_describe, case_level=case_level,
                                                                    is_init=is_init, init_database=init_database,
                                                                    is_back=is_back, back_sql=back_sql,
                                                                    update_time=update_time, csv_file=csv_file,
                                                                    is_encrypt=is_encrypt, init_sql=init_sql,
                                                                    interface_data=interface_data,
                                                                    update_person=username,
                                                                    asserts=assert_id, is_assert=is_assert,
                                                                    interface_api_id=interface_id, header=header)
                    case_after = CaseInfo.objects.get(case_id=case_id).__dict__
                    content = "用户 %s 修改%s案例信息成功，修改前信息：%s，修改后信息：%s" % (username,case_type, case_before, case_after)
                    # 日志记录
                    CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
                except Exception as e:
                    logger.error(e)
                    transaction.savepoint_rollback(save_id)
                    return ResponseUtils.return_fail(AppErrors.ERROR_SAVE_SCENE)
                # 提交订单成功，显式的提交一次事务
                transaction.savepoint_commit(save_id)
            return ResponseUtils.return_success("案例编辑成功")
        else:
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, 'case_type=' + case_type)

    @action(methods=['DELETE'], detail=True)
    def case_delete(self, request, pk):
        case_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        if request.method == 'DELETE':
            case = CaseInfo.objects.get(case_id=case_id)
            case_info = case.__dict__
            case_type = case_info['case_type']
            # 判断案例是否被场景所引用
            scene_id_list = []
            scene_relation = SceneRelation.objects.filter(relation_case_id=case_id, is_active=True)
            if scene_relation:
                for relation in scene_relation:
                    scene_relation_id = relation.scene_relation_id
                    scene1 = str(scene_relation_id) + "|"
                    scene2 = "|" + str(scene_relation_id) + "|"
                    scene3 = "|" + str(scene_relation_id)
                    # case_pool like ('XXXX%')
                    result1 = SceneInfo.objects.filter(scene_relation_id__startswith=scene1, is_active=True)
                    # case_pool like ('%XXXX%')
                    result2 = SceneInfo.objects.filter(scene_relation_id__contains=scene2, is_active=True)
                    # case_pool like ('%XXXX')
                    result3 = SceneInfo.objects.filter(scene_relation_id__endswith=scene3, is_active=True)
                    # case_pool = ('XXXX')   当任务只引用了一个场景时,数据库存储的数据为XXXX
                    result4 = SceneInfo.objects.filter(scene_relation_id=scene_relation_id, is_active=True)
                    sceneList = result1.union(result2, result3, result4)
                    for scene in sceneList:
                        scene_id_list.append(scene.scene_id)
                scene_id_list = list(set(scene_id_list))
                return ResponseUtils.return_fail(AppErrors.ERROR_CASE_IS_USE_BY_SCENE,
                                                 'scene_id: %s ' % str(scene_id_list))
            # # 判断案例是否被引用在任务中
            # task_info = TaskInfo.objects.filter(case_pool__icontains=str(case_id), is_active=True)
            # if task_info:
            #     task_id_list = []
            #     for task in task_info:
            #         task_id_list.append(task.task_id)
            #     task_id_list = list(set(task_id_list))
            #     return ResponseUtils.return_fail(AppErrors.ERROR_CASE_IS_USE_BY_TASK,
            #                                      'task_id: %s ' % str(task_id_list))
            else:
                if case.is_assert:
                    asserts = case.asserts.split('|')
                    AssertResultInfo.objects.filter(assert_id__in=asserts).update(is_active=False)
                CaseInfo.objects.filter(case_id=case_id).update(is_active=False, update_person=username,
                                                                update_time=DateUtils.get_current_time())
                # 判断是否更新案例中所属接口在interface_info表中的is_related字段为0:若存在其他案例引用则保持为1,若无其他案例引用则更新为0
                # 获取案例关联的接口id
                interface_id = case.interface_id
                # 当案例类型为API或者导出解析时,接口id不为空
                if interface_id != "":
                    # 根据接口id在案例表中过滤是否还有其他未被删除的案例引用该接口
                    if CaseInfo.objects.filter(interface_id=interface_id, is_active=True):
                        # 若存在则不更新该接口is_related字段为0
                        logger.info("接口%s被其他案例关联,不更新该接口is_related值" % interface_id)
                    else:
                        # 若不存在则更新该接口is_related字段为0
                        InterfaceInfo.objects.filter(interface_id=interface_id).update(is_related=0)
                        logger.info("接口%s更新is_related字段为0成功" % interface_id)
                else:
                    # 若接口id为空,则证明该案例为SQL或REDIS案例
                    logger.info("案例未关联接口")
                # 如果是上传案例，需移除已上传的数据文件夹
                if case_type == "UPLOAD":
                    interface_data = json.loads(case_info["interface_data"])
                    file_path = interface_data['file_path']
                    # 截取文件夹路径
                    folder_path = os.path.dirname(file_path)
                    GetModelInfo().delete_file(folder_path)

                content = "用户 %s 删除了案例：%s" % (username, case_info)
                # 日志记录
                CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("案例删除成功")

    @action(methods=['POST'], detail=True)
    def case_run(self, request, pk):
        case_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            env_type = temp['env_type']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        # api = APIAutoTest(case_id, env_type, username)
        case_info = str(case_id) + "-" + CaseInfo.objects.get(case_id=case_id).case_name
        content = "用户 %s 开始调试案例：%s" % (username, case_info)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        api = ApiExecute(case_id, env_type, username)
        log = api.report()
        return ResponseUtils.return_success("案例调试完成", log)

    @action(methods=['GET'], detail=True)
    def case_copy(self, request, pk):
        """
          复制接口 infoCopy
        :param request:
        :param pk:  必填 复制数据的id
               type: 必填  复制的类型 apiCase：api案例  scene：场景 interface：接口
        :return:
        """
        id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        if request.method == "GET":
            try:
                type = request.GET['type']
            except Exception as e:
                error_info = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{error_info}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
            if type == "apiCase":
                content = "用户 %s 请求复制案例信息，案例ID为 %s" % (username, id)
                flag, msg = CommonUtil().caseCopy(id, username)
            elif type == "scene":
                content = "用户 %s 请求复制场景信息，场景ID为 %s" % (username, id)
                flag, msg = CommonUtil().sceneCopy(id, username)
            elif type == 'interface':
                content = "用户 %s 请求复制接口信息，接口ID为 %s" % (username, id)
                flag, msg = CommonUtil().interface_copy(id, username)
            else:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, "type参数异常：%s" % type)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            if flag:
                return ResponseUtils.return_success(msg)
            else:
                return ResponseUtils.return_fail(AppErrors.ERROR_CASE_COPY, msg)

    def get_unique_str(self):
        uuid_str = str(uuid.uuid4())
        md5 = hashlib.md5()
        md5.update(uuid_str.encode('utf-8'))
        return md5.hexdigest()

    @action(methods=['GET'], detail=False)
    def download_csv(self, request):
        """
          下载csv文件
        :param request:
           csv_file： 必填  下载的文件路径
        :return:
        """
        csv_file = request.GET.get("csv_file", "")
        if csv_file and csv_file.strip() != "":
            file_name = csv_file.split('/')[-1].encode('utf-8')
            if not os.path.exists(settings.BASE_DIR + '/' + csv_file):
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, "文件不存在")
            file = open(settings.BASE_DIR + '/' + csv_file, 'rb')
            response = FileResponse(file)
            response['Content-Type'] = 'application/octet-stream'
            response["Content-Disposition"] = "attachment; filename*=UTF-8''{}".format(escape_uri_path(file_name))
            return response
        return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION)


class SceneInfoViewSet(viewsets.ModelViewSet):
    queryset = SceneInfo.objects.all()
    serializer_class = s.SceneInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def scene_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
            scene_id = request.GET.get('search_scene_id', '')
            scene_name = request.GET.get('search_scene_name', '')
            case_id = request.GET.get('case_id', '')
            is_related = request.GET.get('is_related', '')
            business_id = request.GET.get('business', '')
            creater = request.GET.get('creater', "")
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询场景，查询条件：场景编号=%s，场景名称=%s,案例编号=%s" % (username, scene_id, scene_name, case_id)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = SceneInfo.objects.filter(is_active=True)
        if scene_id:
            obj = obj.filter(scene_id=scene_id)
        if scene_name:
            obj = obj.filter(scene_name__contains=scene_name)
        if case_id:
            # 通过关联表查到案例关联id,再通过关联ID查场景表
            scene_relation_id_list = SceneRelation.objects.filter(relation_case_id=case_id, is_active=True).values_list('scene_relation_id', flat=True)
            # 定义一个空的结果集,用于存放for循环中过滤出来的场景数据
            scene_list = SceneInfo.objects.none()
            # 根据案例关联id循环过滤出场景数据
            for scene_relation_id in scene_relation_id_list:
                # 拼接结果集
                scene1 = str(scene_relation_id) + "|"
                scene2 = "|" + str(scene_relation_id) + "|"
                scene3 = "|" + str(scene_relation_id)
                # case_pool like ('XXXX%')
                result1 = SceneInfo.objects.filter(scene_relation_id__startswith=scene1, is_active=True)
                # case_pool like ('%XXXX%')
                result2 = SceneInfo.objects.filter(scene_relation_id__contains=scene2, is_active=True)
                # case_pool like ('%XXXX')
                result3 = SceneInfo.objects.filter(scene_relation_id__endswith=scene3, is_active=True)
                # case_pool = ('XXXX')   当任务只引用了一个场景时,数据库存储的数据为XXXX
                result4 = SceneInfo.objects.filter(scene_relation_id=scene_relation_id, is_active=True)
                # resultlist = result1.union(result2, result3, result4, all=True)
                resultlist = result1 | result2 | result3 | result4
                scene_list = scene_list | resultlist
            obj = scene_list
        if business_id:
            obj = obj.filter(business_id=business_id)
        if is_related:
            obj = obj.filter(is_related=is_related)
        if creater:
            obj = obj.filter(creater__contains=creater)
        obj = obj.order_by('-update_time')
        paginator = Paginator(obj, limit)
        data = paginator.page(page)
        data_list = list(data.object_list.values())
        for item in data_list:
            businessInfo = BusinessInfo.objects.get(business_id=item["business_id"])
            item["businessName"] = businessInfo.business_name
        response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count, 'search_scene_id': scene_id,
                         'search_scene_name': scene_name, 'scenes': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def scene_add(self, request):
        """
            场景添加
        :param request:
           scene_name 必填  场景名称
           scene_describe 非必填  场景描述
           before_param   非必填  前置参数
           relation       必填    案例数据
        :return:
        """
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            scene_name = temp['scene_name']
            business_id = temp['business_id']
            scene_describe = temp['scene_describe']
            before_param = temp['before_param']
            relation_list = temp["relation"]
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        relation = []
        # 显式的开启一个事务
        with transaction.atomic():
            # 创建事务保存点
            save_id = transaction.savepoint()
            scene_type = '0'
            try:
                for item in relation_list:
                    assert_ids = None
                    index = item.get('index', 1)
                    is_enable = item.get('is_enable', 1)
                    is_check = item.get('is_check', 0)
                    # 判断是否存在导出解析案例
                    case_type = item.get('relation_case_type')
                    case_id = item.get('relation_case_id')
                    if case_type == 'EXPORT' and is_enable is True:
                        scene_type = '1'
                    if case_type == 'UPLOAD' and is_enable is True:
                        scene_type = '2'
                    if item.get('reset_param'):
                        # 判断为导入案例
                        if case_type == 'UPLOAD':
                            interface_data = item.get('interface_data')
                            upload_min_rows = item.get('upload_min_rows')
                            upload_max_rows = item.get('upload_max_rows', '')
                            template_file = item.get('temp_file', '')
                            upload_param = item.get('upload_param', '')
                            interface_data = json.loads(interface_data)
                            # 先更新 upload_min_rows 和 upload_max_rows 的值
                            interface_data["upload_min_rows"] = upload_min_rows
                            interface_data["upload_max_rows"] = upload_max_rows
                            interface_data["template_file"] = template_file
                            interface_data["upload_param"] = upload_param
                            interface_data = json.dumps(interface_data)
                        else:
                            interface_data = item.get('interface_data')
                        is_assert = item.get('is_assert')
                        asserts = item.get('asserts')
                        if is_assert:  # 是否断言
                            assert_ids = CommonUtil().insertAssertResultInfo(asserts, username, case_id, is_case_assert=False)
                    else:
                        interface_data = None
                        is_assert = False
                        item['reset_param'] = False
                    if item.get('response_param_alias') is None:
                        item['response_param_alias'] = item.get('response_param')
                    else:
                        response_param_num = len(item.get('response_param').split(';'))
                        response_param_alias_num = len(item.get('response_param_alias').split(';'))
                        if response_param_num != response_param_alias_num:
                            transaction.savepoint_rollback(save_id)
                            return ResponseUtils.return_fail(AppErrors.ERROR_SCENE_PARAM_ARGS,
                                                             'case_id: %s' % item['relation_case_id'].split("-")[0])
                    cr = SceneRelation.objects.create(relation_case_id=item['relation_case_id'],
                                                      is_get_param=item['is_get_param'],
                                                      request_param=item['request_param'],
                                                      response_param=item['response_param'],
                                                      response_param_alias=item['response_param_alias'],
                                                      sleep_time=item['sleep_time'],
                                                      response_header_param=item['response_header_param'],
                                                      reset_param=item.get('reset_param'),
                                                      instructions=item['instructions'],
                                                      interface_data=interface_data,
                                                      is_assert=is_assert,
                                                      asserts=assert_ids, index=index,is_enable=is_enable,is_check=is_check,
                                                      creater=username, create_time=create_time)
                    scene_relation_id = cr.scene_relation_id
                    # 如果文件路径存在，表示文件更新，需移动文件
                    file_path = item.get('file_path', '')
                    if case_type == 'UPLOAD' and file_path:
                        new_path = os.path.join(settings.FILE_PATH, 'scene',  str(scene_relation_id))
                        # 移动文件至指定目录
                        target_file_path = GetModelInfo().move_file(file_path, new_path)
                        interface_data = json.loads(cr.interface_data)
                        # 更新参数文件路径
                        interface_data["file_path"] = target_file_path
                        new_interface_data = json.dumps(interface_data)
                        # 更新场景关联表的interface_data字段
                        SceneRelation.objects.filter(scene_relation_id=scene_relation_id).update(interface_data=new_interface_data)
                    # 更新场景中所属案例在case_info表中的is_related字段为1
                    try:
                        # 若不存在则更新该案例is_related字段为0
                        CaseInfo.objects.filter(case_id=case_id).update(is_related=1)
                        logger.info("案例%s更新is_related字段为1成功" % case_id)
                    except:
                        logger.error("案例%s更新is_related字段失败" % case_id)
                    relation.append(str(cr.scene_relation_id))
                relation = '|'.join(relation)
                # 保存场景信息至数据库
                scene = SceneInfo(scene_name=scene_name, scene_describe=scene_describe, scene_relation_id=relation,
                                  creater=username, update_person=username, create_time=create_time,
                                  before_param=before_param, business_id=business_id,
                                  update_time=create_time, scene_type=scene_type, is_related=0)
                scene.save()
                content = "用户 %s 新增场景成功，场景信息：%s" % (username, scene.__dict__)
                # 日志记录
                CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            except Exception as e:
                logger.error(e)
                transaction.savepoint_rollback(save_id)
                return ResponseUtils.return_fail(AppErrors.ERROR_SAVE_SCENE)
            # 提交订单成功，显式的提交一次事务
            transaction.savepoint_commit(save_id)
        return ResponseUtils.return_success("场景添加成功")

    @action(methods=['PUT'], detail=True)
    def scene_update(self, request, pk):
        scene_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            scene_name = temp['scene_name']
            business_id = temp['business_id']
            scene_describe = temp['scene_describe']
            before_param = temp['before_param']
            relation_list = temp["relation"]
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        scene = SceneInfo.objects.get(scene_id=scene_id)
        scene_before = scene.__dict__
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        # 显式的开启一个事务
        with transaction.atomic():
            # 创建事务保存点
            save_id = transaction.savepoint()
            try:
                # 删除原有关联案例
                old_relation = scene.scene_relation_id.split('|')
                for item in old_relation:
                    SceneRelation.objects.filter(scene_relation_id=item).update(is_active=False)
                    old_case_id = SceneRelation.objects.get(scene_relation_id=item).relation_case_id
                    if SceneRelation.objects.filter(relation_case_id=old_case_id, is_active=True):
                        logger.info("案例%s被其他场景关联,不更新该案例is_related值" % old_case_id)
                    else:
                        CaseInfo.objects.filter(case_id=old_case_id).update(is_related=0)
                        logger.info("案例%s更新is_related字段为0成功" % old_case_id)
                relation = []
                scene_type = '0'
                for item in relation_list:
                    assert_ids = None
                    index = item.get('index', 1)
                    is_enable = item.get('is_enable', 1)
                    is_check = item.get('is_check', 0)
                    # 判断是否存在导出解析案例
                    case_type = item.get('relation_case_type')
                    case_id = item.get('relation_case_id')
                    if case_type == 'EXPORT' and is_enable is True:
                        scene_type = '1'
                    if case_type == 'UPLOAD' and is_enable is True:
                        scene_type = '2'
                    if item.get('reset_param'):
                        interface_data = item.get('interface_data')
                        if case_type == 'UPLOAD':
                            upload_min_rows = item.get('upload_min_rows')
                            upload_max_rows = item.get('upload_max_rows','')
                            template_file = item.get('temp_file', '')
                            upload_param = item.get('upload_param', '')
                            # 更新interface_data
                            interface_data = json.loads(interface_data)
                            # 更新 upload_min_rows 和 upload_max_rows 的值
                            interface_data["upload_min_rows"] = upload_min_rows
                            interface_data["upload_max_rows"] = upload_max_rows
                            interface_data["template_file"] = template_file
                            interface_data["upload_param"] = upload_param
                            interface_data=json.dumps(interface_data)
                        is_assert = item.get('is_assert')
                        asserts = item.get('asserts')
                        if is_assert:  # 是否断言
                            assert_ids = CommonUtil().insertAssertResultInfo(asserts, username, case_id, is_case_assert=False)
                    else:
                        interface_data = None
                        is_assert = False
                        item['reset_param'] = False
                    if item.get('response_param_alias') is None:
                        item['response_param_alias'] = item.get('response_param')
                    else:
                        response_param_num = len(item.get('response_param').split(';'))
                        response_param_alias_num = len(item.get('response_param_alias').split(';'))
                        if response_param_num != response_param_alias_num:
                            transaction.savepoint_rollback(save_id)
                            return ResponseUtils.return_fail(AppErrors.ERROR_SCENE_PARAM_ARGS,
                                                             'case_id: %s' % item['relation_case_id'])
                    cr = SceneRelation.objects.create(relation_case_id=item['relation_case_id'],
                                                      is_get_param=item['is_get_param'],
                                                      request_param=item['request_param'],
                                                      response_param=item['response_param'],
                                                      response_param_alias=item['response_param_alias'],
                                                      sleep_time=item['sleep_time'],
                                                      reset_param=item.get('reset_param'),
                                                      instructions=item['instructions'],
                                                      interface_data=interface_data,
                                                      is_assert=is_assert,
                                                      asserts=assert_ids, index=index,is_enable=is_enable,is_check=is_check,
                                                      response_header_param=item['response_header_param'],
                                                      creater=username, create_time=update_time)
                    # 导入案例，如果is_update=1，表示文件更新，需移动参数文件至关联ID目录下
                    file_path = item.get('file_path', '')
                    is_update = item.get('is_update')
                    if case_type == 'UPLOAD' and is_update =="1":
                        new_path = os.path.join(settings.FILE_PATH, 'scene', str(cr.scene_relation_id))
                        # 移动文件至指定目录
                        target_file_path = GetModelInfo().move_file(file_path, new_path)
                        interface_data = json.loads(cr.interface_data)
                        # 更新参数文件路径
                        interface_data["file_path"] = target_file_path
                        new_interface_data = json.dumps(interface_data)
                        # 更新场景关联表的interface_data字段
                        SceneRelation.objects.filter(scene_relation_id=cr.scene_relation_id).update(
                            interface_data=new_interface_data)
                    relation.append(str(cr.scene_relation_id))
                    try:
                        CaseInfo.objects.filter(case_id=case_id).update(is_related=1)
                        logger.info("案例%s更新is_related字段为1成功" % case_id)
                    except:
                        logger.info("案例%s更新is_related字段为1失败" % case_id)
                relation = '|'.join(relation)
                SceneInfo.objects.filter(scene_id=scene_id).update(scene_name=scene_name,
                                                                   scene_describe=scene_describe,
                                                                   before_param=before_param,
                                                                   scene_relation_id=relation, update_time=update_time,
                                                                   update_person=username, business_id=business_id,
                                                                   scene_type=scene_type)
                scene_after = SceneInfo.objects.get(scene_id=scene_id).__dict__
                content = "用户 %s 修改场景信息成功，修改前：%s，修改后：%s" % (username, scene_before, scene_after)
                # 日志记录
                CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            except Exception as e:
                logger.error(e)
                transaction.savepoint_rollback(save_id)
                return ResponseUtils.return_fail(AppErrors.ERROR_SAVE_SCENE)
                # 提交订单成功，显式的提交一次事务
            transaction.savepoint_commit(save_id)
        return ResponseUtils.return_success("场景编辑成功")

    @action(methods=['DELETE'], detail=True)
    def scene_delete(self, request, pk):
        scene_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        scene = SceneInfo.objects.get(scene_id=scene_id)
        scene_info = scene.__dict__
        # 判断案例是否被引用在任务中
        query_scene1 = scene_id + "|"
        query_scene2 = "|" + scene_id + "|"
        query_scene3 = "|" + scene_id
        # case_pool like ('XXXX%')
        result1 = TaskInfo.objects.filter(case_pool__startswith=query_scene1, is_active=True,
                                          api_type='SCENE')
        # case_pool like ('%XXXX%')
        result2 = TaskInfo.objects.filter(case_pool__contains=query_scene2, is_active=True,
                                          api_type='SCENE')
        # case_pool like ('%XXXX')
        result3 = TaskInfo.objects.filter(case_pool__endswith=query_scene3, is_active=True,
                                          api_type='SCENE')
        # case_pool = ('XXXX')   当任务只引用了一个场景时,数据库存储的数据为XXXX
        result4 = TaskInfo.objects.filter(case_pool=scene_id, is_active=True, api_type='SCENE')
        task_info = result1.union(result2, result3, result4)
        # task_info = TaskInfo.objects.filter(api_type="Scene").filter(case_pool__icontains=str(scene_id),
        #                                                               is_active=True)
        if task_info:
            task_id_list = []
            for task in task_info:
                task_id_list.append(task.task_id)
            task_id_list = list(set(task_id_list))
            return ResponseUtils.return_fail(AppErrors.ERROR_CASE_SCENE_IS_QUOTE, '任务id: %s' % task_id_list)
        else:
            # 显式的开启一个事务
            with transaction.atomic():
                # 创建事务保存点
                save_id = transaction.savepoint()
                try:
                    # 先删除场景组成数据
                    for rela_id in scene.scene_relation_id.split("|"):
                        SceneRelation.objects.filter(scene_relation_id=rela_id).update(is_active=False)
                        case_id = SceneRelation.objects.get(scene_relation_id=rela_id).relation_case_id
                        if SceneRelation.objects.filter(relation_case_id=case_id, is_active=True):
                            logger.info("案例%s被其他场景关联,不更新该案例is_related值" % case_id)
                        else:
                            CaseInfo.objects.filter(case_id=case_id).update(is_related=0)
                            logger.info("案例%s更新is_related字段为0成功" % case_id)
                    # 再删除场景本身
                    scene.is_active = False
                    scene.update_time = DateUtils.get_current_time()
                    scene.update_person = username
                    scene.save()
                except Exception as e:
                    logger.error(e)
                    transaction.savepoint_rollback(save_id)
                    return ResponseUtils.return_fail(AppErrors.ERROR_DELETE_SCENE)
                transaction.savepoint_commit(save_id)
        content = "用户 %s 删除了场景：%s" % (username, scene_info)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("场景删除成功")

    @action(methods=['POST'], detail=False)
    def scene_run(self, request):
        """
            场景执行
        :param request:
          scene_id  必填  场景id
          env_type  必填  执行环境
        :return:
        """
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            scene_id = str(temp['scene_id'])
            env_id = temp['env_type']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        log = t.task_scene_job(scene_id, env_id, username)
        return ResponseUtils.return_success("场景异步提交成功，请稍后查看调用日志结果！", log)

    @action(methods=['GET'], detail=False)
    def scene_debug_log(self, request):
        """
          获取场景调试日志
        :param request:
          scene_id ： 必填 场景id
          log_type： 必填  日志类型
        :return:
        """
        try:
            scene_id = request.GET['scene_id']
            log_type = request.GET['log_type']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

        if scene_id == '' or log_type == '':
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, '缺少参数值')
        log_record = LogRecord.objects.filter(log_type=log_type, running_log_type__exact='2',
                                              running_log_id__exact=scene_id).first()
        if not log_record:
            return ResponseUtils.return_fail(AppError("HD1000", "暂无日志记录，请先调试!"))

        return ResponseUtils.return_success("获取成功", eval(log_record.log_content))

class TestPlatReportViewSet(viewsets.ModelViewSet):
    queryset = ProjectInfo.objects.all()
    serializer_class = s.ProjectInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def calculate_case_pool_counts(self, request):
        try:
            production_task_ids = request.GET.get('production_task_ids', '')
            test_task_ids = request.GET.get('test_task_ids', '')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        data = weekly.calculate_case_pool_counts(production_task_ids, test_task_ids)
        production_total_count = data["production_total_count"]
        print(production_total_count)
        return ResponseUtils.return_success("查询成功", data)

    @action(methods=['GET'], detail=False)
    def get_weekly_scene_counts(self, request):
        data = weekly.get_weekly_scene_counts(request)
        return ResponseUtils.return_success("查询成功", data)

    @action(methods=['GET'], detail=False)
    def get_weekly_task_counts(self,request):
        data = weekly.get_weekly_task_counts(request)
        return  ResponseUtils.return_success("查询成功", data)

    @action(methods=['GET'], detail=False)
    def get_task_running_summary(self, request):
        data = weekly.get_task_running_summary(request)
        return ResponseUtils.return_success("查询成功", data)

    @action(methods=['GET'], detail=False)
    def send_weekly_report(self, request):
        username = UserUtils.get_login_user(request).username
        data = t.task_week_report_job()
        content = "用户 %s 手动发送测试周报" % (username)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("查询成功", data)


