from django.db.models import F, <PERSON>, Sum, Subquery, OuterRef
from django.db.models.functions import TruncDate

from base.models import UserInfo
from .models import *
from data_config.models import TaskInfo
from django.db.models import Count
from tools_help.models import ServiceConfig
from django.utils import timezone
from interface_test.models import SceneInfo
from data_config.models import TaskRunningRecord
from tools_help.models import ReleaseRecordInfo
from interface_test.models import WeeklyStatistics
from datetime import timedelta
from collections import defaultdict
import pandas as pd
import matplotlib
from matplotlib.ticker import FuncFormatter

matplotlib.use('Agg')
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
from io import BytesIO
import base64
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import smtplib

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()


class WeekReport:

    # 统计核心用例任务自动化场景总数
    def calculate_case_pool_counts(self, production_task_ids=None, test_task_ids=None):
        if not production_task_ids:
            production_task_ids = [143, 230, 244]  # 生产核心任务+导出任务+会员标签人群数据比对
        if not test_task_ids:
            test_task_ids = [138, 237, 240, 250]  # 测试核心任务+导出任务+oms分摊任务+会员自定义人群(数据条件比对)
        today = timezone.now().date()
        # 计算上周五和本周周四的日期范围
        start_of_week = today - timezone.timedelta(days=today.weekday() + 3)  # 上周周五
        end_of_week = today + timezone.timedelta(days=(3 - today.weekday()))  # 本周周四
        start_of_last_week = start_of_week - timedelta(days=7)  # 上周开始时间
        end_of_last_week = end_of_week - timedelta(days=7)  # 上周结束时间

        try:
            # 查询生产任务的 case_pool 并计算生产总数
            production_case_pools = TaskInfo.objects.filter(task_id__in=production_task_ids).values_list('case_pool',
                                                                                                         flat=True)
            production_total_count = sum(len(case_pool.split('|')) for case_pool in production_case_pools if case_pool)
        except Exception as e:
            production_total_count = 0
            print(f"Error calculating production total count: {e}")

        try:
            # 查询测试任务的 case_pool 并计算测试总数
            test_case_pools = TaskInfo.objects.filter(task_id__in=test_task_ids).values_list('case_pool', flat=True)
            test_total_count = sum(len(case_pool.split('|')) for case_pool in test_case_pools if case_pool)
        except Exception as e:
            test_total_count = 0
            print(f"Error calculating test total count: {e}")

        # 获取上周的记录
        last_week_record = WeeklyStatistics.objects.filter(
            start_time__gte=start_of_last_week,
            end_time__lte=end_of_last_week
        ).first()  # 假定每周只有一条记录

        # 计算新增数量
        production_new_count = production_total_count - (last_week_record.pro_case_counts if last_week_record else 0)
        test_new_count = test_total_count - (last_week_record.test_case_counts if last_week_record else 0)
        # 保存当前周的统计数据
        ws = WeeklyStatistics.objects.create(
            start_time=start_of_week,
            end_time=end_of_week,
            pro_case_counts=production_total_count,
            test_case_counts=test_total_count,
            test_new_count=test_new_count,
            pro_new_count=production_new_count
        )
        data = {
            "production_total_count": production_total_count,
            "test_total_count": test_total_count,
            "production_new_count": production_new_count,
            "test_new_count": test_new_count,
            "start_of_last_week": start_of_last_week,
            "end_of_last_week": end_of_last_week
        }
        print(last_week_record)
        return data

    # 测试人员当周自动化实现情况统计

    def get_weekly_scene_counts(self):
        # 获取当前日期
        today = timezone.now().date()

        # 计算上周五和本周周四的日期范围
        start_of_period = today - timezone.timedelta(days=today.weekday() + 3)  # 上周周五
        end_of_period = today + timezone.timedelta(days=(3 - today.weekday()))  # 本周周四

        # 查询在此日期范围内创建的记录，并按创建者分组统计场景数量
        scene_counts = (
            SceneInfo.objects.filter(
                create_time__date__gte=start_of_period,
                create_time__date__lte=end_of_period,
                is_active=True
            )
            .values('creater')
            .annotate(
                total_scenes=Count('scene_id')
            )
            .annotate(
                chinese_name=Subquery(
                    UserInfo.objects.filter(
                        username=OuterRef('creater')
                    ).values('chinese_name')[:1]  # [:1]只取符合条件的第一条记录，确保子查询返回单一值，避免报错
                )
            )
            .values('chinese_name', 'total_scenes')
            .order_by('chinese_name')  # 根据创建者分组
        )

        # 固定用户列表
        fixed_users = {
            '刘艳霞': 0,
            '周日明': 0,
            '杨逍': 0,
            '张蓝心': 0,
            '胡鹏': 0,
            '周倩雯': 0,
            '余巧云': 0,
            '赵梦姿': 0,
            '周珍': 0,
            '李明丽': 0
        }

        # 更新固定用户字典
        for record in scene_counts:
            chinese_name = record['chinese_name']
            if chinese_name in fixed_users:
                fixed_users[chinese_name] = record['total_scenes']

        # 构建statistics数据,转换字典为列表
        result_count = [
            {'chinese_name': name, 'total_scenes': total_scenes}
            for name, total_scenes in fixed_users.items()
        ]
        # 按 total_scenes 从高到低排序
        result_count = sorted(result_count, key=lambda x: x['total_scenes'], reverse=True)
        # 格式化返回数据
        result = {
            'period': {
                'start_date': start_of_period.strftime('%Y-%m-%d'),
                'end_date': end_of_period.strftime('%Y-%m-%d')
            },
            'statistics': result_count,
            'total_creators': len(fixed_users),
            'total_scenes': sum(fixed_users.values())
        }
        return result

    # 自动化发版运行统计情况

    def get_weekly_task_counts(self):
        # 定义 task_id 到任务名称的映射
        task_name_map = {
            '143': "生产核心",  # 改为字符串类型的键
            '196': "微商城",
            '137': "药事云",
            '239': "四季蝉",
            '166': "会员营销",
            '169': "电商云",
            '164': "商品云",
            '165': "支付云",
            '195': "组织云",
            '163': "随心看"
        }
        # 获取当前日期
        today = timezone.now().date()

        # 计算上周五到本周周四的日期范围
        start_of_period = today - timezone.timedelta(days=today.weekday() + 3)  # 上周周五
        end_of_period = today + timezone.timedelta(days=(3 - today.weekday()))  # 本周周四

        # 筛选出本周的记录，并按 task_id 分组统计记录总数
        task_counts = (
            ReleaseRecordInfo.objects
            .filter(create_time__date__gte=start_of_period, create_time__date__lte=end_of_period)
            .values('task_id')
            .annotate(total_records=Count('record_id'))
            .order_by('task_id')
        )

        # 初始化结果字典，默认所有任务的记录数为 0
        result_dict = {task_id: {"task_name": task_name, "task_id": task_id, "total_records": 0}
                       for task_id, task_name in task_name_map.items()}

        # 将查询结果更新到结果字典中
        for record in task_counts:
            task_id = record['task_id']
            if task_id in result_dict:
                result_dict[task_id]["total_records"] = record['total_records']

        result = list(result_dict.values())
        return result

    # 自动化持续集成失败率

    def get_task_running_summary(self):
        """
        查询接口：统计 task_id=143 和 230 的 test_fail + test_error 数据合并结果。
        生产task_ids = [143, 230]
        测试task_ids = [138, 237]
        """
        # 获取当前日期
        today = timezone.now().date()

        # 计算上周五和本周周四的日期范围
        start_of_week = today - timezone.timedelta(days=today.weekday() + 3)  # 上周周五
        end_of_period = today + timezone.timedelta(days=(3 - today.weekday()))  # 本周周四
        pro_task_ids = [143, 230]
        test_task_ids = [138, 237]

        # 先按任务ID和日期分组,获取每个任务每天最新的记录id
        test_record_ids = (TaskRunningRecord.objects.filter(
            task_id__in=test_task_ids,
            time_stamp__date__gte=start_of_week,
            time_stamp__date__lte=end_of_period,
            batch_number=''
        )
           .annotate(date=TruncDate('time_stamp__date'))  # 使用TruncDate对时间戳字段time_stamp__date进行日期截取,只保留日期部分
           .values('task_id', 'date')  # 按task_id和date分组
           .annotate(latest_record_id=Max('task_record_id')))  # 获取每个分组中 task_record_id 的最大值

        # 用这些id过滤出最新记录
        latest_records = TaskRunningRecord.objects.filter(
            task_record_id__in=test_record_ids.values('latest_record_id')
        ).annotate(
            date=TruncDate('time_stamp__date')
        )

        # 按日期分组,计算每天所有任务的总错误数
        test_total_errors = latest_records.values('date').annotate(
            total_fail_error_test=Sum(F('test_fail') + F('test_error'))
        ).order_by('date')

        # 生产
        # 先按任务ID和日期分组,获取每个任务每天最新的记录id
        pro_record_ids = (TaskRunningRecord.objects.filter(
            task_id__in=pro_task_ids,
            time_stamp__date__gte=start_of_week,
            time_stamp__date__lte=end_of_period
        )
                          .annotate(date=TruncDate('time_stamp__date'))
                          .values('task_id', 'date')
                          .annotate(latest_record_id=Max('task_record_id')))

        # 用这些id过滤出最新记录
        latest_records = TaskRunningRecord.objects.filter(
            task_record_id__in=pro_record_ids.values('latest_record_id')
        ).annotate(
            date=TruncDate('time_stamp__date')
        )

        # 按日期分组,计算每天所有任务的总错误数
        pro_total_errors = latest_records.values('date').annotate(
            total_fail_error_pro=Sum(F('test_fail') + F('test_error'))
        ).order_by('date')

        # 合并两个查询结果，基于日期分组
        result_dict = defaultdict(lambda: {"total_fail_error_pro": 0, "total_fail_error_test": 0})

        # 添加生产数据
        for record in pro_total_errors:
            date = record["date"]
            result_dict[date]["total_fail_error_pro"] = record["total_fail_error_pro"]

        # 添加测试数据
        for record in test_total_errors:
            date = record["date"]
            result_dict[date]["total_fail_error_test"] = record["total_fail_error_test"]

        # 转换为列表，构造最终的 JSON 响应
        result = [
            {
                "date": date,
                "total_fail_error_pro": data["total_fail_error_pro"],
                "total_fail_error_test": data["total_fail_error_test"],
            }
            for date, data in sorted(result_dict.items())  # 按日期排序
        ]

        return result

    # 查询生产调用频次top20的接口
    def get_interface_traffic_summary(self):
        # 获取当前日期
        today = timezone.now().date()

        # 计算上周五和本周周四的日期范围
        start_of_week = today - timezone.timedelta(days=(today.weekday() + 3) % 7) # 上周周五
        end_of_period = today + timezone.timedelta(days=(3 - today.weekday()) % 7)  # 本周周四
        try:
            # 获取上周top20接口
            traffic_obj = InterfaceTrafficStatistics.objects.filter(statistics_date__range=(start_of_week, end_of_period))
            interface_ids = [obj.interface_id for obj in traffic_obj]
            interface_infos = InterfaceDetail.objects.in_bulk(interface_ids)
            # 获取service_condigs
            service_configs = ServiceConfig.objects.in_bulk(
                set(interface_info.service_config_id for interface_id, interface_info in interface_infos.items() if
                    interface_info.service_config_id is not None)
            )
            # 对top20接口降序排序
            aggregated_obj = traffic_obj.values('interface_id').annotate(
                total_access_count=Sum('access_count')
            ).order_by('-total_access_count')[:20]

            interface_list = []
            for interface in aggregated_obj:
                interface_id = interface['interface_id']
                if interface_id in interface_infos:
                    interface_info = interface_infos[interface_id]
                    # 使用 in_bulk 获取的 service_configs 字典来避免额外的数据库查询
                    if interface_info.service_config_id in service_configs:
                        skywalking_service = service_configs[interface_info.service_config_id].skywalking_service
                    else:
                        skywalking_service = None

                    interface_dict = {
                        'interface_id': interface['interface_id'],
                        'interface_name': interface_info.interface_description,
                        'interface_address': interface_info.interface_address,
                        'interface_way': interface_info.interface_way,
                        'service': skywalking_service,
                        'total_access_count': interface['total_access_count']
                    }
                    interface_list.append(interface_dict)

            return interface_list

        except Exception as e:
            # 添加适当的日志记录或异常处理
            logger.error(f"Error occurred while processing interface traffic summary: {e}")
            return []

    # 封装邮件内容
    def send_weekly_report(self):
        # 统计核心任务用例总数
        result1 = self.calculate_case_pool_counts()
        production_total_count = result1["production_total_count"]
        test_total_count = result1["test_total_count"]
        production_new_count = result1["production_new_count"]
        test_new_count = result1["test_new_count"]
        print(result1)
        # 统计每周发版各业无线任务执行次数
        result2 = self.get_weekly_task_counts()
        names = [item["task_name"] for item in result2]
        values = [item["total_records"] for item in result2]

        # 统计自动化持续集成失败数
        result3 = self.get_task_running_summary()
        data_task = [item["date"] for item in result3]
        data_fail_error_pro = [item["total_fail_error_pro"] for item in result3]
        data_fail_error_test = [item["total_fail_error_test"] for item in result3]

        # 统计测试人员当周自动化实现情况
        result4 = self.get_weekly_scene_counts()
        creators = [item.get("chinese_name", "Unknown") for item in result4["statistics"]]
        total_scenes = [item.get("total_scenes", "0") for item in result4["statistics"]]

        # 查询top20接口
        result5 = self.get_interface_traffic_summary()
        if not result5:
            logger.warning("No interface traffic summary data found.")
            result5 = []
        interface_name = [item.get("interface_name") for item in result5]
        total_access_count = [item.get("total_access_count") for item in result5]

        # 封装图表需要的数据格式
        data = {
            "环境": ["生产总数", "测试总数", "生产当周新增", "测试当周新增"],
            "值": [production_total_count, test_total_count, production_new_count, test_new_count]
        }
        data1 = {
            "名称": names,
            "值": values
        }
        data2 = {
            "名称": data_task,
            "测试": data_fail_error_test,
            "生产": data_fail_error_pro

        }
        data3 = {
            "名称": creators,
            "值": total_scenes
        }
        data4 = {
            "名称": interface_name,
            "值": total_access_count
        }
        df = pd.DataFrame(data)
        df1 = pd.DataFrame(data1)
        df2 = pd.DataFrame(data2)
        df3 = pd.DataFrame(data3)
        df4 = pd.DataFrame(data4)

        # 生成图表并转换为 Base64
        def plot_to_base64(df, title, x_col, y_cols, figsize=(8, 6), chart_type='bar'):
            font_path = '/usr/share/fonts/wqy-microhei/wqy-microhei.ttc'
            # font_path = '/System/Library/Fonts/STHeiti Medium.ttc'
            font_prop = FontProperties(fname=font_path)

            plt.figure(figsize=figsize)
            plt.rcParams['font.family'] = ['sans-serif']
            plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei']
            plt.rcParams['axes.unicode_minus'] = False
            plt.rcParams['axes.formatter.useoffset'] = False

            # 根据图片尺寸计算相对大小的参数
            base_size = min(figsize[0], figsize[1])
            title_size = base_size * 2  # 标题字体大小
            label_size = base_size * 2  # 轴标签字体大小
            tick_size = base_size * 2  # 刻度标签字体大小
            value_size = base_size * 2  # 柱状图上的数值字体大小
            bar_width = 0.5 * (5 / figsize[0])  # 根据图片宽度调整柱宽
            if title == '生产接口调用频次前20的接口':
                bar_width = 0.6  # 柱状图的宽度
                label_size = 5  # 轴标签字体大小
                value_size = 8
                tick_size = 10

            if chart_type == 'bar':
                x = range(len(df[x_col]))

                # 如果有多个y_cols，依次绘制
                for i, y_col in enumerate(y_cols):
                    bars = plt.bar(
                        [pos + i * bar_width for pos in x],
                        df[y_col],
                        width=bar_width,
                        label=y_col
                    )
                    # 在每个柱子上方显示数值
                    for bar in bars:
                        plt.text(
                            bar.get_x() + bar.get_width() / 2,  # x坐标
                            bar.get_height(),  # y坐标（柱子高度）
                            f'{int(bar.get_height())}',
                            ha='center',  # 水平居中
                            va='bottom',  # 垂直对齐底部
                            fontproperties=font_prop,
                            fontsize=value_size
                        )

                # 设置标签
                # plt.xlabel(x_col, fontproperties=font_prop, fontsize=12)
                # plt.ylabel('/'.join(y_cols), fontproperties=font_prop, fontsize=12)
                truncated_labels = [truncate_text(str(label)) for label in df[x_col]]
                if title == '生产接口调用频次前20的接口':
                    rotation_angle = 45  # 设置旋转角度
                    ha_alignment = 'right'  # 右对齐
                else:
                    rotation_angle = 0  # 不旋转
                    ha_alignment = 'center'  # 居中对齐
                plt.xticks(
                    [pos + bar_width * (len(y_cols) - 1) / 2 for pos in x],
                    truncated_labels,
                    fontproperties=font_prop,
                    fontsize=tick_size,
                    rotation=rotation_angle,
                    ha=ha_alignment
                )
                # 格式化Y轴刻度
                ax = plt.gca()
                ax.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{int(x):,}'))
            elif chart_type == 'line':
                # 格式化日期
                df[x_col] = pd.to_datetime(df[x_col]).dt.strftime('%Y-%m-%d')
                for y_col in y_cols:
                    plt.plot(df[x_col], df[y_col], marker='o', label=y_col)
                    # 在每个点上标注数值
                    for x, y in zip(df[x_col], df[y_col]):
                        plt.annotate(f'{y}',  # 显示的文本
                                     xy=(x, y),  # 点的坐标
                                     xytext=(0, 5),  # 文本相对于点的偏移量
                                     textcoords='offset points',  # 偏移量的单位
                                     ha='center',  # 水平对齐方式
                                     va='bottom',  # 垂直对齐方式
                                     fontproperties=font_prop,  # 使用中文字体
                                     fontsize=6)  # 字体大小
                # 使用fontproperties设置中文标签
                # plt.xlabel(x_col, fontproperties=font_prop, fontsize=label_size)
                # plt.ylabel('/'.join(y_cols), fontproperties=font_prop, fontsize=label_size)
                plt.legend(prop=font_prop)  # 确保图例也使用中文字体

            # 设置标题和刻度标签的字体
            plt.title(title, fontproperties=font_prop, fontsize=title_size)
            plt.tight_layout()
            # 设置刻度标签的字体
            plt.xticks(fontproperties=font_prop, fontsize=tick_size)
            plt.yticks(fontproperties=font_prop, fontsize=tick_size)
            buffer = BytesIO()
            # plt.savefig(buffer, format="png", bbox_inches='tight', dpi=200)
            # plt.savefig(buffer, format="png", bbox_inches='tight', encoding='utf-8', dpi=200)
            plt.savefig(buffer, format="png", bbox_inches="tight", dpi=200)
            plt.close()
            buffer.seek(0)
            img_base64 = base64.b64encode(buffer.read()).decode("utf-8")
            return f'<img src="data:image/png;base64,{img_base64}" alt="{title}">'

        def truncate_text(text, max_length=10):
            """截断文本，超过max_length个字符时添加省略号"""
            if len(text) > max_length:
                return text[:max_length] + '...'
            return text

        def send_email(html_content):
            # 获取当前日期
            today = timezone.now().date()
            start_of_week = today - timezone.timedelta(days=(today.weekday() + 3) % 7)  # 上周周五
            end_of_week = today + timezone.timedelta(days=(3 - today.weekday()) % 7)  # 本周周四
            start_of_week_str = start_of_week.strftime('%Y-%m-%d')
            end_of_week_str = end_of_week.strftime('%Y-%m-%d')
            my_addr = '<EMAIL>'
            my_pwd = 'QdeJ8eS9KcBA72Df'
            msg = MIMEMultipart("alternative")
            msg.attach(MIMEText(html_content, "html"))
            msg['Subject'] = f"自动化数据统计周报{start_of_week_str}~{end_of_week_str}"
            msg['From'] = '海典自动化测试平台<%s>' % my_addr
            msg['To'] = ("<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;"
                         "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;"
                         "<EMAIL>;<EMAIL>")
            to_list = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
            # msg['To'] = '<EMAIL>'
            # to_list = ['<EMAIL>']

            try:
                logger.info(f'开始发送周报邮件')
                with smtplib.SMTP_SSL("smtp.exmail.qq.com", 465) as server:
                    server.login(my_addr, my_pwd)
                    logger.info(f'发送邮件列表: {to_list}')
                    server.sendmail(my_addr, to_list, msg.as_string())
                    logger.info('成功发送邮件')
            except Exception as e:
                logger.error(f'发送邮件失败原因：{e}')
                raise e

        # 生成各个图表
        img_tag1 = plot_to_base64(df, "统计核心任务用例总数", "环境", "值", (5, 3))
        img_tag2 = plot_to_base64(df1, "当周发版各业务线运行统计", "名称", "值", (5, 3))
        img_tag3 = plot_to_base64(df3, "测试人员当周自动化实现情况", "名称", "值", (5, 3))
        img_tag4 = plot_to_base64(df4, "生产接口调用频次前20的接口", "名称", "值", (12, 7))
        img_tag5 = plot_to_base64(df2, "核心任务每日监控失败数统计", "名称", ["测试", "生产"], (5, 3), chart_type='line')
        # 构建邮件 HTML 内容
        html_content = f"""
        <h2>1、核心用例任务自动化场景总数统计（包含导出和oms分摊）</h2>
        {img_tag1}
        <h2>2、自动化发版运行统计情况</h2>
        {img_tag2}
        <h2>3、自动化持续集成失败率趋势</h2>
        {img_tag5}
        <h2>4、测试人员当周自动化实现情况</h2>
        {img_tag3}
        <h2>5、生产接口调用频次前20的接口</h2>
        {img_tag4}
        <h2>6、生产接口调用频次前20的接口详细信息</h2>
        <table border="1" style="width:100%; border-collapse: collapse;">
          <tr>
            <th>序号</th>
            <th>接口名称</th>
            <th>接口地址</th>
            <th>请求方式</th>
            <th>服务名</th>
            <th>访问次数</th>
          </tr>
          {''.join(f"<tr><td>{i+1}</td><td>{item['interface_name']}</td><td>{item['interface_address']}</td><td>{item['interface_way']}</td><td>{item['service']}</td><td>{item['total_access_count']}</td></tr>" for i, item in enumerate(result5))}
        </table>
        """

        send_email(html_content)