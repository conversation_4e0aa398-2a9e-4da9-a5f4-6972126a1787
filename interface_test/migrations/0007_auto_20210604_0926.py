# Generated by Django 3.0.6 on 2021-06-04 09:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('interface_test', '0006_scenerelation_instructions'),
    ]

    operations = [
        migrations.AddField(
            model_name='assertinfo',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True, verbose_name='是否可用'),
        ),
        migrations.AddField(
            model_name='assertresultinfo',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True, verbose_name='是否可用'),
        ),
        migrations.AddField(
            model_name='caseinfo',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True, verbose_name='是否可用'),
        ),
        migrations.AddField(
            model_name='interfaceinfo',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True, verbose_name='是否可用'),
        ),
        migrations.AddField(
            model_name='projectinfo',
            name='is_active',
            field=models.<PERSON>oleanField(db_index=True, default=True, verbose_name='是否可用'),
        ),
        migrations.AddField(
            model_name='sceneinfo',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True, verbose_name='是否可用'),
        ),
        migrations.AddField(
            model_name='scenerelation',
            name='is_active',
            field=models.BooleanField(db_index=True, default=True, verbose_name='是否可用'),
        ),
        migrations.AlterField(
            model_name='interfaceinfo',
            name='project_id',
            field=models.CharField(db_index=True, max_length=32),
        ),
        migrations.AlterField(
            model_name='scenerelation',
            name='relation_case_id',
            field=models.CharField(db_index=True, max_length=16, verbose_name='caseInfo的id'),
        ),
    ]
