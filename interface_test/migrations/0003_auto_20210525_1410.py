# Generated by Django 3.0.6 on 2021-05-25 14:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('interface_test', '0002_auto_20210524_1634'),
    ]

    operations = [
        migrations.AddField(
            model_name='scenerelation',
            name='response_param_alias',
            field=models.CharField(blank=True, max_length=512, null=True, verbose_name='接口返回值参数别名'),
        ),
        migrations.AlterField(
            model_name='scenerelation',
            name='is_assert',
            field=models.BooleanField(default=False, verbose_name='是否开启断言'),
        ),
        migrations.AlterField(
            model_name='scenerelation',
            name='response_param',
            field=models.CharField(blank=True, max_length=512, null=True, verbose_name='接口返回值参数'),
        ),
    ]
