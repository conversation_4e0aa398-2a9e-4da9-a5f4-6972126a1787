# Generated by Django 3.0.6 on 2021-05-24 16:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('interface_test', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='assertresultinfo',
            name='is_case_assert',
            field=models.BooleanField(default=True, help_text='True: 是，False: 场景的断言', verbose_name='是否是caseInfo的断言'),
        ),
        migrations.AddField(
            model_name='scenerelation',
            name='asserts',
            field=models.TextField(blank=True, null=True, verbose_name='断言id集合，以|分割'),
        ),
        migrations.AddField(
            model_name='scenerelation',
            name='interface_data',
            field=models.TextField(blank=True, null=True, verbose_name='请求参数json格式'),
        ),
        migrations.AddField(
            model_name='scenerelation',
            name='is_assert',
            field=models.<PERSON><PERSON>anField(default=False),
        ),
        migrations.AddField(
            model_name='scenerelation',
            name='reset_param',
            field=models.<PERSON>oleanField(default=False, verbose_name='是否重新设置接口参数'),
        ),
        migrations.AlterField(
            model_name='assertresultinfo',
            name='expect_result',
            field=models.TextField(blank=True, null=True, verbose_name='期望的值'),
        ),
        migrations.AlterField(
            model_name='assertresultinfo',
            name='operation_value',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='assertresultinfo',
            name='return_value',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='scenerelation',
            name='is_get_param',
            field=models.BooleanField(default=False, verbose_name='是否提取参数'),
        ),
        migrations.AlterField(
            model_name='scenerelation',
            name='relation_case_id',
            field=models.CharField(max_length=16, verbose_name='caseInfo的id'),
        ),
        migrations.AlterField(
            model_name='scenerelation',
            name='request_param',
            field=models.CharField(blank=True, max_length=512, null=True),
        ),
        migrations.AlterField(
            model_name='scenerelation',
            name='response_header_param',
            field=models.CharField(blank=True, max_length=512, null=True),
        ),
        migrations.AlterField(
            model_name='scenerelation',
            name='response_param',
            field=models.CharField(blank=True, max_length=512, null=True),
        ),
    ]
