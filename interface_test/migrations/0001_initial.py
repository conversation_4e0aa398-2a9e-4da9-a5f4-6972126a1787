# Generated by Django 3.0.6 on 2021-05-13 18:47

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AssertInfo',
            fields=[
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('creater', models.CharField(max_length=128, verbose_name='创建人')),
                ('update_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='修改时间')),
                ('update_person', models.CharField(max_length=128, verbose_name='修改人')),
                ('assert_id', models.AutoField(primary_key=True, serialize=False)),
                ('assert_type', models.CharField(max_length=1)),
                ('assert_operator', models.CharField(max_length=8)),
                ('assert_result', models.TextField()),
                ('assert_database', models.CharField(max_length=128, null=True)),
                ('assert_sql', models.TextField()),
            ],
            options={
                'db_table': 'assert_info',
            },
        ),
        migrations.CreateModel(
            name='AssertResultInfo',
            fields=[
                ('assert_id', models.AutoField(primary_key=True, serialize=False)),
                ('assert_type', models.CharField(max_length=1)),
                ('assert_operator', models.CharField(max_length=16)),
                ('return_value', models.TextField()),
                ('expect_result', models.TextField()),
                ('operation_value', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': '断言信息表',
                'db_table': 'assert_result_info',
                'ordering': ['-create_time'],
            },
        ),
        migrations.CreateModel(
            name='CaseInfo',
            fields=[
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('creater', models.CharField(max_length=128, verbose_name='创建人')),
                ('update_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='修改时间')),
                ('update_person', models.CharField(max_length=128, verbose_name='修改人')),
                ('case_id', models.AutoField(primary_key=True, serialize=False)),
                ('interface_id', models.CharField(max_length=32)),
                ('case_name', models.CharField(max_length=256)),
                ('case_describe', models.TextField()),
                ('is_init', models.BooleanField()),
                ('init_database', models.CharField(max_length=32, null=True)),
                ('init_sql', models.TextField()),
                ('is_back', models.BooleanField()),
                ('back_sql', models.TextField()),
                ('is_encrypt', models.BooleanField()),
                ('interface_data', models.TextField()),
                ('is_assert', models.BooleanField(default=False)),
                ('case_level', models.CharField(default='低', max_length=16)),
                ('asserts', models.TextField()),
                ('case_type', models.CharField(default='API', max_length=16)),
            ],
            options={
                'verbose_name': '案例信息表',
                'db_table': 'case_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='CaseRunPreRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pre_record_id', models.CharField(max_length=64)),
                ('environment_id', models.CharField(max_length=1)),
                ('case_type', models.CharField(max_length=16)),
                ('run_type', models.CharField(max_length=16)),
                ('case_id', models.CharField(max_length=64)),
                ('case_name', models.CharField(max_length=256)),
                ('interface_agreement', models.CharField(max_length=32)),
                ('ip', models.CharField(max_length=128)),
                ('port', models.CharField(max_length=8)),
                ('interface_way', models.CharField(max_length=128)),
                ('interface_address', models.TextField()),
                ('header', models.TextField()),
                ('interface_data', models.TextField()),
                ('is_init', models.BooleanField()),
                ('init_database', models.CharField(max_length=64, null=True)),
                ('init_sql', models.TextField()),
                ('is_back', models.BooleanField()),
                ('back_sql', models.TextField()),
                ('is_encrypt', models.BooleanField()),
                ('asserts', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
            ],
            options={
                'db_table': 'case_run_pre_record',
            },
        ),
        migrations.CreateModel(
            name='InterfaceInfo',
            fields=[
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('creater', models.CharField(max_length=128, verbose_name='创建人')),
                ('update_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='修改时间')),
                ('update_person', models.CharField(max_length=128, verbose_name='修改人')),
                ('interface_id', models.AutoField(primary_key=True, serialize=False)),
                ('project_id', models.CharField(max_length=32)),
                ('interface_name', models.CharField(max_length=256)),
                ('interface_agreement', models.CharField(max_length=16)),
                ('interface_way', models.CharField(max_length=128)),
                ('interface_address', models.CharField(max_length=512)),
                ('header', models.TextField(blank=True, null=True)),
                ('remark', models.TextField(blank=True, null=True)),
            ],
            options={
                'verbose_name': '接口信息表',
                'db_table': 'interface_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='LogRecord',
            fields=[
                ('log_id', models.AutoField(primary_key=True, serialize=False)),
                ('log_type', models.CharField(max_length=1)),
                ('running_log_type', models.CharField(max_length=1, null=True)),
                ('running_log_id', models.CharField(max_length=32, null=True)),
                ('log_content', models.TextField()),
                ('person', models.CharField(max_length=128)),
                ('temp_time', models.DateTimeField()),
            ],
            options={
                'verbose_name': '日志记录表',
                'db_table': 'log_record',
                'ordering': ['-temp_time'],
            },
        ),
        migrations.CreateModel(
            name='ProjectInfo',
            fields=[
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('creater', models.CharField(max_length=128, verbose_name='创建人')),
                ('update_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='修改时间')),
                ('update_person', models.CharField(max_length=128, verbose_name='修改人')),
                ('project_id', models.AutoField(primary_key=True, serialize=False)),
                ('project_name', models.CharField(max_length=256)),
                ('ip', models.CharField(max_length=128)),
                ('port', models.CharField(max_length=16, null=True)),
                ('remark', models.TextField(blank=True, null=True)),
            ],
            options={
                'verbose_name': '项目信息表',
                'db_table': 'project_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='SceneInfo',
            fields=[
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('creater', models.CharField(max_length=128, verbose_name='创建人')),
                ('update_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='修改时间')),
                ('update_person', models.CharField(max_length=128, verbose_name='修改人')),
                ('scene_id', models.AutoField(primary_key=True, serialize=False)),
                ('scene_name', models.CharField(max_length=256)),
                ('scene_describe', models.TextField()),
                ('before_param', models.TextField()),
                ('scene_relation_id', models.TextField()),
            ],
            options={
                'verbose_name': '场景信息表',
                'db_table': 'scene_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='SceneRelation',
            fields=[
                ('scene_relation_id', models.AutoField(primary_key=True, serialize=False)),
                ('relation_case_id', models.CharField(max_length=16)),
                ('is_get_param', models.BooleanField()),
                ('request_param', models.CharField(max_length=512)),
                ('response_param', models.CharField(max_length=512)),
                ('response_header_param', models.CharField(max_length=512)),
                ('sleep_time', models.CharField(default='0', max_length=16)),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': '场景组成关系表',
                'db_table': 'scene_relation',
                'ordering': ['-create_time'],
            },
        ),
    ]
