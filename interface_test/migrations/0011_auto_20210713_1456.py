# Generated by Django 3.0.6 on 2021-07-13 14:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('interface_test', '0010_caseinfo_csv_file'),
    ]

    operations = [
        migrations.CreateModel(
            name='BusinessInfo',
            fields=[
                ('is_active', models.BooleanField(db_index=True, default=True, verbose_name='是否可用')),
                ('create_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('creater', models.CharField(max_length=128, verbose_name='创建人')),
                ('update_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='修改时间')),
                ('update_person', models.CharField(max_length=128, verbose_name='修改人')),
                ('business_id', models.AutoField(primary_key=True, serialize=False, verbose_name='业务ID')),
                ('business_name', models.Char<PERSON><PERSON>(max_length=256, verbose_name='业务名称')),
                ('tester', models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=512, null=True, verbose_name='测试人员')),
                ('remark', models.TextField(blank=True, null=True, verbose_name='备注')),
            ],
            options={
                'verbose_name': '业务线信息表',
                'db_table': 'business_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.AddField(
            model_name='sceneinfo',
            name='business_id',
            field=models.IntegerField(default=0, verbose_name='业务ID'),
        ),
    ]
