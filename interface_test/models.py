from django.db import models
from scaffold.models.abstract.meta import ActiveModel, HierarchicalModel
from django.db.models import UniqueConstraint

from core.models import DateModel


# 业务线数据表
class BusinessInfo(DateModel, ActiveModel):
    business_id = models.AutoField(primary_key=True, verbose_name='业务ID')
    business_name = models.CharField(max_length=256, verbose_name='业务名称')
    tester = models.CharField(max_length=512, verbose_name='测试人员', null=True, blank=True)
    remark = models.TextField(verbose_name='备注', blank=True, null=True)

    def __str__(self):
        return self.business_name

    class Meta(object):
        # 定义表名
        db_table = "business_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = '业务线信息表'


# 项目数据表
class ProjectInfo(DateModel, ActiveModel):
    project_id = models.AutoField(primary_key=True)
    project_name = models.CharField(max_length=256)
    ip = models.CharField(max_length=128)
    port = models.CharField(max_length=16, null=True)
    remark = models.TextField(blank=True, null=True)
    project_mode = models.BooleanField(verbose_name='项目模式', default=False)
    test_host = models.CharField(verbose_name='测试环境地址', max_length=128, null=True, blank=True)
    test_header = models.TextField(verbose_name='测试环境请求头', null=True, blank=True)
    test_before_param = models.TextField(verbose_name='测试环境前置参数', null=True, blank=True)
    test_case = models.TextField(verbose_name='测试环境前置案例', null=True, blank=True)
    uat_host = models.CharField(verbose_name='预发环境地址', max_length=128, null=True, blank=True)
    uat_header = models.TextField(verbose_name='预发环境请求头', null=True, blank=True)
    uat_before_param = models.TextField(verbose_name='预发环境前置参数', null=True, blank=True)
    uat_case = models.TextField(verbose_name='预发环境前置案例', null=True, blank=True)
    pro_host = models.CharField(verbose_name='生产环境地址', max_length=128, null=True, blank=True)
    pro_header = models.TextField(verbose_name='生产环境请求头', null=True, blank=True)
    pro_before_param = models.TextField(verbose_name='生产环境前置参数', null=True, blank=True)
    pro_case = models.TextField(verbose_name='生产环境前置案例', null=True, blank=True)

    def __str__(self):
        return self.project_name

    class Meta(object):
        # 定义表名
        db_table = "project_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = '项目信息表'


# 接口数据表
class InterfaceInfo(DateModel, ActiveModel):
    interface_id = models.AutoField(primary_key=True)
    project_id = models.CharField(max_length=32, db_index=True)
    interface_name = models.CharField(max_length=256)
    interface_agreement = models.CharField(max_length=16)
    interface_way = models.CharField(max_length=128)
    interface_address = models.CharField(max_length=512)
    header = models.TextField(null=True, blank=True)
    remark = models.TextField(null=True, blank=True)
    request_param = models.TextField(verbose_name='请求参数', null=True, blank=True)
    response_param = models.TextField(verbose_name='返回参数', null=True, blank=True)
    body_demo = models.TextField(verbose_name='请求体示例', null=True, blank=True)
    param_demo = models.TextField(verbose_name='查询参数示例', null=True)
    body_demo_bak = models.TextField(verbose_name='更新前的body参数', null=True, blank=True)
    param_demo_bak = models.TextField(verbose_name='更新前的param参数', null=True)
    is_cover = models.IntegerField(verbose_name='是否覆盖', default=0)
    is_handle = models.IntegerField(verbose_name='是否处理', default=0)
    compare_result = models.IntegerField(verbose_name='比对结果', default=0)
    is_related = models.IntegerField(verbose_name='是否被引用', default=0)

    def __str__(self):
        info = '"interface_id":"%s","interface_name":"%s","project_id":"%s","interface_agreement":"%s",' \
               '"interface_way":"%s","interface_address":"%s","header":"%s","remark":"%s",' \
               '"create_time":"%s","creater":"%s","update_time":"%s","update_person":"%s","param_demo":"%s",' \
               '"is_cover":"%s","is_handle":"%s","compare_result":"%s","body_demo":"%s","body_demo_back":"%s","param_demo_back":"%s","is_related"' % \
               (self.interface_id, self.interface_name, self.project_id, self.interface_agreement, self.interface_way,
                self.interface_address, self.header, self.remark, self.create_time, self.creater, self.update_time,
                self.update_person, self.param_demo, self.is_cover, self.is_handle, self.compare_result, self.body_demo,
                self.body_demo_bak, self.param_demo_bak, self.is_related)
        return info

    class Meta(object):
        # 定义表名
        db_table = "interface_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = '接口信息表'


# 案例数据表
class CaseInfo(DateModel, ActiveModel):
    objects = None
    case_id = models.AutoField(primary_key=True)
    interface_id = models.CharField(max_length=32, verbose_name='旧的字段，冗余', db_index=True)
    case_name = models.CharField(max_length=256)
    case_describe = models.TextField()
    is_init = models.BooleanField()
    init_database = models.CharField(max_length=32, null=True)
    init_sql = models.TextField()
    is_back = models.BooleanField()
    back_sql = models.TextField()
    is_encrypt = models.BooleanField()
    interface_data = models.TextField()
    is_assert = models.BooleanField(default=False)
    case_level = models.CharField(max_length=16, default='低')
    asserts = models.TextField()
    case_type = models.CharField(max_length=16, default='API')
    interface_api = models.ForeignKey(verbose_name='接口对象', to='InterfaceInfo',
                                      related_name='interface', on_delete=models.DO_NOTHING, null=True, blank=True)
    csv_file = models.CharField(max_length=500, null=True, blank=True, verbose_name='外部参数文件路径')
    header = models.TextField(null=True, blank=True)
    is_related = models.IntegerField(verbose_name='是否被引用', default=0)

    def __str__(self):
        return self.case_name

    class Meta(object):
        # 定义表名
        db_table = "case_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = '案例信息表'


# 案例运行预登记表
class CaseRunPreRecord(models.Model):
    pre_record_id = models.CharField(max_length=64)
    environment_id = models.CharField(max_length=1)
    case_type = models.CharField(max_length=16)
    run_type = models.CharField(max_length=16)  # 运行模式：debug和plan
    case_id = models.CharField(max_length=64)
    case_name = models.CharField(max_length=256)
    interface_agreement = models.CharField(max_length=32)
    ip = models.CharField(max_length=128)
    port = models.CharField(max_length=8)
    interface_way = models.CharField(max_length=128)
    interface_address = models.TextField()
    header = models.TextField()
    interface_data = models.TextField()
    is_init = models.BooleanField()
    init_database = models.CharField(max_length=64, null=True)
    init_sql = models.TextField()
    is_back = models.BooleanField()
    back_sql = models.TextField()
    is_encrypt = models.BooleanField()
    asserts = models.TextField()
    create_time = models.DateTimeField()
    creater = models.CharField(max_length=128)

    class Meta(object):
        # 定义表名
        db_table = "case_run_pre_record"


# 场景信息表
class SceneInfo(DateModel, ActiveModel):
    scene_id = models.AutoField(primary_key=True)
    scene_name = models.CharField(max_length=256)
    business_id = models.IntegerField(verbose_name='业务ID', default=0)
    scene_describe = models.TextField()
    before_param = models.TextField()
    scene_relation_id = models.TextField()
    scene_type = models.IntegerField(verbose_name='案例类型：0-普通；1-导出；2-导入', default=0)
    is_related = models.IntegerField(verbose_name='是否被引用', default=0)
    creater = models.CharField(max_length=128)

    def __str__(self):
        return self.scene_name

    class Meta(object):
        # 定义表名
        db_table = "scene_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = '场景信息表'


# 场景组成
class SceneRelation(ActiveModel):
    scene_relation_id = models.AutoField(primary_key=True)
    relation_case_id = models.CharField(max_length=16, verbose_name='caseInfo的id', db_index=True)
    is_get_param = models.BooleanField(verbose_name='是否提取参数', default=False)
    request_param = models.CharField(max_length=512, null=True, blank=True)
    response_param = models.CharField(verbose_name='接口返回值参数', max_length=512, null=True, blank=True)
    response_header_param = models.CharField(max_length=512, null=True, blank=True)
    sleep_time = models.CharField(max_length=16, default='0')
    create_time = models.DateTimeField()
    creater = models.CharField(max_length=128)
    reset_param = models.BooleanField(verbose_name='是否重新设置接口参数', default=False)
    interface_data = models.TextField(verbose_name='请求参数json格式', null=True, blank=True)
    is_assert = models.BooleanField(verbose_name='是否开启断言', default=False)
    asserts = models.TextField(verbose_name='断言id集合，以|分割', null=True, blank=True)
    response_param_alias = models.CharField(verbose_name='接口返回值参数别名', max_length=512, null=True, blank=True)
    index = models.IntegerField(verbose_name='排序', default=1)
    instructions = models.TextField(verbose_name='案例说明', null=True, blank=True)
    is_enable = models.BooleanField(verbose_name='是否执行', default=True)
    is_check = models.IntegerField(verbose_name='场景检查 0-普通 1-数据越权', default=0)

    class Meta(object):
        # 定义表名
        db_table = "scene_relation"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-create_time']
        # 表别名
        verbose_name = '场景组成关系表'


# 断言数据表 (老版的断言，UI自动化在使用)
class AssertInfo(DateModel, ActiveModel):
    assert_id = models.AutoField(primary_key=True)
    assert_type = models.CharField(max_length=1)
    assert_operator = models.CharField(max_length=8)
    assert_result = models.TextField()
    assert_database = models.CharField(max_length=128, null=True)
    assert_sql = models.TextField()

    class Meta(object):
        # 定义表名
        db_table = "assert_info"


# 断言数据表 （新版断言，接口自动化）
class AssertResultInfo(ActiveModel):
    assert_id = models.AutoField(primary_key=True)
    assert_type = models.CharField(max_length=1)
    assert_operator = models.CharField(max_length=16)
    return_value = models.TextField(null=True, blank=True)
    expect_result = models.TextField(verbose_name='期望的值', null=True, blank=True)
    operation_value = models.TextField(null=True, blank=True)
    is_case_assert = models.BooleanField(verbose_name='是否是caseInfo的断言', help_text='True: 是，False: 场景的断言',
                                         default=True)
    create_time = models.DateTimeField()
    creater = models.CharField(max_length=128)
    case_id = models.CharField(max_length=11, null=True, verbose_name='案例ID')

    def __str__(self):
        info = '"assert_id":"%s","assert_type":"%s","return_value":"%s","assert_operator":"%s",' \
               '"operation_value":"%s","expect_result":"%s","create_time":"%s","creater":"%s"' % \
               (self.assert_id, self.assert_type, self.return_value, self.assert_operator, self.operation_value,
                self.expect_result, self.create_time, self.creater)
        return info

    class Meta(object):
        # 定义表名
        db_table = "assert_result_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-create_time']
        # 表别名
        verbose_name = '断言信息表'


# 日志记录表
class LogRecord(models.Model):
    log_id = models.AutoField(primary_key=True)
    log_type = models.CharField(max_length=1)
    running_log_type = models.CharField(max_length=1, null=True)
    running_log_id = models.CharField(max_length=32, null=True)
    log_content = models.TextField()
    person = models.CharField(max_length=128)
    temp_time = models.DateTimeField()

    class Meta(object):
        # 定义表名
        db_table = "log_record"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-temp_time']
        indexes = [models.Index(fields=['running_log_id']), ]
        # 表别名
        verbose_name = '日志记录表'


class ExecuteRecord(DateModel):
    record_id = models.AutoField(primary_key=True)
    type = models.CharField(max_length=16, verbose_name='类型')
    env_id = models.CharField(max_length=1, verbose_name='环境ID')
    run_id = models.CharField(max_length=10, verbose_name='执行数据ID')
    result = models.CharField(max_length=4, verbose_name='运行结果')

    class Meta(object):
        # 定义表名
        db_table = "execute_record"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = '执行记录表'


class InterfaceVisit(models.Model):
    id = models.AutoField(primary_key=True)
    service = models.CharField(max_length=200)
    source = models.CharField(max_length=100)
    interface_address = models.CharField(max_length=100)
    is_handle = models.BooleanField(default=0)
    handle_time = models.DateTimeField(verbose_name='比对完成时间', db_index=True)
    create_time = models.DateTimeField(verbose_name='落数时间')

    def __str__(self):
        interfaceVisit = '"id":"%s","interface_address":"%s",' \
                         '"source":"%s","service":"%s","is_handle":"%s"' % \
                         (self.id, self.interface_address, self.source,
                          self.service, self.is_handle)
        return interfaceVisit

    class Meta(object):
        # 定义表名
        db_table = "interface_visit"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        indexes = [models.Index(fields=['id']), ]
        # 表别名
        verbose_name = '接口清单表'


class InterfaceDetail(models.Model):
    id = models.AutoField(primary_key=True)
    service_config_id = models.CharField(max_length=128, unique=True)
    interface_way = models.CharField(max_length=20, unique=True)
    interface_address = models.CharField(max_length=100, unique=True)
    interface_class = models.CharField(max_length=128)
    interface_description = models.CharField(max_length=100)
    interface_parameter = models.CharField(max_length=256)
    interface_body = models.CharField(max_length=512)
    interface_header = models.CharField(max_length=512)
    service = models.CharField(max_length=200)
    is_handle = models.BooleanField(default=0)
    handle_time = models.DateTimeField(verbose_name='比对完成时间', db_index=True)
    create_time = models.DateTimeField(verbose_name='落数时间')

    def __str__(self):
        interfaceDetail = '"id":"%s","interface_way":"%s","interface_description":"%s","interface_parameter":"%s",' \
                          '"interface_body":"%s","service":"%s","is_handle":"%s","interface_address":"%s","interface_header":"%s"' % \
                          (self.id, self.interface_way, self.interface_description, self.interface_parameter,
                           self.interface_body,
                           self.service, self.is_handle, self.interface_address, self.interface_header)
        return interfaceDetail

    class Meta(object):
        # 定义表名
        db_table = "interface_detail"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        indexes = [models.Index(fields=['id']), ]
        # 表别名
        verbose_name = '接口详细信息表'


class InterfaceCompareResult(models.Model):
    id = models.IntegerField(primary_key=True)
    interface_id = models.IntegerField()
    compare_result = models.CharField(max_length=10, default=0)
    date = models.DateField()

    def __str__(self):
        interfaceCompareResult = '"date":"%s","interface_id":"%s","compare_result":"%s"' % \
                                 (self.date, self.interface_id, self.compare_result)
        return interfaceCompareResult

    class Meta(object):
        # 定义表名
        db_table = "interface_compare_result"
        # 表别名
        verbose_name = '接口比对结果表'


class InterfaceTrafficStatistics(models.Model):
    id = models.AutoField(primary_key=True)
    interface_id = models.IntegerField(verbose_name='接口id', db_index=True, unique=True)
    access_count = models.IntegerField(verbose_name='访问量', default=0)
    statistics_date = models.DateField(verbose_name='统计日期', null=False, unique=True)
    create_time = models.DateTimeField(verbose_name='创建时间', auto_now_add=True, db_index=True)
    update_time = models.DateTimeField(verbose_name='更新时间', auto_now=True, db_index=True)

    class Meta(object):
        db_table = "interface_traffic_statistics"
        verbose_name = '每日接口流量统计表'


class InterfaceTrafficStatisticsLog(models.Model):
    id = models.AutoField(primary_key=True)
    old_interface_address = models.CharField(verbose_name='原请求地址', max_length=100)
    new_interface_address = models.CharField(verbose_name='匹配后请求地址', max_length=100)
    interface_way = models.CharField(verbose_name='请求方式', max_length=20)
    service_config_id = models.CharField(max_length=128)
    create_time = models.DateTimeField(verbose_name='创建时间', auto_now_add=True, db_index=True)

    class Meta(object):
        db_table = "interface_traffic_statistics_log"
        constraints = [
            UniqueConstraint(
                fields=['old_interface_address', 'new_interface_address', 'interface_way', 'service_config_id'],
                name='union_unique')
        ]
        verbose_name = '每日接口流量统计日志表'


# 周任务场景数统计表
class WeeklyStatistics(models.Model):
    id = models.AutoField(primary_key=True)
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    pro_case_counts = models.IntegerField()
    test_case_counts = models.IntegerField()
    test_new_count = models.IntegerField()
    pro_new_count = models.IntegerField()

    def __str__(self):
        return f"{self.id} - {self.start_time}"

    class Meta(object):
        db_table = 'weekly_statistics'
        verbose_name = "每周统计"
        verbose_name_plural = "每周统计"
