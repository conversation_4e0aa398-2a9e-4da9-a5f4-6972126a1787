""" Current project exception classes.
"""
from django.http import Http404

from rest_framework import exceptions
from rest_framework.exceptions import PermissionDenied, ErrorDetail
from rest_framework.response import Response
from rest_framework.views import set_rollback

from scaffold.exceptions.exceptions import AppError

import core.exceptions


class AppErrors(core.exceptions.AppErrors):
    """ 本应用的具体错误列表 """
    ERROR_PROJECT_IS_EXISTS_INTERFACE = AppError("HD4001", "存在下级接口数据，请删除接口后再试")
    ERROR_BUSINESS_IS_EXISTS_INTERFACE = AppError("HD4001", "存在下级场景数据，请删除场景后再试")
    ERROR_INTERFACE_IS_EXISTS_CHILDERN = AppError("HD4002", "接口存在下级案例，请删除案例后再试")
    ERROR_CASE_INIT_SQL_IS_ILLEGAL = AppError("HD4003", "初始化SQL语句包含敏感信息")
    ERROR_CASE_BACK_SQL_IS_ILLEGAL = AppError("HD4004", "回退SQL语句包含敏感信息")
    ERROR_CASE_RUN_SQL_IS_ILLEGAL = AppError("HD4005", "执行SQL语句包含敏感信息")
    ERROR_CASE_IS_USE_BY_SCENE = AppError("HD4006", "该案例已被场景引用，请先解除引用后再试！")
    ERROR_CASE_IS_USE_BY_TASK = AppError("HD4007", "该案例已被任务引用，请先解除引用后再试！")
    ERROR_CASE_SCENE_IS_QUOTE = AppError("HD4008", "该场景已被任务引用，请先解除引用后再试！")
    ERROR_CASE_COPY = AppError("HD4009", "复制失败！")
    ERROR_SCENE_PARAM_ARGS = AppError("HD4010", "返回参数与返回参数别名数量不一致！")
    ERROR_SAVE_SCENE = AppError("HD4011", "保存场景失败！")
    ERROR_DELETE_SCENE = AppError("HD4012", "删除场景失败！")
    ERROR_UPLOAD_CSV = AppError("HD4013", "上传失败！")


# pylint: disable=unused-argument
def exception_handler(exc, context):
    """
    Returns the response that should be used for any given exception.

    By default we handle the REST framework `APIException`, and also
    Django's built-in `Http404` and `PermissionDenied` exceptions.

    Any unhandled exceptions may return `None`, which will cause a 500 error
    to be raised.
    """
    if isinstance(exc, Http404):
        exc = exceptions.NotFound()
    elif isinstance(exc, PermissionDenied):
        exc = exceptions.PermissionDenied()

    if isinstance(exc, exceptions.APIException):
        headers = {}
        if getattr(exc, 'auth_header', None):
            headers['WWW-Authenticate'] = exc.auth_header
        if getattr(exc, 'wait', None):
            headers['Retry-After'] = '%d' % exc.wait

        if isinstance(exc.detail, ErrorDetail):
            if exc.detail.code == 'not_authenticated':
                exc.status_code = 401
                data = dict(
                    ok=False,
                    msg=str(exc.detail),
                    errcode=10006
                )
            else:
                data = dict(
                    ok=False,
                    msg=exc.detail
                )
        elif isinstance(exc.detail, (list, dict)):
            data = dict(
                ok=False,
                msg=str(exc.detail),
                data=exc.detail
            )
        else:
            data = dict(
                ok=False,
                msg=exc.detail
            )

        set_rollback()
        return Response(data, status=exc.status_code, headers=headers)

    return None
