import re

from django.db.models import Max
from rest_framework import serializers
from scaffold.restframework.utils import auto_declare_serializers

from . import models as m


class ProjectInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.ProjectInfo
        fields = '__all__'


class InterfaceInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.InterfaceInfo
        fields = '__all__'


class CaseInfoSerializer(serializers.ModelSerializer):
    interface_uri = serializers.ReadOnlyField(source='interface_api.interface_address')
    class Meta:
        model = m.CaseInfo
        fields = '__all__'


class SceneInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.SceneInfo
        fields = '__all__'


# # Auto declare serializer classes from models.
auto_declare_serializers(m, locals())
