import requests
import pytz
import json
import time
import traceback

from core.commonUtils.commonUtil import CommonUtil
from core.servers.sceneExecute import SceneExecute
from interface_test.weekly import WeekReport
from hydee_auto_server import settings
from hydee_auto_server.celery import app
from . import models as m
from django_celery_beat.models import PeriodicTask, CrontabSchedule
from celery_once import QueueOnce
from .models import *

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()

#异步任务，队列消息一直增加，尝试增加ignore_result=True属性
#增加celery_once配置，'graceful': True设置后，当同一任务出现堆积，不会报错
@app.task(bind=True, ignore_result=True, base =QueueOnce, once={'graceful': True, 'timeout': 60 * 10})
def task_scene_job(self, scene_id, env_id, username):
    logger.info(f'场景任务开始执行，scene_id={scene_id}')
    logger.info(f'场景任务开始执行，username={username}')
    scene_exec = SceneExecute(scene_id, env_id, username)
    scene_info = str(scene_id) + "-" + SceneInfo.objects.get(scene_id=scene_id).scene_name
    content = "用户 %s 开始调试场景：%s" % (username, scene_info)
    # 日志记录
    CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
    log = scene_exec.debug()
    return eval(log)

@app.task(bind=True)
def task_week_report_job(self):
    report = WeekReport()
    report.send_weekly_report()