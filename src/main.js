import Vue from 'vue'
import App from './App.vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import router from './router'
import './plugins/element.js'
import './assets/css/global.css'
import './assets/fonts/iconfont.css'
import axios from 'axios'
import treeTransfer from 'el-tree-transfer'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import * as echarts from 'echarts'
import Sortable from 'sortablejs'
// 导入 NProgress 包对应的JS和CSS
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

import VueCodemirror from 'vue-codemirror'

// 引入 codemirror 的样式
import 'codemirror/lib/codemirror.css'
import 'codemirror/theme/dracula.css'
import 'codemirror/addon/merge/merge.css'
import 'codemirror/addon/lint/lint.css'
import 'codemirror/addon/lint/lint'
import 'codemirror/addon/lint/yaml-lint'

// 必须的代码模式
import 'codemirror/mode/javascript/javascript'

// 必须的插件
import 'codemirror/addon/merge/merge.js'

import DiffMatchPatch from 'diff-match-patch'
window.diff_match_patch = DiffMatchPatch
window.DIFF_DELETE = -1
window.DIFF_INSERT = 1
window.DIFF_EQUAL = 0

Vue.use(ElementUI)
Vue.use(VueCodemirror)
Vue.config.productionTip = false

window.diff_match_patch = DiffMatchPatch
export const baseUrl = 'http://127.0.0.1:8000/api/'
// export const baseUrl = 'https://testplatform-dev.hydee.cn/api/'
// export const baseUrl = 'http://************:8000/api/'
// export const baseUrl = 'http://************:8000/api/'
// export const baseUrl = 'https://testplatform.hydee.cn/api/'
axios.defaults.baseURL = baseUrl

axios.interceptors.request.use(config => {
  NProgress.start()
  config.headers.Authorization = window.localStorage.getItem('token')
  config.headers.userId = window.localStorage.getItem('userId')
  // 在最后必须 return config
  return config
})
axios.interceptors.response.use(config => {
  NProgress.done()
  const res = config.data
  // 判断接口返参以及接口状态码
  // 如果接口返回的meta.status为HD1003或者HD1004，则提示用户重新登录
  // 如果接口返参没有mate,则跳过该验证
  if (res.meta && res.meta.status) {
    if (res.meta.status === 'HD1003' || res.meta.status === 'HD1004') {
      // 设置重定向锁
      window.isRedirecting = true
      window.sessionStorage.clear()
      window.localStorage.clear()
      const url = encodeURIComponent(location.href)
      console.log('登录失效,请重新登录')
      // 延迟重定向，确保其他请求不会触发多次跳转
      setTimeout(() => {
        router.push(`/login?url=${url}`)
      }, 100)
    }
  }
  return config
})
// axios.defaults.headers.common.Authorization = window.localStorage.getItem('token')
Vue.prototype.$http = axios
Vue.prototype.$echarts = echarts
Vue.prototype.$sortable = Sortable

Vue.component('tree-transfer', treeTransfer)
Vue.component('treeselect', Treeselect)

new Vue({
  router,
  render: h => h(App)
}).$mount('#app')
