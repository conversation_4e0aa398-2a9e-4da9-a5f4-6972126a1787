<template>
  <div>
    <div class="div-container">
      <div>{{ '新数据' }}</div>
      <div>{{ '老数据' }}</div>
    </div>
    <div ref="codeDiff"></div>
  </div>
</template>

<script>
import CodeMirror from 'codemirror'

export default {
  name: 'CodeDiff',
  props: {
    origText: {
      type: String,
      required: true
    },
    newText: {
      type: String,
      required: true
    }
  },
  watch: {
    origText() {
      this.createDiffView()
    },
    newText() {
      this.createDiffView()
    }
  },
  data() {
    return {
      diffDescription: '左侧为新数据，右侧为旧数据'
    }
  },
  mounted() {
    this.createDiffView()
  },
  methods: {
    createDiffView() {
      const target = this.$refs.codeDiff
      target.replaceChildren()
      // 创建 diff 视图
      CodeMirror.MergeView(target, {
        value: this.newText,
        origRight: this.origText,
        lineNumbers: true,
        mode: 'javascript',
        highlightDifferences: false,
        connect: 'align',
        readOnly: true
      })
    }
  }
}
</script>

<style scoped>
/* 根据需要添加样式 */
  .div-container {
    display: flex;
    font-weight: bold;
    font-size: 18px;
  }
 .div-container div:first-child {
    flex-basis: 55%;
  }
</style>
