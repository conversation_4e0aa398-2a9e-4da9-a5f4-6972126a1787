<template>
  <el-dialog
    title="AI测试助手"
    :visible.sync="visible"
    width="800px"
    custom-class="ai-assitant"
    append-to-body
  >
    <div
      class="chat-content"
      ref="messagesContainer"
    >
      <!-- 首次展示的消息 -->
      <m-ai-intro
        :short-question="shortQuestion"
        v-if="!messages.length"
      />

      <!-- 消息对话内容 -->
      <m-ai-message-list
        :messages="messages"
        :user-name="userName"
        :user-merlogo="userMerlogo"
        v-if="messages.length"
        ref="chatList"
      />

      <!-- 快捷问题区域 -->
      <div class="quick-questions" v-if="shortQuestion.length">
        <span
          v-for="(question, index) in shortQuestion"
          :key="index"
          class="quick-question-item"
          @click="fillQuestion(question.value)"
        >
          {{ question.text }}
        </span>
      </div>

      <!-- 底部输入区域 -->
      <m-ai-input-area
        :short-question="shortQuestion"
        :messages="messages"
        :user-images="userImages"
        :user-input="userInput"
        :is-thinking="isThinking"
        :is-sending="isSending"
        @send="sendMessage"
        @stop="stopStream"
        @quick-question="sendQuickQuestion"
        @update-input="userInput = $event"
        @file-change="handleFileChange"
        @remove-image="removeImage"
        @paste="handlePaste"
      />
    </div>
  </el-dialog>
</template>

<script>
import { fetchEventSource } from '@microsoft/fetch-event-source'
import _ from 'lodash'

import mAiInputArea from './aiAssitant/AiInputArea.vue'
import mAiIntro from './aiAssitant/AiIntro.vue'
import mAiMessageList from './aiAssitant/AiMessageList.vue'
import { baseUrl } from '@/main.js'

// import ChatSseService from '@/module/apis/chatSseService' //对话接口
// import { getComnoList } from '@/module/apis/home'
// import MC from '@/module/MC'

const maxRetries = 2
const userInputMaxLen = 1000

export default {
  name: 'ai-assistant',
  data() {
    return {
      visible: false, // 对话框显示状态
      isThinking: false, // 思考状态
      isSending: false, // 发送状态
      userInput: '', // 用户输入内容
      userImages: [], // 用户上传的图片信息
      shortQuestion: [
        { text: '下载小程序代码', value: '下载小程序包，项目为smart-bi-miniprogram-mp' },
        { text: '生成测试报告', value: '帮我生成迭代的测试报告并通知对应业务线人员上预发，迭代id为 ，业务线为' },
        { text: '从hops获取异常日志', value: '查询hops异常日志，traceid为' },
        { text: '从skywalking获取sql ', value: '从skywalking获取sql：traceid为' },
        { text: 'jenkins构建', value: '构建jenkins服务，环境test，分支release，服务名为' }
      ], // 快捷问题
      messages: [], // 消息列表（用户消息+AI消息）
      abortController: null, // 用于中断请求的AbortController对象
      lockRequest: true // 锁定请求，防止多次请求
    }
  },
  methods: {
    onCsToggle(visible) {
      this.visible = !!visible
    },
    // 发送消息
    sendMessage() {
      // 发送前先停止流式响应
      if (!this.userInput.trim() || this.isSending) return
      if (this.userInput.length > userInputMaxLen) {
        this.$hmessage.error('内容太长了，缩短一点吧~~')
        return
      }
      // 发送新消息前，先停止当前正在进行的请求，发送新消息前，先停止当前正在进行的请求
      this.stopStream()
      this.isSending = true
      // 创建用户消息对象
      const userMessage = {
        sender: 'user',
        content: this.userInput.trim(),
        status: null,
        files: Array.isArray(this.userImages) && this.userImages.length ? this.userImages : null
      }
      // 开始流式请求
      this.fetchStream(userMessage)
    },
    // 处理流式请求
    async fetchStream(userMessage) {
      // 创建新的中断控制器，用于中断正在进行的异步操作
      this.abortController = new AbortController()
      // 创建AI消息对象
      const aiMessage = {
        sender: 'assistant',
        content: '', // AI回复内容
        thinkContent: '', // 思考过程内容
        status: 'thinking', // 状态：thinking -> success/error
        createId: Date.now().toString() // 唯一标识符
      }
      try {
        // 添加用户消息和AI初始消息
        this.messages.push(userMessage)
        // 清空输入
        this.userInput = ''
        this.userImages = []
        this.messages.push({ ...aiMessage })
        this.scrollToBottom({ type: 'end' })
        const apiUrl = `${baseUrl}ai-agent/chat/?message=${encodeURIComponent(userMessage.content)}&userId=${encodeURIComponent(this.userId)}`
        // 使用fetchEventSource处理流式响应
        fetchEventSource(apiUrl, {
          openWhenHidden: true,
          method: 'get',
          signal: this.abortController.signal,
          onopen: (response) => {
            if (response.ok && response.status === 200) {
              this.isSending = false
              this.isThinking = true
            } else {
              throw new Error(`服务器返回错误: ${response.status}`)
            }
          },
          onmessage: (event) => {
            // 处理complete事件
            if (event.event === 'complete') {
              this.isThinking = false
              this.isSending = false
              return
            }
            if (!event.data) return
            try {
              const data = JSON.parse(event.data)
              // 检查status字段
              if (data.status === 200) {
                if (data.text) {
                  aiMessage.content += data.text
                  this.messages.splice(-1, 1, { ...aiMessage })
                  this.scrollToBottom()
                }
                if (data.finish) {
                  // 消息完成
                  this.messages.splice(-1, 1, {
                    ...aiMessage,
                    status: 'success'
                  })
                  this.isThinking = false
                }
              } else {
                // 处理错误状态
                console.error('接收到错误状态:', data)
                this.$hmessage.error(data.error || '处理请求时出错')
                this.isThinking = false
                this.isSending = false
              }
            } catch (error) {
              console.error('解析错误:', error)
            }
          },
          onclose: () => {
            this.isSending = false
            this.isThinking = false
          },
          onerror: (error) => {
            console.error('连接错误:', error)
            this.isSending = false
            this.isThinking = false
            this.$hmessage.error('连接错误，请稍后再试')
          }
        })
      } catch (error) {
        console.error('错误:', error)
        this.isSending = false
        this.isThinking = false
        this.$hmessage.error('请求失败，请稍后再试')
      }
    },
    stopStream() {
      const lastIndex = this.messages.length - 1
      const last = this.messages[lastIndex]
      if (this.abortController) {
        if (lastIndex >= 0 && last?.status === 'thinking') {
          let safeContent = last.content || ''
          safeContent += '\n\n⚠️ **AI回答中断，部分内容可能不完整，请稍后再尝试**'
          this.messages.splice(lastIndex, 1, {
            ...last,
            content: safeContent,
            status: 'error'
          })
          this.abortController.abort()
          this.isSending = false
          this.isThinking = false
        }
      }
    },
    sendQuickQuestion: _.debounce(function (question) {
      this.stopStream()
      this.userInput = question
      this.sendMessage()
    }, 500),
    handlePaste(event) {
      const clipboardItems = event.clipboardData.items
      for (let i = 0; i < clipboardItems.length; i++) {
        const item = clipboardItems[i]
        if (item.type.indexOf('image') !== -1) {
          event.preventDefault()
          const file = item.getAsFile()
          this.processImageFile(file)
        }
      }
    },
    processImageFile(file) {
      if (!file) return
      if (this.isThinking || this.isSending) return

      const fileType = file.type.toLowerCase()
      const fileSizeMB = file.size / (1024 * 1024)

      if (!['image/png', 'image/jpeg', 'image/jpg'].includes(fileType)) {
        this.$hmessage.error('仅支持 JPG 和 PNG 格式图片')
        return
      }
      if (this.userImages.length >= 5) {
        this.$hmessage.error('最多上传5张图片')
        return
      }
      if (fileSizeMB > 10) {
        this.$hmessage.error('图片大小不能超过 10MB')
        return
      }

      const formData = new FormData()
      formData.append('file', file)
      const tempId = Date.now()
      const tempImage = {
        id: tempId,
        name: file.name,
        type: fileType.toUpperCase().split('/')[1],
        size: this.formatFileSize(file.size),
        status: 'init',
        url: ''
      }

      this.userImages.push(tempImage)
      this.updateImageStatus(tempId, 'pending')

      global
        .ioUploadImg(formData)
        .then((res) => {
          this.updateImageData(tempId, {
            url: res.data ? global.cdnImg(res.data) : null,
            status: res.data ? 'done' : 'error'
          })
        })
        .catch(() => {
          this.updateImageStatus(tempId, 'error')
        })
    },
    updateImageStatus(id, status) {
      const img = this.userImages.find((img) => img.id === id)
      if (img) img.status = status
    },
    updateImageData(id, data = {}) {
      const img = this.userImages.find((img) => img.id === id)
      if (img) Object.assign(img, data)
    },
    removeImage(id) {
      const index = this.userImages.findIndex((img) => img.id === id)
      if (index !== -1) {
        this.userImages.splice(index, 1)
      }
    },
    handleFileChange(event) {
      const file = event.target.files[0]
      if (!file) return
      this.processImageFile(file)
      event.target.value = ''
    },
    formatFileSize(sizeInBytes) {
      if (typeof sizeInBytes !== 'number' || isNaN(sizeInBytes) || sizeInBytes < 0) {
        return '0 Bytes'
      }
      if (sizeInBytes === 0) return '0 Bytes'
      const sizeInKB = sizeInBytes / 1024
      return sizeInKB >= 1000 ? `${(sizeInKB / 1024).toFixed(2)}MB` : `${sizeInKB.toFixed(1)}KB`
    },
    scrollToBottom: _.throttle(function (params) {
      this.$nextTick(() => {
        const container = this.$refs.chatList?.$el || this.$refs.chatList
        const threshold = 100 // 离底部多少像素以内才滚动
        const isScroll = container.scrollTop + container.clientHeight >= container.scrollHeight - threshold
        if (container && (isScroll || params?.type === 'end')) {
          container.scrollTop = container.scrollHeight
        }
      })
    }, 100),
    fillQuestion(value) {
      // 这里不调用 sendMessage()，只填充输入框
      this.userInput = value
    }
  },
  watch: {
    async visible(val) {
      if (!val) {
        // this.stopStream()
        // global.setTrackingEvent({
        //   _action: 'AI客服助手-关闭',
        //   _actionType: 2,
        //   _dataSource: 9099
        // })
        // 清空输入框
        this.userInput = ''
        // 判断是否还有为思考完成的数据，否则直接清空消息列表
        if (!this.messages.filter((item) => item.status === 'thinking').length) {
          this.messages = []
        }
      } else {
        // await this.formatPagesAuth()
        // this.getQuesition()
      }
    }
  },
  async mounted() {
    // await this.formatPagesAuth()
    // this.getQuesition()
  },
  beforeDestroy() {
    this.stopStream()
  },
  computed: {
    userName() {
      // return MC.config.userInfo.merName
      return window.localStorage.getItem('chinese_name')
    },
    userId() {
      // return MC.config.userInfo.id
      return window.localStorage.getItem('userId')
    },
    userMerlogo() {
      return 'http://centermerchant-test.oss-cn-shanghai.aliyuncs.com/robotFile/20250605/e4bf48907f8f48638b3b9c22dab16100.png'
    }
  },
  components: {
    mAiIntro,
    mAiMessageList,
    mAiInputArea
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep .ai-assitant {
    border-radius: 10px;
    max-height: 650px;
    max-height: calc(65vh + 85px);
    max-width: 900px;
    width: 70vw !important;
    background-color: #f5f7fa !important; /* 修改对话框整体背景色为灰色 */
    .h-modal__headerbtn {
      padding-top: 15px !important;
    }
    /* 修改对话框内容区域背景色 */
    .el-dialog {
      background-color: #f5f7fa !important;
    }
    .el-dialog__header {
      background-color: #f5f7fa !important;
      border-bottom: 1px solid #e4e7ed;
    }
    .el-dialog__body {
      background-color: #f5f7fa !important;
      padding: 0 !important; /* 移除默认padding，因为我们在chat-content中已经设置了 */
    }
  }
  .chat-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    // height: 565px;
    height: 65vh;
    color: #3d3d3d;
    background-color: #f5f7fa; /* 添加浅灰色背景，与AI白色气泡形成对比 */
    border-radius: 8px; /* 添加圆角让背景更美观 */
    padding: 16px; /* 添加内边距 */
    box-sizing: border-box; /* 确保padding不影响总宽度 */
  }
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2); /* 半透明灰 */
    border-radius: 10px;
    transition: background-color 0.3s;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.3); /* 鼠标悬停更深一些 */
  }

  .quick-questions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;
    padding: 0 16px;
  }

  .quick-question-item {
    padding: 6px 12px;
    background: #e1f5fe;
    border-radius: 16px;
    cursor: pointer;
    color: #0288d1;
    font-size: 12px;
    transition: all 0.2s;
    border: 1px solid #b3e5fc;

  &:hover {
    background: #b3e5fc;
    color: #01579b;
  }
  }
</style>
