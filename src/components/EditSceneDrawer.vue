<template>
  <div>
    <el-drawer :visible.sync="drawerVisible"
             :title="title"
             direction="btt"
             size="90%"
             class="scene-drawer"
             :close-on-press-escape="false"
             @close="handleClose"
             v-loading="loading"
             @opened="handleOpened">
    <el-scrollbar class="scene-drawer-content">
      <el-form :model="sceneForm"
              label-width="100px"
              :rules="sceneFormRules"
              ref="sceneFormRef">
        <el-divider content-position="left">基本信息</el-divider>
        <el-row>
          <el-col :span="12">
            <el-form-item label="场景名称" prop="scene_name">
              <el-input v-model="sceneForm.scene_name" :readonly="currentType === 'check'">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属业务" prop="business_id">
              <el-select v-model="sceneForm.business_id" filterable placeholder="请选择" :disabled="currentType === 'check'">
                <el-option v-for="item in businessList"
                           :key="item.id"
                           :label="item.label"
                           :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="场景描述" prop="scene_describe">
              <el-input v-model="sceneForm.scene_describe" :readonly="currentType === 'check'">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="left">前置参数</el-divider>
        <el-row>
            <el-col :span="12">
              <el-form-item label="是否展示">
                <el-switch
                  v-model="sceneForm.isShowPreparam"
                  active-color="#13ce66"
                  :disabled="currentType === 'check'">
                </el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item v-if="sceneForm.isShowPreparam && currentType !== 'check'">
                <el-button type="primary" @click="addPreparam" size="small">添加参数</el-button>
              </el-form-item>
            </el-col>
        </el-row>
        <el-row v-if="sceneForm.isShowPreparam" :gutter="25">
          <el-col :span="12" v-for="(item, index) in preparamList" :key="index">
            <el-form-item :label="'参数' + (index + 1)">
              <el-col :span="8">
                <el-input type="text" v-model="item.paramName" :disabled="currentType === 'check'" />
              </el-col>
              <el-col :span="2">
                <span>=</span>
              </el-col>
              <el-col :span="8">
                <el-input type="text" v-model="item.paramValue" :disabled="currentType === 'check'" />
              </el-col>
              <el-col :span="2" v-if="currentType !== 'check'">
                <i class="el-icon-delete" @click="deletePreparm(index)"></i>
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="left">案例组成</el-divider>
        <el-row>
          <el-button size="small" @click="addCaseDrawerShow(9999)">添加案例</el-button>
          <span style="font-size:12px;margin-left:10px;color:red;">拖拽表格行可进行排序</span>
        </el-row>
        <div class="relationCaseTabel">
          <el-table :data="sceneForm.relation"
                    id="crTable"
                    row-key="counterKey">
            <el-table-column type="index" />
            <el-table-column prop="relation_case_id" min-width="100px" label="案例ID" />
            <el-table-column prop="relation_case_name" min-width="120px" label="案例名称" />
            <el-table-column prop="relation_case_type" min-width="80px" label="案例类型" />
            <el-table-column prop="is_get_param" min-width="80px" label="是否提取参数">
              <template v-slot="slotProps">
                <span v-if="slotProps.row.is_get_param">是</span>
                <span v-else>否</span>
              </template>
            </el-table-column>
            <el-table-column prop="response_param" min-width="100px" label="返回参数" />
            <el-table-column prop="response_param_alias" min-width="100px" label="返回参数别名" />
            <el-table-column prop="reset_param" min-width="80px" label="重设请求数据">
              <template v-slot="slotProps">
                <span v-if="slotProps.row.reset_param">是</span>
                <span v-else>否</span>
              </template>
            </el-table-column>
            <el-table-column prop="instructions" min-width="100px" label="备注说明" show-overflow-tooltip />
            <el-table-column prop="is_check" min-width="100px" label="是否数据越权">
              <template v-slot="slotProps">
                <el-switch
                  v-model="slotProps.row.is_check"
                  @change="changeIsCheck(slotProps.row)"
                  :disabled="currentType === 'check'"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column min-width="170px" label="操作" v-if="currentType !== 'check'">
              <template v-slot="slotProps">
                <el-button type="text" @click="editCaseDialogShow(slotProps.row, slotProps.$index)">编辑</el-button>
                <el-button type="text" @click="addCaseDrawerShow(slotProps.$index)">添加</el-button>
                <el-button type="text" @click="copyCase(slotProps.row, slotProps.$index)">复制</el-button>
                <el-button type="text" @click="deleteCase(slotProps.$index)">删除</el-button>
                <el-button type="text" @click="slotProps.row.is_enable = !slotProps.row.is_enable">
                  <span v-if="slotProps.row.is_enable">禁用</span>
                  <span v-else style="color:red">启用</span>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
    </el-scrollbar>
    <div class="scene-drawer-footer" v-if="currentType !== 'check'">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="success" @click="handleSubmit(false)" v-if="currentType === 'update'">保 存</el-button>
      <el-button type="primary" @click="handleSubmit(true)">保存并关闭</el-button>
      <el-button type="warning" @click="handleRunScene" v-if="currentType === 'update'">运 行</el-button>
    </div>

    <!-- 添加案例抽屉 -->
    <el-drawer :visible.sync="addCaseDialogVisible"
               title="添加案例"
               direction="rtl"
               size="70%"
               class="add-case-drawer"
               :z-index="3000"
               :modal="false"
               :close-on-press-escape="false"
               @close="addCaseDialogClosed">
      <el-scrollbar class="add-case-drawer-content">
        <el-row :gutter="20">
          <el-col :span="4">
            <el-input placeholder="请输入案例ID"
                      v-model="caseQueryInfo.search_case_id"
                      clearable>
            </el-input>
          </el-col>
          <el-col :span="5">
            <el-input placeholder="请输入案例名称"
                      v-model="caseQueryInfo.search_case_name"
                      clearable>
            </el-input>
          </el-col>
          <el-col :span="5">
            <el-input placeholder="请输入接口地址"
                      v-model="caseQueryInfo.search_case_uri"
                      clearable>
            </el-input>
          </el-col>
          <el-col :span="5">
            <el-input placeholder="请输入创建人"
                      v-model="caseQueryInfo.creater"
                      clearable>
            </el-input>
          </el-col>
          <el-col :span="3">
            <el-button type="primary" @click="queryCase">查 询</el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-tabs v-model="caseQueryInfo.type"
                   class="add"
                   @tab-click="getCaseList">
            <el-tab-pane label="接口案例" name="API">
            </el-tab-pane>
            <el-tab-pane label="SQL案例" name="SQL">
            </el-tab-pane>
            <el-tab-pane label="Redis案例" name="REDIS">
            </el-tab-pane>
            <el-tab-pane label="导入/导出解析案例" name="EXPORT">
            </el-tab-pane>
          </el-tabs>
        </el-row>
        <el-row>
          <el-table :data="caseList"
                    border
                    width="100%"
                    @selection-change="caseSelectionChange"
                    :row-key="(row) => row.case_id"
                    ref="caseSelectTable"
                    v-loading="getCaseListLoading">
            <el-table-column type="selection" min-width="55px" :reserve-selection="true"/>
            <el-table-column label="案例ID" prop="case_id" min-width="50px"></el-table-column>
            <el-table-column label="案例名称" prop="case_name" show-overflow-tooltip></el-table-column>
            <el-table-column v-if="caseQueryInfo.type === 'API'" label="所属接口" prop="interface_id" width="80px" show-overflow-tooltip></el-table-column>
            <el-table-column v-if="caseQueryInfo.type === 'API'" label="接口地址" prop="interface_uri" min-width="200px" show-overflow-tooltip></el-table-column>
            <el-table-column label="修改人" prop="update_person" width="80px"></el-table-column>
            <el-table-column label="修改时间" prop="update_time" width="140px"></el-table-column>
          </el-table>
        </el-row>
        <el-row>
          <el-pagination
            @size-change="caseSizeChange"
            @current-change="caseCurrentChange"
            :current-page.sync="caseQueryInfo.pagenum"
            :page-size="caseQueryInfo.pagesize"
            :page-sizes="[5, 10, 20, 30, 40]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="caseTotal">
          </el-pagination>
        </el-row>
      </el-scrollbar>
      <div class="add-case-drawer-footer">
        <el-button @click="addCaseDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addCase">确 定</el-button>
      </div>
    </el-drawer>

    <!-- 编辑案例抽屉 -->
    <el-drawer :visible.sync="editCaseDialogVisible"
               title="编辑案例"
               direction="rtl"
               size="50%"
               class="edit-case-drawer"
               :z-index="3000"
               :modal="false"
               :close-on-press-escape="false"
               @close="editCaseDrawerClosed">
      <el-scrollbar class="edit-case-drawer-content">
        <el-form :model="caseForm"
                 label-width="100px"
                 :rules="caseFormRules"
                 ref="caseFormRef">
          <el-row>
            <el-form-item label="案例名称" prop="case_name">
              <el-input v-model="caseForm.relation_case_name" readonly></el-input>
            </el-form-item>
          </el-row>
          <el-row v-if="caseForm.relation_case_type==='API' ||caseForm.relation_case_type==='EXPORT'">
            <el-form-item label="接口地址" prop="case_name">
              <el-input v-model="caseForm.interface_address" readonly></el-input>
            </el-form-item>
          </el-row>
          <el-form-item label="备注说明" prop="case_name">
            <el-input v-model="caseForm.instructions"></el-input>
          </el-form-item>
          <!-- 参数开关，休眠时间 -->
          <el-row>
            <el-col :span="12">
              <el-form-item label="是否提取参数">
                <el-switch
                  v-model="caseForm.is_get_param"
                  active-color="#13ce66">
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 返回参数 -->
          <el-row v-if="caseForm.is_get_param">
            <el-col :span="24">
              <el-form-item label="返回参数">
                <el-input v-model="caseForm.response_param"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="caseForm.is_get_param">
            <el-col :span="24">
              <el-form-item label="返回参数别名">
                <el-input v-model="caseForm.response_param_alias"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 请求参数，头部 -->
          <el-row v-if="caseForm.is_get_param">
            <el-col :span="12">
              <el-form-item label="请求参数">
                <el-input v-model="caseForm.request_param"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="返回头部">
                <el-input v-model="caseForm.response_header_param"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 休眠时间，排序 -->
          <el-row>
            <el-col :span="12">
              <el-form-item label="休眠时间">
                <el-input v-model="caseForm.sleep_time">
                  <template slot="append">秒</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 请求数据 -->
          <el-row>
            <el-col :span="12">
              <el-form-item label="是否重设数据">
                <el-tooltip content="开启可编辑请求数据与断言" placement="top" :enterable="true">
                  <el-switch v-model="caseForm.reset_param"
                            active-color="#13ce66">
                  </el-switch>
                </el-tooltip>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 模板地址和上传行数 (UPLOAD类型) -->
          <el-row>
            <el-form-item label="模板地址" prop="temp_file" v-if="caseForm.relation_case_type==='UPLOAD'">
              <el-input v-model="caseForm.temp_file" :disabled="caseForm.reset_param === false"></el-input>
            </el-form-item>
          </el-row>
          <el-row v-if="caseForm.relation_case_type==='UPLOAD'">
            <el-col :span="24">
              <el-form-item label="上传行数" prop="upload_min_rows">
                <div style="display: flex; align-items: center; gap: 10px;">
                  <el-input
                    v-model="caseForm.upload_min_rows"
                    placeholder="最小值"
                    style="width: 100px"
                    :disabled="caseForm.reset_param === false"
                  ></el-input>
                  <span>至</span>
                  <el-tooltip class="item" effect="dark" content="为空表示不限制行数,如最小值=2,最大值为空,则表示上传行数为第2行到最后一行" placement="top">
                    <el-input
                      v-model="caseForm.upload_max_rows"
                      placeholder="最大值"
                      style="width: 100px"
                      :disabled="caseForm.reset_param === false"
                    ></el-input>
                  </el-tooltip>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="caseForm.relation_case_type==='UPLOAD'">
            <el-form-item label="文件上传" prop="is_upload">
              <el-input v-model="caseForm.is_upload" v-show="false"></el-input>
              <el-upload
                ref="Files"
                :action="uploadPath"
                :headers="headers"
                class="upload-demo"
                :auto-upload="true"
                :on-change="handleFileChange"
                :before-remove="beforeRemove"
                :on-remove="handleRemove"
                :on-exceed="handleExceed"
                :on-success="handleUploadSuccess"
                :file-list="fileList"
                :limit="1">
                <el-button type="primary" :disabled="caseForm.reset_param === false">选择文件</el-button>
              </el-upload>
            </el-form-item>
          </el-row>
          <!-- 请求数据 (非EXPORT和UPLOAD类型) -->
          <el-row v-if="caseForm.relation_case_type !=='EXPORT' && caseForm.relation_case_type !=='UPLOAD'">
            <el-col>
              <el-form-item label="请求数据">
                <el-input type="textarea"
                          :rows="10"
                          placeholder="请输入json数据"
                          v-model="caseForm.interface_data"
                          :disabled="caseForm.reset_param === false"
                          @blur="transferJson">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 请求数据 (UPLOAD类型) -->
          <el-row v-if="caseForm.relation_case_type =='UPLOAD'">
            <el-col>
              <el-form-item label="请求数据">
                <el-input type="textarea"
                          :rows="10"
                          placeholder="请输入json数据"
                          v-model="caseForm.upload_param"
                          :disabled="caseForm.reset_param === false"
                          @blur="transferJson">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 断言 -->
          <el-row>
            <el-col :span="12">
              <el-form-item label="是否断言">
                <el-switch v-model="caseForm.is_assert" @change="handleExportFormIsAssertSwitch"
                          active-color="#13ce66">
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 断言配置 -->
          <div v-if="caseForm.is_assert">
            <el-row>
              <el-form-item :label="caseForm.relation_case_type ==='EXPORT' ? '断言配置':'匹配断言'">
                <el-button type="primary" @click="addMatchAssert" size="small" :disabled="caseForm.reset_param === false">添加断言</el-button>
                <el-table :data="matchAssertList" border width="60%">
                  <el-table-column :label="caseForm.relation_case_type ==='EXPORT' ? '断言字段':'返回值'" min-width="50px">
                    <template v-slot="slotProps">
                      <el-input v-model="slotProps.row.return_value" :disabled="(caseForm.relation_case_type ==='EXPORT' && slotProps.$index === 0) || caseForm.reset_param === false"></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column label="判断符" min-width="50px">
                    <template v-slot="slotProps">
                      <el-select v-model="slotProps.row.assert_operator" :disabled="(caseForm.relation_case_type ==='EXPORT' && slotProps.$index === 0) || caseForm.reset_param === false">
                        <el-option value="1" label="等于"></el-option>
                        <el-option value="2" label="不等于"></el-option>
                        <el-option value="3" label="包含"></el-option>
                        <el-option value="4" label="不包含"></el-option>
                        <el-option value="5" label="大于"></el-option>
                        <el-option value="6" label="小于"></el-option>
                        <el-option value="7" label="大于等于"></el-option>
                        <el-option value="8" label="小于等于"></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="期望结果" min-width="50px">
                    <template v-slot="slotProps">
                      <el-input v-model="slotProps.row.expect_result" :disabled="(caseForm.relation_case_type ==='EXPORT' && slotProps.$index === 0) || caseForm.reset_param === false"></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" min-width="50px">
                    <template v-slot="slotProps">
                      <el-button type="danger" size="mini"
                                 :disabled="caseForm.reset_param === false"
                                @click="deleteAssert(true, slotProps.$index)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-row>
          </div>
        </el-form>
      </el-scrollbar>
      <div class="edit-case-drawer-footer">
        <el-button @click="editCaseDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="editCase">确 定</el-button>
      </div>
    </el-drawer>
  </el-drawer>

  <!-- 运行环境选择对话框 -->
  <el-dialog :visible.sync="enviromentDialogVisible"
             width="30%"
             title="选择运行环境"
             @close="enviromentDialogClosed">
    <el-form label-width="140px"
             :model="sceneRunForm"
             ref="sceneRunFormRef"
             :rules="sceneRunFormRules">
      <el-form-item label="运行环境" prop="env_type">
        <el-select v-model="sceneRunForm.env_type" placeholder="请选择运行环境">
          <el-option label="测试环境" value="1"></el-option>
          <el-option label="预发环境" value="2"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="enviromentDialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitSceneRun">确 定</el-button>
    </span>
  </el-dialog>

  <!-- 展示运行结果对话框 -->
  <el-dialog :visible.sync="resultDialogVisible"
             width="60%"
             :close-on-click-modal="false"
             @close="resultDialogClosed">
    <template slot="title">
      <p style="font-weight: 600;font-size: 16px">{{ resultForm.msg }}</p>
    </template>
    <template v-if="resultForm.code">
      <div v-if="resultForm.code!='0000'" style="color: red;font-size:15px;font-weight: bold;margin-bottom: 10px;">{{resultForm.error}}</div>
      <div v-else style="color: #67C23A;font-size:15px;font-weight: bold;margin-bottom: 10px;">场景执行完成！</div>
    </template>
    <el-collapse>
      <el-collapse-item v-for="(item, index) in resultForm.data" :key="index">
        <template slot="title">
          <span v-if="item.assert==='0'" style="color: red;font-size:15px;">{{ item.title }}</span>
          <span v-else style="font-size:15px;">{{ item.title }}</span>
        </template>
        <template slot="name">
          {{ index }}
        </template>
          <div v-for="(detail, index) in item.detail"
               style="color: #505050;"
               :key="index">{{ detail }}</div>
      </el-collapse-item>
    </el-collapse>
    <span slot="footer" class="dialog-footer">
      <el-button @click="resultDialogVisible = false">关 闭</el-button>
    </span>
  </el-dialog>
  </div>
</template>

<script>
import Sortable from 'sortablejs'

export default {
  name: 'EditSceneDrawer',
  props: {
    // 弹框显示状态
    visible: {
      type: Boolean,
      default: false
    },
    // 弹框标题
    title: {
      type: String,
      default: '编辑场景'
    },
    // 操作类型：add/update/check
    type: {
      type: String,
      default: 'add'
    },
    // 场景数据
    sceneData: {
      type: Object,
      default: () => ({})
    },
    // 业务列表
    businessList: {
      type: Array,
      default: () => []
    },
    // API模型名称
    model: {
      type: String,
      default: 'scene_info'
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 表格标题
    tableTitle: {
      type: String,
      default: '关联案例'
    },
    // 是否显示添加案例按钮
    showAddCaseButton: {
      type: Boolean,
      default: false
    },
    // 表格ID
    tableId: {
      type: String,
      default: 'relationTable'
    },
    // 表格行key
    rowKey: {
      type: String,
      default: 'counterKey'
    },
    // 表格边框
    tableBorder: {
      type: Boolean,
      default: true
    },
    // 表格样式
    tableStyle: {
      type: String,
      default: 'width: 100%'
    },
    // 表格最大高度
    tableMaxHeight: {
      type: [String, Number],
      default: 400
    }
  },
  data() {
    return {
      // 抽屉显示状态
      drawerVisible: false,
      // 场景表单数据
      sceneForm: {
        scene_name: '',
        scene_describe: '',
        isShowPreparam: false,
        before_param: '',
        relation: []
      },
      // 前置参数列表
      preparamList: [],
      // 场景表单验证规则
      sceneFormRules: {
        scene_name: [
          { required: true, message: '请输入场景名称', trigger: 'blur' }
        ]
      },
      // 拖拽排序实例
      sortable: null,
      // 案例计数器
      counterKey: 0,
      // 是否为新增操作
      isAdd: true,
      // 内部操作类型
      internalType: 'add',
      // 运行功能相关数据
      enviromentDialogVisible: false,
      resultDialogVisible: false,
      sceneRunForm: {
        scene_id: '',
        env_type: '1'
      },
      // 运行结果数据
      resultForm: {},
      sceneRunFormRules: {
        env_type: [
          { required: true, message: '请选择运行环境', trigger: 'blur' }
        ]
      },
      // 添加案例相关数据
      addCaseDialogVisible: false,
      // 案例查询参数
      caseQueryInfo: {
        search_case_id: null,
        search_case_name: '',
        search_case_uri: '',
        creater: window.localStorage.getItem('user_name') || '',
        pagenum: 1,
        pagesize: 5,
        type: 'API'
      },
      // 案例列表数据
      caseList: [],
      // 案例总数
      caseTotal: 0,
      // 选中的案例列表
      selectedCaseList: [],
      // 获取案例列表加载状态
      getCaseListLoading: false,
      // 添加案例的位置索引
      addIndex: 0,
      // 编辑案例相关数据
      editCaseDialogVisible: false,
      // 案例表单数据
      caseForm: {},
      // 文件列表
      fileList: [],
      // 匹配断言列表
      matchAssertList: [],
      // 四则运算断言列表
      arithmeticAssertList: [],
      // 原始索引位置
      originalIndex: 0,
      // 案例表单验证规则
      caseFormRules: {
        upload_min_rows: [
          { required: true, message: '请输入最小行数', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value && value < 1) {
                callback(new Error('最小行数不能小于1'))
              } else if (value && this.caseForm.upload_max_rows && parseInt(value) > parseInt(this.caseForm.upload_max_rows)) {
                callback(new Error('最小行数不能大于最大行数'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],

        is_upload: [
          { required: true, message: '请上传文件', trigger: 'blur' }
        ],
        temp_file: [
          { required: true, message: '请输入模板下载地址', trigger: 'blur' }
        ]
      },
      // 提交状态控制
      isSubmitting: false
    }
  },
  computed: {
    // 文件上传路径
    uploadPath() {
      const baseUrl = this.$http.defaults.baseURL || ''
      return `${baseUrl}user_info/upload_file/`
    },
    // 请求头信息
    headers() {
      return {
        Authorization: window.localStorage.getItem('token') || ''
      }
    },
    // 当前操作类型
    currentType() {
      return this.internalType || this.type
    }
  },
  watch: {
    // 监听visible属性变化
    visible: {
      handler(newVal) {
        this.drawerVisible = newVal
        // 只在新增模式且有sceneData时才初始化数据
        if (newVal && this.isAdd && this.sceneData && Object.keys(this.sceneData).length > 0) {
          this.initSceneData()
        }
      },
      immediate: true
    },
    // 监听抽屉显示状态变化
    drawerVisible(newVal) {
      if (newVal) {
        // 当主抽屉打开时，添加ESC键监听器
        this.addEscapeListener()
      } else {
        // 当主抽屉关闭时，重置所有子抽屉状态并移除ESC键监听器
        this.resetAllSubDrawers()
        this.removeEscapeListener()
        if (this.visible) {
          this.$emit('update:visible', false)
        }
      }
    },
    'caseForm.upload_min_rows'(newVal) {
      // 最小值不能小于1
      if (newVal < 1) {
        this.caseForm.upload_min_rows = 1
      }
      // 如果最小值大于最大值，将最大值设置为与最小值相等
      if (newVal && this.caseForm.upload_max_rows && newVal > this.caseForm.upload_max_rows) {
        this.caseForm.upload_max_rows = newVal
      }
    },
    'caseForm.upload_max_rows'(newVal) {
      // 最大值不能小于最小值
      if (newVal && newVal < this.caseForm.upload_min_rows) {
        this.caseForm.upload_max_rows = this.caseForm.upload_min_rows
      }
    }
  },
  methods: {
    // ========== 数据初始化相关方法 ==========
    // 初始化场景数据
    initSceneData() {
      this.sceneForm = { ...this.sceneData }
      this.parsePreparamList()
    },

    // 解析前置参数字符串为列表
    parsePreparamList() {
      if (this.sceneForm.before_param && typeof this.sceneForm.before_param === 'string') {
        const preParaArry = this.sceneForm.before_param.split(';')
        if (preParaArry.length > 1) {
          this.preparamList = []
          preParaArry.pop()
          this.$set(this.sceneForm, 'isShowPreparam', true)
          preParaArry.forEach(item => {
            // 确保item是字符串且包含等号
            if (item && typeof item === 'string' && item.includes('=')) {
              const parts = item.split('=')
              const paramName = parts[0] || ''
              const paramValue = parts[1] || ''
              this.preparamList.push({
                paramName: paramName.trim(),
                paramValue: paramValue.trim()
              })
            }
          })
        }
      }
    },

    // ========== 前置参数管理方法 ==========
    // 添加前置参数
    addPreparam() {
      this.preparamList.push({
        paramName: '',
        paramValue: ''
      })
    },

    // 删除前置参数
    deletePreparm(index) {
      this.preparamList.splice(index, 1)
    },

    // ========== 抽屉控制相关方法 ==========
    // 处理抽屉关闭
    handleClose() {
      // 确保清理Sortable实例
      if (this.sortable) {
        this.sortable.destroy()
        this.sortable = null
      }
      this.resetForm()
      // 关闭抽屉
      this.drawerVisible = false
      this.$emit('update:visible', false)
      this.$emit('close')
    },

    // 处理场景表单提交
    handleSubmit(flag) {
      // 使用$nextTick确保DOM已经更新
      this.$nextTick(() => {
        // 确保表单引用存在
        if (!this.$refs.sceneFormRef) {
          this.$message.error('表单未准备就绪，请稍后再试')
          return
        }

        this.$refs.sceneFormRef.validate(valid => {
          if (!valid) return false
          if (this.sceneForm.relation.length === 0) {
            this.$message.error('请添加至少一个案例')
            return false
          }
          // 构建前置参数字符串
          let preparamStr = ''
          this.preparamList.forEach(item => {
            preparamStr = preparamStr + item.paramName + '=' + item.paramValue + ';'
          })
          this.sceneForm.before_param = preparamStr
          this.$emit('submit', { sceneData: this.sceneForm, flag })
        })
      })
    },

    // ========== 场景操作相关方法 ==========
    // 处理运行场景
    handleRunScene() {
      this.$emit('run-scene', this.sceneForm.scene_id)
    },

    // ========== 案例管理相关方法 ==========
    // 处理添加案例
    handleAddCase(index) {
      this.addIndex = index
      this.addCaseDialogVisible = true
      this.getCaseList()
    },

    // 处理编辑案例
    handleEditCase(caseInfo, index) {
      this.editCaseDialogShow(caseInfo, index)
    },

    // 处理删除案例
    handleDeleteCase(index) {
      this.$emit('delete-case', index)
    },
    // 处理抽屉打开动画结束
    handleOpened() {
      this.rowDrop()
      this.$emit('opened')
    },

    // ========== 拖拽排序相关方法 ==========
    // 初始化案例列表拖拽排序功能
    rowDrop() {
      const tbody = document.querySelector('#crTable .el-table__body-wrapper tbody')

      // 检查DOM元素是否存在
      if (!tbody) {
        console.warn('表格DOM元素未找到，无法初始化拖拽排序')
        return
      }

      // 检查$sortable是否可用
      if (!this.$sortable || typeof this.$sortable.create !== 'function') {
        console.warn('Sortable插件未正确加载')
        return
      }

      const _this = this
      const ops = {
        animation: 800,
        onEnd: function({ newIndex, oldIndex }) {
          // 确保索引有效且数组存在
          if (typeof newIndex === 'number' && typeof oldIndex === 'number' &&
              Array.isArray(_this.sceneForm.relation) &&
              oldIndex < _this.sceneForm.relation.length) {
            const currRow = _this.sceneForm.relation.splice(oldIndex, 1)[0]
            _this.sceneForm.relation.splice(newIndex, 0, currRow)
          }
        }
      }

      try {
        this.$sortable.create(tbody, ops)
      } catch (error) {
        console.error('初始化拖拽排序失败:', error)
      }
    },

    // ========== 表单重置相关方法 ==========
    // 重置场景表单数据
    resetForm() {
      this.sceneForm = {
        scene_name: '',
        scene_describe: '',
        isShowPreparam: false,
        before_param: '',
        relation: []
      }
      this.preparamList = []
      this.caseForm = {}
    },

    // 重置所有子抽屉状态
    resetAllSubDrawers() {
      // 重置所有子抽屉的显示状态
      this.addCaseDialogVisible = false
      this.editCaseDialogVisible = false
      this.enviromentDialogVisible = false
      this.resultDialogVisible = false

      // 清理选中的案例列表
      this.selectedCaseList = []

      // 重置编辑案例相关数据
      this.matchAssertList = []
      this.arithmeticAssertList = []
      this.originalIndex = -1
      this.caseForm = {}

      // 如果有表格引用，清除选择
      if (this.$refs.caseSelectTable) {
        this.$refs.caseSelectTable.clearSelection()
      }
    },

    // ========== ESC键处理相关方法 ==========
    // 添加ESC键监听器
    addEscapeListener() {
      document.addEventListener('keydown', this.handleEscapeKey)
    },

    // 移除ESC键监听器
    removeEscapeListener() {
      document.removeEventListener('keydown', this.handleEscapeKey)
    },

    // 处理ESC键事件，实现智能的嵌套抽屉关闭逻辑
    handleEscapeKey(event) {
      if (event.key === 'Escape' || event.keyCode === 27) {
        // 阻止默认的ESC键行为
        event.preventDefault()
        event.stopPropagation()

        // 按照层级优先关闭最上层的抽屉
        if (this.editCaseDialogVisible) {
          // 如果编辑案例抽屉打开，关闭它
          this.editCaseDialogVisible = false
        } else if (this.addCaseDialogVisible) {
          // 如果添加案例抽屉打开，关闭它
          this.addCaseDialogVisible = false
        } else if (this.enviromentDialogVisible) {
          // 如果环境选择抽屉打开，关闭它
          this.enviromentDialogVisible = false
        } else if (this.resultDialogVisible) {
          // 如果结果显示抽屉打开，关闭它
          this.resultDialogVisible = false
        } else if (this.drawerVisible) {
          // 如果只有主抽屉打开，关闭主抽屉
          this.handleClose()
        }
      }
    },

    // ========== 案例列表管理方法 ==========
    // 获取案例列表数据
    async getCaseList() {
      this.getCaseListLoading = true
      try {
        const { data: res } = await this.$http.get('case_info/case_list/', { params: this.caseQueryInfo })
        if (res.meta.status !== 200) {
          if (res.meta.status === 'HD1003' || res.meta.status === 'HD1004') {
            console.log('登录失效')
          } else {
            this.$message.error('获取案例列表失败')
          }
        } else {
          this.caseList = res.data.cases
          this.caseTotal = res.data.total
        }
      } catch (error) {
        this.$message.error('获取案例列表失败')
      } finally {
        this.getCaseListLoading = false
      }
    },
    // 查询案例
    queryCase() {
      this.caseQueryInfo.pagenum = 1
      this.getCaseList()
    },

    // 重置案例查询条件
    resetCaseQuery() {
      this.caseQueryInfo = {
        search_case_id: null,
        search_case_name: '',
        search_case_uri: '',
        creater: window.localStorage.getItem('user_name') || '',
        pagenum: 1,
        pagesize: 5,
        type: 'API'
      }
      this.getCaseList()
    },

    // ========== 案例分页相关方法 ==========
    // 案例分页大小改变处理
    caseSizeChange(newSize) {
      this.caseQueryInfo.pagesize = newSize
      this.getCaseList()
    },

    // 案例分页页码改变处理
    caseCurrentChange(newPage) {
      this.caseQueryInfo.pagenum = newPage
      this.getCaseList()
    },

    // ========== 案例选择和添加方法 ==========
    // 案例选择状态改变处理
    caseSelectionChange(value) {
      this.selectedCaseList = value
    },

    // 将选中的案例添加到场景中
    addCase() {
      // 验证是否选择了案例
      if (!Array.isArray(this.selectedCaseList) || this.selectedCaseList.length === 0) {
        this.$message.warning('请先选择要添加的案例')
        return
      }

      const addCaseList = []
      this.selectedCaseList.forEach(item => {
        addCaseList.push({
          relation_case_id: item.case_id,
          relation_case_name: item.case_name,
          interface_address: item.interface_uri,
          relation_interface_address: item.interface_uri,
          instructions: '',
          relation_case_type: item.case_type,
          is_get_param: false,
          request_param: '',
          response_param: '',
          response_param_alias: '',
          response_header_param: '',
          sleep_time: 0,
          reset_param: false,
          interface_data: '',
          is_assert: false,
          asserts: '',
          counterKey: this.counterKey,
          is_enable: true
        })
        this.counterKey += 1
      })

      if (this.addIndex === 9999) {
        this.sceneForm.relation.unshift(...addCaseList)
      } else {
        this.sceneForm.relation.splice(this.addIndex + 1, 0, ...addCaseList)
      }
      this.addCaseDialogVisible = false
    },
    // 关闭添加案例抽屉
    addCaseDialogClosed() {
      if (this.$refs.caseSelectTable) {
        this.$refs.caseSelectTable.clearSelection()
      }
      this.caseQueryInfo = {
        search_case_id: null,
        search_case_name: '',
        search_case_uri: '',
        creater: window.localStorage.getItem('user_name') || '',
        pagenum: 1,
        pagesize: 5,
        type: 'API'
      }
      this.caseTotal = 0

      // 重新初始化Sortable
      this.$nextTick(() => {
        this.rowDrop()
      })
    },
    // 打开编辑案例抽屉
    async editCaseDialogShow(caseInfo, index) {
      // 参数验证
      if (!caseInfo || typeof index !== 'number') {
        this.$message.error('参数错误，无法编辑案例')
        return
      }

      try {
        this.originalIndex = index
        const objString = JSON.stringify(caseInfo)
        this.caseForm = JSON.parse(objString)
        if (!this.caseForm.scene_relation_id) {
          this.caseForm.reset_param = true
        }

        const { data: res } = await this.$http.get('/user_info/get_base_info/', {
          params: {
            id: caseInfo.relation_case_id,
            scene_relation_id: caseInfo.scene_relation_id,
            type: 'API'
          }
        })

        if (res.meta.status !== 200) {
          this.$message.error('获取案例编辑信息失败')
          return
        }
        if (this.caseForm.relation_case_type === 'UPLOAD' && (this.caseForm.file_path === '' || !this.caseForm.interface_data)) {
          this.caseForm = {
            ...this.caseForm,
            upload_max_rows: '',
            upload_min_rows: '',
            temp_file: ''
          }
          this.caseForm.interface_address = res.data.interface_address
          this.caseForm.upload_max_rows = res.data.upload_max_rows
          this.caseForm.upload_min_rows = res.data.upload_min_rows
          this.caseForm.temp_file = res.data.temp_file
          this.fileList = [{
            name: this.extractFileName(res.data.file_path), // 从文件路径中提取文件名
            url: res.data.file_path, // 文件路径
            status: 'success' // 设置文件状态为成功
          }]
          this.caseForm.file_path = ''
        }
        if (this.caseForm.relation_case_type === 'UPLOAD' && this.caseForm.file_path !== '') {
          this.caseForm = {
            ...this.caseForm
          }
          this.fileList = [{
            name: this.extractFileName(this.caseForm.file_path), // 从文件路径中提取文件名
            url: this.caseForm.file_path, // 文件路径
            status: 'success' // 设置文件状态为成功
          }]
        }
        this.caseForm.is_upload = '1'
        this.caseForm.file_path = ''
        if (this.caseForm.relation_case_type === 'API' || this.caseForm.relation_case_type === 'EXPORT') {
          this.caseForm.interface_address = res.data.interface_address
        }
        if ((this.caseForm.interface_data === '' || !this.caseForm.interface_data) && this.caseForm.relation_case_type !== 'EXPORT') {
          this.caseForm.interface_data = res.data.interface_data
          this.caseForm.is_assert = res.data.is_assert
          this.caseForm.asserts = res.data.asserts
        }
        if (this.caseForm.relation_case_type === 'EXPORT' && Array.isArray(this.caseForm.asserts) && this.caseForm.asserts.length === 0) {
          this.caseForm.interface_data = res.data.interface_data
          this.caseForm.is_assert = res.data.is_assert
          this.caseForm.asserts = res.data.asserts
        }

        // 安全地处理断言数组
        if (Array.isArray(this.caseForm.asserts)) {
          this.caseForm.asserts.forEach(item => {
            if (item.assert_type === '1') {
              this.matchAssertList.push(item)
            } else if (item.assert_type === '2') {
              this.arithmeticAssertList.push(item)
            }
          })
        }

        this.editCaseDialogVisible = true
      } catch (error) {
        console.error('编辑案例数据加载失败:', error)
        this.$message.error('加载案例编辑信息失败，请重试')
      }
    },

    // 编辑案例
    editCase() {
      // 防重复提交
      if (this.isSubmitting) {
        this.$message.warning('正在提交中，请勿重复操作')
        return
      }

      // 检查表单引用是否存在
      if (!this.$refs.caseFormRef) {
        this.$message.error('表单未准备就绪，请稍后再试')
        return
      }

      this.$refs.caseFormRef.validate(async valid => {
        if (!valid) return false

        this.isSubmitting = true

        try {
          const asserts = [...this.matchAssertList, ...this.arithmeticAssertList]
          this.caseForm.asserts = asserts

          if (this.caseForm.is_assert) {
            if (this.caseForm.asserts.length === 0) {
              return this.$message.error('断言为是，请填写断言内容')
            }
            // 判断断言字段是否有重复项
            let flag = false
            this.matchAssertList.forEach(item => {
              if (this.matchAssertList.some(item2 => item !== item2 && item.return_value === item2.return_value)) {
                flag = true
              }
            })
            if (flag) return this.$message.error('断言字段不能有重复项')
          }

          if (this.caseForm.is_upload) {
            if (this.caseForm.is_upload !== '1') {
              return this.$message.error('请上传文件!')
            }
          }

          // 对于UPLOAD类型案例，验证上传行数的逻辑关系
          if (this.caseForm.relation_case_type === 'UPLOAD') {
            const minRows = parseInt(this.caseForm.upload_min_rows)
            const maxRows = parseInt(this.caseForm.upload_max_rows)

            if (minRows && minRows < 1) {
              this.isSubmitting = false
              return this.$message.error('上传行数最小值不能小于1')
            }

            if (minRows && maxRows && minRows > maxRows) {
              this.isSubmitting = false
              return this.$message.error('上传行数最小值不能大于最大值')
            }
          }

          // 确保接口地址字段同步
          // 将interface_address的值同步到relation_interface_address
          if (this.caseForm.interface_address) {
            this.caseForm.relation_interface_address = this.caseForm.interface_address
          }

          this.$set(this.sceneForm.relation, this.originalIndex, this.caseForm)
          this.editCaseDialogVisible = false
        } catch (error) {
          console.error('编辑案例失败:', error)
          this.$message.error('编辑案例失败，请重试')
        } finally {
          this.isSubmitting = false
        }
      })
    },
    // 关闭编辑案例抽屉
    editCaseDrawerClosed() {
      this.matchAssertList = []
      this.arithmeticAssertList = []
      this.caseForm = {}

      // 重新初始化Sortable
      this.$nextTick(() => {
        this.rowDrop()
      })
    },
    // 添加匹配断言
    addMatchAssert() {
      this.matchAssertList.push({
        assert_type: '1',
        return_value: '',
        assert_operator: '1',
        expect_result: '',
        operation_value: ''
      })
    },
    // 删除断言
    deleteAssert(isInterface, index) {
      if (isInterface) {
        this.matchAssertList.splice(index, 1)
      } else {
        this.arithmeticAssertList.splice(index, 1)
      }
    },
    // 添加四则运算断言
    addArithmeticAssert() {
      this.arithmeticAssertList.push({
        assert_type: '2',
        return_value: '',
        assert_operator: '1',
        expect_result: '',
        operation_value: ''
      })
    },

    // ========== 公共方法供父组件调用 ==========
    // 显示添加案例抽屉
    showAddCaseDrawer(index) {
      this.addIndex = index
      this.addCaseDialogVisible = true
      this.getCaseList()
    },
    // 显示编辑案例抽屉
    showEditCaseDrawer(caseInfo, index) {
      this.editCaseDialogShow(caseInfo, index)
    },

    // ========== 文件上传相关方法 ==========
    // 文件改变处理
    handleFileChange(file, fileList) {
      this.caseForm.file = file
    },
    // 文件删除前处理
    beforeRemove(file, fileList) {
      if (this.caseForm.reset_param === false) {
        this.$message.warning('重设数据关闭无法删除文件!')
        return false
      }
      return true
    },
    // 文件删除处理
    handleRemove(file, fileList) {
      this.caseForm.file = null
      this.caseForm.is_upload = '0'
      this.$forceUpdate()
    },
    // 文件超出限制处理
    handleExceed(files, fileList) {
      this.$message.warning('请先删除已上传文件后重新上传!')
    },
    // 文件上传成功处理
    handleUploadSuccess(response, file) {
      this.caseForm.is_update = '1'
      this.caseForm.is_upload = '1'
      this.caseForm.file_path = response.data.file_path
      if (response.meta.status !== 200) return this.$message.error('文件上传失败')
      this.$message.success('文件上传成功！')
    },

    // ========== 其他辅助方法 ==========
    // 提取文件名的公共方法
    extractFileName(filePath) {
      if (!filePath || typeof filePath !== 'string') {
        return ''
      }
      return filePath.split('/').pop() || ''
    },

    // ========== 断言和数据处理方法 ==========
    // 处理导出表单断言开关状态变化
    handleExportFormIsAssertSwitch(val) {
      if (val && this.caseForm.relation_case_type === 'EXPORT') {
        this.matchAssertList.push({
          assert_type: '1',
          return_value: 'ROW',
          assert_operator: '1',
          expect_result: ''
        })
      } else if (!val && this.caseForm.relation_case_type === 'EXPORT') {
        this.matchAssertList = this.matchAssertList.filter(item => item.return_value !== 'ROW')
      }
    },

    // 转换JSON格式，处理特殊字符
    transferJson() {
      if (this.caseForm.interface_data && this.caseForm.relation_case_type === 'API') {
        this.caseForm.interface_data = this.caseForm.interface_data.replace(/:\s*@{/g, ':"*@{').replace(/:\s*\[@{/g, ':["*@{').replace(/:\s*{@{/g, ':{"*@{')
        this.caseForm.interface_data = this.caseForm.interface_data.replace(/}@,/g, '}@*",').replace(/}@]/g, '}@*"]').replace(/}@}/g, '}@*"}')
        this.caseForm.interface_data = JSON.parse(this.caseForm.interface_data)
        this.caseForm.interface_data = JSON.stringify(this.caseForm.interface_data, null, 4)
      }
      if (this.caseForm.upload_param && this.caseForm.relation_case_type === 'UPLOAD') {
        this.caseForm.upload_param = this.caseForm.upload_param.replace(/:\s*@{/g, ':"*@{').replace(/:\s*\[@{/g, ':["*@{').replace(/:\s*{@{/g, ':{"*@{')
        this.caseForm.upload_param = this.caseForm.upload_param.replace(/}@,/g, '}@*",').replace(/}@]/g, '}@*"]').replace(/}@}/g, '}@*"}')
        this.caseForm.upload_param = JSON.parse(this.caseForm.upload_param)
        this.caseForm.upload_param = JSON.stringify(this.caseForm.upload_param, null, 4)
      }
    },

    // 处理数据越权检查状态变化
    changeIsCheck(caseInfo) {
      const objString = JSON.stringify(caseInfo)
      this.caseForm = JSON.parse(objString)
    },

    // ========== 迁移的基础方法 ==========
    // 根据ID获取案例关联信息
    async getCaseRelationById(id) {
      // 参数验证
      if (!id) {
        this.$message.error('场景ID不能为空')
        return
      }

      try {
        const { data: res } = await this.$http.get('/user_info/get_base_info/', { params: { id: id, type: 'SCENE' } })
        if (res.meta.status !== 200) {
          this.$message.error('获取场景信息失败')
          return
        }

        // 使用Object.assign确保响应式更新
        Object.assign(this.sceneForm, res.data)

        // 强制更新视图
        this.$forceUpdate()

        // 处理前置参数
        if (res.data.before_param) {
          const preParaArry = res.data.before_param.split(';')
          if (preParaArry.length > 1) {
            this.preparamList = []
            preParaArry.pop()
            this.$set(this.sceneForm, 'isShowPreparam', true)
            preParaArry.forEach(item => {
              const paramName = item.split('=')[0]
              const paramValue = item.split('=')[1]
              this.preparamList.push({
                paramName: paramName,
                paramValue: paramValue
              })
            })
          }
        }
        // 处理关联案例数据
        if (this.sceneForm.relation && Array.isArray(this.sceneForm.relation)) {
          this.sceneForm.relation.forEach(item => {
            item.counterKey = this.counterKey
            item.is_update = '0'
            this.counterKey += 1
            if (item.is_check === 1) {
              item.is_check = true
            } else {
              item.is_check = false
            }
          })
        }
      } catch (error) {
        console.error('获取场景信息失败:', error)
        this.$message.error('获取场景信息失败，请重试')
      }
    },

    // ========== 案例复制相关方法 ==========
    // 复制案例到指定位置
    copyCase(caseInfo) {
      // 参数验证
      if (!caseInfo) {
        this.$message.error('案例信息不能为空')
        return
      }

      // 深拷贝案例信息，避免引用问题
      const copiedCase = JSON.parse(JSON.stringify(caseInfo))
      copiedCase.counterKey = this.counterKey
      this.counterKey += 1

      const addCaseList = [copiedCase]

      if (this.addIndex === 9999) {
        this.sceneForm.relation.unshift(...addCaseList)
      } else {
        this.sceneForm.relation.splice(this.addIndex + 1, 0, ...addCaseList)
      }
    },

    // 处理案例复制操作
    handleCopyCase(caseInfo, index) {
      // 如果没有传递index，则查找当前案例在列表中的位置
      if (typeof index === 'undefined') {
        index = this.sceneForm.relation.findIndex(item =>
          item.counterKey === caseInfo.counterKey ||
          item.relation_case_id === caseInfo.relation_case_id
        )
      }

      // 设置插入位置（在当前案例后面）
      this.addIndex = index >= 0 ? index : 9999
      this.copyCase(caseInfo)
    },

    // ========== 案例抽屉管理方法 ==========
    // 显示添加案例抽屉
    addCaseDrawerShow(index) {
      this.addIndex = index
      this.addCaseDialogVisible = true
      this.getCaseList()
    },

    // ========== 案例删除相关方法 ==========
    // 删除指定索引的案例
    deleteCase(index) {
      this.sceneForm.relation.splice(index, 1)
    },

    // 删除当前案例
    deleteCurrentCase(index) {
      this.sceneForm.relation.splice(index, 1)
    },

    // ========== 场景表单管理方法 ==========
    // 重置场景表单数据
    resetSceneForm() {
      this.sceneForm = {
        scene_name: '',
        business_id: 0,
        scene_describe: '',
        isShowPreparam: false,
        before_param: '',
        relation: []
      }
      this.preparamList = []
      this.counterKey = 0
    },

    // ========== 对话框显示管理方法 ==========
    // 显示编辑场景对话框
    async showDialog(isAdd, id, type) {
      this.internalType = type
      this.isAdd = isAdd

      if (!isAdd && id) {
        // 编辑模式：先获取数据
        await this.getCaseRelationById(id)
      } else if (isAdd) {
        // 新增模式：重置表单数据
        this.resetSceneForm()
      }

      this.drawerVisible = true
      this.$emit('update:visible', true)
    },

    // ========== 场景提交相关方法 ==========
    // 提交场景数据，包含数据越权检查
    async submitScene(flag) {
      // 表单验证已经在组件内部完成
      if (this.sceneForm.relation.length === 0) return this.$message.error('请添加至少一个案例')

      this.$emit('update:loading', true)

      // 前置参数处理已经在组件内部完成
      let isCheck = false
      let needCheck = false
      this.sceneForm.relation.forEach(item => {
        if (item.is_enable === true && item.relation_case_type === 'API') {
          let url
          try {
            // 只构造 URL 如果是有效的地址
            url = new URL('http:/' + item.relation_interface_address)
          } catch (error) {
            // 如果地址无效，跳过这个项或采取其他措施
            return // 跳过当前项
          }
          const pathSegments = url.pathname.split('/')
          let interfaceMethodName = pathSegments[pathSegments.length - 1] // 获取路径的最后部分
          if (/[?@$]/.test(interfaceMethodName)) {
            interfaceMethodName = item.relation_interface_address.split('/').filter(Boolean).slice(-2, -1)[0]
          }
          if (interfaceMethodName.startsWith('find') || interfaceMethodName.startsWith('get')) {
            needCheck = true
          }
          if (item.is_check === true) {
            isCheck = true
          }
        }
        if (!item.is_get_param) {
          item.response_param = ''
          item.response_param_alias = ''
          item.request_param = ''
          item.response_header_param = ''
        }
      })

      if (needCheck && !isCheck) {
        this.$confirm('当前场景中可能存在数据越权风险的接口，请确认是否已添加数据越权案例!', '提示', {
          confirmButtonText: '无需添加',
          cancelButtonText: '去添加',
          type: 'warning',
          customClass: 'check-notice-dialog'
        }).then(async () => {
          await this.performSceneSubmit(flag)
        }).catch(() => {
          this.$emit('update:loading', false)
          return false
        })
      } else {
        await this.performSceneSubmit(flag)
      }
    },

    // 执行实际的场景提交操作
    async performSceneSubmit(flag) {
      try {
        // 准备提交数据，确保数据格式正确
        const submitData = this.prepareSubmitData()

        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/scene_add/', submitData)
          this.$emit('update:loading', false)
          if (res.meta.status !== 200) return this.$message.error('新增场景失败')
          this.$message.success('新增场景成功')
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.sceneForm.scene_id + '/scene_update/', submitData)
          this.$emit('update:loading', false)
          if (res.meta.status !== 200) return this.$message.error('编辑场景失败')
          this.$message.success('编辑场景成功')
          await this.getCaseRelationById(this.sceneForm.scene_id)
        }
        if (flag) {
          this.drawerVisible = false
          this.$emit('update:visible', false)
          this.$emit('scene-updated')
          // 通知父组件刷新列表
          this.$emit('refresh-list')
        }
      } catch (error) {
        this.$emit('update:loading', false)
        this.$message.error('操作失败')
      }
    },

    // ========== 数据处理工具方法 ==========
    // 准备场景提交数据，清理UI字段并处理关联ID
    prepareSubmitData() {
      // 深拷贝场景表单数据
      const submitData = JSON.parse(JSON.stringify(this.sceneForm))

      // 删除UI专用字段，这些字段不应该发送给后端
      // isShowPreparam 是前端UI状态字段，后端不需要
      if (Object.prototype.hasOwnProperty.call(submitData, 'isShowPreparam')) {
        delete submitData.isShowPreparam
      }

      // 收集所有有效的scene_relation_id
      const sceneRelationIds = []

      submitData.relation.forEach(item => {
        // 如果案例有scene_relation_id，收集它
        if (item.scene_relation_id) {
          sceneRelationIds.push(item.scene_relation_id)
        }
      })

      // 设置顶层的scene_relation_id字段
      if (sceneRelationIds.length > 0) {
        submitData.scene_relation_id = sceneRelationIds.join('|')
      }

      return submitData
    },

    // ========== 场景运行功能相关方法 ==========
    // 显示场景运行环境选择对话框
    async showSceneRunDialog(id) {
      this.sceneRunForm.scene_id = id
      this.enviromentDialogVisible = true
    },

    // 环境选择对话框关闭处理
    enviromentDialogClosed() {
      this.$refs.sceneRunFormRef.resetFields()
    },

    // ========== 场景运行相关方法 ==========
    // 提交场景运行请求
    async submitSceneRun() {
      try {
        this.$emit('update:loading', true)
        const { data: res } = await this.$http.post(this.model + '/scene_run/', this.sceneRunForm)

        if (res.meta.status !== 200) {
          this.$message.error('调试场景失败')
          return
        }

        this.resultForm = res.data
        this.enviromentDialogVisible = false
        this.resultDialogVisible = true
      } catch (error) {
        console.error('场景运行失败:', error)
        this.$message.error('场景运行失败，请重试')
      } finally {
        this.$emit('update:loading', false)
      }
    },

    // 获取场景调试日志数据
    async getSceneDebugLog(id) {
      const { data: res } = await this.$http.get('scene_info/scene_debug_log/?scene_id=' + id + '&log_type=2')
      if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
      this.resultForm = res.data
      this.enviromentDialogVisible = false
      this.resultDialogVisible = true
    },

    // ========== 对话框关闭处理方法 ==========
    // 运行结果对话框关闭处理
    resultDialogClosed() {
      this.resultForm = {}
    }
  },

  // ========== 生命周期钩子 ==========
  // 组件销毁前清理资源
  beforeDestroy() {
    try {
      // 清理Sortable实例
      if (this.sortable && typeof this.sortable.destroy === 'function') {
        this.sortable.destroy()
        this.sortable = null
      }

      // 清理可能残留的DOM事件监听器
      // 使用固定的表格ID，避免undefined问题
      const tableSelector = '#crTable .el-table__body-wrapper tbody'
      const tbody = document.querySelector(tableSelector)
      if (tbody && tbody.sortable && typeof tbody.sortable.destroy === 'function') {
        tbody.sortable.destroy()
      }

      // 移除ESC键监听器
      this.removeEscapeListener()

      // 重置所有子抽屉状态
      this.resetAllSubDrawers()

      // 重置提交状态
      this.isSubmitting = false
    } catch (error) {
      console.error('组件销毁时清理资源失败:', error)
    }
  }
}
</script>

<style scoped>
.scene-drawer {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
  }
  .el-drawer__header{
    margin-bottom: 20px;
  }
  .el-drawer__body{
    height: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  .scene-drawer-content{
    height: 0;
    flex-grow: 2;
  }
  .scene-drawer-footer {
    padding: 20px 0;
    text-align: center;
  }
  .el-scrollbar__view {
    padding:0 30px 15px;
  }
  .relationCaseTabel {
    margin-top: 15px;
    border-top: 1px solid #dfe6ec;
    border-left: 1px solid #dfe6ec;
    border-right: 1px solid #dfe6ec;
  }
  .el-divider__text {
    font-size: 15px;
    font-weight: bold;
    color: #606266;
  }
  .el-drawer__header {
    text-align: center;
  }
}

/* 添加案例抽屉样式 */
.add-case-drawer {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
  }
  .el-drawer__body{
    height: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  .el-scrollbar__view {
    padding:0 30px 20px;
  }
  .add-case-drawer-footer {
    padding: 20px 30px;
    text-align: right;
  }
}

/* 编辑案例抽屉样式 */
.edit-case-drawer {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
  }
   .el-drawer__body{
    height: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  .el-scrollbar__view {
    padding:0 30px 20px;
  }
  .edit-case-drawer-footer {
    padding: 20px 30px;
    text-align: right;
  }
}

.check-notice-dialog .el-message-box__btns {
  display: flex;
  flex-direction: row-reverse;
}

.check-notice-dialog .el-message-box__btns button:nth-child(1) {
  margin-left: 15px;  /* 增加按钮之间的间距 */
}

.check-notice-dialog .el-message-box__btns button:nth-child(2) {
  margin-left: 20px;
  margin-right: 0;
}
</style>
