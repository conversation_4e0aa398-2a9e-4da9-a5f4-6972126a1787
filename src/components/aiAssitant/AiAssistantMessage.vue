<template>
  <div class="assistant-message">
    <div class="message-header">
      <img class="avatar" :src="require('./../img/ai-assitant-logo.png')" />
    </div>
    <div class="message-item">
      <div class="sender-name">AI测试助手</div>
      <div class="message-content">
        <m-single-collapse :default-active="true">
          <div :class="['status', message.status]" slot="title">
            <el-button icon="el-icon-loading" size="mini" circle v-if="message.status === 'thinking'"></el-button>
            <!-- <m-loading :size="16" :element-size="6" color="#59B0FF" v-if="message.status === 'thinking'" /> -->
            <el-button type="danger" icon="el-icon-close" size="mini" circle
                       v-if="message.status === 'error'"></el-button>
            <!-- <h-icon name="cancel" style-type="fill" style="color: var(--h-red-s80); font-size: 18px"
              v-if="message.status === 'error'" /> -->
            <el-button type="success" icon="el-icon-check" size="mini" circle
                       v-if="message.status === 'success'"></el-button>
            <!-- <h-icon name="success" style-type="fill" style="color: #59b0ff; font-size: 18px"
              v-if="message.status === 'success'" /> -->
            <span style="margin-left: 6px; color: var(--h-text-basis-default-color); font-size: large;">{{
                statusText
              }}</span>
          </div>
          <!-- 思考过程 -->
          <div class="markdown-body thinking-body" v-if="message.thinkContent" v-html="marked(message.thinkContent)"/>

          <!-- 智能内容渲染 -->
          <div class="content-wrapper">
            <div v-if="contentType === 'markdown'" class="markdown-body reponse-body" v-html="marked(message.content)"/>
            <div v-else-if="contentType === 'sql'" class="sql-content">
              <div class="content-header">
                <i class="el-icon-document"></i>
                <span>SQL查询结果</span>
                <el-button size="mini" type="text" @click="copyContent" class="copy-btn" :loading="copying">
                  <i class="el-icon-copy-document" v-if="!copying"></i>
                  {{ copying ? '复制中...' : '复制' }}
                </el-button>
              </div>
              <div class="code-block">
                <pre><code v-html="highlightedContent"></code></pre>
              </div>
            </div>
            <div v-else-if="contentType === 'code'" class="code-content">
              <div class="content-header">
                <i class="el-icon-document"></i>
                <span>代码内容</span>
                <el-button size="mini" type="text" @click="copyContent" class="copy-btn" :loading="copying">
                  <i class="el-icon-copy-document" v-if="!copying"></i>
                  {{ copying ? '复制中...' : '复制' }}
                </el-button>
              </div>
              <div class="code-block">
                <pre><code v-html="highlightedContent"></code></pre>
              </div>
            </div>
            <div v-else-if="contentType === 'tool_result'" class="tool-result-content">
              <!-- 检查是否包含多个SQL -->
              <div v-if="parsedSqlStatements.length > 1">
                <!-- 工具执行结果头部 -->
                <div class="content-header">
                  <i class="el-icon-setting"></i>
                  <span>工具执行结果</span>
                </div>
                <!-- 显示格式化的工具结果（不包含SQL部分） -->
                <div class="formatted-result">
                  <pre class="structured-text">{{ formattedToolResultWithoutSql }}</pre>
                </div>
                <!-- 多SQL语句显示 -->
                <div class="multi-sql-container">
                  <div v-for="(sqlItem, index) in parsedSqlStatements" :key="index" class="sql-item">
                    <div class="content-header">
                      <div class="header-left">
                        <i class="el-icon-document"></i>
                        <span>{{ sqlItem.title }}</span>
                      </div>
                      <el-button size="mini" type="text" @click="copySqlContent(sqlItem.sql, index)"
                                 class="copy-btn" :loading="getSqlCopyingState(index)">
                        <i class="el-icon-copy-document" v-if="!getSqlCopyingState(index)"></i>
                        {{ getSqlCopyingState(index) ? '复制中...' : '复制' }}
                      </el-button>
                    </div>
                    <div class="code-block">
                      <pre><code v-html="highlightSqlBlock(sqlItem.displayContent)"></code></pre>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 原有的单一工具结果显示 -->
              <div v-else>
                <div class="content-header">
                  <i class="el-icon-setting"></i>
                  <span>工具执行结果</span>
                  <el-button size="mini" type="text" @click="copyContent" v-if ="!downloadUrl" class="copy-btn" :loading="copying">
                    <i class="el-icon-copy-document" v-if="!copying"></i>
                    {{ copying ? '复制中...' : '复制' }}
                  </el-button>
                  <!-- 添加下载按钮 -->
                  <el-button
                    size="mini"
                    type="primary"
                    plain
                    @click="handleDownload"
                    v-if="downloadUrl"
                    :loading="downloading"
                    :disabled="downloading">
                    <i class="el-icon-download"></i>
                    {{ downloading ? '下载中...' : '下载' }}
                  </el-button>
                </div>
                <div class="formatted-result">
                  <pre class="structured-text">{{ formattedContent }}</pre>
                </div>
              </div>
            </div>
            <div v-else class="formatted-text">
              <pre class="plain-text">{{ formattedContent }}</pre>
            </div>
          </div>
        </m-single-collapse>
      </div>
    </div>
  </div>
</template>

<script>
import DOMPurify from 'dompurify'
import { marked } from 'marked'
import CodeMirror from 'codemirror'

import mSingleCollapse from './AIsingleCollapse.vue'

// 导入SQL语法高亮模式
import 'codemirror/mode/sql/sql'
import 'codemirror/mode/javascript/javascript'
import 'codemirror/mode/python/python'
import 'codemirror/mode/xml/xml'
import 'codemirror/mode/css/css'

// import mLoading from '@/module/components/loading'
import 'github-markdown-css/github-markdown-light.css'

export default {
  name: 'ai-assistant-message',
  props: {
    // 当前AI助手的消息
    message: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      copying: false, // 复制状态
      sqlCopyingStates: {}, // 存储每个SQL的复制状态
      downloading: false // 下载状态
    }
  },
  methods: {
    marked(content) {
      const html = DOMPurify.sanitize(marked(content))
      // 创建一个临时 div 用于 DOM 操作
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = html

      // 为所有链接添加 target="_blank"
      tempDiv.querySelectorAll('a').forEach((a) => {
        a.setAttribute('target', '_blank')
        a.setAttribute('rel', 'noopener noreferrer')
      })

      // 可选：将图片包裹在链接中
      tempDiv.querySelectorAll('img').forEach((img) => {
        const wrapper = document.createElement('a')
        wrapper.href = img.src
        wrapper.target = '_blank'
        wrapper.rel = 'noopener noreferrer'
        img.parentNode.insertBefore(wrapper, img)
        wrapper.appendChild(img)
      })

      return tempDiv.innerHTML
    },

    // 检测内容类型
    detectContentType(content) {
      if (!content || typeof content !== 'string') return 'plain'

      // 检测是否包含工具调用结果的特征
      if (content.includes('调用工具:') || content.includes('获取sqlTraceId:') || content.includes('数据库操作:')) {
        return 'tool_result'
      }

      // 检测SQL查询结果
      if (content.includes('SQL') && (content.includes('select') || content.includes('SELECT') ||
          content.includes('insert') || content.includes('INSERT') ||
          content.includes('update') || content.includes('UPDATE') ||
          content.includes('delete') || content.includes('DELETE'))) {
        return 'sql'
      }

      // 检测代码块
      if (content.includes('```') || content.includes('function') || content.includes('class') ||
          content.includes('def ') || content.includes('import ') || content.includes('const ') ||
          content.includes('var ') || content.includes('let ')) {
        return 'code'
      }

      // 检测Markdown格式
      if (content.includes('#') || content.includes('**') || content.includes('*') ||
          content.includes('[') || content.includes('](') || content.includes('`')) {
        return 'markdown'
      }

      return 'plain'
    },

    // 解析多个SQL语句
    parseSqlStatements(content) {
      if (!content || typeof content !== 'string') return []

      const sqlStatements = []

      // 使用正则表达式匹配 === SQL X === 格式的SQL块
      const sqlBlockRegex = /=== SQL (\d+) ===([\s\S]*?)(?=(?:=== SQL \d+ ===)|$)/g
      let match

      while ((match = sqlBlockRegex.exec(content)) !== null) {
        const sqlNumber = match[1]
        const sqlBlock = match[2].trim()

        // 提取完整SQL内容（用于复制）
        const sqlMatch = sqlBlock.match(/完整SQL:\s*([\s\S]*?)(?=\n(?:[^\n]*?:)|$)/i)
        if (sqlMatch) {
          const cleanSql = sqlMatch[1].trim()

          // 格式化显示内容（包含所有信息）
          const formattedDisplay = this.formatSqlBlockForDisplay(sqlBlock)

          sqlStatements.push({
            title: `SQL ${sqlNumber}`,
            sql: cleanSql, // 用于复制的纯SQL
            displayContent: formattedDisplay // 用于显示的完整内容
          })
        }
      }

      return sqlStatements
    },

    // 格式化SQL块用于显示
    formatSqlBlockForDisplay(sqlBlock) {
      return sqlBlock
        .replace(/数据库类型:/g, '🗄️ 数据库类型:')
        .replace(/数据库实例:/g, '🏢 数据库实例:')
        .replace(/服务名称:/g, '⚙️ 服务名称:')
        .replace(/状态:/g, '✅ 状态:')
        .replace(/完整SQL:/g, '📄 完整SQL:')
        .trim()
    },

    // 获取SQL复制状态
    getSqlCopyingState(index) {
      return this.sqlCopyingStates[index] || false
    },

    // 复制单个SQL内容
    async copySqlContent(sqlContent, index) {
      if (!sqlContent || this.getSqlCopyingState(index)) return

      // 设置当前SQL项的复制状态
      this.$set(this.sqlCopyingStates, index, true)

      try {
        // 优先使用现代的 Clipboard API
        if (navigator.clipboard && navigator.clipboard.writeText) {
          await navigator.clipboard.writeText(sqlContent)
          this.$message.success('SQL内容已复制到剪贴板')
          return
        }

        // 降级方案：使用传统的 document.execCommand
        this.fallbackCopyTextToClipboard(sqlContent)
        this.$message.success('SQL内容已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        this.$message.error('复制失败，请手动选择文本复制')
      } finally {
        this.$set(this.sqlCopyingStates, index, false)
      }
    },

    // 复制内容到剪贴板
    async copyContent() {
      if (this.copying) return // 防止重复点击

      this.copying = true
      try {
        // 优先使用现代的 Clipboard API
        if (navigator.clipboard && navigator.clipboard.writeText) {
          await navigator.clipboard.writeText(this.message.content)
          this.$message.success('内容已复制到剪贴板')
          return
        }

        // 降级方案：使用传统的 document.execCommand
        this.fallbackCopyTextToClipboard(this.message.content)
        this.$message.success('内容已复制到剪贴板')
      } catch (error) {
        console.error('复制失败:', error)
        this.$message.error('复制失败，请手动选择文本复制')
      } finally {
        this.copying = false
      }
    },

    // 降级复制方案
    fallbackCopyTextToClipboard(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text

      // 避免滚动到底部
      textArea.style.top = '0'
      textArea.style.left = '0'
      textArea.style.position = 'fixed'
      textArea.style.opacity = '0'
      textArea.style.pointerEvents = 'none'

      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      try {
        const successful = document.execCommand('copy')
        if (!successful) {
          throw new Error('execCommand copy failed')
        }
      } catch (err) {
        console.error('降级复制方案也失败了:', err)
        throw err
      } finally {
        document.body.removeChild(textArea)
      }
    },

    // 格式化工具执行结果
    formatToolResult(content) {
      return content
        .replace(/调用工具:/g, '\n📋 调用工具:')
        .replace(/获取sqlTraceId:/g, '\n🔍 获取sqlTraceId:')
        .replace(/数据库操作:/g, '\n💾 数据库操作:')
        .replace(/SQL\s*1\s*---/g, '\n📝 SQL查询:')
        .replace(/数据库实例:/g, '\n🗄️ 数据库实例:')
        .replace(/状态:/g, '\n✅ 状态:')
        .replace(/完整SQL:/g, '\n📄 完整SQL:')
        .trim()
    },

    // SQL语法高亮
    highlightSql(content) {
      // 简单的SQL语法高亮
      return content
        .replace(/(SELECT|FROM|WHERE|JOIN|LEFT JOIN|RIGHT JOIN|INNER JOIN|ORDER BY|GROUP BY|HAVING|INSERT|UPDATE|DELETE|CREATE|ALTER|DROP)/gi,
          '<span class="sql-keyword">$1</span>')
        .replace(/('.*?')/g, '<span class="sql-string">$1</span>')
        .replace(/(\b\d+\b)/g, '<span class="sql-number">$1</span>')
        .replace(/(--.*$)/gm, '<span class="sql-comment">$1</span>')
        .replace(/\n/g, '<br>')
    },

    // SQL块语法高亮（包含完整信息）
    highlightSqlBlock(content) {
      return content
        // 高亮信息标签
        .replace(/(🗄️|🏢|⚙️|✅|📄)\s*([^:]+:)/g, '<span class="sql-info-label">$1 $2</span>')
        // 高亮SQL关键字
        .replace(/(SELECT|FROM|WHERE|JOIN|LEFT JOIN|RIGHT JOIN|INNER JOIN|ORDER BY|GROUP BY|HAVING|INSERT|UPDATE|DELETE|CREATE|ALTER|DROP)/gi,
          '<span class="sql-keyword">$1</span>')
        // 高亮字符串
        .replace(/('.*?')/g, '<span class="sql-string">$1</span>')
        // 高亮数字
        .replace(/(\b\d+\b)/g, '<span class="sql-number">$1</span>')
        // 高亮注释
        .replace(/(--.*$)/gm, '<span class="sql-comment">$1</span>')
        // 换行
        .replace(/\n/g, '<br>')
    },

    // 代码语法高亮
    highlightCode(content) {
      // 简单的代码语法高亮
      return content
        .replace(/(function|class|def|import|const|var|let|if|else|for|while|return)/g,
          '<span class="code-keyword">$1</span>')
        .replace(/('.*?'|".*?")/g, '<span class="code-string">$1</span>')
        .replace(/(\b\d+\b)/g, '<span class="code-number">$1</span>')
        .replace(/(\/\/.*$|#.*$)/gm, '<span class="code-comment">$1</span>')
        .replace(/\n/g, '<br>')
    },
    async handleDownload() {
      if (!this.downloadUrl) return
      this.downloading = true
      try {
        // 添加时间戳避免缓存
        const response = await this.$http.get(this.downloadUrl, {
          responseType: 'blob' // 正确设置responseType
        })
        const filename = 'dist.zip'
        await this.downloadFile(response, filename)
        this.$message.success('下载已开始')
      } catch (error) {
        console.error('下载失败:', error)
        this.$message.error(`下载失败: ${error.message || '未知错误'}`)
      } finally {
        this.downloading = false
      }
    },
    // 下载文件
    async downloadFile(response, filename) {
      try {
        // 创建 Blob 对象，使用正确的MIME类型
        const blob = new Blob([response.data], { type: 'application/zip' })
        const downloadUrl = window.URL.createObjectURL(blob)
        // 创建一个隐藏的链接元素
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = filename
        document.body.appendChild(link)
        // 触发下载并移除链接元素
        link.click()
        link.remove()
        // 释放 URL 对象
        window.URL.revokeObjectURL(downloadUrl)
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    },
    // HTML转义
    escapeHtml(text) {
      const div = document.createElement('div')
      div.textContent = text
      return div.innerHTML
    },
    initImageLoadHandler() {
      const container = this.$el.querySelector('.reponse-body')
      if (!container) return
      const imgs = container.querySelectorAll('img')

      imgs.forEach((img) => {
        if (img.complete) {
          img.style.visibility = 'visible'
        } else {
          img.style.visibility = 'hidden'
          img.addEventListener('load', () => {
            img.style.visibility = 'visible'
          })
        }
        if (!img.hasAttribute('width')) img.style.width = '100%'
        if (!img.hasAttribute('height')) img.style.height = 'auto'
      })
    }
  },
  watch: {
    message: {
      deep: true,
      handler() {
        this.$nextTick(() => {
          this.initImageLoadHandler()
        })
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initImageLoadHandler()
    })
  },
  computed: {
    statusText() {
      const texts = {
        thinking: '思考中...',
        error: '思考已停止',
        success: '思考完成'
      }
      return texts[this.message.status] || ''
    },
    aiAsstantImg() {
      return `${global.domainImg()}ewx_hdsec/ai-assitant-logo.png`
    },

    // 内容类型
    contentType() {
      return this.detectContentType(this.message.content)
    },

    // 解析后的SQL语句列表
    parsedSqlStatements() {
      if (this.contentType === 'tool_result') {
        return this.parseSqlStatements(this.message.content)
      }
      return []
    },

    // 格式化工具结果（移除SQL部分）
    formattedToolResultWithoutSql() {
      if (!this.message.content) return ''

      const content = this.message.content
      // 移除所有SQL块，只保留其他信息
      const contentWithoutSql = content.replace(/=== SQL \d+ ===[\s\S]*?(?=(?:=== SQL \d+ ===)|$)/g, '').trim()

      return this.formatToolResult(contentWithoutSql)
    },

    // 格式化后的内容
    formattedContent() {
      if (!this.message.content) return ''

      const content = this.message.content
      const type = this.contentType

      if (type === 'tool_result') {
        // 格式化工具执行结果
        return this.formatToolResult(content)
      }

      // 对于普通文本，添加适当的换行和格式化
      return content
        .replace(/\n\s*\n/g, '\n\n') // 规范化换行
        .replace(/([。！？])\s*([^\n])/g, '$1\n$2') // 句号后换行
        .trim()
    },

    // 高亮后的内容
    highlightedContent() {
      if (!this.message.content) return ''

      const content = this.message.content
      const type = this.contentType

      if (type === 'sql') {
        return this.highlightSql(content)
      } else if (type === 'code') {
        return this.highlightCode(content)
      }

      return this.escapeHtml(content)
    },
    downloadUrl() {
      if (this.message.content && typeof this.message.content === 'string') {
        // 从纯文本中提取download_url
        const match = this.message.content.match(/download_url:([^\s]+)/)
        if (match) {
          // 直接返回相对路径，让axios自动拼接baseURL
          return match[1].trim()
        }
      }
      return null
    }
  },
  components: {
    // mLoading,
    mSingleCollapse
  }
}
</script>

<style lang="scss" scoped>
.assistant-message {
  display: flex;
  margin-bottom: 20px;
  max-width: calc(100% - 56px);
  overflow: hidden;
  padding-right: 56px;

  .avatar {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    border: 1px solid #59b1ff;
  }

  .message-item {
    margin-left: 10px;
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .sender-name {
    text-align: left;
    line-height: 38px;
    font-weight: normal;
    color: var(--h-text-aider-default-color);
  }

  .message-content {
    border-radius: 6px 20px 20px 20px;
    background: #ffffff;
    box-sizing: border-box;
    border: 1px solid var(--h-boder-default-color);
    box-shadow: 0px 2px 10px 0px rgba(59, 58, 58, 0.1);
    color: var(--h-text-main-default-color);
    padding: 20px;
    word-wrap: break-word;
    font-weight: 500;
  }

  .status {
    color: #999;
    font-size: 0.9em;
    padding-left: 10px;
    display: flex;
    // align-content: center;
    align-items: center;
    font-size: 16px;
    line-height: 24px;
    min-width: 180px;
    color: var(--h-text-basis-default-color);
  }

  .status.error {
    color: #f5222d;
  }

  .markdown-body {
    img {
      transition:
        visibility 0.3s ease,
        opacity 0.3s ease;
      opacity: 1;
      max-width: 100%;
      height: auto;
      display: block;
      margin: 10px 0;
    }

    img[style*='visibility: hidden'] {
      opacity: 0;
    }

    &.thinking-body {
      border-left: 1px solid #dddddd;
      padding-left: 10px;
      color: #8b8c8d;
      font-size: 14px;
      margin-bottom: 10px;
    }
  }

  // 内容包装器样式
  .content-wrapper {
    margin-top: 10px;
  }

  // SQL内容样式
  .sql-content {
    .content-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-bottom: none;
      border-radius: 6px 6px 0 0;
      font-size: 14px;
      font-weight: 500;
      color: #495057;

      i {
        margin-right: 6px;
        color: #6c757d;
      }

      .copy-btn {
        padding: 0;
        font-size: 12px;
        color: #6c757d;

        &:hover {
          color: #007bff;
        }
      }
    }

    .code-block {
      border: 1px solid #e9ecef;
      border-radius: 0 0 6px 6px;
      background: #f8f9fa;
      overflow-x: auto;

      pre {
        margin: 0;
        padding: 16px;
        background: transparent;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        line-height: 1.5;
        white-space: pre-wrap;
        word-wrap: break-word;

        code {
          background: transparent;
          padding: 0;
          border: none;
          font-family: inherit;
        }
      }
    }
  }

  // 代码内容样式
  .code-content {
    @extend .sql-content;
  }

  // 工具结果样式
  .tool-result-content {
    // 多SQL容器样式
    .multi-sql-container {
      margin-top: 16px;

      .sql-item {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .content-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 8px 12px;
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-bottom: none;
          border-radius: 6px 6px 0 0;
          font-size: 14px;
          font-weight: 500;
          color: #495057;

          .header-left {
            display: flex;
            align-items: center;
          }

          i {
            margin-right: 6px;
            color: #6c757d;
          }

          .copy-btn {
            padding: 0;
            font-size: 12px;
            color: #6c757d;

            &:hover {
              color: #007bff;
            }
          }
        }

        .code-block {
          border: 1px solid #e9ecef;
          border-radius: 0 0 6px 6px;
          background: #f8f9fa;
          overflow-x: auto;

          pre {
            margin: 0;
            padding: 16px;
            background: transparent;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
            word-wrap: break-word;

            code {
              background: transparent;
              padding: 0;
              border: none;
              font-family: inherit;
            }
          }
        }
      }
    }

    .content-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      background: #e8f4fd;
      border: 1px solid #bee5eb;
      border-bottom: none;
      border-radius: 6px 6px 0 0;
      font-size: 14px;
      font-weight: 500;
      color: #0c5460;

      i {
        margin-right: 6px;
        color: #17a2b8;
      }

      .copy-btn {
        padding: 0;
        font-size: 12px;
        color: #17a2b8;

        &:hover {
          color: #138496;
        }
      }
    }

    .formatted-result {
      border: 1px solid #bee5eb;
      border-radius: 0 0 6px 6px;
      background: #f1f9fc;
      overflow-x: auto;

      .structured-text {
        margin: 0;
        padding: 16px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        line-height: 1.6;
        white-space: pre-wrap;
        word-wrap: break-word;
        color: #0c5460;
      }
    }
  }

  // 格式化文本样式
  .formatted-text {
    margin-top: 10px;

    .plain-text {
      margin: 0;
      padding: 16px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      line-height: 1.6;
      white-space: pre-wrap;
      word-wrap: break-word;
      color: #495057;
    }
  }

  // 语法高亮样式
  .sql-keyword {
    color: #0066cc;
    font-weight: bold;
  }

  .sql-string {
    color: #009900;
  }

  .sql-number {
    color: #cc6600;
  }

  .sql-comment {
    color: #999999;
    font-style: italic;
  }

  .sql-info-label {
    color: #6c757d;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 4px;
  }

  .code-keyword {
    color: #0066cc;
    font-weight: bold;
  }

  .code-string {
    color: #009900;
  }

  .code-number {
    color: #cc6600;
  }

  .code-comment {
    color: #999999;
    font-style: italic;
  }
}
</style>
