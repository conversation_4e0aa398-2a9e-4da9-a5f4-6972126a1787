<template>
  <div class="single-collapse">
    <div class="single-collapse__header">
      <!-- 标题插槽，默认显示 props.title -->
      <span class="simple-collapse__title">
        <slot name="title">{{ title }}</slot>
      </span>
<!--      <h-icon name="upward" class="single-collapse__arrow" :class="{ 'is-active': isActive }" />-->
    </div>
    <transition name="collapse-transition">
      <div class="single-collapse__content" v-show="isActive" ref="content">
        <div class="single-collapse__content-box">
          <slot />
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'single-collapse',
  data() {
    return { isActive: this.defaultActive }
  },
  props: {
    // 面板标题（当不使用title插槽时显示）
    title: String,
    // 是否默认展开
    defaultActive: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    toggleCollapse() {
      this.isActive = !this.isActive
      this.$emit('change', this.isActive)
    }
  }
}
</script>

<style lang="scss" scoped>
.single-collapse {
  overflow: hidden;
}

.single-collapse__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
  transition: all 0.3s;
  font-weight: 500;
}

// .single-collapse__header:hover {
//   background-color: #f5f7fa;
// }

.single-collapse__arrow {
  transition: transform 0.3s;
  color: #909399;
}

.single-collapse__arrow.is-active {
  transform: rotate(180deg);
}

.single-collapse__content {
  overflow: hidden;
  background-color: #fff;
  border-top: 1px solid #ebeef5;
}

.single-collapse__content-box {
  padding: 15px;
}

/* 展开收起动画 */
.collapse-transition-enter-active,
.collapse-transition-leave-active {
  transition: height 0.3s ease-in-out;
  overflow: hidden;
}

.collapse-transition-enter,
.collapse-transition-leave-to {
  height: 0 !important;
}
</style>
