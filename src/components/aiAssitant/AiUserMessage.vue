<template>
  <div class="user-message">
    <div class="message-header">
      <img class="avatar" :src="userMerlogo" />
    </div>
    <div class="message-item">
      <div class="sender-name">
        {{ userName }}
      </div>
      <div class="message-content">
        <div class="content markdown-body" v-html="marked(message.content)" />
      </div>
      <template v-if="message.files">
        <div class="file-wrapper">
          <div class="file-preview" v-for="file in message.files" :key="file.id">
            <h-image placeholder-type="image-fill" :size="60" :src="file.url" class="item-image" />
            <div class="file-info">
              <div class="item-title">
                {{ file.name }}
              </div>
              <div class="extra">
                <span>{{ file.type }}</span> <span>({{ file.size }})</span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import DOMPurify from 'dompurify'
import { marked } from 'marked'
import 'github-markdown-css/github-markdown-light.css'

export default {
  name: 'ai-user-message',
  props: {
    // 消息列表（用户消息+AI消息）
    message: {
      type: Object,
      required: true
    },
    // 用户名称
    userName: {
      type: String,
      required: true
    },
    // 用户头像
    userMerlogo: {
      type: String,
      required: true
    }
  },
  methods: {
    marked(content) {
      const fixed = this.fixBrokenMarkdown(this.wrapCodeIfNeeded(content))
      return DOMPurify.sanitize(marked(fixed))
    },
    fixBrokenMarkdown(md) {
      // 自动闭合粗体 **
      const boldCount = (md.match(/\*\*/g) || []).length
      if (boldCount % 2 !== 0) {
        md += '**'
      }

      // 自动闭合代码块 ```
      const codeFenceCount = (md.match(/```/g) || []).length
      if (codeFenceCount % 2 !== 0) {
        md += '\n```'
      }

      return md
    },
    shouldAutoWrapAsCode(text) {
      // 看起来像代码？用一些关键词判断
      return /^(<template>|function|const|import|export|\s{4,})/m.test(text)
    },
    wrapCodeIfNeeded(text) {
      if (!/^```/.test(text) && this.shouldAutoWrapAsCode(text)) {
        return `\`\`\`\n${text}\n\`\`\``
      }
      return text
    },
    isImage(type) {
      return type?.startsWith('image/')
    },
    formatFileSize(sizeInBytes) {
      if (typeof sizeInBytes !== 'number' || isNaN(sizeInBytes) || sizeInBytes < 0) {
        return '0 Bytes'
      }
      if (sizeInBytes === 0) return '0 Bytes'
      const sizeInKB = sizeInBytes / 1024
      return sizeInKB >= 1000 ? `${(sizeInKB / 1024).toFixed(2)}MB` : `${sizeInKB.toFixed(1)}KB`
    }
  }
}
</script>

<style lang="scss" scoped>
.user-message {
  display: flex;
  margin-bottom: 20px;
  // max-width: calc(100% - 56px);
  overflow: hidden;
  padding-left: 56px;
  padding-right: 15px; /* 增加与滚动条的距离 */
  justify-content: flex-end;

  .avatar {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    border: 1px solid #59b1ff;
  }

  .message-item {
    order: -1;
    margin-right: 10px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    overflow: hidden;
  }

  .sender-name {
    text-align: right;
    line-height: 38px;
    font-weight: normal;
    color: var(--h-text-aider-default-color);
  }

  .message-content {
    overflow-anchor: auto;
    border-radius: 20px 6px 20px 20px;
    background: #3c7def;
    box-sizing: border-box;
    border: var(--h-border) solid var(--h-boder-default-color);
    box-shadow: 0px 2px 10px 0px rgba(59, 58, 58, 0.1);
    color: #ffffff;
    padding: 20px;
    word-wrap: break-word;
    font-weight: 500;
    font-size: 16px;
  }

  .file-wrapper {
    margin-top: 12px;
    width: fit-content;
  }

  .file-preview {
    display: flex;
    background: #fff;
    align-items: center;
    border-radius: 12px;
    padding: 12px;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .item-image {
      width: 60px;
      height: 60px;
      margin-right: 12px;
      flex-shrink: 0;
    }

    .item-title {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      font-weight: bold;
      font-size: 14px;
      color: var(--h-text-main-default-color);
      margin-bottom: 4px;
    }

    .extra {
      color: var(--h-text-aider-default-color);
      font-size: 12px;
    }
  }

  .file-info {
    padding: 8px;
    font-size: 12px;
    color: #666;
    flex: 1;
    overflow: hidden;
    max-width: 200px;
  }

  .markdown-body {
    color: #fff;
    background: transparent;
  }
}
</style>
