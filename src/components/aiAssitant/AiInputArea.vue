<template>
  <div :class="['footer', userImages.length ? '' : 'border']">
    <!-- 图片文件区域
    <div class="image-content" v-if="userImages.length">
      <div class="image-item" v-for="imageItem in userImages" :key="imageItem.id">
        <img class="item-image" :src="imageItem.url" v-if="imageItem.status === 'done'" />
        <h-image placeholder-type="image-fill" :size="24" src="" class="item-image"
          v-else-if="imageItem.status === 'error'" />
        <h-icon name="iconloading" class="item-image" animation-name="rotating" v-else />
        <div class="item-info">
          <div class="item-title">
            {{ imageItem.name }}
          </div>
          <div class="extra" v-if="imageItem.status === 'init' || imageItem.status === 'pending'">
            <span>上传中...</span>
          </div>
          <div class="extra" v-else-if="imageItem.status === 'done'">
            <span>{{ imageItem.type }}</span>
            <span>{{ imageItem.size }}</span>
          </div>
          <div class="extra" v-else-if="imageItem.status === 'error'">
            <span class="error">服务器繁忙</span>
          </div>
        </div>
        <h-icon :class="['close-icon', imageItem.status]" name="cancel" style-type="fill"
          @click="$emit('remove-image', imageItem.id)" />
      </div>
    </div> -->

    <!-- 输入框发送区域 -->
    <div :class="['footer-content', userImages.length ? 'border' : '']">
      <div class="input">
        <!-- :rows="messages.length ? '1' : '2'" -->
        <textarea id="chat-input" :value="userInput"
          :class="['input-textarea', 'inner-text', messages.length ? 'inner-text' : null]" placeholder="输入您的问题..."
          rows="1" ref="textarea" @input="handleInput" @paste="$emit('paste', $event)"
          @keydown.enter.exact.prevent="$emit('send')"></textarea>
      </div>
      <div :class="['action', 'inner-action', messages.length ? 'inner-action' : null]">
        <!-- <h-tooltip
          content="上传图片： jpg、png图片不超过10M"
          placement="top"
        >
          <h-button
            shape="circle"
            size="large"
            :disabled="limitUpload || isSending || isThinking"
            @click="triggerFileInput"
          >
            <h-icon
              name="image"
              style-type="fill"
            />
          </h-button>
        </h-tooltip> -->
        <!-- <el-input
          :content="sendMessageText"
          placement="top"
        >
          <h-button
            shape="circle"
            size="large"
            :type="isThinking || userInput.trim() ? 'primary' : 'default'"
            :disabled="userImagesInvaidStatus || isSending || (!userInput.trim() && !isThinking)"
            @click="$emit(isThinking ? 'stop' : 'send')"
          >
            <div
              class="action-end"
              v-if="isThinking"
            />
            <h-icon
              :name="isSending ? 'iconloading' : 'rise'"
              :animation-name="isSending ? 'rotating' : ''"
              v-else
            />
          </h-button>
        </el-input> -->
        <el-button size="small" :type="isThinking || userInput.trim() ? 'primary' : 'info'"
          :disabled="userImagesInvaidStatus || isSending || (!userInput.trim() && !isThinking)"
          @click="$emit(isThinking ? 'stop' : 'send')">
          <i class="el-icon-video-pause" v-if="isThinking" />
          <i :class="isSending ? 'el-icon-loading' : 'el-icon-top'" v-else></i>
        </el-button>
        <input type="file" accept=".jpg,.jpeg,.png" style="display: none" ref="fileInput"
          @change="$emit('file-change', $event)" />
      </div>
    </div>
  </div>
</template>

<script>
import _ from 'lodash'
export default {
  name: 'ai-input-area',
  props: {
    // 快捷问题
    shortQuestion: {
      type: Array,
      default: () => []
    },
    // 消息列表（用户消息+AI消息）
    messages: {
      type: Array,
      default: () => []
    },
    // 用户上传的图片信息
    userImages: {
      type: Array,
      default: () => []
    },
    // 用户输入框要发送的内容
    userInput: {
      type: String,
      default: ''
    },
    // 正在思考中
    isThinking: {
      type: Boolean,
      default: false
    },
    // 正在发送中
    isSending: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleQuickQuestion: _.debounce(function (item) {
      this.$emit('quick-question', item)
    }, 500),
    triggerFileInput() {
      this.$refs.fileInput.click()
    },
    handleInput(e) {
      this.$emit('update-input', e.target.value)
      this.adjustHeight()
    },
    adjustHeight() {
      const textarea = this.$refs.textarea
      if (!textarea) return
      textarea.style.height = 'auto'
      // const minHeight = this.messages.length ? 28 : 56 // 2 lines
      const minHeight = 28 // 2 lines
      const maxHeight = 112 // 4 lines
      textarea.style.height = `${Math.min(Math.max(textarea.scrollHeight, minHeight), maxHeight)}px`
    }
  },
  watch: {
    message: {
      deep: true,
      handler() {
        this.adjustHeight()
      },
      immediate: true
    }
  },
  computed: {
    sendMessageText() {
      if (this.userImagesInvaidStatus) {
        return '请删除异常文件再发送'
      }
      if (this.isSending) {
        return '问题发送中'
      }
      if (this.isThinking) {
        return '思考中，点击终止回答'
      }
      if (!this.userInput.trim()) {
        return '请输入你的问题'
      }
      return '发送问题'
    },
    userImagesInvaidStatus() {
      return !!this.userImages.filter((item) => item.status !== 'done').length
    },
    limitUpload() {
      return this.userImages.length >= 5
    }
  }
}
</script>

<style lang="scss" scoped>
/* 修改默认按钮的背景色 */
.el-button--default {
  background-color: #DDDFE6; /* 你想要的背景色 */
}
.el-icon-video-pause,
.el-icon-loading,
.el-icon-top {
  font-size: 20px !important; /* 强制覆盖 */
}
.footer {
  flex-shrink: 0;
  cursor: text;
  border-radius: 30px;
  background: #ffffff;
  box-sizing: border-box;
  box-shadow: 0px 4px 14px 0px rgba(59, 58, 58, 0.1);
  position: relative;

  // margin-top: 42px;
  &.border {
    border: 1px solid var(--h-boder-default-color);
  }

  .short-question {
    width: 100%;
    position: absolute;
    top: -12px;
    font-size: 14px;
    display: flex;
    transform: translateY(-100%);
    align-items: center;

    &.short-center {
      justify-content: center;
    }

    .question-tips {
      flex-shrink: 0;
    }

    .short-question-list {
      display: flex;
      font-size: 12px;
      margin-left: 10px;
      flex: 1;
      overflow: hidden;
    }

    .short-question-item {
      padding: 0 12px;
      border-radius: 100px;
      background: #ffffff;
      line-height: 30px;
      border: 1px solid var(--h-boder-default-color);
      margin-right: 10px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
      max-width: 175px;

      &:hover {
        color: #0055ff;

        .assitant-icon {
          color: #0055ff;
        }
      }

      .assitant-icon {
        margin-right: 2px;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .image-content {
    padding: 16px 30px;
    margin-top: 2px;
    font-size: 14px;
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;

    .image-item {
      display: flex;
      align-items: center;
      border-radius: 6px;
      padding: 10px;
      box-sizing: border-box;
      border: 1px solid var(--h-boder-default-color);
      margin-right: 10px;
      position: relative;
      width: calc(25% - 10px);
      // margin-bottom: 10px;

      .item-image {
        width: 24px;
        height: 24px;
        margin-right: 12px;
      }

      .item-info {
        overflow: hidden;
      }

      &:last-child {
        margin-right: 0;
      }

      .item-title {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        font-weight: bold;
        font-size: 14px;
        color: var(--h-text-main-default-color);
        margin-bottom: 4px;
      }

      .extra {
        color: var(--h-text-aider-default-color);
        font-size: 12px;

        .error {
          color: #f5222d;
        }
      }

      .close-icon {
        position: absolute;
        right: 0;
        top: 0;
        transform: translate(50%, -50%);
        cursor: pointer;
        padding: 10px;

        &.error {
          color: #f5222d;
        }
      }
    }
  }

  .footer-content {
    border-radius: 30px;
    background: #ffffff;
    box-sizing: border-box;
    box-shadow: 0px 4px 14px 0px rgba(59, 58, 58, 0.1);
    z-index: 1;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 30px;
    display: flex;
    overflow: hidden;
    position: relative;

    &.border {
      border: 1px solid var(--h-boder-default-color);
    }

    .input {
      position: relative;
      height: auto;
      min-height: 28px;
      max-height: 112px;
      width: 100%;
      font-size: 18px;
      line-height: 28px;
    }

    .input-textarea {
      resize: none;
      background-color: transparent;
      font-size: inherit;
      line-height: inherit;
      word-break: break-word;
      white-space: pre-wrap;
      border: none;
      width: 100%;
      margin: 0;
      padding: 0;
      display: block;
      transition: height 0.2s ease;
      overflow: auto;
      outline: none;
      box-sizing: border-box;

      &.inner-text {
        padding-right: 108px;
      }
    }

    .input-textarea::-webkit-scrollbar {
      width: 6px;
    }

    .input-textarea::-webkit-scrollbar-track {
      background: transparent;
    }

    .input-textarea::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 10px;
    }

    .input-textarea::-webkit-scrollbar-thumb:hover {
      background-color: rgba(0, 0, 0, 0.3);
    }

    .action {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      margin-top: 10px;

      &.inner-action {
        position: absolute;
        right: 30px;
        bottom: 19px;
        width: auto;
        margin-top: 0;
      }

      .action-end {
        width: 16px;
        height: 16px;
        border-radius: 2px;
        background: #ffffff;
      }

      ::v-deep {
        .h-icon {
          font-size: 24px !important;
        }

        .h-button--circle {
          padding: 6px;
          width: 38px;
          height: 38px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .h-button--default {
          color: var(--h-boder-default-color) !important;
          border-color: var(--h-boder-default-color) !important;
        }

        .h-button--default .h-icon {
          color: var(--h-boder-default-color) !important;
        }

        .h-button--default[disabled],
        .h-button--default[disabled]:active,
        .h-button--default[disabled]:focus,
        .h-button--default[disabled]:hover {
          background-color: #dcdfe6;
          border-color: #dcdfe6;
          color: #fff;

          .h-icon {
            color: #fff !important;
          }
        }
      }
    }
  }
}
</style>
