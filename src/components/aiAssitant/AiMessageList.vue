<template>
  <div class="chat-list" ref="chatList">
    <div :class="['message', message.sender]" v-for="(message, index) in messages" :key="index">
      <m-ai-user-message :message="message" :user-name="userName" :user-merlogo="userMerlogo"
        v-if="message.sender === 'user'" />
      <m-ai-assistant-message :message="message" v-else />
    </div>
  </div>
</template>

<script>
import mAiAssistantMessage from './AiAssistantMessage.vue'
import mAiUserMessage from './AiUserMessage.vue'

export default {
  name: 'ai-message-list',
  // 定义本dom作为容器被引用时，组件必须接收的属性
  props: {
    // 消息列表（用户消息+AI消息）
    messages: {
      type: Array,
      required: true
    },
    // 用户名称
    userName: {
      type: String,
      required: true
    },
    // 用户头像
    userMerlogo: {
      type: String,
      required: true
    }
  },
  components: {
    mAiUserMessage,
    mAiAssistantMessage
  }
}
</script>

<style lang="scss" scoped>
.chat-list {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow-y: auto;
  box-sizing: border-box;
  padding-bottom: 42px;

  .message {
    margin-bottom: 15px;
    animation: fadeIn 0.3s ease;

    .user {
      display: flex;
      justify-content: end;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
</style>
