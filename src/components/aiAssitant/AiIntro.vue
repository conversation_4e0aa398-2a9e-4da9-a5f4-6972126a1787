<template>
  <div :class="['header', shortQuestion.length ? 'has-quick-btn' : '']">
    <img src="./../img/ai-assitant-intro.png" alt="" class="intro-img" />
  </div>
</template>

<script>
export default {
  name: 'ai-intro',
  props: {
    // 快捷问题
    shortQuestion: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  flex: 1;
  display: flex;
  justify-content: center;
  overflow: hidden;

  .intro-img {
    max-height: 293px;
    width: auto;
    height: 70%;
    transform: translateY(-20%);
    align-self: center;
  }
}
</style>
