<template>
  <div
    class="ai-helper-wrapper"
    :style="wrapperStyle"
    @mousedown.stop="startDrag"
  >
    <div style="position: relative">
      <div
        class="bubble"
        v-if="showBubble"
      >
        <!-- <h-icon
          name="cancel"
          class="close-btn"
          style-type="fill"
          ref="closeBtn"
          @click.stop="handleCloseBubble"
        /> -->
      </div>
      <div class="btn-wrap"  v-if="roleId === '1' || roleId === '0'">
        <img
          :src="require('./img/ai_mini_logo.png')"
          alt="icon"
          class="ai-icon"
        />
        <!-- <div class="main-button"> -->
          <!-- <img
            :src="require('./img/ai-arrow.png')"
            alt="icon"
            class="ai-arrow"
          /> -->
          <!-- <div class="btn-text">
            <div class="title">AI测试助手</div>
          </div> -->
        <!-- </div> -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'm-quick-assitant',
  data() {
    return {
      showBubble: true,
      dragging: false,
      position: { x: 20, y: window.innerHeight - this.bottom },
      offset: { x: 0, y: 0 },
      dragStartPos: { x: 0, y: 0 },
      scale: 1 // 拖动时放大比例
    }
  },
  props: {
    /**
      * 底部距离
      * @type {Number}
      */
    bottom: {
      type: Number,
      default: 60
    }
  },
  methods: {
    startDrag(e) {
      e.preventDefault()
      e.stopPropagation()
      // 记录拖动起始位置
      this.dragStartPos = { x: e.clientX, y: e.clientY }
      this.dragging = false // 初始状态设为未拖动
      this.offset = {
        x: window.innerWidth - e.clientX - this.position.x,
        y: e.clientY - this.position.y
      }
      // 添加事件监听（使用 passive: false 确保能阻止默认行为）
      document.addEventListener('mousemove', this.handleDrag, { passive: false })
      document.addEventListener('mouseup', this.handleDragEnd, { passive: false, once: true })
    },
    handleDrag(e) {
      const dx = Math.abs(e.clientX - this.dragStartPos.x)
      const dy = Math.abs(e.clientY - this.dragStartPos.y)
      // 移动超过阈值才视为拖动
      if (dx > 5 || dy > 5) {
        this.scale = 1.1 // 放大
        this.dragging = true
        // 👉 计算新的 right 和 top（基于偏移）
        let newRight = window.innerWidth - e.clientX - this.offset.x
        let newTop = e.clientY - this.offset.y

        // 👉 限制边界（按钮宽度约 65px）
        const btnWidth = 145
        const btnHeight = 60
        newRight = Math.max(0, Math.min(newRight, window.innerWidth - btnWidth))
        newTop = Math.max(0, Math.min(newTop, window.innerHeight - btnHeight)) // 保底部不出界
        this.position = {
          x: newRight,
          y: newTop
        }
      }
    },
    handleDragEnd(e) {
      document.removeEventListener('mousemove', this.handleDrag)
      document.removeEventListener('mouseup', this.handleDragEnd)
      this.scale = 1
      if (this.isInsideCloseButton(e.target)) {
        return
      }
      if (!this.dragging) {
        this.$emit('open', e)
      }
      setTimeout(() => {
        this.dragging = false
      }, 200)
    },
    isInsideCloseButton(target) {
      const closeBtn = this.$refs.closeBtn
      return closeBtn && closeBtn.$el && closeBtn.$el.contains(target)
    },
    handleCloseBubble(e) {
      e.preventDefault()
      e.stopPropagation()
      this.showBubble = false // 独立处理关闭事件，不冒泡
    }
  },
  beforeDestroy() {
    document.removeEventListener('mousemove', this.handleDrag)
    document.removeEventListener('mouseup', this.handleDragEnd)
  },
  computed: {
    wrapperStyle() {
      return {
        position: 'fixed',
        right: `${this.position.x}px`,
        top: `${this.position.y}px`,
        transform: `scale(${this.scale})`,
        transition: 'transform 0.2s ease'
      }
    },
    roleId() {
      return window.localStorage.getItem('role_id')
    }
  }
}
</script>

<style lang="scss" scoped>
  .ai-helper-wrapper {
    width: 65px;
    cursor: pointer;
    z-index: 2002;
    user-select: none;
    will-change: transform;
    transform-origin: center center;
  }
  .btn-wrap {
    position: relative;
    width: 65px;
    .ai-icon {
      position: absolute;
      right: 0;
      top: 6px;
      transform: translateY(-20%);
      width: 65px;
      height: auto;
      z-index: 1;
    }
  }
  // .main-button {
  //   display: flex;
  //   align-items: center;
  //   background: linear-gradient(90deg, #0ebefd 3%, #017bff 95%);
  //   // background: linear-gradient(270deg, #00c6ff, #0072ff, #00c6ff);
  //   // background-size: 400% 400%;
  //   // animation: gradientFlow 6s ease infinite;
  //   // transition: box-shadow 0.3s ease;
  //   color: white;
  //   position: relative;
  //   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  //   transition: box-shadow 0.3s ease;
  //   border-radius: 100px;
  //   padding: 6px 8px;
  //   width: 145px;
  //   overflow: hidden;
  //   &::before {
  //     content: '';
  //     position: absolute;
  //     top: 0;
  //     left: -75%;
  //     width: 50%;
  //     height: 100%;
  //     background: linear-gradient(120deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0) 100%);
  //     transform: skewX(-20deg);
  //     animation: shineSweep 2.5s infinite;
  //   }

    // .ai-arrow {
    //   height: 38px;
    //   width: 38px;
    //   margin-right: 6px;
    // }
  // }
  @keyframes shineSweep {
    0% {
      left: -75%;
    }
    100% {
      left: 125%;
    }
  }
  @keyframes gradientFlow {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
  // .main-button:hover {
  //   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  // }
  // .btn-text {
  //   .title {
  //     font-size: 16px;
  //     line-height: 24px;
  //     font-weight: bold;
  //   }
  //   .desc {
  //     font-size: 12px;
  //     letter-spacing: 0.11em;
  //   }
  // }

  .bubble {
    position: absolute;
    right: 0;
    top: 0;
    transform: translate(0, -100%);
  }
  .qipao {
    height: 140px;
    width: auto;
    max-width: fit-content;
  }
  .close-btn {
    position: absolute;
    top: -10px;
    right: -5px;
    font-size: 16px;
    padding: 10px;
    cursor: pointer;
    color: #999;
  }
</style>
