<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>接口管理</el-breadcrumb-item>
      <el-breadcrumb-item>业务线管理</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="5">
          <el-input placeholder="请输入业务ID"
                    v-model="queryInfo.business_id"
                    clearable>
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入业务名称"
                    v-model="queryInfo.business_name"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryBusiness">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">
          <el-button class="add" type="primary" @click="showDialog(true, {}, 'add')">添加业务线</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="businessList" border>
          <el-table-column label="业务ID" prop="business_id" min-width="50px"></el-table-column>
          <el-table-column label="业务名称" prop="business_name" min-width="120px" show-overflow-tooltip>
            <template v-slot="slotProps">
              <el-link type="primary" v-if="slotProps.row.creater==user_name" @click="showDialog(false, slotProps.row, 'update')">{{ slotProps.row.business_name }}</el-link>
              <el-link type="primary" v-else @click="showDialog(false,slotProps.row, 'check')">{{ slotProps.row.business_name }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="测试人员" prop="tester" show-overflow-tooltip></el-table-column>
          <el-table-column label="备注" prop="remark" show-overflow-tooltip></el-table-column>
          <el-table-column label="创建人" prop="creater"></el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="120px"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="120px">
            <template v-slot="slotProps">
            <el-tooltip class="item" effect="dark" content="查看" placement="top">
              <el-button type="warning" icon="el-icon-view" size="mini" @click="showDialog(false,slotProps.row, 'check')"></el-button>
            </el-tooltip>
            <el-button type="primary" v-if="slotProps.row.creater==user_name || role_id==0" icon="el-icon-edit" size="mini" @click="showDialog(false,slotProps.row, 'update')"></el-button>
            <el-button type="danger" v-if="slotProps.row.creater==user_name || role_id==0" icon="el-icon-delete" size="mini" @click="removeBusinessById(slotProps.row.business_id)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 新增编辑对话框 -->
    <el-dialog :visible.sync="dialogVisible"
                :title="dialogTitle"
                width="50%"
                @close="DialogClosed"
                :close-on-click-modal="false">
      <el-form :model="businessForm"
                label-width="100px"
                :rules="businessFormRules"
                ref="businessFormRef">
        <el-row>
          <el-col :span="24">
            <el-form-item label="业务名称" prop="business_name">
              <el-input v-model="businessForm.business_name" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="测试人员" prop="tester">
              <el-input v-model="businessForm.tester" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="businessForm.remark"></el-input>
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="type !== 'check'">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitBusiness">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'business_info',
      user_name: window.localStorage.getItem('user_name'),
      role_id: window.localStorage.getItem('role_id'),
      queryInfo: {
        business_id: null,
        business_name: '',
        pagenum: 1,
        pagesize: 5,
        creater: window.localStorage.getItem('user_name')
      },
      businessList: [],
      isAdd: true,
      businessForm: {},
      dialogTitle: '',
      total: 0,
      dialogVisible: false,
      businessFormRules: {
        business_name: [
          { required: true, message: '请输入业务名称', trigger: 'blur' }
        ]
      },
      type: ''
    }
  },
  created() {
    this.getBusinessList()
  },
  methods: {
    queryBusiness() {
      this.queryInfo.pagenum = 1
      this.getBusinessList()
    },
    async getBusinessList() {
      const { data: res } = await this.$http.get(this.model + '/business_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取业务列表失败')
      this.businessList = res.data.data
      this.total = res.data.total
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getBusinessList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getBusinessList()
    },
    DialogClosed() {
      this.$refs.businessFormRef.resetFields()
    },
    showDialog(isAdd, business, type) {
      this.type = type
      if (this.type === 'add') {
        this.dialogTitle = '新增业务线'
      } else if (this.type === 'update') {
        this.dialogTitle = '编辑业务线'
      } else if (this.type === 'check') {
        this.dialogTitle = '查看业务线'
      }
      this.isAdd = isAdd
      if (!this.isAdd) {
        const objString = JSON.stringify(business)
        this.businessForm = JSON.parse(objString)
      }
      this.dialogVisible = true
    },
    submitBusiness() {
      this.$refs.businessFormRef.validate(async valid => {
        if (!valid) return false
        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/business_add/', this.businessForm)
          if (res.meta.status !== 200) return this.$message.error('新增业务线失败')
          this.$message.success('新增业务线成功')
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.businessForm.business_id + '/business_update/', this.businessForm)
          if (res.meta.status !== 200) return this.$message.error('编辑业务线失败')
          this.$message.success('编辑业务线成功')
        }
        this.getBusinessList()
        this.dialogVisible = false
      })
    },
    removeBusinessById(id) {
      this.$confirm('此操作将永久删除该业务线, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete(this.model + '/' + id + '/business_delete/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error(result.data.meta.msg)
          this.$message.success('删除业务线成功')
          this.getBusinessList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>
