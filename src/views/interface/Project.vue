<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>接口管理</el-breadcrumb-item>
      <el-breadcrumb-item>域名管理</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="5">
          <el-input placeholder="请输入域名ID"
                    v-model="queryInfo.search_project_id"
                    clearable>
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入域名名称"
                    v-model="queryInfo.search_project_name"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryProject">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">
          <el-button class="add" type="primary" @click="showDialog(true, {}, 'add')">添加域名</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="projectList" border>
          <el-table-column label="域名ID" prop="project_id" min-width="50px"></el-table-column>
          <el-table-column label="域名名称" prop="project_name" min-width="120px" show-overflow-tooltip>
            <template v-slot="slotProps">
              <el-link type="primary" v-if="slotProps.row.creater==user_name" @click="showDialog(false, slotProps.row.project_id, 'update')">{{ slotProps.row.project_name }}</el-link>
              <el-link type="primary" v-else @click="showDialog(false, slotProps.row.project_id, 'check')">{{ slotProps.row.project_name }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="域名/主机" prop="ip" show-overflow-tooltip></el-table-column>
          <el-table-column label="端口" prop="port" show-overflow-tooltip></el-table-column>
          <el-table-column label="备注" prop="remark" show-overflow-tooltip></el-table-column>
          <el-table-column label="创建人" prop="creater"></el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="120px"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="120px">
            <template v-slot="slotProps">
              <el-button type="primary"  icon="el-icon-edit" size="mini" @click="showDialog(false,slotProps.row.project_id,'update')"></el-button>
              <el-button type="danger" v-if="slotProps.row.creater==user_name" icon="el-icon-delete" size="mini" @click="removeProjectById(slotProps.row.project_id)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 新增编辑对话框 -->
    <el-dialog :visible.sync="dialogVisible"
                :title="dialogTitle"
                width="75%"
                @close="DialogClosed"
                :close-on-click-modal="false">
      <el-form :model="projectForm"
                label-width="100px"
                :rules="projectFormRules"
                ref="projectFormRef">
        <el-row>
          <el-col :span="24">
            <el-form-item label="域名名称" prop="project_name">
              <el-input v-model="projectForm.project_name" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="域名描述">
            <el-input v-model="projectForm.remark"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-col :span="12">
                <el-form-item label="环境模式">
                  <el-switch
                    v-model="projectForm.project_mode"
                    active-color="#13ce66">
                  </el-switch>
                </el-form-item>
              </el-col>
        </el-row>
        <!-- 非项目模式 -->
        <template v-if="!projectForm.project_mode">
        <el-row>
          <el-col :span="24">
            <el-form-item label="域名/主机" prop="ip">
              <el-input v-model="projectForm.ip" placeholder="示例：merchants.test.ydjia.cn"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="端口" prop="port">
              <el-input v-model="projectForm.port" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        </template>
        <!-- 项目模式 -->
        <template v-if="projectForm.project_mode">
           <el-tabs  v-model="activeName" type="border-card">
            <el-tab-pane label="测试环境" name="test">
            <el-row>
              <el-col :span="24">
                <el-form-item label="主机地址">
                  <el-input v-model="projectForm.test_host" placeholder="示例：merchants.test.ydjia.cn"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="请求头">
                  <el-input v-model="projectForm.test_header"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <!-- 前置参数 -->
            <el-row>
                <el-col :span="5">
                  <el-form-item label="前置参数">
                    <el-button type="primary" @click="addPreparam" size="small">添加参数</el-button>
                  </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="10">
              <template v-for="(item, index) in test_before_param">
                <el-col :span="12" :key="index">
                  <el-form-item :label="'参数' + (index + 1)">
                    <el-col :span="8">
                    <el-input type="text" v-model="item.paramName" />
                    </el-col>
                    <el-col :span="2">
                      <span>=</span>
                    </el-col>
                    <el-col :span="8">
                    <el-input type="text" v-model="item.paramValue" />
                    </el-col>
                    <el-col :span="2">
                      <i class="el-icon-delete" @click="deletePreparm(index)"></i>
                    </el-col>
                  </el-form-item>
                </el-col>
              </template>
            </el-row>
            <!-- 前置案例 -->
            <el-row>
                <el-col :span="12">
                  <el-form-item label="前置案例">
                    <el-button type="primary" @click="addCaseDrawerShow(9999)" size="small">添加案例</el-button>
                  </el-form-item>
                </el-col>
            </el-row>
            <div class="relationCaseTabel">
              <el-table :data="projectForm.test_case"
                        id="crTable"
                        row-key="counterKey">
                <el-table-column type="index" />
                <el-table-column prop="relation_case_id" min-width="100px" label="案例ID" />
                <el-table-column prop="relation_case_name" min-width="120px" label="案例名称" />
                <el-table-column prop="relation_case_type" min-width="80px" label="案例类型" />
                <el-table-column prop="is_get_param" min-width="80px" label="是否提取参数">
                  <template v-slot="scopeProps">
                    <span v-if="scopeProps.row.is_get_param">是</span>
                    <span v-else>否</span>
                  </template>
                </el-table-column>
                <el-table-column prop="response_param" min-width="100px" label="返回参数" />
                <el-table-column prop="response_param_alias" min-width="100px" label="返回参数别名" />
                <el-table-column prop="reset_param" min-width="80px" label="重设请求数据">
                  <template v-slot="scopeProps">
                    <span v-if="scopeProps.row.reset_param">是</span>
                    <span v-else>否</span>
                  </template>
                </el-table-column>
                <el-table-column prop="instructions" min-width="120px" label="案例说明" show-overflow-tooltip />
                <el-table-column min-width="150px" label="操作">
                  <template v-slot="slotProps">
                    <el-button type="text" @click="editCaseDialogShow(slotProps.row, slotProps.$index)">编辑</el-button>
                    <el-button type="text" @click="addCaseDrawerShow(slotProps.$index)">添加</el-button>
                    <el-button type="text" @click="deleteCase(slotProps.$index)">删除</el-button>
                    <el-button type="text" @click="slotProps.row.is_enable = !slotProps.row.is_enable">
                      <span v-if="slotProps.row.is_enable">禁用</span>
                      <span v-else style="color:red">启用</span>
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            </el-tab-pane>
            <el-tab-pane label="预发环境" name="uat">
            <el-row>
            <el-col :span="24">
              <el-form-item label="主机地址" prop="ip">
                <el-input v-model="projectForm.uat_host" placeholder="示例：merchants.test.ydjia.cn"></el-input>
              </el-form-item>
            </el-col>
            </el-row>
            <el-row>
            <el-col :span="24">
              <el-form-item label="请求头" prop="ip">
                <el-input v-model="projectForm.uat_header" placeholder="示例：merchants.test.ydjia.cn"></el-input>
              </el-form-item>
            </el-col>
            </el-row>
            <!-- 前置参数 -->
            <el-row>
                <el-col :span="5">
                  <el-form-item label="前置参数">
                    <el-button type="primary" @click="addPreparam" size="small">添加参数</el-button>
                  </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="10">
              <template v-for="(item, index) in uat_before_param">
                <el-col :span="12" :key="index">
                  <el-form-item :label="'参数' + (index + 1)">
                    <el-col :span="8">
                    <el-input type="text" v-model="item.paramName" />
                    </el-col>
                    <el-col :span="2">
                      <span>=</span>
                    </el-col>
                    <el-col :span="8">
                    <el-input type="text" v-model="item.paramValue" />
                    </el-col>
                    <el-col :span="2">
                      <i class="el-icon-delete" @click="deletePreparm(index)"></i>
                    </el-col>
                  </el-form-item>
                </el-col>
              </template>
            </el-row>
            <!-- 前置案例 -->
            <el-row>
                <el-col :span="12">
                  <el-form-item label="前置案例">
                    <el-button type="primary" @click="addCaseDrawerShow(9999)" size="small">添加案例</el-button>
                  </el-form-item>
                </el-col>
            </el-row>
            <div class="relationCaseTabel">
              <el-table :data="projectForm.uat_case"
                        id="crTable"
                        row-key="counterKey">
                <el-table-column type="index" />
                <el-table-column prop="relation_case_id" min-width="100px" label="案例ID" />
                <el-table-column prop="relation_case_name" min-width="120px" label="案例名称" />
                <el-table-column prop="relation_case_type" min-width="80px" label="案例类型" />
                <el-table-column prop="is_get_param" min-width="80px" label="是否提取参数">
                  <template v-slot="scopeProps">
                    <span v-if="scopeProps.row.is_get_param">是</span>
                    <span v-else>否</span>
                  </template>
                </el-table-column>
                <el-table-column prop="response_param" min-width="100px" label="返回参数" />
                <el-table-column prop="response_param_alias" min-width="100px" label="返回参数别名" />
                <el-table-column prop="reset_param" min-width="80px" label="重设请求数据">
                  <template v-slot="scopeProps">
                    <span v-if="scopeProps.row.reset_param">是</span>
                    <span v-else>否</span>
                  </template>
                </el-table-column>
                <el-table-column prop="instructions" min-width="120px" label="案例说明" show-overflow-tooltip />
                <el-table-column min-width="150px" label="操作">
                  <template v-slot="slotProps">
                    <el-button type="text" @click="editCaseDialogShow(slotProps.row, slotProps.$index)">编辑</el-button>
                    <el-button type="text" @click="addCaseDrawerShow(slotProps.$index)">添加</el-button>
                    <el-button type="text" @click="deleteCase(slotProps.$index)">删除</el-button>
                    <el-button type="text" @click="slotProps.row.is_enable = !slotProps.row.is_enable">
                      <span v-if="slotProps.row.is_enable">禁用</span>
                      <span v-else style="color:red">启用</span>
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            </el-tab-pane>
            <el-tab-pane label="生产环境" name="pro">
            <el-row>
            <el-col :span="24">
              <el-form-item label="主机地址" prop="ip">
                <el-input v-model="projectForm.pro_host" placeholder="示例：merchants.test.ydjia.cn"></el-input>
              </el-form-item>
            </el-col>
            </el-row>
            <el-row>
            <el-col :span="24">
              <el-form-item label="请求头" prop="ip">
                <el-input v-model="projectForm.pro_header" placeholder="示例：merchants.test.ydjia.cn"></el-input>
              </el-form-item>
            </el-col>
            </el-row>
            <!-- 前置参数 -->
            <el-row>
                <el-col :span="5">
                  <el-form-item label="前置参数">
                    <el-button type="primary" @click="addPreparam" size="small">添加参数</el-button>
                  </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="10">
              <template v-for="(item, index) in pro_before_param">
                <el-col :span="12" :key="index">
                  <el-form-item :label="'参数' + (index + 1)">
                    <el-col :span="8">
                    <el-input type="text" v-model="item.paramName" />
                    </el-col>
                    <el-col :span="2">
                      <span>=</span>
                    </el-col>
                    <el-col :span="8">
                    <el-input type="text" v-model="item.paramValue" />
                    </el-col>
                    <el-col :span="2">
                      <i class="el-icon-delete" @click="deletePreparm(index)"></i>
                    </el-col>
                  </el-form-item>
                </el-col>
              </template>
            </el-row>
            <!-- 前置案例 -->
            <el-row>
                <el-col :span="12">
                  <el-form-item label="前置案例">
                    <el-button type="primary" @click="addCaseDrawerShow(9999)" size="small">添加案例</el-button>
                  </el-form-item>
                </el-col>
            </el-row>
            <div class="relationCaseTabel">
              <el-table :data="projectForm.pro_case"
                        id="crTable"
                        row-key="counterKey">
                <el-table-column type="index" />
                <el-table-column prop="relation_case_id" min-width="100px" label="案例ID" />
                <el-table-column prop="relation_case_name" min-width="120px" label="案例名称" />
                <el-table-column prop="relation_case_type" min-width="80px" label="案例类型" />
                <el-table-column prop="is_get_param" min-width="80px" label="是否提取参数">
                  <template v-slot="scopeProps">
                    <span v-if="scopeProps.row.is_get_param">是</span>
                    <span v-else>否</span>
                  </template>
                </el-table-column>
                <el-table-column prop="response_param" min-width="100px" label="返回参数" />
                <el-table-column prop="response_param_alias" min-width="100px" label="返回参数别名" />
                <el-table-column prop="reset_param" min-width="80px" label="重设请求数据">
                  <template v-slot="scopeProps">
                    <span v-if="scopeProps.row.reset_param">是</span>
                    <span v-else>否</span>
                  </template>
                </el-table-column>
                <el-table-column prop="instructions" min-width="120px" label="案例说明" show-overflow-tooltip />
                <el-table-column min-width="150px" label="操作">
                  <template v-slot="slotProps">
                    <el-button type="text" @click="editCaseDialogShow(slotProps.row, slotProps.$index)">编辑</el-button>
                    <el-button type="text" @click="addCaseDrawerShow(slotProps.$index)">添加</el-button>
                    <el-button type="text" @click="deleteCase(slotProps.$index)">删除</el-button>
                    <el-button type="text" @click="slotProps.row.is_enable = !slotProps.row.is_enable">
                      <span v-if="slotProps.row.is_enable">禁用</span>
                      <span v-else style="color:red">启用</span>
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            </el-tab-pane>
           </el-tabs>
        </template>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="type !== 'check'">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitProject">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 添加案例抽屉 -->
    <el-drawer :visible.sync="addCasedialogVisible"
               title="添加案例"
               direction="rtl"
               size="70%"
               class="add-case-drawer"
               @close="addCasedialogClosed">
      <el-scrollbar class="add-case-drawer-content">
        <el-row :gutter="20">
          <el-col :span="4">
            <el-input placeholder="请输入案例ID"
                      v-model="caseQueryInfo.search_case_id"
                      clearable>
            </el-input>
          </el-col>
          <el-col :span="5">
            <el-input placeholder="请输入案例名称"
                      v-model="caseQueryInfo.search_case_name"
                      clearable>
            </el-input>
          </el-col>
          <el-col :span="5">
            <el-input placeholder="请输入接口地址"
                      v-model="caseQueryInfo.search_case_uri"
                      clearable>
            </el-input>
          </el-col>
          <el-col :span="5">
          <el-input placeholder="请输入创建人"
                    v-model="caseQueryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
          <el-col :span="3">
            <el-button type="primary" @click="queryCase">查 询</el-button>
          </el-col>
        </el-row>
        <el-row>
        <el-tabs v-model="caseQueryInfo.type"
                 class="add"
                 @tab-click="getCaseList">
          <el-tab-pane label="接口案例" name="API">
          </el-tab-pane>
          <el-tab-pane label="SQL案例" name="SQL">
          </el-tab-pane>
           <el-tab-pane label="Redis案例" name="REDIS">
          </el-tab-pane>
        </el-tabs>
      </el-row>
        <el-row>
        <el-table :data="caseList"
                  border
                  width="100%"
                  @selection-change="caseSelectionChange"
                  ref="caseSelectTable">
          <el-table-column type="selection" min-width="55px" />
          <el-table-column label="案例ID" prop="case_id" min-width="50px"></el-table-column>
          <el-table-column label="案例名称" prop="case_name"  show-overflow-tooltip></el-table-column>
          <el-table-column v-if="caseQueryInfo.type === 'API'" label="所属接口" prop="interface_id" width="80px" show-overflow-tooltip></el-table-column>
          <el-table-column v-if="caseQueryInfo.type === 'API'" label="接口地址" prop="interface_uri" min-width="200px" show-overflow-tooltip></el-table-column>
          <el-table-column label="修改人" prop="update_person" width="80px"></el-table-column>
          <el-table-column label="修改时间" prop="update_time" width="140px"></el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="caseSizeChange"
          @current-change="caseCurrentChange"
          :current-page.sync="caseQueryInfo.pagenum"
          :page-size="caseQueryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="caseTotal">
        </el-pagination>
      </el-row>
      </el-scrollbar>
      <div class="add-case-drawer-footer">
        <el-button @click="addCasedialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addCase">确 定</el-button>
      </div>
    </el-drawer>
    <!-- 编辑案例抽屉 -->
    <el-drawer :visible.sync="editCasedialogVisible"
               title="编辑案例"
               direction="rtl"
               size="50%"
               class="edit-case-drawer"
               @close="editCaseDrawerClosed">
      <el-scrollbar class="edit-case-drawer-content">
        <el-form :model="caseForm"
                 label-width="100px">
          <el-row>
            <el-form-item label="案例名称" prop="case_name">
              <el-input v-model="caseForm.relation_case_name" readonly></el-input>
            </el-form-item>
          </el-row>
          <el-row v-if="caseForm.relation_case_type==='API'">
            <el-form-item label="接口地址" prop="case_name">
              <el-input v-model="caseForm.interface_address" readonly></el-input>
            </el-form-item>
          </el-row>
          <el-form-item label="案例说明" prop="case_name">
              <el-input v-model="caseForm.instructions"></el-input>
            </el-form-item>
          <!-- 参数开关，休眠时间 -->
          <el-row>
            <el-col :span="12">
              <el-form-item label="是否提取参数">
                <el-switch
                  v-model="caseForm.is_get_param"
                  active-color="#13ce66">
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 返回参数 -->
          <el-row v-if="caseForm.is_get_param">
            <el-col :span="24">
              <el-form-item label="返回参数">
                <el-input v-model="caseForm.response_param"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="caseForm.is_get_param">
            <el-col :span="24">
              <el-form-item label="返回参数别名">
                <el-input v-model="caseForm.response_param_alias"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 请求参数，头部 -->
          <el-row v-if="caseForm.is_get_param">
            <el-col :span="12">
              <el-form-item label="请求参数">
                <el-input v-model="caseForm.request_param"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="返回头部">
                <el-input v-model="caseForm.response_header_param"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 休眠时间，排序 -->
          <el-row>
            <el-col :span="12">
              <el-form-item label="休眠时间">
                <el-input v-model="caseForm.sleep_time">
                  <template slot="append">秒</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 请求数据 -->
          <el-row>
            <el-col :span="12">
              <el-form-item label="是否重设数据">
                <el-switch v-model="caseForm.reset_param"
                          active-color="#13ce66">
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item label="请求数据">
                <el-input type="textarea"
                          :rows="10"
                          placeholder="请输入json数据"
                          v-model="caseForm.interface_data"
                          @blur="transferJson">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 断言 -->
          <el-row>
            <el-col :span="12">
              <el-form-item label="是否断言">
                <el-switch v-model="caseForm.is_assert"
                          active-color="#13ce66">
                </el-switch>
              </el-form-item>
            </el-col>
          </el-row>
        <div v-if="caseForm.is_assert">
          <el-row>
          <el-col :span="12">
            <el-form-item label="断言类型">
              <el-input disabled value="数据匹配"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-button type="primary" @click="addMatchAssert">添加断言</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item>
          <el-table :data="matchAssertList" border width="60%">
            <el-table-column label="返回值" min-width="50px">
              <template v-slot="scopeProps">
                <el-input v-model="scopeProps.row.return_value"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="判断符" min-width="50px">
              <template v-slot="scopeProps">
                <el-select v-model="scopeProps.row.assert_operator">
                  <el-option value="1" label="等于"></el-option>
                  <el-option value="2" label="不等于"></el-option>
                  <el-option value="3" label="包含"></el-option>
                  <el-option value="4" label="不包含"></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="预期值" min-width="50px">
              <template v-slot="scopeProps">
                <el-input v-model="scopeProps.row.expect_result"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="删除" min-width="50px">
              <template v-slot="scopeProps">
                <el-button type="danger"
                          @click="deleteAssert(true, scopeProps.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="断言类型">
              <el-input disabled value="四则运算"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-button type="primary" @click="addArithmeticAssert">添加断言</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item>
            <el-table :data="arithmeticAssertList" border width="60%">
              <el-table-column label="被运算值" min-width="50px">
                <template v-slot="scopeProps">
                  <el-input v-model="scopeProps.row.return_value"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="运算符" min-width="50px">
                <template v-slot="scopeProps">
                  <el-select v-model="scopeProps.row.assert_operator">
                    <el-option value="1" label="+"></el-option>
                    <el-option value="2" label="-"></el-option>
                    <el-option value="3" label="×"></el-option>
                    <el-option value="4" label="÷"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="运算值" min-width="50px">
                <template v-slot="scopeProps">
                  <el-input v-model="scopeProps.row.operation_value"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="运算结果" min-width="50px">
                <template v-slot="scopeProps">
                  <el-input v-model="scopeProps.row.expect_result"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="删除" min-width="50px">
                <template v-slot="scopeProps">
                  <el-button type="danger"
                            @click="deleteAssert(false, scopeProps.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-row>
        </div>
        </el-form>
      </el-scrollbar>
      <div class="edit-case-drawer-footer">
        <el-button @click="editCasedialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="editCase">确 定</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'project_info',
      user_name: window.localStorage.getItem('user_name'),
      queryInfo: {
        search_project_id: null,
        search_project_name: '',
        pagenum: 1,
        pagesize: 5,
        creater: window.localStorage.getItem('user_name')
      },
      projectList: [],
      isAdd: true,
      projectForm: {
        project_mode: false,
        test_case: [],
        uat_case: [],
        pro_case: []
      },
      test_before_param: [],
      uat_before_param: [],
      pro_before_param: [],
      activeName: 'test',
      caseQueryInfo: {
        search_case_id: null,
        search_case_name: '',
        search_case_uri: '',
        creater: window.localStorage.getItem('user_name'),
        pagenum: 1,
        pagesize: 5,
        type: 'API'
      },
      matchAssertList: [],
      arithmeticAssertList: [],
      originalIndex: 0,
      caseForm: {},
      sqlForm: {},
      selectedCaseList: [],
      editCasedialogVisible: false,
      caseList: [],
      caseTotal: 0,
      addIndex: 0,
      addCasedialogVisible: false,
      dialogTitle: '',
      total: 0,
      dialogVisible: false,
      projectFormRules: {
        project_name: [
          { required: true, message: '请输入任务名称', trigger: 'blur' }
        ]
      },
      type: ''
    }
  },
  created() {
    this.getProjectList()
    this.getCaseList()
  },
  methods: {
    queryProject() {
      this.queryInfo.pagenum = 1
      this.getProjectList()
    },
    async getProjectList() {
      const { data: res } = await this.$http.get(this.model + '/project_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取项目列表失败')
      this.projectList = res.data.projects
      this.total = res.data.total
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getProjectList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getProjectList()
    },
    DialogClosed() {
      this.$refs.projectFormRef.resetFields()
      this.projectForm = {
        project_mode: false,
        test_case: [],
        uat_case: [],
        pro_case: []
      }
      this.test_before_param = []
      this.uat_before_param = []
      this.pro_before_param = []
    },
    async showDialog(isAdd, id, type) {
      this.type = type
      if (this.type === 'add') {
        this.dialogTitle = '新增域名'
      } else if (this.type === 'update') {
        this.dialogTitle = '编辑域名'
      } else if (this.type === 'check') {
        this.dialogTitle = '查看域名'
      }
      this.isAdd = isAdd
      if (!isAdd) {
        const { data: res } = await this.$http.get('/user_info/get_base_info/', { params: { id: id, type: 'Project' } })
        if (res.meta.status !== 200) return this.$message.error('获取域名信息失败')
        this.projectForm = res.data
        if (this.projectForm.project_mode) {
          this.test_before_param = this.trans_param(res.data.test_before_param)
          this.uat_before_param = this.trans_param(res.data.uat_before_param)
          this.pro_before_param = this.trans_param(res.data.pro_before_param)
        }
      }
      this.dialogVisible = true
    },
    submitProject() {
      this.$refs.projectFormRef.validate(async valid => {
        if (!valid) return false
        this.projectForm.test_before_param = this.addBeforeParam(this.test_before_param)
        this.projectForm.uat_before_param = this.addBeforeParam(this.uat_before_param)
        this.projectForm.pro_before_param = this.addBeforeParam(this.pro_before_param)
        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/project_add/', this.projectForm)
          if (res.meta.status !== 200) return this.$message.error('新增项目失败')
          this.$message.success('新增项目成功')
          this.getProjectList()
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.projectForm.project_id + '/project_update/', this.projectForm)
          if (res.meta.status !== 200) return this.$message.error('编辑项目失败')
          this.$message.success('编辑项目成功')
          this.getProjectList()
        }
        this.dialogVisible = false
      })
    },
    removeProjectById(id) {
      this.$confirm('此操作将永久删除该项目, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete(this.model + '/' + id + '/project_delete/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error('删除项目失败')
          this.$message.success('删除项目成功')
          this.getProjectList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    addPreparam() {
      if (this.activeName === 'test') {
        this.test_before_param.push({})
      } else if (this.activeName === 'uat') {
        this.uat_before_param.push({})
      } else if (this.activeName === 'pro') {
        this.pro_before_param.push({})
      }
    },
    addCaseDrawerShow(index) {
      this.addIndex = index
      this.addCasedialogVisible = true
      this.getCaseList()
    },
    async getCaseList() {
      const { data: res } = await this.$http.get('case_info/case_list/', { params: this.caseQueryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取案例列表失败')
      this.caseList = res.data.cases
      this.caseTotal = res.data.total
    },
    addCase() {
      const addCaseList = []
      this.selectedCaseList.forEach(item => {
        addCaseList.push(
          {
            relation_case_id: item.case_id,
            relation_case_name: item.case_name,
            interface_address: '',
            instructions: '',
            relation_case_type: item.case_type,
            is_get_param: false,
            request_param: '',
            response_param: '',
            response_param_alias: '',
            response_header_param: '',
            sleep_time: 0,
            reset_param: false,
            interface_data: '',
            is_assert: false,
            asserts: '',
            counterKey: this.counterKey,
            is_enable: true
          }
        )
        this.counterKey += 1
      })
      if (this.addIndex === 9999) {
        if (this.activeName === 'test') {
          console.log('this.projectForm.test_case==================>', this.projectForm.test_case)
          this.projectForm.test_case.unshift(...addCaseList)
        } else if (this.activeName === 'uat') {
          this.projectForm.uat_case.unshift(...addCaseList)
        } else if (this.activeName === 'pro') {
          this.projectForm.pro_case.unshift(...addCaseList)
        }
      } else {
        if (this.activeName === 'test') {
          this.projectForm.test_case.splice(this.addIndex + 1, 0, ...addCaseList)
        } else if (this.activeName === 'uat') {
          this.projectForm.uat_case.splice(this.addIndex + 1, 0, ...addCaseList)
        } else if (this.activeName === 'pro') {
          this.projectForm.pro_case.splice(this.addIndex + 1, 0, ...addCaseList)
        }
      }
      this.addCasedialogVisible = false
    },
    addBeforeParam(paramList) {
      var preparamStr = ''
      paramList.forEach(item => {
        preparamStr = preparamStr + item.paramName + '=' + item.paramValue + ';'
      })
      return preparamStr
    },
    addCasedialogClosed() {
      this.$refs.caseSelectTable.clearSelection()
      this.caseQueryInfo = {
        search_case_id: null,
        search_case_name: '',
        search_case_uri: '',
        pagenum: 1,
        pagesize: 5,
        type: 'API'
      }
      this.caseTotal = 0
    },
    queryCase() {
      this.caseQueryInfo.pagenum = 1
      this.getCaseList()
    },
    caseSelectionChange(value) {
      this.selectedCaseList = value
    },
    caseSizeChange(newSize) {
      this.caseQueryInfo.pagesize = newSize
      this.getCaseList()
    },
    caseCurrentChange(newPage) {
      this.caseQueryInfo.pagenum = newPage
      this.getCaseList()
    },
    deleteCase(index) {
      if (this.activeName === 'test') {
        this.projectForm.test_case.splice(index, 1)
      } else if (this.activeName === 'uat') {
        this.projectForm.uat_case.splice(index, 1)
      } else if (this.activeName === 'pro') {
        this.projectForm.pro_case.splice(index, 1)
      }
    },
    editCaseDrawerClosed() {
      this.matchAssertList = []
      this.arithmeticAssertList = []
    },
    async editCaseDialogShow(caseInfo, index) {
      this.originalIndex = index
      const objString = JSON.stringify(caseInfo)
      this.caseForm = JSON.parse(objString)
      if (!this.caseForm.scene_relation_id) {
        this.caseForm.reset_param = true
      }
      const { data: res } = await this.$http.get('/user_info/get_base_info/', { params: { id: caseInfo.relation_case_id, type: 'API' } })
      if (res.meta.status !== 200) return this.$message.error('获取案例编辑信息失败')
      if (this.caseForm.relation_case_type === 'API') {
        this.caseForm.interface_address = res.data.interface_address
      }
      if (this.caseForm.interface_data === '' || !this.caseForm.interface_data) {
        this.caseForm.interface_data = res.data.interface_data
        this.caseForm.is_assert = res.data.is_assert
        this.caseForm.asserts = res.data.asserts
      }
      this.caseForm.asserts.forEach(item => {
        if (item.assert_type === '1') {
          this.matchAssertList.push(item)
        } else if (item.assert_type === '2') {
          this.arithmeticAssertList.push(item)
        }
      })
      this.editCasedialogVisible = true
    },
    transferJson() {
      if (this.caseForm.interface_data && this.caseForm.relation_case_type === 'API') {
        this.caseForm.interface_data = this.caseForm.interface_data.replace(/:\s*@{/g, ':"*@{').replace(/:\s*\[@{/g, ':["*@{').replace(/:\s*{@{/g, ':{"*@{').replace(/:\s*\${/g, ':"*${').replace(/:\s*\[\${/g, ':["*${').replace(/:\s*{\${/g, ':{"*${')
        this.caseForm.interface_data = this.caseForm.interface_data.replace(/}@,/g, '}@*",').replace(/}@]/g, '}@*"]').replace(/}@}/g, '}@*"}').replace(/}\$,/g, '}$*",').replace(/}\$]/g, '}$*"]').replace(/}\$}/g, '}$*"}')
        this.caseForm.interface_data = JSON.parse(this.caseForm.interface_data)
        this.caseForm.interface_data = JSON.stringify(this.caseForm.interface_data, null, 4)
        this.caseForm.interface_data = this.caseForm.interface_data.replace(/"\*/g, '').replace(/\*"/g, '')
      }
    },
    editCase() {
      const asserts = [...this.matchAssertList, ...this.arithmeticAssertList]
      this.caseForm.asserts = asserts
      if (this.caseForm.is_assert) {
        if (this.caseForm.asserts.length === 0) return this.$message.error('断言为是，请填写断言内容')
      }
      if (this.activeName === 'test') {
        this.$set(this.projectForm.test_case, this.originalIndex, this.caseForm)
      } else if (this.activeName === 'uat') {
        this.$set(this.projectForm.uat_case, this.originalIndex, this.caseForm)
      } else if (this.activeName === 'pro') {
        this.$set(this.projectForm.pro_case, this.originalIndex, this.caseForm)
      }
      this.editCasedialogVisible = false
    },
    addMatchAssert() {
      this.matchAssertList.push({
        assert_type: '1',
        return_value: '',
        assert_operator: '1',
        expect_result: '',
        operation_value: ''
      })
    },
    addArithmeticAssert() {
      this.arithmeticAssertList.push({
        assert_type: '2',
        return_value: '',
        assert_operator: '1',
        expect_result: '',
        operation_value: ''
      })
    },
    trans_param(value) {
      const preParaArry = value.split(';')
      const preparamList = []
      if (preParaArry.length > 1) {
        preParaArry.pop()
        preParaArry.forEach(item => {
          const paramName = item.split('=')[0]
          const paramValue = item.split('=')[1]
          preparamList.push(
            {
              paramName: paramName,
              paramValue: paramValue
            }
          )
        })
      }
      return preparamList
    },
    deletePreparm(index) {
      if (this.activeName === 'test') {
        this.test_before_param.splice(index, 1)
      } else if (this.activeName === 'uat') {
        this.uat_before_param.splice(index, 1)
      } else if (this.activeName === 'pro') {
        this.pro_before_param.splice(index, 1)
      }
    }
  }
}
</script>

<style lang="less">
.cell button {
  margin-left: 5px !important;
  margin-right: 10px !important;
  margin-top: 5px !important;
}
.scene-drawer {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
    }
  .el-drawer__header{
    margin-bottom: 20px;
  }
  .el-drawer__body{
    height: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  .scene-drawer-content{
    height: 0;
    flex-grow: 2;
  }
  .scene-drawer-footer {
    padding: 20px 0;
    text-align: center;
  }
  .el-scrollbar__view {
    padding:0 30px 15px;
  }
  .relationCaseTabel {
    margin-top: 15px;
    border-top: 1px solid #dfe6ec;
    border-left: 1px solid #dfe6ec;
    border-right: 1px solid #dfe6ec;
  }
  .el-divider__text {
    font-size: 15px;
    font-weight: bold;
    color: #606266;
  }
  .el-drawer__header {
    text-align: center;
  }
}
.add-case-drawer {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
  }
  .el-drawer__body{
    height: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  .el-scrollbar__view {
    padding:0 30px 20px;
  }
  .add-case-drawer-footer {
    padding: 20px 30px;
    text-align: right;
  }
}
.edit-case-drawer {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
  }
   .el-drawer__body{
    height: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  .el-scrollbar__view {
    padding:0 30px 20px;
  }
  .edit-case-drawer-footer {
    padding: 20px 30px;
    text-align: right;
  }
}
</style>
