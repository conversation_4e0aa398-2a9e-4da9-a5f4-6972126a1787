<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>数据管理</el-breadcrumb-item>
      <el-breadcrumb-item>测试任务</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="3">
          <el-select placeholder="请选择运行环境"
                     v-model="queryInfo.search_environment"
                     clearable>
            <el-option value="1" label="测试环境"></el-option>
            <el-option value="2" label="预发环境"></el-option>
            <el-option value="3" label="生产环境"></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-input placeholder="请输入任务ID"
                    v-model="queryInfo.search_task_id"
                    clearable>
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入任务名称"
                    v-model="queryInfo.search_task_name"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryTask">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">
          <el-button class="add" type="primary" @click="showDialog(true, {})">添加任务</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="taskList" border>
          <el-table-column label="ID" prop="task_id" min-width="50px" fixed="left"></el-table-column>
          <el-table-column label="任务名称" prop="task_name" min-width="140px" show-overflow-tooltip fixed="left">
            <template v-slot="slotProps">
              <el-link type="primary" v-if="primary_tester.indexOf(user_name) !== -1 || slotProps.row.creater==user_name" @click="showDialog(false,slotProps.row)">{{ slotProps.row.task_name }}</el-link>
              <span v-else>{{ slotProps.row.task_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="运行环境" min-width="80px">
            <template v-slot="scopeProps">
              <span v-if="scopeProps.row.environment=='1'">测试环境</span>
              <span v-else-if="scopeProps.row.environment=='2'">预发环境</span>
              <span v-else>生产环境</span>
            </template>
          </el-table-column>
          <el-table-column label="crontab表达式" prop="cron" show-overflow-tooltip min-width="110px"></el-table-column>
          <el-table-column label="是否开启" min-width="70px">
            <template v-slot="scopeProps">
              <span v-if="scopeProps.row.is_open" type="success">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column label="是否监控" min-width="70px">
            <template v-slot="scopeProps">
              <span v-if="scopeProps.row.is_monitoring" type="success">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column label="是否发送邮件" prop="is_send_email" show-overflow-tooltip min-width="100px">
            <template v-slot="scopeProps">
              <span v-if="scopeProps.row.is_send_email">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column label="是否推送企微" prop="is_wx_push" show-overflow-tooltip min-width="100px">
            <template v-slot="scopeProps">
              <span v-if="scopeProps.row.is_wx_push">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column label="场景数" prop="scene_total" min-width="60px"></el-table-column>
          <el-table-column label="运行状态" min-width="80px">
            <template v-slot="scopeProps">
              <el-tag v-if="scopeProps.row.task_status=='C'" type="success">未运行</el-tag>
              <el-tag v-else-if="scopeProps.row.task_status=='R'" type="warning">运行中</el-tag>
              <el-tag v-else-if="scopeProps.row.task_status=='S'">已完成</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建人" prop="creater" min-width="85px"></el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="150px"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="260px">
            <template v-slot="slotProps">
              <el-button type="primary" v-if="primary_tester.indexOf(user_name) !== -1 " icon="el-icon-edit" size="mini" @click="showDialog(false,slotProps.row,'update')"></el-button>
              <el-tooltip class="item" effect="dark" content="查看测试报告列表" placement="top" :enterable="false">
                <el-button type="warning" icon="el-icon-document-checked" size="mini" @click="goReportPage(slotProps.row.task_id)"></el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="运行测试任务" placement="top" :enterable="false">
                <el-button type="success" icon="el-icon-caret-right" size="mini" @click="runTask(slotProps.row.task_id)"></el-button>
              </el-tooltip>
            <el-button type="danger" v-if="slotProps.row.creater==user_name || role_id==0" icon="el-icon-delete" size="mini" @click="removeTaskById(slotProps.row.task_id)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 新增编辑对话框 -->
    <el-dialog :visible.sync="dialogVisible"
                :title="dialogTitle"
                width="80%"
                @close="DialogClosed"
                :close-on-click-modal="false"
                @opened="addDrawerOpened">
      <el-form :model="taskForm"
                label-width="100px"
                :rules="taskFormRules"
                ref="taskFormRef">
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务名称" prop="task_name">
              <el-input v-model="taskForm.task_name" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="运行环境" prop="environment" label-width="120px">
              <el-select v-model="taskForm.environment">
                <el-option value="1" label="测试环境"></el-option>
                <el-option value="2" label="预发环境"></el-option>
                <el-option value="3" label="生产环境"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="任务描述">
            <el-input type="text" v-model="taskForm.remark"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-col :span="5">
            <el-form-item label="开启定时" prop="is_open">
              <el-switch v-model="taskForm.is_open" active-color="#13ce66">
              </el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="5" v-if="taskForm.is_open">
            <el-form-item label="加入监控" prop="is_monitoring">
              <el-switch v-model="taskForm.is_monitoring" active-color="#13ce66">
              </el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="taskForm.is_open">
            <el-form-item label="crontab表达式" prop="cron" label-width="120px">
              <el-row :gutter="10">
              <el-col :span="4">
                <el-tooltip class="item" effect="dark" placement="bottom">
                  <div slot="content">分钟，范围：1-59<br/>* 时表示每分钟都要执行<br/>a-b：a分钟至b分钟内执行<br/> */n：每n分钟执行一次<br/>a,b：第a，b分钟执行</div>
                  <el-input type="text" v-model="taskForm.cron[0]" />
                </el-tooltip>
              </el-col>
              <el-col :span="4">
                <el-tooltip class="item" effect="dark" placement="bottom">
                  <div slot="content">小时，范围：0-23<br/>* 时表示每小时都要执行<br/>a-b：a小时至 b 小时内执行<br/> */n：每n小时执行一次<br/>a,b：第a，b小时执行</div>
                  <el-input type="text" v-model="taskForm.cron[1]" />
                </el-tooltip>
              </el-col>
              <el-col :span="4">
                <el-tooltip class="item" effect="dark" placement="bottom">
                  <div slot="content">一个月中的第几天，范围：1-31<br/>* 时表示每天都要执行<br/>a-b：a日至b日内执行<br/> */n：每n天执行一次<br/>a,b：第a，b日执行</div>
                  <el-input type="text" v-model="taskForm.cron[2]" />
                </el-tooltip>
              </el-col>
              <el-col :span="4">
                <el-tooltip class="item" effect="dark" placement="bottom">
                  <div slot="content">月份，范围：1-12<br/>* 时表示每月都要执行<br/>a-b：a月至b月内执行<br/> */n：每n月执行一次<br/>a,b：第a，b月执行</div>
                  <el-input type="text" v-model="taskForm.cron[3]" />
                </el-tooltip>
              </el-col>
              <el-col :span="4">
                <el-tooltip class="item" effect="dark" placement="bottom">
                  <div slot="content">星期<br/>范围：0-6（星期天为0）<br/>* 时表示每星期都要执行<br/>a-b：星期a至星期b内执行<br/> */n：每n星期执行一次<br/>a,b：星期a和星期b执行</div>
                  <el-input type="text" v-model="taskForm.cron[4]" />
                </el-tooltip>
              </el-col>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否发送邮件">
              <el-switch v-model="taskForm.is_send_email" active-color="#13ce66">
                </el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="taskForm.is_send_email">
            <el-form-item label="邮件模板">
              <el-select v-model="taskForm.email_model">
                <el-option v-for="item in emailList"
                           :key="item.id"
                           :label="item.label"
                           :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <el-form-item label="是否推送企微">
              <el-switch v-model="taskForm.is_wx_push" active-color="#13ce66">
                </el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="13" v-if="taskForm.is_wx_push">
            <el-form-item label="企微机器人地址" prop="wx_address" label-width="125px">
              <div style="display: flex; align-items: center;">
                <el-tooltip content="多个地址请用英文分号';'隔开" effect="dark" placement="top" :enterable="true">
                    <el-icon style="margin-right: 15px;">
                      <i class="el-icon-info" style="font-size: 15px;"></i>
                    </el-icon>
                  </el-tooltip>
                <el-input v-model="taskForm.wx_address" placeholder="请输入内容" style="flex: 1;"></el-input>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-button size="small" @click="addSceneDrawerShow(9999)">添加场景</el-button>
          <span style="font-size:12px;margin-left:10px;color:red;">拖拽表格行可进行排序</span>
          <div style="display: inline-block; float: right; height: 32px">
            <el-form-item label="场景ID">
              <el-input size="small" v-model="searchId" placeholder="请输入场景ID"></el-input>
            </el-form-item>
          </div>
        </el-row>
        <div class="relationCaseTabel">
          <virtual-scroll
            ref="Virtualtable"
            :key="VirtualtableKey"
            :data="taskForm.scene"
            :item-size="62"
            key-prop="counterKey"
            @change="changeVirtualList">
            <el-table :data="virtualList"
                      border
                      id="crTable"
                      row-key="counterKey"
                      height="50vh"
            >
              <virtual-column label="序号" type="index" width="50px"></virtual-column>
<!--              <el-table-column label="序号1" prop="counterKey" min-width="50px"></el-table-column>-->
              <el-table-column label="场景ID" prop="scene_id" min-width="50px"></el-table-column>
              <el-table-column label="场景名称" prop="scene_name" min-width="160px" show-overflow-tooltip></el-table-column>
              <el-table-column label="场景描述" prop="scene_describe" min-width="120px" show-overflow-tooltip></el-table-column>
              <el-table-column label="所属业务" prop="businessName" min-width="100px" show-overflow-tooltip></el-table-column>
              <el-table-column label="创建人" prop="creater" min-width="100px"></el-table-column>
              <el-table-column label="最终修改时间" prop="update_time" min-width="120px"></el-table-column>
              <el-table-column min-width="100px" label="操作">
                    <template v-slot="slotProps">
                      <el-button type="text" @click="addSceneDrawerShow(slotProps.row.realIndex)">添加</el-button>
                      <el-button type="text" @click="deleteScene(slotProps.row.realIndex)">删除</el-button>
                    </template>
                  </el-table-column>
            </el-table>
          </virtual-scroll>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitTask">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 添加场景抽屉 -->
    <el-drawer :visible.sync="addScenedialogVisible"
               title="添加场景"
               direction="rtl"
               size="70%"
               class="add-case-drawer"
               @close="addScenedialogClosed">
      <el-scrollbar class="add-case-drawer-content">
        <el-row :gutter="20">
        <el-col :span="5">
          <el-input placeholder="请输入场景ID"
                    v-model="sceneQueryInfo.search_scene_id"
                    clearable>
          </el-input>
          </el-col>
          <el-col :span="5">
          <el-input placeholder="请输入场景名称"
                    v-model="sceneQueryInfo.search_scene_name"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入创建人"
                    v-model="sceneQueryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-select placeholder="请选择业务线"
                     v-model="sceneQueryInfo.business"
                     clearable
                     filterable>
            <el-option v-for="item in businessList"
                        :key="item.id"
                        :label="item.label"
                        :value="item.id">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryScene">查 询</el-button>
        </el-col>
        </el-row>
         <el-row>
        <el-table :data="sceneList" border
                  @selection-change="sceneSelectionChange"
                  ref="sceneSelectTable">
          <el-table-column type="selection" min-width="55px" />
          <el-table-column label="场景ID" prop="scene_id" min-width="50px"></el-table-column>
          <el-table-column label="场景名称" prop="scene_name" min-width="160px" show-overflow-tooltip>
            <template v-slot="slotProps">
              <el-link type="primary" v-if="slotProps.row.creater==user_name" @click="showDialog(false,slotProps.row.scene_id, 'update')">{{ slotProps.row.scene_name }}</el-link>
              <el-link type="primary" v-else @click="showDialog(false,slotProps.row.scene_id, 'check')">{{ slotProps.row.scene_name }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="场景描述" prop="scene_describe" min-width="120px" show-overflow-tooltip></el-table-column>
          <el-table-column label="所属业务" prop="businessName" min-width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column label="前置数据" prop="before_param" min-width="120px" show-overflow-tooltip></el-table-column>
          <el-table-column label="创建人" prop="creater" min-width="100px"></el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="120px"></el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="sceneSizeChange"
          @current-change="sceneCurrentChange"
          :current-page.sync="sceneQueryInfo.pagenum"
          :page-size="sceneQueryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
      </el-scrollbar>
      <div class="add-case-drawer-footer">
        <el-button @click="addScenedialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addScene">确 定</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { VirtualScroll, VirtualColumn } from 'el-table-virtual-scroll'
export default {
  components: {
    VirtualScroll,
    VirtualColumn
  },
  watch: {
    // sarchId更变后 滚动到指定行
    searchId(newVal) {
      const index = this.taskForm.scene.findIndex(item => item.scene_id === Number(newVal))
      this.$refs.Virtualtable.scrollTo(index)
    }
    // 当是否推送企微开关关闭时,清空企微机器人地址
    // 'taskForm.is_wx_push'(newVal) {
    //   if (!newVal) {
    //     this.taskForm.wx_address = ''
    //   }
    // }
  },
  data() {
    // 验证cron规则
    var checkCron = (rule, value, cb) => {
      var str = value.join(' ')
      const regCron = /^((((\d+,)+\d+|(\d+(\/|-|#)\d+)|\d+L?|\*(\/\d+)?|L(-\d+)?|\?|[A-Z]{3}(-[A-Z]{3})?) ?){5})$/
      if (regCron.test(str)) {
        return cb()
      }
      cb(new Error('请输入合法的crontab表达式'))
    }
    return {
      VirtualtableKey: 0,
      searchId: null,
      virtualList: [],
      model: 'task_info',
      user_name: window.localStorage.getItem('user_name'),
      role_id: window.localStorage.getItem('role_id'),
      queryInfo: {
        search_task_id: null,
        search_task_name: '',
        search_environment: '1',
        search_task_type: 'API',
        search_api_type: 'SCENE',
        creater: window.localStorage.getItem('user_name'),
        pagenum: 1,
        pagesize: 5
      },
      taskList: [],
      total: 0,
      dialogVisible: false,
      taskForm: {
        task_name: '',
        environment: '',
        task_type: 'API',
        api_type: 'SCENE',
        is_open: false,
        is_monitoring: false,
        case_pool: [],
        case: [],
        run_time: '0000',
        remark: '',
        is_send_email: false,
        email_model: '',
        cron: ['6', '6', '6', '*', '*'],
        scene: [],
        is_wx_push: false,
        wx_address: ''
      },
      isAdd: false,
      dialogTitle: '',
      taskFormRules: {
        task_name: [
          { required: true, message: '请输入任务名称', trigger: 'blur' }
        ],
        environment: [
          { required: true, message: '请选择任务环境', trigger: 'blur' }
        ],
        cron: [
          { required: true, message: '请输入crontab表达式', trigger: 'blur' },
          { validator: checkCron, trigger: 'blur' }
        ],
        wx_address: [
          { required: true, message: '请输入企微机器人地址', trigger: 'blur' }
        ]
      },
      emailList: [],
      primary_tester: ['huangjiaojiao', 'huwei', 'hupeng', 'zhouqianwen', 'zhaomengzi', 'limingli', 'zhanglin', 'yangxiao', 'zhanglanxin', 'zhangqiao', 'liuyanxia', 'zhouriming', 'yuqiaoyun', 'liudawei', 'zhouzhen'],
      addScenedialogVisible: false,
      sceneList: [],
      businessList: [],
      sceneQueryInfo: {
        search_scene_id: null,
        search_scene_name: '',
        // creater: window.localStorage.getItem('user_name'),
        creater: '',
        business: null,
        pagenum: 1,
        pagesize: 5
      },
      selectedSceneList: [],
      addIndex: 0,
      counterKey: 1
    }
  },
  created() {
    this.getBusinessList()
    this.getTaskList()
    this.getEmailList()
  },
  methods: {
    changeVirtualList(renderData, start, end) {
      // 创建一个索引映射，以便快速查找 realIndex
      const indexMap = new Map()
      this.taskForm.scene.forEach((item, index) => {
        indexMap.set(item, index)
      })
      // 使用索引映射来快速找到 realIndex
      renderData.forEach(item => {
        item.realIndex = indexMap.get(item)
      })
      this.virtualList = renderData
    },
    queryTask() {
      this.queryInfo.pagenum = 1
      this.getTaskList()
    },
    async getTaskList() {
      const { data: res } = await this.$http.get(this.model + '/task_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取任务列表失败')
      console.log('任务数据===》', res)
      this.taskList = res.data.tasks
      this.total = res.data.total
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getTaskList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getTaskList()
    },
    DialogClosed() {
      this.$refs.taskFormRef.resetFields()
      this.taskForm = {
        task_name: '',
        environment: '',
        task_type: 'API',
        api_type: 'SCENE',
        is_open: false,
        is_monitoring: false,
        case_pool: [],
        case: [],
        run_time: '0000',
        remark: '',
        is_send_email: false,
        email_model: '',
        cron: ['6', '6', '6', '', ''],
        scene: [],
        is_wx_push: false,
        wx_address: ''
      }
      this.searchId = ''
    },
    async showDialog(isAdd, task) {
      this.isAdd = isAdd
      if (this.isAdd) {
        this.dialogTitle = '新增任务'
      } else {
        this.dialogTitle = '编辑任务'
        const { data: res } = await this.$http.get('/user_info/get_base_info/', { params: { id: task.task_id, type: 'Task' } })
        if (res.meta.status !== 200) return this.$message.error('获取任务信息失败')
        this.taskForm = res.data
        if (this.taskForm.is_send_email) {
          this.taskForm.email_model = parseInt(this.taskForm.email_model_id)
        }
        this.taskForm.cron = this.taskForm.cron ? this.taskForm.cron.split(' ') : ''
        this.counterKey = 1
        this.taskForm.scene.forEach(item => {
          item.counterKey = this.counterKey
          this.counterKey += 1
        })
      }
      this.dialogVisible = true
    },
    submitTask() {
      this.$refs.taskFormRef.validate(async valid => {
        if (!valid) return false
        if (this.taskForm.cron) {
          this.taskForm.cron = this.taskForm.cron.join(' ')
        } else {
          this.taskForm.cron = '6 6 6 * *'
        }
        this.taskForm.scene.forEach(item => {
          this.taskForm.case_pool.push(item.scene_id.toString())
        })
        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/task_add/', this.taskForm)
          if (res.meta.status !== 200) return this.$message.error('新增任务失败')
          this.$message.success('新增任务成功')
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.taskForm.task_id + '/task_update/', this.taskForm)
          if (res.meta.status !== 200) return this.$message.error('编辑任务失败')
          this.$message.success('编辑任务成功')
        }
        this.getTaskList()
        this.getEmailList()
        this.dialogVisible = false
      })
    },
    removeTaskById(id) {
      this.$confirm('此操作将永久删除该任务, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log('id====>', id)
        this.$http.delete(this.model + '/' + id + '/task_delete/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error('删除任务失败')
          this.$message.success('删除任务成功')
          this.getTaskList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async getEmailList() {
      const { data: res } = await this.$http.get('/user_info/info/', { params: { type: 'emails' } })
      if (res.meta.status !== 200) return this.$message.error('获取邮件列表失败')
      this.emailList = res.data
    },
    async showReport(id) {
      window.open(this.$http.defaults.baseURL + this.model + '/task_report/?task_id=' + id)
    },
    async runTask(id) {
      const { data: res } = await this.$http.get(this.model + '/task_run/', { params: { task_id: parseInt(id) } })
      if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
      this.$message.success(res.meta.msg)
    },
    goReportPage(taskId) {
      this.$router.push({ path: '/report', query: { taskId: taskId } })
    },
    addSceneDrawerShow(index) {
      this.addIndex = index
      this.addScenedialogVisible = true
      this.getSceneList()
    },
    addScenedialogClosed() {
      this.$refs.sceneSelectTable.clearSelection()
      this.sceneQueryInfo = {
        search_scene_id: null,
        search_scene_name: '',
        creater: '',
        business: null,
        pagenum: 1,
        pagesize: 5
      }
      this.caseTotal = 0
    },
    queryScene() {
      this.queryInfo.pagenum = 1
      this.getSceneList()
    },
    sceneSizeChange(newSize) {
      this.sceneQueryInfo.pagesize = newSize
      this.getSceneList()
    },
    sceneCurrentChange(newPage) {
      this.sceneQueryInfo.pagenum = newPage
      this.getSceneList()
    },
    sceneSelectionChange(value) {
      this.selectedSceneList = [...value]
    },
    addScene() {
      const addSceneList = []
      const sceneList = [...this.taskForm.scene]
      this.selectedSceneList.forEach(item => {
        if (!sceneList.find(k => k.scene_id === item.scene_id)) {
          addSceneList.push(
            {
              scene_id: item.scene_id,
              scene_name: item.scene_name,
              scene_describe: item.scene_describe,
              businessName: item.businessName,
              creater: item.creater,
              update_time: item.update_time,
              counterKey: this.counterKey
            }
          )
          this.counterKey += 1
        }
      })
      if (this.addIndex === 9999) {
        this.taskForm.scene.unshift(...addSceneList)
      } else {
        this.taskForm.scene.splice(this.addIndex + 1, 0, ...addSceneList)
      }
      this.addScenedialogVisible = false
    },
    async getBusinessList() {
      const { data: res } = await this.$http.get('/user_info/info/', { params: { type: 'businesses' } })
      if (res.meta.status !== 200) return this.$message.error('获取域名查询列表失败')
      this.businessList = res.data
    },
    async getSceneList() {
      const { data: res } = await this.$http.get('scene_info/scene_list/', { params: this.sceneQueryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取场景列表失败')
      this.total = res.data.total
      this.sceneList = res.data.scenes
    },
    deleteScene(index) {
      this.taskForm.scene.splice(index, 1)
    },
    rowDrop() {
      const tbody = document.querySelector('#crTable .el-table__body-wrapper tbody')
      const _this = this
      let oIndex = 0
      let nIndex = 0
      var ops = {
        animation: 800,
        onStart: function ({ oldIndex }) {
          oIndex = _this.taskForm.scene.findIndex(i => i === _this.virtualList[oldIndex])
        },
        onEnd: function({ newIndex, oldIndex }) {
          nIndex = _this.taskForm.scene.findIndex(i => i === _this.virtualList[newIndex])

          if (nIndex) {
            const currRow = _this.taskForm.scene.splice(oIndex, 1)[0]
            _this.taskForm.scene.splice(nIndex, 0, currRow)

            // 强制刷新table
            _this.VirtualtableKey += 1
            // 等待table渲染完成
            _this.$nextTick(() => {
              // 新table初始化拖动排序
              _this.rowDrop()
              // 等待初始化渲染完成
              _this.$nextTick(() => {
                // 获取滚动到的行的index
                const index = _this.taskForm.scene.findIndex(item => item.scene_id === Number(currRow.scene_id))
                // 滚动到行
                _this.$refs.Virtualtable.scrollTo(index)
              })
            })
          }
        }
      }
      this.$sortable.create(tbody, ops)
    },
    addDrawerOpened() {
      this.rowDrop()
    }
  }
}
</script>

<style lang="less">
.cell button {
  margin-left: 5px !important;
  margin-right: 10px !important;
  margin-top: 5px !important;
}
.scene-drawer {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
    }
  .el-drawer__header{
    margin-bottom: 20px;
  }
  .el-drawer__body{
    height: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  .scene-drawer-content{
    height: 0;
    flex-grow: 2;
  }
  .scene-drawer-footer {
    padding: 20px 0;
    text-align: center;
  }
  .el-scrollbar__view {
    padding:0 30px 15px;
  }
  .relationCaseTabel {
    margin-top: 15px;
    border-top: 1px solid #dfe6ec;
    border-left: 1px solid #dfe6ec;
    border-right: 1px solid #dfe6ec;
  }
  .el-divider__text {
    font-size: 15px;
    font-weight: bold;
    color: #606266;
  }
  .el-drawer__header {
    text-align: center;
  }
}
.add-case-drawer {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
  }
  .el-drawer__body{
    height: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  .el-scrollbar__view {
    padding:0 30px 20px;
  }
  .add-case-drawer-footer {
    padding: 20px 30px;
    text-align: right;
  }
}
.edit-case-drawer {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
  }
   .el-drawer__body{
    height: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  .el-scrollbar__view {
    padding:0 30px 20px;
  }
  .edit-case-drawer-footer {
    padding: 20px 30px;
    text-align: right;
  }
}
</style>
