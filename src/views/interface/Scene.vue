<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>接口测试</el-breadcrumb-item>
      <el-breadcrumb-item>场景管理</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="4">
          <el-input placeholder="请输入场景ID"
                    v-model="queryInfo.search_scene_id"
                    clearable>
          </el-input>
          </el-col>
          <el-col :span="4">
          <el-input placeholder="请输入场景名称"
                    v-model="queryInfo.search_scene_name"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-input placeholder="请输入案例ID"
                    v-model="queryInfo.case_id"
                    clearable>
          </el-input>
          </el-col>
        <el-col :span="3">
          <el-select placeholder="是否被引用"
                     v-model="queryInfo.is_related"
                     clearable>
            <el-option value="1" label="是"></el-option>
            <el-option value="0" label="否"></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select placeholder="请选择业务线"
                     v-model="queryInfo.business"
                     clearable
                     filterable>
            <el-option v-for="item in businessList"
                        :key="item.id"
                        :label="item.label"
                        :value="item.id">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryScene">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">
          <el-button class="add" type="primary" @click="showDialog(true, 0, 'add')">添加场景</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="sceneList" border>
          <el-table-column label="场景ID" prop="scene_id" min-width="50px"></el-table-column>
          <el-table-column label="场景名称" prop="scene_name" min-width="160px" show-overflow-tooltip>
            <template v-slot="slotProps">
              <el-link type="primary" v-if="slotProps.row.creater==user_name" @click="showDialog(false,slotProps.row.scene_id, 'update')">{{ slotProps.row.scene_name }}</el-link>
              <el-link type="primary" v-else @click="showDialog(false,slotProps.row.scene_id, 'check')">{{ slotProps.row.scene_name }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="场景描述" prop="scene_describe" min-width="120px" show-overflow-tooltip></el-table-column>
          <el-table-column label="所属业务" prop="businessName" min-width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column label="前置数据" prop="before_param" min-width="120px" show-overflow-tooltip></el-table-column>
          <el-table-column label="创建人" prop="creater" min-width="100px"></el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="120px"></el-table-column>
          <el-table-column label="是否被引用" prop="is_related" show-overflow-tooltip min-width="50px">
            <template v-slot="slotProps">
              <span v-if="slotProps.row.is_related===1">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" min-width="190px">
            <template v-slot="slotProps">
            <el-button type="primary" icon="el-icon-edit" @click="showDialog(false,slotProps.row.scene_id, 'update')" size="mini"></el-button>
            <el-button type="danger" v-if="slotProps.row.creater==user_name || role_id==0"  icon="el-icon-delete" size="mini" @click="removeSceneById(slotProps.row.scene_id)"></el-button>
            <el-tooltip class="item" effect="dark" content="复制场景" placement="top" :enterable="false">
              <el-button type="info" icon="el-icon-document-copy" size="mini" @click="copyScene(slotProps.row.scene_id)"></el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="调试场景" placement="top" :enterable="false">
              <el-button type="success" icon="el-icon-caret-right" size="mini" @click="showSceneRunDialog(slotProps.row.scene_id)"></el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="关联任务" placement="top" :enterable="false" >
              <el-button icon="el-icon-paperclip" type="warning" size="mini" @click="showTaskList(slotProps.row.scene_id,slotProps.row.scene_name)"></el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="调试日志" placement="top" :enterable="false" >
              <el-button icon="el-icon-message" size="mini" @click="getSceneDebugLog(slotProps.row.scene_id)"></el-button>
            </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 新增编辑抽屉 -->
    <EditSceneDrawer
      ref="editSceneDrawerRef"
      :visible.sync="dialogVisible"
      :title="title"
      :type="type"
      :scene-data="sceneForm"
      :business-list="businessList"
      :loading="loading"
      :model="model"
      :table-title="'案例组成'"
      :show-add-case-button="true"
      :table-id="'crTable'"
      :table-border="false"
      :table-style="'width: 100%'"
      @close="sceneDrawerClosed"
      @submit="handleSceneSubmit"
      @run-scene="showSceneRunDialog"
      @opened="addDrawerOpened"
      @refresh-list="getSceneList"
    >
      <template #table-columns="{ data, type }">
        <el-table-column type="index" />
        <el-table-column prop="relation_case_id" min-width="100px" label="案例ID" />
        <el-table-column prop="relation_case_name" min-width="120px" label="案例名称" />
        <el-table-column prop="relation_case_type" min-width="80px" label="案例类型" />
        <el-table-column prop="is_get_param" min-width="80px" label="是否提取参数">
          <template v-slot="slotProps">
            <span v-if="slotProps.row.is_get_param">是</span>
            <span v-else>否</span>
          </template>
        </el-table-column>
        <el-table-column prop="response_param" min-width="100px" label="返回参数" />
        <el-table-column prop="response_param_alias" min-width="100px" label="返回参数别名" />
        <el-table-column prop="reset_param" min-width="80px" label="重设请求数据">
          <template v-slot="slotProps">
            <span v-if="slotProps.row.reset_param">是</span>
            <span v-else>否</span>
          </template>
        </el-table-column>
        <el-table-column prop="instructions" min-width="100px" label="备注说明" show-overflow-tooltip />
        <el-table-column prop="is_check" min-width="100px" label="是否数据越权">
          <template v-slot="slotProps">
            <el-switch
              v-model="slotProps.row.is_check"
              :disabled="type === 'check'"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column min-width="170px" label="操作" v-if="type !== 'check'">
          <template v-slot="slotProps">
            <el-button type="text" @click="handleEditCase({ caseInfo: slotProps.row, index: slotProps.$index })">编辑</el-button>
            <el-button type="text" @click="handleAddCase(slotProps.$index)">添加</el-button>
            <el-button type="text" @click="handleCopyCase(slotProps.row, slotProps.$index)">复制</el-button>
            <el-button type="text" @click="handleDeleteCase(slotProps.$index)">删除</el-button>
            <el-button type="text" @click="slotProps.row.is_enable = !slotProps.row.is_enable">
              <span v-if="slotProps.row.is_enable">禁用</span>
              <span v-else style="color:red">启用</span>
            </el-button>
          </template>
        </el-table-column>
      </template>
    </EditSceneDrawer>

    <!-- 环境选择对话框 -->
    <el-dialog :visible.sync="enviromentDialogVisible"
                width="30%"
                @close="enviromentDialogClosed"
                v-loading="loading"
                element-loading-text="场景运行中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.8)"
                :close-on-click-modal="false">
      <el-form label-width="130px"
               :model="sceneRunForm"
               ref="sceneRunFormRef"
               :rules="sceneRunFormRules">
        <el-form-item label="请选择运行环境" prop="env_type">
          <el-select v-model="sceneRunForm.env_type">
            <el-option label="测试环境" value="1"></el-option>
            <el-option label="预发环境" value="2"></el-option>
            <el-option label="生产环境" value="3"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="enviromentDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitSceneRun">确 定</el-button>
      </span>
    </el-dialog>
        <!-- 任务选择对话框 -->
    <el-dialog :visible.sync="taskDialogVisible"
                width="40%"
                :title = "title"
                @close = "taskListClosed">
      <el-form label-width="140px"
               :model="sceneRunForm"
               ref="sceneRunFormRef"
               :rules="sceneRunFormRules">
        <el-form-item label="请选择需添加的任务">
          <el-select v-model="selectedTaskList" multiple filterable>
            <el-option
              v-for="task in taskList"
              :key="task.task_id"
              :label="`${task.task_id}-${task.task_name}`"
              :value="task.task_id">
          </el-option>
          </el-select>
        </el-form-item>
        <template>
          <div>
            <h3>该场景已关联任务</h3>
            <el-table
              :data="selected_tasks"
              style="width: 100%">
              <el-table-column
                prop="task_name"
                label="任务名称"
                width="450">
              </el-table-column>
              <el-table-column
                label="操作"
                width="80">
                <template v-slot="slotProps">
                  <el-button @click="deleteSelectedTask(slotProps.row)" type="text" size="small">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="taskListClosed">取 消</el-button>
        <el-button type="primary" @click="submitTaskList">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 展示运行结果对话框 -->
    <el-dialog :visible.sync="resultDialogVisible"
               width="60%"
               :close-on-click-modal="false"
               @close="resultDialogClosed">
      <template slot="title">
        <p style="font-weight: 600;font-size: 16px">{{ resultForm.msg }}</p>
      </template>
      <template v-if="resultForm.code">
        <div v-if="resultForm.code!='0000'" style="color: red;font-size:15px;font-weight: bold;margin-bottom: 10px;">{{resultForm.error}}</div>
        <div v-else style="color: #67C23A;font-size:15px;font-weight: bold;margin-bottom: 10px;">场景执行完成！</div>
      </template>
      <el-collapse>
        <el-collapse-item v-for="(item, index) in resultForm.data" :key="index">
          <template slot="title">
            <span v-if="item.assert==='0'" style="color: red;font-size:15px;">{{ item.title }}</span>
            <span v-else style="font-size:15px;">{{ item.title }}</span>
          </template>
          <template slot="name">
            {{ index }}
          </template>
            <div v-for="(detail, index) in item.detail"
                 style="color: #505050;"
                 :key="index">{{ detail }}</div>
        </el-collapse-item>
        </el-collapse>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="resultDialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import EditSceneDrawer from '../../components/EditSceneDrawer.vue'
import { baseUrl } from '../../main'

export default {
  components: {
    EditSceneDrawer
  },
  data() {
    return {
      model: 'scene_info',
      user_name: window.localStorage.getItem('user_name'),
      role_id: window.localStorage.getItem('role_id'),
      queryInfo: {
        search_scene_id: null,
        search_scene_name: '',
        case_id: null,
        creater: window.localStorage.getItem('user_name'),
        business: null,
        pagenum: 1,
        pagesize: 5
      },
      sceneList: [],
      total: 0,
      dialogVisible: false,
      title: '',
      preparamList: [],
      caseParamList: [{}],

      sceneForm: {
        scene_name: '',
        business_id: 0,
        scene_describe: '',
        isShowPreparam: false,
        before_param: '',
        relation: []
      },
      sceneFormRules: {
        scene_name: [
          { required: true, message: '请输入场景名称', trigger: 'blur' }
        ]
      },
      sceneRunFormRules: {
        env_type: [
          { required: true, message: '请选择运行环境', trigger: 'blur' }
        ]
      },
      isAdd: true,
      enviromentDialogVisible: false,
      sceneRunForm: {
        scene_id: '',
        env_type: '1'
      },
      resultDialogVisible: false,
      resultForm: {},
      loading: false,
      caseDialogVisible: false,
      sqlDialogVisible: false,
      sqlForm: {},
      type: '',
      counterKey: 0,
      businessList: [],

      taskDialogVisible: false,
      selectedTaskList: [],
      taskList: [],
      selected_tasks: [],
      selectedTaskParam: {
        task_id: [],
        scene_id: '',
        scene_name: ''
      },
      fileList: [],
      headers: {
        Authorization: window.localStorage.getItem('token'),
        userId: window.localStorage.getItem('userId')
      },
      caseFormRules: {
        upload_min_rows: [
          { required: true, message: '请输入最小行数', trigger: 'blur' }
        ],
        is_upload: [
          { required: true, message: '请上传文件', trigger: 'blur' }
        ],
        temp_file: [
          { required: true, message: '请输入模板下载地址', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    uploadPath() {
      // 动态生成上传路径
      // const baseURl = axios.defaults.baseURL
      return `${baseUrl}user_info/upload_file/`
    }
  },
  created() {
    this.getSceneList()
    this.getBusinessList()
  },

  methods: {
    async getBusinessList() {
      const { data: res } = await this.$http.get('/user_info/info/', { params: { type: 'businesses' } })
      if (res.meta.status !== 200) return this.$message.error('获取域名查询列表失败')
      this.businessList = res.data
    },
    queryScene() {
      this.queryInfo.pagenum = 1
      this.getSceneList()
    },
    async getSceneList() {
      const { data: res } = await this.$http.get(this.model + '/scene_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取场景列表失败')
      this.total = res.data.total
      this.sceneList = res.data.scenes
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getSceneList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getSceneList()
    },
    DialogClosed() {
      this.sceneForm = {
        scene_name: '',
        scene_describe: '',
        isShowPreparam: false,
        before_param: '',
        relation: []
      }
      this.preparamList = []
      this.caseForm = []
    },
    async showDialog(isAdd, id, type) {
      this.type = type
      if (this.type === 'add') {
        this.title = '新增场景'
      } else if (this.type === 'update') {
        this.title = '编辑场景'
      } else if (this.type === 'check') {
        this.title = '查看场景'
      }
      await this.$refs.editSceneDrawerRef.showDialog(isAdd, id, type)
    },

    removeSceneById(id) {
      this.$confirm('此操作将永久删除该场景, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete(this.model + '/' + id + '/scene_delete/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error(result.data.meta.msg)
          this.$message.success('删除场景成功')
          this.getSceneList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    deleteCurrentCase(index) {
      this.sceneForm.relation.splice(index, 1)
    },
    async copyScene(id) {
      const { data: res } = await this.$http.get('case_info/' + id + '/case_copy/', { params: { type: 'scene' } })
      if (res.meta.status !== 200) return this.$message.error('复制场景失败')
      this.$message.success('复制场景成功')
      this.getSceneList()
    },
    enviromentDialogClosed() {
      this.$refs.sceneRunFormRef.resetFields()
    },
    async showTaskList(sceneId, sceneName) {
      this.title = sceneId.toString() + '-' + sceneName
      const { data: res } = await this.$http.get('task_info/task_list/?channel=3&scene_id=' + sceneId)
      this.taskList = res.data.tasks
      this.selected_tasks = res.data.selected_tasks
      if (res.meta.status !== 200) return this.$message.error('任务列表获取失败')
      this.selectedTaskParam.scene_id = sceneId.toString()
      this.selectedTaskParam.scene_name = sceneName
      this.taskDialogVisible = true
    },
    async deleteSelectedTask(index) {
      this.$confirm('是否确认将场景从任务中移除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { data: res } = await this.$http.get('/task_info/delete_task_scene?task_id=' + index.task_id + '&scene_id=' + this.selectedTaskParam.scene_id)
        if (res.meta.status === 200) {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          await this.showTaskList(this.selectedTaskParam.scene_id, this.selectedTaskParam.scene_name)
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async submitTaskList() {
      if (this.selectedTaskList.length !== 0) {
        this.selectedTaskParam = { ...this.selectedTaskParam, task_id: this.selectedTaskList }
        const { data: res } = await this.$http.post('task_info/add_scene_to_more_task/', this.selectedTaskParam)
        if (res.meta.status !== 200) return this.$message.error('操作失败')
        this.$message.success('添加成功')
      } else {
        this.$message.success('操作成功')
      }
      this.selectedTaskParam = []
      this.selectedTaskList = []
      this.taskDialogVisible = false
      await this.getSceneList()
    },
    taskListClosed() {
      this.selectedTaskParam = []
      this.selectedTaskList = []
      this.taskDialogVisible = false
    },
    async submitSceneRun() {
      this.loading = true
      const { data: res } = await this.$http.post(this.model + '/scene_run/', this.sceneRunForm)
      if (res.meta.status !== 200) return this.$message.error('调试场景失败')
      this.resultForm = res.data
      this.loading = false
      this.enviromentDialogVisible = false
      this.resultDialogVisible = true
    },
    async showSceneRunDialog(id) {
      this.sceneRunForm.scene_id = id
      this.enviromentDialogVisible = true
    },
    async getSceneDebugLog(id) {
      const { data: res } = await this.$http.get(this.model + '/scene_debug_log/?scene_id=' + id + '&log_type=2')
      if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
      this.resultForm = res.data
      this.enviromentDialogVisible = false
      this.resultDialogVisible = true
    },
    resultDialogClosed() {
      this.resultForm = {}
    },

    sceneDrawerClosed() {
      this.sceneForm = {
        scene_name: '',
        business_id: 0,
        scene_describe: '',
        isShowPreparam: false,
        before_param: '',
        relation: []
      }
      this.preparamList = []
    },
    // 处理场景提交
    handleSceneSubmit({ sceneData, flag }) {
      this.$refs.editSceneDrawerRef.submitScene(flag)
    },
    // 处理添加案例
    handleAddCase(index) {
      this.$refs.editSceneDrawerRef.showAddCaseDrawer(index)
    },
    // 处理编辑案例
    handleEditCase({ caseInfo, index }) {
      this.$refs.editSceneDrawerRef.showEditCaseDrawer(caseInfo, index)
    },
    // 处理复制案例
    handleCopyCase(caseInfo, index) {
      this.$refs.editSceneDrawerRef.handleCopyCase(caseInfo, index)
    },
    // 处理删除案例
    handleDeleteCase(index) {
      this.$refs.editSceneDrawerRef.deleteCase(index)
    },
    addDrawerOpened() {
      // 拖拽排序现在由EditSceneDrawer组件处理
    }
  }
}
</script>
<style lang="less">
.cell button {
  margin-left: 5px !important;
  margin-right: 10px !important;
  margin-top: 5px !important;
}
.scene-drawer {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
    }
  .el-drawer__header{
    margin-bottom: 20px;
  }
  .el-drawer__body{
    height: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  .scene-drawer-content{
    height: 0;
    flex-grow: 2;
  }
  .scene-drawer-footer {
    padding: 20px 0;
    text-align: center;
  }
  .el-scrollbar__view {
    padding:0 30px 15px;
  }
  .relationCaseTabel {
    margin-top: 15px;
    border-top: 1px solid #dfe6ec;
    border-left: 1px solid #dfe6ec;
    border-right: 1px solid #dfe6ec;
  }
  .el-divider__text {
    font-size: 15px;
    font-weight: bold;
    color: #606266;
  }
  .el-drawer__header {
    text-align: center;
  }
}
.add-case-drawer {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
  }
  .el-drawer__body{
    height: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  .el-scrollbar__view {
    padding:0 30px 20px;
  }
  .add-case-drawer-footer {
    padding: 20px 30px;
    text-align: right;
  }
}
.edit-case-drawer {
  .el-scrollbar .el-scrollbar__wrap {
    overflow-x: hidden;
  }
   .el-drawer__body{
    height: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }
  .el-scrollbar__view {
    padding:0 30px 20px;
  }
  .edit-case-drawer-footer {
    padding: 20px 30px;
    text-align: right;
  }
}
.check-notice-dialog .el-message-box__btns {
  display: flex;
  flex-direction: row-reverse;
}

.check-notice-dialog .el-message-box__btns button:nth-child(1) {
  margin-left: 15px;  /* 增加按钮之间的间距 */
}

.check-notice-dialog .el-message-box__btns button:nth-child(2) {
  margin-left: 20px;
  margin-right: 0;
}
</style>
