<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>接口测试</el-breadcrumb-item>
      <el-breadcrumb-item>接口管理</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="5">
          <el-select v-model="queryInfo.search_project_id"
                         clearable
                         filterable
                         placeholder="请选择域名">
                <el-option v-for="item in projectSearchList"
                          :key="item.id"
                          :label="item.label"
                          :value="item.id"></el-option>
            </el-select>
        </el-col>
        <el-col :span="3">
          <el-input v-model="queryInfo.search_interface_id"
                    placeholder="请输入接口ID"
                    clearable></el-input>
        </el-col>
        <el-col :span="7">
          <el-input v-model="queryInfo.search_interface_address"
                    placeholder="请输入接口地址"
                    clearable></el-input>
        </el-col>
        <el-col :span="3">
          <el-select placeholder="是否被引用"
                     v-model="queryInfo.is_related"
                     clearable>
            <el-option value="1" label="是"></el-option>
            <el-option value="0" label="否"></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryInterface">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">
          <el-button class="add" type="primary" @click="showDialog(true, {}, 'add')">添加接口</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="interfaceList" border>
          <el-table-column label="ID" prop="interface_id" min-width="50px" fixed="left"></el-table-column>
          <el-table-column label="接口名称" prop="interface_name" min-width="150px" show-overflow-tooltip fixed="left">
            <template v-slot="slotProps">
              <el-link type="primary" v-if="slotProps.row.creater==user_name" @click="showDialog(false, slotProps.row, 'update')">{{ slotProps.row.interface_name }}</el-link>
              <el-link type="primary" v-else @click="showDialog(false,slotProps.row, 'check')">{{ slotProps.row.interface_name }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="所属域名" prop="project_name" show-overflow-tooltip min-width="120px"></el-table-column>
          <el-table-column label="协议" prop="interface_agreement" show-overflow-tooltip min-width="100px">
            <template v-slot="scopeProps">
              <span v-if="scopeProps.row.interface_agreement==='HTTPS'">HTTPS</span>
              <span v-else-if="scopeProps.row.interface_agreement==='HTTP'">HTTP</span>
              <span v-else-if="scopeProps.row.interface_agreement==='NULL'">空</span>
            </template>
          </el-table-column>
          <el-table-column label="请求方式" prop="interface_way" min-width="100px"></el-table-column>
          <el-table-column label="请求头" prop="header" min-width="120px" show-overflow-tooltip></el-table-column>
          <el-table-column label="接口地址" prop="interface_address" show-overflow-tooltip min-width="150px"></el-table-column>
          <el-table-column label="备注" prop="remark" show-overflow-tooltip min-width="120px"></el-table-column>
          <el-table-column label="创建人" prop="creater" min-width="100px"></el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="150px"></el-table-column>
          <el-table-column label="是否被引用" prop="is_related" show-overflow-tooltip min-width="100px">
            <template v-slot="scopeProps">
              <span v-if="scopeProps.row.is_related===1">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" min-width="200px">
            <template v-slot="slotProps">
            <el-button type="primary" icon="el-icon-edit" size="mini" @click="showDialog(false,slotProps.row,'update')"></el-button>
            <el-button type="danger" v-if="slotProps.row.creater==user_name" icon="el-icon-delete" size="mini" @click="removeInterfaceById(slotProps.row.interface_id)"></el-button>
            <el-tooltip class="item" effect="dark" content="添加案例" placement="top" :enterable="false">
              <el-button type="success" icon="el-icon-document-add" size="mini" @click="goApiCasePage(slotProps.row)"></el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="复制接口" placement="top" :enterable="false">
              <el-button type="info" icon="el-icon-document-copy" size="mini" @click="copyInterface(slotProps.row.interface_id)"></el-button>
            </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 新增编辑对话框 -->
    <el-dialog :visible.sync="dialogVisible"
                :title="dialogTitle"
                width="60%"
                @close="DialogClosed"
                :close-on-click-modal="false">
      <el-form :model="interfaceForm"
                label-width="100px"
                :rules="interfaceFormRules"
                ref="interfaceFormRef">
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属域名" prop="project_id">
              <el-select v-model="interfaceForm.project_id"
                         clearable
                         filterable>
                <el-option v-for="item in projectSearchList"
                          :key="item.id"
                          :label="item.label"
                          :value="item.id"></el-option>
            </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="接口名称" prop="interface_name">
              <el-input v-model="interfaceForm.interface_name" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="协议类型" prop="interface_agreement">
              <el-select v-model="interfaceForm.interface_agreement"
                     clearable>
                <el-option value="HTTP" label="HTTP"></el-option>
                <el-option value="HTTPS" label="HTTPS"></el-option>
                <el-option value="NULL" label="空"></el-option>
            </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求方式" prop="interface_way">
              <el-select v-model="interfaceForm.interface_way"
                     clearable>
                <el-option value="GET" label="GET"></el-option>
                <el-option value="POST" label="POST"></el-option>
                <el-option value="multipart/form-data_POST" label="multipart/form-data_POST"></el-option>
                <el-option value="x-www-form-urlencoded_POST" label="x-www-form-urlencoded_POST"></el-option>
                <el-option value="PUT" label="PUT"></el-option>
                <el-option value="DELETE" label="DELETE"></el-option>
            </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="请求头" prop="header">
              <el-input v-model="interfaceForm.header" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="接口地址" prop="interface_address">
              <el-input v-model="interfaceForm.interface_address" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <span style="color:red;font-size:12px;margin-left:30px">此处参数仅作为字段注释，非接口访问数据</span>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="请求参数">
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-button @click="addRequestParam" size="small">添加参数</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="25">
          <template v-for="(item, index) in interfaceForm.request_param">
            <el-form-item :key="index" :label="'参数' + (index + 1)">
              <el-col :span="5">
              <el-select v-model="item.paramType">
                <el-option label="string" value="string"></el-option>
                <el-option label="number" value="number"></el-option>
                <el-option label="integer" value="integer"></el-option>
                <el-option label="object" value="object"></el-option>
                <el-option label="array" value="array"></el-option>
                <el-option label="boolean" value="boolean"></el-option>
              </el-select>
              </el-col>
              <el-col :span="5">
                <el-input type="text" v-model="item.paramName" placeholder="参数名" />
              </el-col>
              <!-- <el-col :span="5">
              <el-input type="text" v-model="item.paramValue" placeholder="参数值"/>
              </el-col> -->
              <el-col :span="7">
              <el-input type="text" v-model="item.paramDescribe" placeholder="注释"/>
              </el-col>
              <el-col :span="1">
                <i class="el-icon-delete" @click="deleteRequestParam(index)"></i>
              </el-col>
            </el-form-item>
          </template>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="返回参数">
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-button @click="addResponseParam" size="small">添加参数</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="25">
          <template v-for="(item, index) in interfaceForm.response_param">
            <el-form-item :key="index" :label="'参数' + (index + 1)">
              <el-col :span="5">
              <el-select v-model="item.paramType">
                <el-option label="string" value="string"></el-option>
                <el-option label="number" value="number"></el-option>
                <el-option label="integer" value="integer"></el-option>
                <el-option label="object" value="object"></el-option>
                <el-option label="array" value="array"></el-option>
                <el-option label="boolean" value="boolean"></el-option>
              </el-select>
              </el-col>
              <el-col :span="5">
                <el-input type="text" v-model="item.paramName" placeholder="参数名" />
              </el-col>
              <!-- <el-col :span="5">
              <el-input type="text" v-model="item.paramValue" placeholder="参数值"/>
              </el-col> -->
              <el-col :span="7">
              <el-input type="text" v-model="item.paramDescribe" placeholder="注释"/>
              </el-col>
              <el-col :span="1">
                <i class="el-icon-delete" @click="deleteResponseParam(index)"></i>
              </el-col>
            </el-form-item>
          </template>
        </el-row>
        <el-row>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="interfaceForm.remark"></el-input>
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="type !== 'check'">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitInterface">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'interface_info',
      user_name: window.localStorage.getItem('user_name'),
      queryInfo: {
        search_project_id: null,
        search_interface_id: null,
        search_interface_address: '',
        is_related: null,
        creater: window.localStorage.getItem('user_name'),
        pagenum: 1,
        pagesize: 5
      },
      total: 0,
      interfaceList: [],
      projectSearchList: [],
      interfaceSearchList: [],
      isAdd: true,
      interfaceForm: {
        project_id: null,
        interface_name: '',
        interface_agreement: 'HTTP',
        interface_way: 'GET',
        header: '',
        interface_address: '',
        request_param: [],
        response_param: []
      },
      dialogTitle: '',
      interfaceFormRules: {
        project_id: [
          { required: true, message: '请选择所属域名', trigger: 'blur' }
        ],
        interface_name: [
          { required: true, message: '请输入接口名称', trigger: 'blur' }
        ],
        interface_agreement: [
          { required: true, message: '请选择协议类型', trigger: 'blur' }
        ],
        interface_way: [
          { required: true, message: '请选择请求方式', trigger: 'blur' }
        ],
        interface_address: [
          { required: true, message: '请输入接口地址', trigger: 'blur' }
        ]
      },
      dialogVisible: false,
      requestParamList: [],
      type: ''
    }
  },
  created() {
    this.getInterfaceList()
    this.getProjectSearchList()
    this.getInterfaceSearchList()
  },
  activated() {
    this.getInterfaceSearchList()
    this.getProjectSearchList()
  },
  methods: {
    queryInterface() {
      this.queryInfo.pagenum = 1
      this.getInterfaceList()
    },
    async getInterfaceList() {
      const { data: res } = await this.$http.get(this.model + '/interface_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取接口列表失败')
      this.interfaceList = res.data.data
      this.total = res.data.total
    },
    async getProjectSearchList() {
      const { data: res } = await this.$http.get('/user_info/info/', { params: { type: 'projects' } })
      if (res.meta.status !== 200) return this.$message.error('获取域名查询列表失败')
      this.projectSearchList = res.data
    },
    async getInterfaceSearchList() {
      const { data: res } = await this.$http.get('interface_info/get_api_list/?api_type=interfaces')
      if (res.meta.status !== 200) return this.$message.error('获取接口查询信息失败')
      this.interfaceSearchList = res.data
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getInterfaceList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getInterfaceList()
    },
    DialogClosed() {
      this.interfaceForm = {
        project_id: null,
        interface_name: '',
        interface_agreement: 'HTTP',
        interface_way: 'GET',
        header: '',
        interface_address: '',
        request_param: [],
        response_param: []
      }
    },
    async copyInterface(id) {
      const { data: res } = await this.$http.get('/case_info/' + id + '/case_copy/', { params: { type: 'interface' } })
      if (res.meta.status !== 200) return this.$message.error('复制接口失败')
      this.$message.success('复制接口成功')
      this.getInterfaceList()
    },
    showDialog(isAdd, info, type) {
      this.type = type
      if (this.type === 'add') {
        this.dialogTitle = '新增接口'
      } else if (this.type === 'update') {
        this.dialogTitle = '编辑接口'
      } else if (this.type === 'check') {
        this.dialogTitle = '查看接口'
      }
      this.isAdd = isAdd
      if (!isAdd) {
        const objString = JSON.stringify(info)
        this.interfaceForm = JSON.parse(objString)
      }
      this.dialogVisible = true
    },
    submitInterface() {
      this.$refs.interfaceFormRef.validate(async valid => {
        if (!valid) return false
        this.interfaceForm.request_param = this.interfaceForm.request_param.filter(item => item.paramName !== '')
        this.interfaceForm.response_param = this.interfaceForm.response_param.filter(item => item.paramName !== '')
        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/interface_add/', this.interfaceForm)
          if (res.meta.status !== 200) return this.$message.error('新增接口失败')
          this.$message.success('新增接口成功')
          this.getInterfaceList()
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.interfaceForm.interface_id + '/interface_update/', this.interfaceForm)
          if (res.meta.status !== 200) return this.$message.error('编辑接口失败')
          this.$message.success('编辑接口成功')
          this.getInterfaceList()
        }
        this.dialogVisible = false
      })
    },
    removeInterfaceById(id) {
      this.$confirm('此操作将永久删除该接口, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete(this.model + '/' + id + '/interface_delete/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error(result.data.meta.msg)
          this.$message.success('删除接口成功')
          this.getInterfaceList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    goApiCasePage(interfaceInfo) {
      window.sessionStorage.setItem('activePath', '/api-cases')
      const params = {
        projectId: interfaceInfo.project_id,
        interfaceId: interfaceInfo.interface_id,
        interfaceName: interfaceInfo.interface_name
      }
      this.$router.push({ path: '/api-cases', query: params })
    },
    addRequestParam() {
      this.interfaceForm.request_param.push(
        {
          paramType: 'string',
          paramName: '',
          paramValue: '',
          paramDescribe: ''
        }
      )
    },
    deleteRequestParam(index) {
      this.interfaceForm.request_param.splice(index, 1)
    },
    addResponseParam() {
      this.interfaceForm.response_param.push(
        {
          paramType: 'string',
          paramName: '',
          paramValue: '',
          paramDescribe: ''
        }
      )
    },
    deleteResponseParam(index) {
      this.interfaceForm.response_param.splice(index, 1)
    }
  }
}
</script>
