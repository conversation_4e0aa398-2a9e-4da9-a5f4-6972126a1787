<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>接口测试</el-breadcrumb-item>
      <el-breadcrumb-item>案例管理</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="3">
          <el-input placeholder="案例ID"
                    v-model="queryInfo.search_case_id"
                    clearable>
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-input placeholder="接口ID"
                    v-model="queryInfo.search_interface_id"
                    clearable>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-input placeholder="请输入案例名称"
                    v-model="queryInfo.search_case_name"
                    clearable>
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入接口地址"
                    v-model="queryInfo.search_case_uri"
                    clearable>
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-select placeholder="是否被引用"
                     v-model="queryInfo.is_related"
                     clearable>
            <el-option value="1" label="是"></el-option>
            <el-option value="0" label="否"></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryCase">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-tabs v-model="queryInfo.type"
                 class="add"
                 @tab-click="getCaseList">
          <el-tab-pane label="接口案例" name="API">
            <el-row>
              <el-col :span="3">
                <el-button class="add"
                           type="primary"
                           @click="showCaseDialog(true, 0, 'add')"
                           size="mini">添加接口案例</el-button>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="SQL案例" name="SQL">
            <el-row>
              <el-col :span="3">
                <el-button class="add"
                           type="primary"
                           @click="showSqlDialog(true, 0, 'add')"
                           size="mini">添加SQL案例</el-button>
              </el-col>
            </el-row>
          </el-tab-pane>
           <el-tab-pane label="Redis案例" name="REDIS">
            <el-row>
              <el-col :span="3">
                <el-button class="add"
                           type="primary"
                           @click="showRedisDialog(true, 0, 'add')"
                           size="mini">添加Redis案例</el-button>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="导入/导出解析案例" name="EXPORT">
            <el-row>
              <el-col :span="3">
                <el-button class="add"
                           type="primary"
                           @click="showExportDialog(true, 0, 'add')"
                           size="mini">添加导入/导出解析案例</el-button>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-row>
      <el-row>
        <el-table :data="caseList" border width="100%" v-loading="getCaseListLoading">
          <el-table-column label="案例ID" prop="case_id" min-width="50px"></el-table-column>
          <el-table-column label="案例名称" prop="case_name" min-width="150px" show-overflow-tooltip>
            <template v-slot="slotProps">
              <el-link type="primary" v-if="slotProps.row.case_type=='API' && slotProps.row.creater==user_name" @click="showCaseDialog(false, slotProps.row.case_id, 'update')">{{ slotProps.row.case_name }}</el-link>
              <el-link type="primary" v-else-if="slotProps.row.case_type=='SQL' && slotProps.row.creater==user_name" @click="showSqlDialog(false, slotProps.row.case_id, 'update')">{{ slotProps.row.case_name }}</el-link>
              <el-link type="primary" v-else-if="slotProps.row.case_type=='REDIS' && slotProps.row.creater==user_name" @click="showRedisDialog(false, slotProps.row.case_id, 'update')">{{ slotProps.row.case_name }}</el-link>
              <el-link type="primary" v-else-if="slotProps.row.case_type=='EXPORT' && slotProps.row.creater==user_name" @click="showExportDialog(false, slotProps.row.case_id, 'update')">{{ slotProps.row.case_name }}</el-link>
              <el-link type="primary" v-else-if="slotProps.row.case_type=='UPLOAD' && slotProps.row.creater==user_name" @click="showExportDialog(false, slotProps.row.case_id, 'update')">{{ slotProps.row.case_name }}</el-link>
              <el-link type="primary" v-else-if="slotProps.row.case_type=='API'" @click="showCaseDialog(false,slotProps.row.case_id, 'check')">{{ slotProps.row.case_name }}</el-link>
              <el-link type="primary" v-else-if="slotProps.row.case_type=='SQL'" @click="showSqlDialog(false,slotProps.row.case_id, 'check')">{{ slotProps.row.case_name }}</el-link>
              <el-link type="primary" v-else-if="slotProps.row.case_type=='REDIS'" @click="showRedisDialog(false,slotProps.row.case_id, 'check')">{{ slotProps.row.case_name }}</el-link>
              <el-link type="primary" v-else-if="slotProps.row.case_type=='EXPORT'" @click="showExportDialog(false,slotProps.row.case_id, 'check')">{{ slotProps.row.case_name }}</el-link>
              <el-link type="primary" v-else-if="slotProps.row.case_type=='UPLOAD'" @click="showExportDialog(false,slotProps.row.case_id, 'check')">{{ slotProps.row.case_name }}</el-link>
            </template>
          </el-table-column>
          <el-table-column v-if="case_type === 'EXPORT' || 'UPLOAD'" label="案例类型" prop="case_type" min-width="60px" show-overflow-tooltip>
            <template v-slot="scopeProps">
              <span v-if="scopeProps.row.case_type==='EXPORT'">导出</span>
              <span v-if="scopeProps.row.case_type==='UPLOAD'">导入</span>
            </template>
          </el-table-column>
          <el-table-column v-if="queryInfo.type === 'API'" label="所属接口" prop="interface_id" min-width="60px" show-overflow-tooltip></el-table-column>
          <el-table-column v-if="queryInfo.type === 'API'" label="接口地址" prop="interface_uri" min-width="180px" show-overflow-tooltip></el-table-column>
          <el-table-column label="是否断言" prop="is_assert" min-width="60px">
            <template v-slot="scopeProps">
              <span v-if="scopeProps.row.is_assert">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column label="创建人" prop="creater" min-width="100px"></el-table-column>
          <el-table-column label="修改时间" prop="update_time" min-width="120px"></el-table-column>
          <el-table-column label="是否被引用" prop="is_related" show-overflow-tooltip min-width="100px">
            <template v-slot="scopeProps">
              <span v-if="scopeProps.row.is_related===1">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" min-width="140px">
            <template v-slot="slotProps">
              <el-button type="primary" v-if="slotProps.row.case_type=='API' " @click="showCaseDialog(false, slotProps.row.case_id, 'update')" icon="el-icon-edit" size="mini"></el-button>
              <el-button type="primary" v-if="slotProps.row.case_type=='SQL' " @click="showSqlDialog(false, slotProps.row.case_id, 'update')" icon="el-icon-edit" size="mini"></el-button>
              <el-button type="primary" v-if="slotProps.row.case_type=='REDIS' " @click="showRedisDialog(false, slotProps.row.case_id, 'update')" icon="el-icon-edit" size="mini"></el-button>
              <el-button type="primary" v-if="slotProps.row.case_type=='EXPORT' " @click="showExportDialog(false, slotProps.row.case_id, 'update')" icon="el-icon-edit" size="mini"></el-button>
              <el-button type="primary" v-if="slotProps.row.case_type=='UPLOAD' " @click="showExportDialog(false, slotProps.row.case_id, 'update')" icon="el-icon-edit" size="mini"></el-button>
              <el-button type="danger" v-if="slotProps.row.creater==user_name || role_id==0" icon="el-icon-delete" size="mini" @click="removeCaseById(slotProps.row.case_id)"></el-button>
              <el-tooltip class="item" effect="dark" content="复制案例" placement="top" :enterable="false">
              <el-button type="info" icon="el-icon-document-copy" size="mini" @click="copyCase(slotProps.row.case_id)"></el-button>
            </el-tooltip>
<!--            <el-tooltip class="item" effect="dark" content="调试案例" placement="top" :enterable="false">-->
<!--              <el-button type="success" icon="el-icon-caret-right" size="mini" @click="showCaseRunDialog(slotProps.row.case_id)"></el-button>-->
<!--            </el-tooltip>-->
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 接口案例新增编辑框 -->
    <el-dialog :visible.sync="caseDialogVisible"
                :title="caseDialogTitle"
                width="60%"
                @close="caseDialogClosed"
                :close-on-click-modal="false">
      <el-form :model="caseForm"
                label-width="100px"
                :rules="caseFormRules"
                ref="caseFormRef">
        <el-row>
          <el-col :span="24">
            <el-form-item label="案例名称" prop="case_name">
              <el-input v-model="caseForm.case_name" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="案例描述">
              <el-input v-model="caseForm.case_describe" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="案例等级">
              <el-select v-model="caseForm.case_level" placeholder="请选择">
                <el-option label="低" value="低"></el-option>
                <el-option label="普通" value="普通"></el-option>
                <el-option label="高" value="高"></el-option>
                <el-option label="紧急" value="紧急"></el-option>
                <el-option label="立刻" value="立刻"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属接口" prop="interface_id" v-if="type=='add'">
              <treeselect v-model="caseForm.interface_id"
                          :options="interfaceList"
                          placeholder="请选择"
                          :disable-branch-nodes="true" />
            </el-form-item>
            <el-form-item label="所属接口" v-else>
              <el-input v-model="caseForm.interface_name" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="接口地址">
              <el-input v-model="caseForm.interface_address" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="请求数据">
              <el-input type="textarea"
                        :rows="10"
                        placeholder="请输入json数据"
                        v-model="caseForm.interface_data"
                        @blur="transferJson">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否断言">
              <el-switch v-model="caseForm.is_assert"
                         active-color="#13ce66">
              </el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="caseForm.is_assert">
          <el-row>
          <el-col :span="12">
            <el-form-item label="断言类型">
              <el-input disabled value="数据匹配"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-button type="primary" @click="addMatchAssert">添加断言</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item>
          <el-table :data="matchAssertList" border width="60%">
            <el-table-column label="返回值" min-width="50px">
              <template v-slot="scopeProps">
                <el-input v-model="scopeProps.row.return_value"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="判断符" min-width="50px">
              <template v-slot="scopeProps">
                <el-select v-model="scopeProps.row.assert_operator">
                  <el-option value="1" label="等于"></el-option>
                  <el-option value="2" label="不等于"></el-option>
                  <el-option value="3" label="包含"></el-option>
                  <el-option value="4" label="不包含"></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="预期值" min-width="50px">
              <template v-slot:header>
                <span>预期值</span>
                <el-tooltip  placement="top" :enterable="true">
                  <template #content>
                    <div>
                      <p>目前可支持的内容：</p>
                      <p>1、中文、数字</p>
                      <p>2、变量 ： ${xx}$、${xx}$.xx、${xx}$.xx.xx</p>
                      <p>3、变量四则运算(加减乘除) ： ${xx}$+${xx}$.xx-${xx}$.xx*${xx}$.xx/2</p>
                      <p>4、round函数及多个round（加减乘除）：  round(${xx}$+${xx}$.xx-${xx}$.xx*${xx}$.xx/2,2) - round(${xx}$+${xx}$.xx-${xx}$.xx*${xx}$.xx/2,2)</p>
                      <p>5、最后结果百分比% ： (round(${xx}$+${xx}$.xx-${xx}$.xx*${xx}$.xx/2,2) - round(${xx}$+${xx}$.xx-${xx}$.xx*${xx}$.xx/2,2)) %</p>
                    </div>
                  </template>
                  <el-icon>
                    <i class="el-icon-info" style="font-size: 15px;"></i>
                  </el-icon>
                </el-tooltip>
              </template>
              <template v-slot="scopeProps">
                <el-input v-model="scopeProps.row.expect_result"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="删除" min-width="50px">
              <template v-slot="scopeProps">
                <el-button type="danger"
                          @click="deleteCase(true, scopeProps.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="断言类型">
              <el-input disabled value="四则运算"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-button type="primary" @click="addArithmeticAssert">添加断言</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item>
            <el-table :data="arithmeticAssertList" border width="60%">
              <el-table-column label="被运算值" min-width="50px">
                <template v-slot="scopeProps">
                  <el-input v-model="scopeProps.row.return_value"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="运算符" min-width="50px">
                <template v-slot="scopeProps">
                  <el-select v-model="scopeProps.row.assert_operator">
                    <el-option value="1" label="+"></el-option>
                    <el-option value="2" label="-"></el-option>
                    <el-option value="3" label="×"></el-option>
                    <el-option value="4" label="÷"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="运算值" min-width="50px">
                <template v-slot="scopeProps">
                  <el-input v-model="scopeProps.row.operation_value"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="运算结果" min-width="50px">
                <template v-slot="scopeProps">
                  <el-input v-model="scopeProps.row.expect_result"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="删除" min-width="50px">
                <template v-slot="scopeProps">
                  <el-button type="danger"
                            @click="deleteCase(false, scopeProps.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-row>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="type!=='check'">
        <el-button @click="caseDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitInterfaceCase">确 定</el-button>
      </span>
    </el-dialog>
    <!-- sql案例新增编辑框 -->
    <el-dialog :visible.sync="sqlDialogVisible"
                :title="caseDialogTitle"
                width="50%"
                @close="sqlDialogClosed"
                :close-on-click-modal="false">
      <el-form :model="sqlForm"
                label-width="100px"
                :rules="sqlFormRules"
                ref="sqlFormRef">
        <el-row>
          <el-col :span="24">
            <el-form-item label="案例名称" prop="case_name">
              <el-input v-model="sqlForm.case_name" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="案例描述">
              <el-input v-model="sqlForm.case_describe" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据库" prop="init_database">
              <el-select v-model="sqlForm.init_database" placeholder="请选择" filterable>
                <el-option v-for="item in databaseList"
                           :label="item.label"
                           :value="item.id"
                           :key="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="执行SQL">
              <el-input type="textarea"
                        :rows="5"
                        placeholder="请输入内容"
                        v-model="sqlForm.interface_data">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否断言">
              <el-switch v-model="sqlForm.is_assert"
                         active-color="#13ce66">
              </el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="sqlForm.is_assert">
          <el-row>
          <el-col :span="12">
            <el-form-item label="断言类型">
              <el-input disabled value="数据匹配"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-button type="primary" @click="addMatchAssert">添加断言</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item>
          <el-table :data="matchAssertList" border width="60%">
            <el-table-column label="返回值" min-width="50px">
              <template v-slot="scopeProps">
                <el-input v-model="scopeProps.row.return_value"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="判断符" min-width="50px">
              <template v-slot="scopeProps">
                <el-select v-model="scopeProps.row.assert_operator">
                  <el-option value="1" label="等于"></el-option>
                  <el-option value="2" label="不等于"></el-option>
                  <el-option value="3" label="包含"></el-option>
                  <el-option value="4" label="不包含"></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="预期值" min-width="50px">
              <template v-slot="scopeProps">
                <el-input v-model="scopeProps.row.expect_result"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="删除" min-width="50px">
              <template v-slot="scopeProps">
                <el-button type="danger"
                          @click="deleteCase(true, scopeProps.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="断言类型">
              <el-input disabled value="四则运算"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-button type="primary" @click="addArithmeticAssert">添加断言</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item>
            <el-table :data="arithmeticAssertList" border width="60%">
              <el-table-column label="被运算值" min-width="50px">
                <template v-slot="scopeProps">
                  <el-input v-model="scopeProps.row.return_value"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="运算符" min-width="50px">
                <template v-slot="scopeProps">
                  <el-select v-model="scopeProps.row.assert_operator">
                    <el-option value="1" label="+"></el-option>
                    <el-option value="2" label="-"></el-option>
                    <el-option value="3" label="×"></el-option>
                    <el-option value="4" label="÷"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="运算值" min-width="50px">
                <template v-slot="scopeProps">
                  <el-input v-model="scopeProps.row.operation_value"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="运算结果" min-width="50px">
                <template v-slot="scopeProps">
                  <el-input v-model="scopeProps.row.expect_result"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="删除" min-width="50px">
                <template v-slot="scopeProps">
                  <el-button type="danger"
                            @click="deleteCase(false, scopeProps.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-row>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="type!=='check'">
        <el-button @click="sqlDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitSqlCase">确 定</el-button>
      </span>
    </el-dialog>
    <!-- Redis案例新增编辑框 -->
    <el-dialog :visible.sync="redisDialogVisible"
                :title="caseDialogTitle"
                width="50%"
                @close="redisDialogClosed"
                :close-on-click-modal="false">
      <el-form :model="redisForm"
                label-width="100px"
                :rules="redisFormRules"
                ref="redisFormRef">
        <el-row>
          <el-col :span="24">
            <el-form-item label="案例名称" prop="case_name">
              <el-input v-model="redisForm.case_name" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="案例描述">
              <el-input v-model="redisForm.case_describe" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Redis库" prop="init_database">
              <el-select v-model="redisForm.init_database" placeholder="请选择" filterable>
                <el-option v-for="item in redisList"
                           :label="item.label"
                           :value="item.id"
                           :key="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="查询key">
              <el-input type="textarea"
                        :rows="5"
                        placeholder="请输入内容"
                        v-model="redisForm.interface_data">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否断言">
              <el-switch v-model="redisForm.is_assert"
                         active-color="#13ce66">
              </el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="redisForm.is_assert">
          <el-row>
          <el-col :span="12">
            <el-form-item label="断言类型">
              <el-input disabled value="数据匹配"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-button type="primary" @click="addMatchAssert">添加断言</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item>
          <el-table :data="matchAssertList" border width="60%">
            <el-table-column label="返回值" min-width="50px">
              <template v-slot="scopeProps">
                <el-input v-model="scopeProps.row.return_value"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="判断符" min-width="50px">
              <template v-slot="scopeProps">
                <el-select v-model="scopeProps.row.assert_operator">
                  <el-option value="1" label="等于"></el-option>
                  <el-option value="2" label="不等于"></el-option>
                  <el-option value="3" label="包含"></el-option>
                  <el-option value="4" label="不包含"></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="预期值" min-width="50px">
              <template v-slot="scopeProps">
                <el-input v-model="scopeProps.row.expect_result"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="删除" min-width="50px">
              <template v-slot="scopeProps">
                <el-button type="danger"
                          @click="deleteCase(true, scopeProps.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="断言类型">
              <el-input disabled value="四则运算"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-button type="primary" @click="addArithmeticAssert">添加断言</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item>
            <el-table :data="arithmeticAssertList" border width="60%">
              <el-table-column label="被运算值" min-width="50px">
                <template v-slot="scopeProps">
                  <el-input v-model="scopeProps.row.return_value"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="运算符" min-width="50px">
                <template v-slot="scopeProps">
                  <el-select v-model="scopeProps.row.assert_operator">
                    <el-option value="1" label="+"></el-option>
                    <el-option value="2" label="-"></el-option>
                    <el-option value="3" label="×"></el-option>
                    <el-option value="4" label="÷"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="运算值" min-width="50px">
                <template v-slot="scopeProps">
                  <el-input v-model="scopeProps.row.operation_value"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="运算结果" min-width="50px">
                <template v-slot="scopeProps">
                  <el-input v-model="scopeProps.row.expect_result"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="删除" min-width="50px">
                <template v-slot="scopeProps">
                  <el-button type="danger"
                            @click="deleteCase(false, scopeProps.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-row>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="type!=='check'">
        <el-button @click="redisDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitRedisCase">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 导出解析案例新增编辑框 -->
    <el-dialog :visible.sync="exportDialogVisible"
                :title="caseDialogTitle"
                width="60%"
                @close="exportDialogClosed"
                :close-on-click-modal="false">
      <el-form :model="exportForm"
                label-width="100px"
                :rules="exportFormRules"
                ref="exportFormRef">
        <el-row>
          <el-col :span="24">
            <el-form-item label="案例名称" prop="case_name">
              <el-input v-model="exportForm.case_name" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="案例描述">
              <el-input v-model="exportForm.case_describe" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="案例等级">
              <el-select v-model="exportForm.case_level" placeholder="请选择">
                <el-option label="低" value="低"></el-option>
                <el-option label="普通" value="普通"></el-option>
                <el-option label="高" value="高"></el-option>
                <el-option label="紧急" value="紧急"></el-option>
                <el-option label="立刻" value="立刻"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属接口" prop="interface_id" v-if="type=='add'">
              <treeselect v-model="exportForm.interface_id"
                          :options="interfaceList"
                          placeholder="请选择"
                          :disable-branch-nodes="true" />
            </el-form-item>
            <el-form-item label="所属接口" v-else>
              <el-input v-model="exportForm.interface_name" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="接口地址">
              <el-input v-model="exportForm.interface_address" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="模板地址" prop="temp_file" v-if="exportForm.case_type==='UPLOAD'">
              <el-input v-model="exportForm.temp_file"></el-input>
              <el-upload
              ref="Files"
              :action="uploadPath"
              :headers="headers"
              class="upload-demo"
              :auto-upload="true"
              :on-change="handleFileChange"
              :on-remove="handleRemove"
              :on-exceed="handleExceed"
              :on-success="(response, file) => handleUploadSuccess(response, file, 'template')"
              :file-list="fileList"
              :limit="1"
              :data="{ is_template: true }">
              <el-button type="primary">选择文件</el-button>
            </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="案例类型" prop="case_type">
              <el-radio-group v-model="exportForm.case_type">
                <el-radio label="UPLOAD">导入</el-radio>
                <el-radio label="EXPORT">导出</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="4" v-if="exportForm.case_type==='UPLOAD'">
            <el-form-item label="上传行数" prop="upload_min_rows">
              <el-input
                v-model="exportForm.upload_min_rows"
                label="最小值"
                placeholder="最小值"
                style=" width: 100px"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4" v-if="exportForm.case_type==='UPLOAD'">
            <el-form-item label="至">
              <el-tooltip class="item" effect="dark" content="为空表示不限制行数,如最小值=2,最大值为空,则表示上传行数为第2行到最后一行" placement="top">
                <el-input
                  v-model="exportForm.upload_max_rows"
                  label="最大值"
                  placeholder="最大值"
                  style=" width: 100px"
                ></el-input>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="exportForm.case_type==='UPLOAD'">
          <el-form-item label="文件上传" prop="is_upload">
            <el-input v-model="exportForm.is_upload" v-show="false"></el-input>
            <el-upload
              ref="Files"
              :action="uploadPath"
              :headers="headers"
              class="upload-demo"
              :auto-upload="true"
              :on-change="handleFileChange"
              :on-remove="handleRemove"
              :on-exceed="handleExceed"
              :on-success="handleUploadSuccess"
              :file-list="fileList"
              :limit="1">
              <el-button type="primary">选择文件</el-button>
            </el-upload>
          </el-form-item>
        </el-row>
        <el-row v-if="exportForm.case_type==='UPLOAD'">
          <el-col>
            <el-form-item label="请求数据">
              <el-input type="textarea"
                        :rows="10"
                        placeholder="请输入json数据"
                        v-model="exportForm.upload_param"
                        @blur="transferJson">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否断言">
              <el-switch v-model="exportForm.is_assert" @change="handleExportFormIsAssertSwitch"
                         active-color="#13ce66">
              </el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="exportForm.is_assert">
          <el-row>
          <el-col :span="12">
            <el-form-item label="断言类型">
              <el-input disabled value="数据匹配"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-button type="primary" @click="addMatchAssert">添加断言</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item>
          <el-table :data="matchAssertList" border width="60%">
            <el-table-column label="断言字段" min-width="50px">
              <template v-slot="scopeProps">
                <el-input v-model="scopeProps.row.return_value" :disabled="scopeProps.$index === 0 && exportForm.case_type === 'EXPORT'"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="判断符" min-width="50px">
              <template v-slot="scopeProps">
                <el-select v-model="scopeProps.row.assert_operator" :disabled="scopeProps.$index === 0 && exportForm.case_type === 'EXPORT'">
                  <el-option value="1" label="等于"></el-option>
                  <el-option value="2" label="不等于"></el-option>
                  <el-option value="3" label="包含"></el-option>
                  <el-option value="4" label="不包含"></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="预期值" min-width="50px">
              <template v-slot:header>
                <span>预期值</span>
                <el-tooltip  placement="top" :enterable="true">
                  <template #content>
                    <div>
                      <p>目前可支持的内容：</p>
                      <p>1、中文、数字</p>
                      <p>2、变量 ： ${xx}$、${xx}$.xx、${xx}$.xx.xx</p>
                      <p>3、变量四则运算(加减乘除) ： ${xx}$+${xx}$.xx-${xx}$.xx*${xx}$.xx/2</p>
                      <p>4、round函数及多个round（加减乘除）：  round(${xx}$+${xx}$.xx-${xx}$.xx*${xx}$.xx/2,2) - round(${xx}$+${xx}$.xx-${xx}$.xx*${xx}$.xx/2,2)</p>
                      <p>5、最后结果百分比% ： (round(${xx}$+${xx}$.xx-${xx}$.xx*${xx}$.xx/2,2) - round(${xx}$+${xx}$.xx-${xx}$.xx*${xx}$.xx/2,2)) %</p>
                    </div>
                  </template>
                  <el-icon>
                    <i class="el-icon-info" style="font-size: 15px;"></i>
                  </el-icon>
                </el-tooltip>
              </template>
              <template v-slot="scopeProps">
                <el-input v-model="scopeProps.row.expect_result"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="删除" min-width="50px">
              <template v-slot="scopeProps">
                <el-button :disabled="scopeProps.$index === 0 && scopeProps.row.return_value === 'ROW' && exportForm.case_type === 'EXPORT'" type="danger"
                          @click="deleteCase(true, scopeProps.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="断言类型">
              <el-input disabled value="四则运算"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-button disabled type="primary" @click="addArithmeticAssert">添加断言</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item>
            <el-table :data="arithmeticAssertList" border width="60%">
              <el-table-column label="被运算值" min-width="50px">
                <template v-slot="scopeProps">
                  <el-input v-model="scopeProps.row.return_value"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="运算符" min-width="50px">
                <template v-slot="scopeProps">
                  <el-select v-model="scopeProps.row.assert_operator">
                    <el-option value="1" label="+"></el-option>
                    <el-option value="2" label="-"></el-option>
                    <el-option value="3" label="×"></el-option>
                    <el-option value="4" label="÷"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="运算值" min-width="50px">
                <template v-slot="scopeProps">
                  <el-input v-model="scopeProps.row.operation_value"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="运算结果" min-width="50px">
                <template v-slot="scopeProps">
                  <el-input v-model="scopeProps.row.expect_result"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="删除" min-width="50px">
                <template v-slot="scopeProps">
                  <el-button type="danger"
                            @click="deleteCase(false, scopeProps.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-row>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="type!=='check'">
        <el-button @click="exportDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitExportCase">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 环境选择对话框 -->
    <el-dialog :visible.sync="enviromentDialogVisible"
                width="30%"
                @close="enviromentDialogClosed"
                :close-on-click-modal="false">
      <el-form label-width="130px"
               :model="caseRunForm"
               ref="caseRunFormRef"
               :rules="caseRunFormRules">
        <el-form-item label="请选择运行环境" prop="env_type">
          <el-select v-model="caseRunForm.env_type">
            <el-option label="测试环境" value="1"></el-option>
            <el-option label="预发环境" value="2"></el-option>
            <el-option label="生产环境" value="3"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="enviromentDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitCaseRun">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 运行结果展示框 -->
    <el-dialog :visible.sync="runResultDialogVisible"
               width="60%"
               :close-on-click-modal="false"
               @close="resultDialogClosed">
      <template slot="title">
        <p style="font-weight: 600;font-size: 16px">{{ resultForm.msg }}</p>
      </template>
      <template v-if="resultForm.code">
        <div v-if="resultForm.code!='0000'" style="color: red;font-size:15px;font-weight: bold;margin-bottom: 10px;">案例执行出错！</div>
        <div v-else style="color: #67C23A;font-size:15px;font-weight: bold;margin-bottom: 10px;">案例执行完成！</div>
      </template>
      <el-collapse>
        <el-collapse-item v-for="(item, index) in resultForm.data" :key="index">
          <template slot="title">
            <span v-if="item.assert==='0'" style="color: red;font-size:15px;">{{ item.title }}</span>
            <span v-else style="font-size:15px;">{{ item.title }}</span>
          </template>
          <template slot="name">
            {{ index }}
          </template>
            <div v-for="(detail, index) in item.detail"
                 style="color: #505050;"
                 :key="index">{{ detail }}</div>
        </el-collapse-item>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="runResultDialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import { baseUrl } from '../../main'
export default {
  data() {
    return {
      model: 'case_info',
      user_name: window.localStorage.getItem('user_name'),
      role_id: window.localStorage.getItem('role_id'),
      queryInfo: {
        search_case_id: null,
        search_interface_id: null,
        search_case_name: '',
        search_case_uri: '',
        creater: window.localStorage.getItem('user_name'),
        pagenum: 1,
        pagesize: 5,
        type: 'API'
      },
      caseList: [],
      isAdd: true,
      caseForm: {
        case_type: 'API',
        case_name: '',
        interface_id: null,
        interface_address: '',
        case_describe: '',
        case_level: '',
        is_init: false,
        init_database: null,
        init_sql: '',
        is_back: false,
        back_sql: '',
        is_encrypt: false,
        interface_data: '',
        is_assert: false,
        asserts: '',
        csv_file: null,
        header: null
      },
      apiForm: {},
      caseDialogTitle: '',
      caseFormRules: {
        interface_id: [
          { required: true, message: '请选择所属接口', trigger: 'blur' }
        ],
        case_name: [
          { required: true, message: '请输入案例名称', trigger: 'blur' }
        ]
      },
      total: 0,
      caseDialogVisible: false,
      interfaceList: [],
      databaseList: [],
      redisList: [],
      exportList: [],
      matchAssertList: [],
      arithmeticAssertList: [],
      enviromentDialogVisible: false,
      caseRunForm: {
        case_id: '',
        env_type: '1'
      },
      caseRunFormRules: {
        env_type: [
          { required: true, message: '请选择运行环境', trigger: 'blur' }
        ]
      },
      resultForm: {},
      runResultDialogVisible: false,
      sqlDialogVisible: false,
      sqlForm: {
        case_type: 'SQL',
        case_name: '',
        case_describe: '',
        init_database: null,
        interface_data: '',
        is_assert: false,
        asserts: ''
      },
      sqlFormRules: {
        case_name: [
          { required: true, message: '请输入案例名称', trigger: 'blur' }
        ],
        init_database: [
          { required: true, message: '请选择数据库', trigger: 'blur' }
        ],
        interface_data: [
          { required: true, message: '请输入执行SQL', trigger: 'blur' }
        ]
      },
      redisDialogVisible: false,
      redisForm: {
        case_type: 'REDIS',
        case_name: '',
        case_describe: '',
        init_database: null,
        interface_data: '',
        is_assert: false,
        asserts: ''
      },
      redisFormRules: {
        case_name: [
          { required: true, message: '请输入案例名称', trigger: 'blur' }
        ],
        init_database: [
          { required: true, message: '请选择redis库', trigger: 'blur' }
        ],
        interface_data: [
          { required: true, message: '请输入redis key', trigger: 'blur' }
        ]
      },
      exportDialogVisible: false,
      exportForm: {
        case_type: 'UPLOAD',
        case_name: '',
        interface_id: null,
        interface_address: '',
        case_describe: '',
        case_level: '',
        is_init: false,
        init_database: '',
        init_sql: '',
        is_back: false,
        back_sql: '',
        is_encrypt: false,
        interface_data: '',
        is_assert: false,
        asserts: '',
        csv_file: null,
        header: null,
        file_path: '',
        upload_min_rows: '',
        upload_max_rows: '',
        is_upload: '',
        temp_file: '',
        upload_param: ''
      },
      exportFormRules: {
        case_name: [
          { required: true, message: '请输入案例名称', trigger: 'blur' }
        ],
        interface_id: [
          { required: true, message: '请选择所属接口', trigger: 'blur' }
        ],
        upload_min_rows: [
          { required: true, message: '请输入最小行数', trigger: 'blur' }
        ],
        is_upload: [
          { required: true, message: '请上传文件', trigger: 'blur' }
        ],
        temp_file: [
          { required: true, message: '请输入模板下载地址', trigger: 'blur' }
        ]
      },
      headers: {
        Authorization: window.localStorage.getItem('token'),
        userId: window.localStorage.getItem('userId')
      },
      fileList: [],
      type: '',
      getCaseListLoading: false
    }
  },
  computed: {
    uploadPath() {
      // 动态生成上传路径
      // const baseURl = axios.defaults.baseURL
      return `${baseUrl}user_info/upload_file/`
    }
  },
  created() {
    if (this.$route.query.from) {
      this.queryInfo.creater = ''
      this.queryInfo.search_interface_id = this.$route.query.from
    }
    this.getCaseList()
    this.getInterfaceList()
    this.getDatabaseList()
    this.getRedisList()
    this.addCaseByInterface()
  },
  activated() {
    if (this.$route.query.from) {
      this.queryInfo.creater = ''
      this.queryInfo.search_case_id = ''
      this.queryInfo.search_case_name = ''
      this.queryInfo.search_case_uri = ''
      this.queryInfo.search_interface_id = this.$route.query.from
    }
    this.getCaseList()
    this.getInterfaceList()
    this.getDatabaseList()
    this.getRedisList()
    this.addCaseByInterface()
  },
  watch: {
    'caseForm.interface_id': 'interfaceChange',
    'exportForm.interface_id': 'exprotInterfaceChange',
    caseDialogVisible(val) {
      if (!val) {
        this.arithmeticAssertList = []
      }
    },
    // 当case_type=export时,将上传行数置为空
    'exportForm.case_type'(newVal) {
      if (newVal === 'EXPORT') {
        this.exportForm.upload_min_rows = ''
        this.exportForm.upload_max_rows = ''
      }
    },
    'exportForm.upload_min_rows'(newVal) {
      // 最小值不能小于1
      if (newVal < 1) {
        this.exportForm.upload_min_rows = 1
      }
      // 如果最小值大于最大值，将最大值设置为与最小值相等
      if (newVal && this.exportForm.upload_max_rows && newVal > this.exportForm.upload_max_rows) {
        this.exportForm.upload_max_rows = newVal
      }
    },
    'exportForm.upload_max_rows'(newVal) {
      // 最大值不能小于最小值
      if (newVal && newVal < this.exportForm.upload_min_rows) {
        this.exportForm.upload_max_rows = this.exportForm.upload_min_rows
      }
    }
  },
  methods: {
    queryCase() {
      this.queryInfo.pagenum = 1
      this.getCaseList()
    },
    async getCaseList() {
      this.getCaseListLoading = true
      const { data: res } = await this.$http.get(this.model + '/case_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取案例列表失败')
      this.caseList = res.data.cases
      this.total = res.data.total
      this.getCaseListLoading = false
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getCaseList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getCaseList()
    },
    caseDialogClosed() {
      this.$refs.caseFormRef.resetFields()
      this.caseForm = {
        case_type: 'API',
        case_name: '',
        interface_id: null,
        interface_address: '',
        case_describe: '',
        case_level: '',
        is_init: false,
        init_database: '',
        init_sql: '',
        is_back: false,
        back_sql: '',
        is_encrypt: false,
        interface_data: '',
        is_assert: false,
        asserts: '',
        csv_file: null,
        header: null
      }
      this.matchAssertList = []
      this.arithmeticAssertList = []
      this.fileList = []
      if (this.$route.fullPath !== '/api-cases') {
        this.$router.push('/api-cases')
      }
    },
    async showCaseDialog(isAdd, id, type) {
      this.matchAssertList = []
      this.type = type
      if (this.type === 'add') {
        this.caseDialogTitle = '新增接口案例'
      } else if (this.type === 'update') {
        this.caseDialogTitle = '编辑接口案例'
      } else if (this.type === 'check') {
        this.caseDialogTitle = '查看接口案例'
      }
      this.isAdd = isAdd
      if (!isAdd) {
        this.matchAssertList = []
        const { data: res } = await this.$http.get('/user_info/get_base_info/', { params: { id: id, type: 'API' } })
        if (res.meta.status !== 200) return this.$message.error('获取案例编辑信息失败')
        this.caseForm = res.data
        this.caseForm.interface_id = res.data.interface_id
        this.caseForm.asserts.forEach(item => {
          if (item.assert_type === '1') {
            this.matchAssertList.push(item)
          } else if (item.assert_type === '2') {
            this.arithmeticAssertList.push(item)
          }
        })
      }
      this.caseDialogVisible = true
    },
    async showSqlDialog(isAdd, id, type) {
      this.matchAssertList = []
      this.type = type
      if (this.type === 'add') {
        this.caseDialogTitle = '新增SQL案例'
      } else if (this.type === 'update') {
        this.caseDialogTitle = '编辑SQL案例'
      } else if (this.type === 'check') {
        this.caseDialogTitle = '查看SQL案例'
      }
      this.caseDialogTitle = isAdd ? '新增SQL案例' : '编辑SQL案例'
      this.isAdd = isAdd
      if (!isAdd) {
        const { data: res } = await this.$http.get('/user_info/get_base_info/', { params: { id: id, type: 'API' } })
        if (res.meta.status !== 200) return this.$message.error('获取SQL案例信息失败')
        this.sqlForm = res.data
        this.sqlForm.init_database = parseInt(this.sqlForm.init_database)
        this.sqlForm.asserts.forEach(item => {
          if (item.assert_type === '1') {
            this.matchAssertList.push(item)
          } else if (item.assert_type === '2') {
            this.arithmeticAssertList.push(item)
          }
        })
      }
      this.sqlDialogVisible = true
    },
    async showRedisDialog(isAdd, id, type) {
      this.type = type
      if (this.type === 'add') {
        this.caseDialogTitle = '新增Redis案例'
      } else if (this.type === 'update') {
        this.caseDialogTitle = '编辑Redis案例'
      } else if (this.type === 'check') {
        this.caseDialogTitle = '查看Redis案例'
      }
      this.caseDialogTitle = isAdd ? '新增Redis案例' : '编辑Redis案例'
      this.isAdd = isAdd
      if (!isAdd) {
        const { data: res } = await this.$http.get('/user_info/get_base_info/', { params: { id: id, type: 'API' } })
        if (res.meta.status !== 200) return this.$message.error('获取Redis案例信息失败')
        this.redisForm = res.data
        this.redisForm.init_database = parseInt(this.redisForm.init_database)
        this.redisForm.asserts.forEach(item => {
          if (item.assert_type === '1') {
            this.matchAssertList.push(item)
          } else if (item.assert_type === '2') {
            this.arithmeticAssertList.push(item)
          }
        })
      }
      this.redisDialogVisible = true
    },
    async showExportDialog(isAdd, id, type) {
      this.matchAssertList = []
      this.type = type
      if (this.type === 'add') {
        this.caseDialogTitle = '新增导入/导出解析案例'
      } else if (this.type === 'update') {
        this.caseDialogTitle = '编辑导入/导出解析案例'
      } else if (this.type === 'check') {
        this.caseDialogTitle = '查看导入/导出解析案例'
      }
      this.caseDialogTitle = isAdd ? '新增导入/导出解析案例' : '编辑导入/导出解析案例'
      this.isAdd = isAdd
      if (!isAdd) {
        const { data: res } = await this.$http.get('/user_info/get_base_info/', { params: { id: id, type: 'API' } })
        if (res.meta.status !== 200) return this.$message.error('获取导入/导出解析案例信息失败')
        this.exportForm = res.data
        this.exportForm.init_database = parseInt(this.exportForm.init_database)
        if (this.type === 'upload') {
          this.fileList = [{
            name: res.data.file_path.split('/').pop(), // 从文件路径中提取文件名
            url: res.data.file_path, // 文件路径
            status: 'success' // 设置文件状态为成功
          }]
        }
        this.exportForm.is_upload = 1
        this.exportForm.file_path = ''
        this.exportForm.asserts.forEach(item => {
          if (item.assert_type === '1') {
            this.matchAssertList.push(item)
          } else if (item.assert_type === '2') {
            this.arithmeticAssertList.push(item)
          }
        })
      }
      this.exportDialogVisible = true
    },
    removeCaseById(id) {
      this.$confirm('此操作将永久删除该案例, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete(this.model + '/' + id + '/case_delete/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error(result.data.meta.msg)
          this.$message.success('删除案例成功')
          this.getCaseList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async getInterfaceList() {
      const { data: res } = await this.$http.get('interface_info/get_api_list/?api_type=interfaces')
      if (res.meta.status !== 200) return this.$message.error('获取接口信息失败')
      this.interfaceList = res.data
    },
    async getDatabaseList() {
      const { data: res } = await this.$http.get('user_info/info/', { params: { type: 'databases' } })
      if (res.meta.status !== 200) return this.$message.error('获取数据库信息失败')
      this.databaseList = res.data
    },
    async getRedisList() {
      const { data: res } = await this.$http.get('user_info/info/', { params: { type: 'redis' } })
      if (res.meta.status !== 200) return this.$message.error('获取Redis信息失败')
      this.redisList = res.data
    },
    addMatchAssert() {
      this.matchAssertList.push({
        assert_type: '1',
        return_value: '',
        assert_operator: '1',
        expect_result: '',
        operation_value: ''
      })
    },
    deleteCase(isInterface, index) {
      if (isInterface) {
        this.matchAssertList.splice(index, 1)
      } else {
        this.arithmeticAssertList.splice(index, 1)
      }
    },
    addArithmeticAssert() {
      this.arithmeticAssertList.push({
        assert_type: '2',
        return_value: '',
        assert_operator: '1',
        expect_result: '',
        operation_value: ''
      })
    },
    submitInterfaceCase() {
      this.$refs.caseFormRef.validate(async valid => {
        if (!valid) return false
        const asserts = [...this.matchAssertList, ...this.arithmeticAssertList]
        this.caseForm.asserts = asserts
        if (this.caseForm.is_assert) {
          if (this.caseForm.asserts.length === 0) return this.$message.error('断言为是，请填写断言内容')
        }
        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/case_add/', this.caseForm)
          if (res.meta.status !== 200) return this.$message.error('新增接口案例失败')
          this.$message.success('新增接口案例成功')
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.caseForm.case_id + '/case_update/', this.caseForm)
          if (res.meta.status !== 200) return this.$message.error('编辑接口案例失败')
          this.$message.success('编辑接口案例成功')
        }
        this.caseDialogVisible = false
        this.getCaseList()
      })
    },
    async copyCase(id) {
      const { data: res } = await this.$http.get(this.model + '/' + id + '/case_copy/', { params: { type: 'apiCase' } })
      if (res.meta.status !== 200) return this.$message.error('复制案例失败')
      this.$message.success('复制案例成功')
      this.getCaseList()
    },
    async showCaseRunDialog(id) {
      this.caseRunForm.case_id = id
      this.enviromentDialogVisible = true
    },
    async submitCaseRun() {
      const { data: res } = await this.$http.post(this.model + '/' + this.caseRunForm.case_id + '/case_run/', this.caseRunForm)
      if (res.meta.status !== 200) return this.$message.error('运行案例失败')
      this.$message.success('运行案例成功')
      this.resultForm = res.data
      this.enviromentDialogVisible = false
      this.runResultDialogVisible = true
    },
    enviromentDialogClosed() {
      this.$refs.caseRunFormRef.resetFields()
    },
    sqlDialogClosed() {
      this.$refs.sqlFormRef.resetFields()
      this.sqlForm = {
        case_type: 'SQL',
        case_name: '',
        case_describe: '',
        init_database: null,
        interface_data: '',
        is_assert: false,
        asserts: ''
      }
      this.matchAssertList = []
      this.arithmeticAssertList = []
    },
    submitSqlCase() {
      this.$refs.sqlFormRef.validate(async valid => {
        if (!valid) return false
        const asserts = [...this.matchAssertList, ...this.arithmeticAssertList]
        this.sqlForm.asserts = asserts
        if (this.sqlForm.is_assert) {
          if (this.sqlForm.asserts.length === 0) return this.$message.error('断言为是，请填写断言内容')
        }
        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/case_add/', this.sqlForm)
          if (res.meta.status !== 200) return this.$message.error('新增SQL案例失败')
          this.$message.success('新增SQL案例成功')
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.sqlForm.case_id + '/case_update/', this.sqlForm)
          if (res.meta.status !== 200) return this.$message.error('编辑SQL案例失败')
          this.$message.success('编辑SQL案例成功')
        }
        this.sqlDialogVisible = false
        this.getCaseList()
      })
    },
    redisDialogClosed() {
      this.$refs.redisFormRef.resetFields()
      this.redisForm = {
        case_type: 'REDIS',
        case_name: '',
        case_describe: '',
        init_database: null,
        interface_data: '',
        is_assert: false,
        asserts: ''
      }
      this.matchAssertList = []
      this.arithmeticAssertList = []
    },
    submitRedisCase() {
      this.$refs.redisFormRef.validate(async valid => {
        if (!valid) return false
        const asserts = [...this.matchAssertList, ...this.arithmeticAssertList]
        this.redisForm.asserts = asserts
        if (this.redisForm.is_assert) {
          if (this.redisForm.asserts.length === 0) return this.$message.error('断言为是，请填写断言内容')
        }
        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/case_add/', this.redisForm)
          if (res.meta.status !== 200) return this.$message.error('新增Redis案例失败')
          this.$message.success('新增Redis案例成功')
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.redisForm.case_id + '/case_update/', this.redisForm)
          if (res.meta.status !== 200) return this.$message.error('编辑Redis案例失败')
          this.$message.success('编辑Redis案例成功')
        }
        this.redisDialogVisible = false
        this.getCaseList()
      })
    },
    exportDialogClosed() {
      this.$refs.exportFormRef.resetFields()
      this.exportForm = {
        case_type: 'UPLOAD',
        case_name: '',
        interface_id: null,
        interface_address: '',
        case_describe: '',
        case_level: '',
        is_init: false,
        init_database: '',
        init_sql: '',
        is_back: false,
        back_sql: '',
        is_encrypt: false,
        interface_data: '',
        is_assert: false,
        asserts: '',
        csv_file: null,
        header: null,
        fileList: [],
        file: ''
      }
      this.matchAssertList = []
      this.arithmeticAssertList = []
      this.$refs.Files.clearFiles()
    },
    submitExportCase() {
      this.$refs.exportFormRef.validate(async valid => {
        if (!valid) return false
        const asserts = [...this.matchAssertList, ...this.arithmeticAssertList]
        this.exportForm.asserts = asserts
        if (this.exportForm.is_assert) {
          if (this.exportForm.asserts.length === 0) return this.$message.error('断言为是，请填写断言内容')
          // 判断断言字段是否有重复项
          let flag = false
          this.matchAssertList.forEach(item => {
            if (this.matchAssertList.some(item2 => item !== item2 && item.return_value === item2.return_value)) flag = true
          })
          if (flag) return this.$message.error('断言字段不能有重复项')
        }
        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/case_add/', this.exportForm)
          if (res.meta.status !== 200) return this.$message.error('新增导出解析案例失败')
          this.$message.success('新增导出解析案例成功')
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.exportForm.case_id + '/case_update/', this.exportForm)
          if (res.meta.status !== 200) return this.$message.error('编辑导出解析案例失败')
          this.$message.success('编辑导出解析案例成功')
        }
        this.exportDialogVisible = false
        this.getCaseList()
      })
    },
    addCaseByInterface() {
      const projectId = this.$route.query.projectId
      const interfaceId = this.$route.query.interfaceId
      const interfaceName = this.$route.query.interfaceName
      if (projectId && interfaceId) {
        this.showCaseDialog(true, 0, 'add')
        this.caseForm.interface_id = projectId + '-' + interfaceId
        this.caseForm.case_name = interfaceName
      }
    },
    transferJson () {
      if (this.caseForm.interface_data) {
        this.caseForm.interface_data = this.caseForm.interface_data.replace(/:\s*@{/g, ':"*@{').replace(/:\s*\[@{/g, ':["*@{').replace(/:\s*{@{/g, ':{"*@{').replace(/:\s*\${/g, ':"*${').replace(/:\s*\[\${/g, ':["*${').replace(/:\s*{\${/g, ':{"*${')
        this.caseForm.interface_data = this.caseForm.interface_data.replace(/}@,/g, '}@*",').replace(/}@]/g, '}@*"]').replace(/}@}/g, '}@*"}').replace(/}\$,/g, '}$*",').replace(/}\$]/g, '}$*"]').replace(/}\$}/g, '}$*"}')
        this.caseForm.interface_data = JSON.parse(this.caseForm.interface_data)
        this.caseForm.interface_data = JSON.stringify(this.caseForm.interface_data, null, 4)
        this.caseForm.interface_data = this.caseForm.interface_data.replace(/"\*/g, '').replace(/\*"/g, '')
      }
    },
    async interfaceChange(value) {
      if (value && this.isAdd) {
        // value来源有两个,一个是从interface页面传过来的,另一个是在case页面通过InterfaceList获取到的id,此id的格式为域名-接口ID,因此需要做处理只保留接口ID
        if (value.includes('-')) {
          value = value.split('-')[1]
        }
        const queryInfo = {
          search_interface_id: value,
          search_interface_address: '',
          pagenum: 1,
          pagesize: 1
        }
        const { data: res } = await this.$http.get('interface_info/interface_list/', { params: queryInfo })
        if (res.meta.status !== 200) return this.$message.error('获取接口地址失败')
        this.caseForm.interface_address = res.data.data[0].interface_address
        this.caseForm.header = res.data.data[0].header
      }
    },
    async exprotInterfaceChange(value) {
      if (value && this.isAdd) {
        if (value.includes('-')) {
          value = value.split('-')[1]
        }
        const queryInfo = {
          search_interface_id: value,
          search_interface_address: '',
          pagenum: 1,
          pagesize: 1
        }
        const { data: res } = await this.$http.get('interface_info/interface_list/', { params: queryInfo })
        if (res.meta.status !== 200) return this.$message.error('获取接口地址失败')
        this.exportForm.interface_address = res.data.data[0].interface_address
        this.exportForm.header = res.data.data[0].header
      }
    },
    handleAvatarSuccess(res) {
      this.caseForm.csv_file = res.data.file_path
    },
    resultDialogClosed() {
      this.resultForm = {}
    },
    downloadFile() {
      window.location.href = 'http://**************:8000/api/case_info/download_csv/?csv_file=' + this.caseForm.csv_file
    },
    // 捕获导出解析案例是否断言按钮切换事件,当按钮打开,并且无断言内容时,初始化第一行断言字段为ROW
    handleExportFormIsAssertSwitch(status) {
      if (status && this.matchAssertList.length === 0 && this.exportForm.case_type === 'EXPORT') {
        this.matchAssertList.push({ assert_type: '1', return_value: 'ROW', assert_operator: '1', expect_result: '', operation_value: '' })
      } else {
        this.matchAssertList = []
      }
    },
    handleFileChange(file, fileList) {
      this.exportForm.file = file
    },
    handleRemove(file, fileList) {
      this.exportForm.file = null
      this.exportForm.is_upload = ''
      this.$forceUpdate()
    },
    handleExceed(files, fileList) {
      this.$message.warning('请先删除已上传文件后重新上传!')
      // const f = {
      //   name: files[0].name,
      //   percentage: 0,
      //   raw: files[0],
      //   size: files[0].size,
      //   status: 'ready',
      //   uid: Date.now()
      // }
      // // this.exportForm.file = f
      // this.$refs.Files.clearFiles()
      // this.$refs.Files.uploadFiles = [f]
      // this.$forceUpdate()
    },
    handleUploadSuccess(response, file, uploadType) {
      this.exportForm.is_upload = 1
      this.exportForm.file_path = response.data.file_path
      if (response.meta.status !== 200) return this.$message.error('文件上传失败')
      this.$message.success('文件上传成功！')
      // 判断是否是模板地址上传
      if (uploadType === 'template') {
        this.exportForm.temp_file = response.data.file_path
      }
      // this.$refs.Files.clearFiles()
    }
  }
}
</script>
