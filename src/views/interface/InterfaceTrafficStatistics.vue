<template>
  <div>
    <el-card>
      <el-row>
        <div class="title">接口访问流量TOP{{barQueryInfo.top_num}}</div>
      </el-row>
      <el-row>
        <div class="time">统计时间范围：{{ this.barQueryInfo.start_date }}至{{ this.barQueryInfo.end_date }}</div>
      </el-row>
      <div style="margin:30px auto;">
        <el-row :gutter="20">
          <el-col :span="6" :offset="6">
           <el-date-picker
              v-model="barQueryDate"
              :clearable="false"
              type="daterange"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd"
              disabledDate="false">
            </el-date-picker>
          </el-col>
<!--          <el-col style="color: black;font-size: small">TOP</el-col>-->
<!--          <el-col :span="2" style="margin-left: 10px">-->
          <el-col :span="2" :offset="1">
            <el-tooltip content="请输入TOP值" placement="top">
              <el-input v-model="barQueryInfo.top_num"
                        placeholder="TOP"
                        clearable>
              </el-input>
            </el-tooltip>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-button type="primary" @click="queryBar()">查 询</el-button>
          </el-col>
        </el-row>
      </div>
      <div class="data-margin">
        <el-row>
          <el-col>
            <div id="trafficCharts" style="width:100%;height:400px;margin-top:30px"></div>
          </el-col>
        </el-row>
      </div>
    </el-card>
    <el-card>
      <div>
        <el-row>
          <div class="title">接口流量汇总列表</div>
        </el-row>
        <el-row>
          <div class="time">统计时间范围：{{ this.listQueryInfo.start_date }}至{{ this.listQueryInfo.end_date }}</div>
        </el-row>
      </div>
      <div style="margin-top:30px">
        <el-row :gutter="20" type="flex" justify="space-between">
          <el-col :span="7">
            <el-input v-model="listQueryInfo.interface_address"
                      placeholder="请输入接口地址"
                      clearable></el-input>
          </el-col>
          <el-col :span="3">
            <el-input placeholder="服务名"
                      v-model="listQueryInfo.service"
                      clearable
                      >
            </el-input>
          </el-col>
        <el-col :span="7">
         <el-date-picker
            v-model="listQueryDate"
            :clearable="false"
            type="daterange"
            align="right"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-col>
          <el-col :span="5" style="display: flex;">
            <el-button type="primary" @click="queryList()">查 询</el-button>
            <el-button @click="exportSummary">导 出</el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-table :data="summaryList" border>
            <el-table-column label="序号" type="index" min-width="50px"></el-table-column>
            <el-table-column label="接口名称" prop="interface_name" show-overflow-tooltip min-width="50px"></el-table-column>
            <el-table-column label="接口地址" prop="interface_address" show-overflow-tooltip></el-table-column>
            <el-table-column label="请求方式" prop="interface_way" min-width="25px"></el-table-column>
            <el-table-column label="服务名" prop="service" min-width="30px" show-overflow-tooltip></el-table-column>
            <el-table-column label="访问次数" prop="total_access_count" min-width="15px"></el-table-column>
            <el-table-column label="操作" fixed="right" min-width="15px">
              <template v-slot="slotProps">
                <el-tooltip content="查看日趋势" placement="top" :enterable="false">
                  <el-button type="primary"  icon="el-icon-data-line" size="mini"
                             @click="showDialog(slotProps.row.interface_id)"></el-button></el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </el-row>
        <el-row>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="listQueryInfo.pagenum"
            :page-size="listQueryInfo.pagesize"
            :page-sizes="[5, 10, 20, 30, 40]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="list_total">
          </el-pagination>
        </el-row>
      </div>
    </el-card>
    <el-dialog
      :visible.sync="dialogVisible"
      width="80%"
      >
      <el-row>
        <div class="title" style="margin-top: -20px">{{ this.interface_address }}接口访问趋势报表</div>
      </el-row>
      <el-row>
        <div class="time">统计时间范围：{{ this.listQueryInfo.start_date }}至{{ this.listQueryInfo.end_date }}</div>
      </el-row>
      <el-tooltip content="导出"  placement="top" :enterable="false">
        <el-button class="el-icon-download" type="info" circle size="mini" style="float: right;margin-right: 150px; font-size: 26px;"
           @click="exportDaily"></el-button>
      </el-tooltip>
      <div id="dailyTrafficChart" style="width:100%;height:400px;margin-top:30px"></div>
      <span slot="footer" class="dialog-footer">
<!--        <el-button @click="exportDaily">导 出</el-button>-->
        <el-button type="primary" @click="dialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import dayjs from 'dayjs'
export default {
  data() {
    return {
      data: {},
      dialogVisible: false,
      TOP: 20,
      barQueryDate: [],
      listQueryDate: [],
      barQueryInfo: {
        start_date: '',
        end_date: '',
        interface_id: '',
        interface_name: '',
        interface_address: '',
        service: '',
        top_num: '20',
        pagenum: 1,
        pagesize: 20
      },
      listQueryInfo: {
        start_date: '',
        end_date: '',
        interface_id: '',
        interface_name: '',
        interface_address: '',
        service: '',
        top_num: '',
        pagenum: 1,
        pagesize: 10
      },
      dailyTrafficQueryInfo: {
        start_date: '',
        end_date: '',
        interface_id: ''
      },
      interface_address: '',
      total: 0,
      list_total: 0,
      summaryList: [],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        },
        {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 180)
            picker.$emit('pick', [start, end])
          }
        },
        {
          text: '最近一年',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created() {
    let starttime = new Date()
    let endtime = new Date()
    starttime.setMonth(starttime.getMonth() - 1)
    endtime.setDate(endtime.getDate() - 1)
    starttime = new Date(starttime).toLocaleDateString()
    endtime = new Date(endtime).toLocaleDateString()
    this.listQueryInfo.start_date = this.barQueryInfo.start_date = starttime.replace(/\//g, '-')
    this.listQueryInfo.end_date = this.barQueryInfo.end_date = endtime.replace(/\//g, '-')
    this.barQueryDate = [this.barQueryInfo.start_date, this.barQueryInfo.end_date]
    this.listQueryDate = [this.listQueryInfo.start_date, this.listQueryInfo.end_date]
    this.getBarData()
    this.getListData()
  },
  methods: {
    // 移除window的resize事件监听器,防止内存泄漏
    onBeforeUnmount() {
      // 离开页面必须进行移除，否则会造成内存泄漏，导致卡顿
      window.removeEventListener('resize', this.getBarData)
    },
    queryBar() {
      if (this.barQueryDate.length === 2) {
        this.barQueryInfo.start_date = this.barQueryDate[0]
        this.barQueryInfo.end_date = this.barQueryDate[1]
      }
      if (!this.barQueryInfo.top_num) return this.$message.error('统计TOP不可为空')
      if (isNaN(this.barQueryInfo.top_num)) return this.$message.error('统计TOP必须是数字')
      this.barQueryInfo.pagesize = this.barQueryInfo.top_num
      this.getBarData()
    },
    queryList() {
      if (this.listQueryDate.length === 2) {
        this.listQueryInfo.start_date = this.listQueryDate[0]
        this.listQueryInfo.end_date = this.listQueryDate[1]
      }
      this.listQueryInfo.pagenum = 1
      this.getListData()
    },
    async getBarData() {
      const { data: res } = await this.$http.post('/interface_info/interface_traffic_summary/', this.barQueryInfo)
      if (res.meta.status !== 200) return this.$message.error('获取流量排行数据失败')
      this.total = res.data.total
      const trafficChart = this.$echarts.init(document.getElementById('trafficCharts'))
      const xData = res.data.data.map(function(item) {
        return item.interface_name
      })
      const yData = res.data.data.map(function(item) {
        return item.total_access_count
      })
      const seriesData = res.data.data.map(item => ({
        value: item.total_access_count,
        interface_address: item.interface_address,
        interface_way: item.interface_way,
        service: item.service,
        total_access_count: item.total_access_count
      }))
      const option = {
        tooltip: {
          show: true,
          trigger: 'item',
          enterable: true,
          formatter: function (params) {
            const data = params.data
            return `
              <span class='dot'></span>接口名称: ${params.name}<br>
              <span class='dot'></span>接口地址: ${data.interface_address}<br>
              <span class='dot'></span>请求方式: ${data.interface_way}<br>
              <span class='dot'></span>服务: ${data.service}<br>
              <span class='dot'></span>访问量: ${data.total_access_count}
            `
          },
          backgroundColor: 'rgba(0,0,0,0.75)',
          borderColor: 'rgba(0,0,0,0.75)',
          textStyle: {
            color: '#fff',
            fontsize: '14',
            width: 10,
            height: 10,
            overflow: 'break'
          }
        },
        // grid: {
        //   left: '3%',
        //   right: '6%',
        //   bottom: '25%',
        //   containLabel: true,
        // },
        xAxis: {
          data: xData,
          // x轴文字配置
          axisLabel: {
            interval: 0,
            width: 50,
            overflow: 'truncate'
            // // 设置文字倾斜显示
            // rotate: 60,
            // // 默认x轴字体大小
            // fontSize: 12,
            // // margin:文字到x轴的距离
            // margin: 5
          }
        },
        yAxis: {},
        series: [{
          name: '接口详情',
          type: 'bar',
          data: seriesData
          // itemStyle: {
          //   normal: {
          //     label: {
          //       show: true,
          //       position: 'top'
          //     }
          //   }
          // }
        }],
        grid: {
          top: '1%',
          bottom: '18%',
          left: '10%',
          right: '1%'
        },
        dataZoom: [{
          // 启用水平缩放
          type: 'slider',
          start: 0,
          end: 100
        }]
      }
      trafficChart.setOption(option)
      // 监听浏览器窗口大小变化,当窗口大小改变时,调用trafficChart.resize()方法来调整图标大小
      window.addEventListener('resize', () => {
        trafficChart.resize() // myChart是上面定义的
      })
    },
    async getListData() {
      const { data: res } = await this.$http.post('/interface_info/interface_traffic_summary/', this.listQueryInfo)
      if (res.meta.status !== 200) return this.$message.error('获取流量排行数据失败')
      this.summaryList = res.data.data
      this.list_total = res.data.total
    },
    async showDialog(id) {
      this.dailyTrafficQueryInfo.interface_id = id
      this.dailyTrafficQueryInfo.start_date = this.listQueryDate[0]
      this.dailyTrafficQueryInfo.end_date = this.listQueryDate[1]
      const { data: res } = await this.$http.get('/interface_info/get_interface_daily_traffic/', { params: this.dailyTrafficQueryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取流量排行数据失败')
      this.interface_address = res.data.data[0].interface_address
      this.dialogVisible = true
      this.$nextTick(() => {
        const dailyTrafficCharts = this.$echarts.init(document.getElementById('dailyTrafficChart'))
        // 构建完成的日期序列
        const startDate = dayjs(this.dailyTrafficQueryInfo.start_date)
        const endDate = dayjs(this.dailyTrafficQueryInfo.end_date)
        const dateRange = []
        for (let date = startDate; date.isBefore(endDate) || date.isSame(endDate); date = date.add(1, 'day')) {
          dateRange.push(date.format('YYYY-MM-DD'))
        }
        // 创建一个空对象用于存储日期对应的数据
        const dateMap = {}
        // 将数据填充到日期映射中
        res.data.data.forEach(item => {
          dateMap[item.statistics_date] = item.access_count
        })
        // 填充图表数据，如果日期缺失则填充 null
        const seriesData = dateRange.map(date => dateMap[date] !== undefined ? dateMap[date] : null)
        const option = {
          tooltip: {
            show: true,
            trigger: 'axis',
            enterable: true
          },
          xAxis: {
            name: '日期',
            type: 'category',
            nameLocation: 'middle', // 标题位置
            nameTextStyle: {
              padding: 30, // 与轴线的距离
              fontWeight: 'bold',
              fontSize: 14
            },
            data: dateRange
          },
          yAxis: {
            name: '访问量',
            nameLocation: 'middle',
            nameTextStyle: {
              padding: 50, // 与轴线的距离
              fontWeight: 'bold',
              fontSize: 14
            },
            type: 'value'
          },
          series: [{
            name: '访问量',
            type: 'line',
            data: seriesData,
            connectNulls: false,
            smooth: true, // 使折线平滑
            lineStyle: {
              color: '#5470C6' // 线条颜色
            },
            itemStyle: {
              color: '#5470C6' // 数据点颜色
            },
            areaStyle: {
              color: 'rgba(84, 112, 198, 0.3)' // 区域填充色
            }
          }]
        }
        dailyTrafficCharts.setOption(option)
      })
    },
    async exportSummary() {
      this.listQueryInfo.pagesize = this.total
      const response = await this.$http.post('/interface_info/export_interface_traffic_summary/', this.listQueryInfo, { responseType: 'blob' })
      const filename = 'TOP接口流量统计.xlsx'
      this.downloadFile(response, filename)
    },
    async exportDaily() {
      const response = await this.$http.get('/interface_info/export_interface_daily_traffic/', { params: this.dailyTrafficQueryInfo, responseType: 'blob' })
      const filename = '接口每日流量统计.xlsx'
      this.downloadFile(response, filename)
    },
    async downloadFile(response, filename) {
      try {
        // 创建 Blob 对象
        const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        const downloadUrl = window.URL.createObjectURL(blob)
        // 创建一个隐藏的链接元素
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = filename
        document.body.appendChild(link)
        // 触发下载并移除链接元素
        link.click()
        link.remove()
        // 释放 URL 对象
        window.URL.revokeObjectURL(downloadUrl)
      } catch (error) {
        this.$message.error('导出失败')
      }
    },
    handleSizeChange(newSize) {
      this.listQueryInfo.pagesize = newSize
      this.getListData()
    },
    handleCurrentChange(newPage) {
      this.listQueryInfo.pagenum = newPage
      this.getListData()
    }
  }
}
</script>

<style lang="less" scoped>
.title {
  width: 100%;
  height:20px;
  line-height:20px;
  font-size: 20px;
  margin-top: 20px;
  margin-bottom: 10px;
  text-align: center;
  font-weight: 700;
  color: black;
}
.time{
    width: 100%;
    height: 14px;
    line-height: 14px;
    font-size: 14px;
    text-align: center;
    color: rgb(74, 74, 74);
    margin-top: 10px;
}
.data-margin {
  width: 90%;
  margin: auto;
  margin-top: 20px;
}
.el-card {
  color: #fff;
  border-radius: 20px;
  margin-bottom: 25px;
}
.top-card {
  background: #4F88FF;
}
.top-card .center-data {
   background: #2663E2;
}
.center-data {
  width: 80%;
  margin: 10px auto 10px auto;
  font-size: 20px;
  height: 40px;
  line-height: 40px;
  border-radius: 20px;
  font-family: PingFangTC-Semibold,PingFangTC;
  font-weight: 600;
}
.maru {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  background: #FFFFFF;
  margin-right: 10px;
}
.item {
  text-align: center;
}
.bottom-card {
  background: #F6B03E;
}
.bottom-card .center-data {
  background: #E0590B;
}
/deep/ .dot {
  width: 10px; height: 10px; background: #a1cc95; display: inline-block; border-radius: 100px; margin: 0 6px 0 0;
}
</style>
