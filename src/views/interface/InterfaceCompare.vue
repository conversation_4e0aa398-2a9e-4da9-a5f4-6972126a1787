<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>接口测试</el-breadcrumb-item>
      <el-breadcrumb-item>待处理接口管理</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="3">
          <el-input placeholder="请输入接口ID"
                    v-model="queryInfo.search_pendinginterface_id"
                    clearable>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-input placeholder="请输入接口名称"
                    v-model="queryInfo.search_pendinginterface_name"
                    clearable>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-input placeholder="请输入接口地址"
                    v-model="queryInfo.search_pendinginterface_address"
                    clearable>
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
          >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-select placeholder="变更状态"
                     v-model="queryInfo.compare_result"
                     clearable>
            <el-option value="1" label="变更"></el-option>
            <el-option value="2" label="老接口"></el-option>
            <el-option value="3" label="新接口"></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select placeholder="是否实现"
                     v-model="queryInfo.is_cover"
                     clearable>
            <el-option value="1" label="已实现"></el-option>
            <el-option value="0" label="未实现"></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryPendingInterface">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="interfaceList" border>
          <el-table-column label="ID" prop="interface_id" min-width="60px" fixed="left"></el-table-column>
          <el-table-column label="接口名称" prop="interface_name" min-width="150px" show-overflow-tooltip fixed="left">
          </el-table-column>
          <el-table-column label="请求方式" prop="interface_way" min-width="80px"></el-table-column>
          <el-table-column label="接口地址" prop="interface_address" show-overflow-tooltip
                           min-width="200px"></el-table-column>
          <el-table-column label="URL入参" prop="param_demo" show-overflow-tooltip min-width="200px"
                           @blur="transferJson">
            <template v-slot="slotProps">
              <span v-if="slotProps.row.param_demo_bak && slotProps.row.param_demo_bak != slotProps.row.param_demo">
               <el-link type="primary" @click="compareJson(slotProps.row.param_demo,slotProps.row.param_demo_bak)">{{
                   slotProps.row.param_demo
                 }}</el-link>
              </span>
              <span v-else>{{ slotProps.row.param_demo || '' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="请求体" prop="body_demo" show-overflow-tooltip min-width="200px" @blur="transferJson">
            <template v-slot="slotProps">
              <span v-if="slotProps.row.body_demo_bak && slotProps.row.body_demo_bak != slotProps.row.body_demo">
              <el-link type="primary" @click="compareJson(slotProps.row.body_demo,slotProps.row.body_demo_bak)">{{
                  slotProps.row.body_demo
                }}</el-link>
            </span>
              <span v-else>{{ slotProps.row.body_demo || '' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建人" prop="creater" min-width="90px"></el-table-column>
          <el-table-column label="变更状态" min-width="80px">
            <template v-slot="scopeProps">
              <el-tag v-if="scopeProps.row.compare_result=='1'" type="warning">变更</el-tag>
              <el-tag v-else-if="scopeProps.row.compare_result=='2'">老接口</el-tag>
              <el-tag v-else-if="scopeProps.row.compare_result=='3'" type="success">新接口</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="是否实现" min-width="80px">
            <template v-slot="scopeProps">
              <el-tag v-if="scopeProps.row.is_cover=='1'" type="success">已实现</el-tag>
              <el-tag v-else-if="scopeProps.row.is_cover=='0'" type="danger">未实现</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="150px"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="140px">
            <template v-slot="slotProps">
              <el-tooltip class="item" effect="dark" content="新增案例" placement="top" :enterable="false">
                <el-button type="success"
                           :disabled="slotProps.row.compare_result =='1' "
                           icon="el-icon-document-add" size="mini" @click="showDialog(slotProps.row)">
                </el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="关联案例" placement="top" :enterable="false">
                <el-button type="warning"
                           :disabled="slotProps.row.compare_result =='2' || slotProps.row.compare_result =='3' "
                           icon="el-icon-connection" size="mini" @click="queryCaseList(slotProps.row.interface_id)">
                </el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="手动处理" placement="top" :enterable="false">
                <el-button type="primary" icon="el-icon-thumb" size="mini"
                           @click="updateInterface(slotProps.row.interface_id)">
                </el-button>
              </el-tooltip>
              <!-- <el-tooltip class="item" effect="dark" content="比对" placement="top" :enterable="false">
                <el-button type="warning" icon="el-icon-c-scale-to-original" size="mini"
                           @click="compareJson(slotProps.row)">
                </el-button>
              </el-tooltip> -->
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <el-dialog
      title="新老入参比对"
      :visible.sync="dialogVisible2"
      width="80%">
      <CodeDiff :origText="originalText" :newText="newText"/>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible2 = false">关 闭</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible"
               title="新增接口用例"
               width="60%"
               @close="closeDialog"
               :close-on-click-modal="false"
               destroy-on-close>
      <el-form :model="interfaceForm"
               label-width="100px"
               :rules="interfaceFormRules"
               ref="interfaceFormRef">
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属域名" prop="project_id">
              <el-select v-model="interfaceForm.project_id"
                         clearable
                         filterable>
                <el-option v-for="item in projectSearchList"
                           :key="item.id"
                           :label="item.label"
                           :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="接口名称" prop="interface_name">
              <el-input v-model="interfaceForm.interface_name" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="协议类型" prop="interface_agreement">
              <el-select v-model="interfaceForm.interface_agreement"
                         clearable>
                <el-option value="HTTP" label="HTTP"></el-option>
                <el-option value="HTTPS" label="HTTPS"></el-option>
                <el-option value="NULL" label="空"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求方式" prop="interface_way">
              <el-select v-model="interfaceForm.interface_way"
                         clearable>
                <el-option value="GET" label="GET"></el-option>
                <el-option value="POST" label="POST"></el-option>
                <el-option value="multipart/form-data_POST" label="multipart/form-data_POST"></el-option>
                <el-option value="x-www-form-urlencoded_POST" label="x-www-form-urlencoded_POST"></el-option>
                <el-option value="PUT" label="PUT"></el-option>
                <el-option value="DELETE" label="DELETE"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="请求头" prop="header">
              <el-input v-model="interfaceForm.header" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="接口地址" prop="interface_address">
              <el-input v-model="interfaceForm.interface_address" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="interfaceForm.remark"></el-input>
          </el-form-item>
        </el-row>
      </el-form>
      <el-form :model="caseForm"
               label-width="100px"
               :rules="caseFormRules"
               ref="caseFormRef">
        <el-row>
          <el-col :span="24">
            <el-form-item label="案例名称" prop="case_name">
              <el-input v-model="caseForm.case_name" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="案例描述">
              <el-input v-model="caseForm.case_describe" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="案例等级">
              <el-select v-model="caseForm.case_level" placeholder="请选择">
                <el-option label="低" value="低"></el-option>
                <el-option label="普通" value="普通"></el-option>
                <el-option label="高" value="高"></el-option>
                <el-option label="紧急" value="紧急"></el-option>
                <el-option label="立刻" value="立刻"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="请求数据">
              <el-input type="textarea"
                        :rows="10"
                        placeholder="请输入json数据"
                        v-model="caseForm.interface_data"
                        @blur="transferJson">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否断言">
              <el-switch v-model="caseForm.is_assert"
                         active-color="#13ce66">
              </el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="caseForm.is_assert">
          <el-row>
            <el-col :span="12">
              <el-form-item label="断言类型">
                <el-input disabled value="数据匹配"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item>
                <el-button type="primary" @click="addMatchAssert">添加断言</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item>
                <el-table :data="matchAssertList" border width="60%">
                  <el-table-column label="返回值" min-width="50px">
                    <template v-slot="scopeProps">
                      <el-input v-model="scopeProps.row.return_value"></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column label="判断符" min-width="50px">
                    <template v-slot="scopeProps">
                      <el-select v-model="scopeProps.row.assert_operator">
                        <el-option value="1" label="等于"></el-option>
                        <el-option value="2" label="不等于"></el-option>
                        <el-option value="3" label="包含"></el-option>
                        <el-option value="4" label="不包含"></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="预期值" min-width="50px">
                    <template v-slot="scopeProps">
                      <el-input v-model="scopeProps.row.expect_result"></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column label="删除" min-width="50px">
                    <template v-slot="scopeProps">
                      <el-button type="danger"
                                 @click="deleteCase(true, scopeProps.$index)">删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="断言类型">
                <el-input disabled value="四则运算"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item>
                <el-button type="primary" @click="addArithmeticAssert">添加断言</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item>
              <el-table :data="arithmeticAssertList" border width="60%">
                <el-table-column label="被运算值" min-width="50px">
                  <template v-slot="scopeProps">
                    <el-input v-model="scopeProps.row.return_value"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="运算符" min-width="50px">
                  <template v-slot="scopeProps">
                    <el-select v-model="scopeProps.row.assert_operator">
                      <el-option value="1" label="+"></el-option>
                      <el-option value="2" label="-"></el-option>
                      <el-option value="3" label="×"></el-option>
                      <el-option value="4" label="÷"></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="运算值" min-width="50px">
                  <template v-slot="scopeProps">
                    <el-input v-model="scopeProps.row.operation_value"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="运算结果" min-width="50px">
                  <template v-slot="scopeProps">
                    <el-input v-model="scopeProps.row.expect_result"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="删除" min-width="50px">
                  <template v-slot="scopeProps">
                    <el-button type="danger"
                               @click="deleteCase(false, scopeProps.$index)">删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-row>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="type !== 'check'">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitInterfaceCase">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import CodeDiff from '@/components/CodeDiff.vue'

export default {
  components: {
    CodeDiff
  },
  data() {
    return {
      dialogVisible2: false,
      originalText: '',
      newText: '',
      model: 'interface_info',
      model1: 'case_info',
      user_name: window.localStorage.getItem('user_name'),
      queryInfo: {
        search_pendinginterface_id: null,
        search_pendinginterface_name: '',
        search_pendinginterface_address: '',
        creater: '',
        compare_result: '',
        pagenum: 1,
        pagesize: 5
      },
      queryCaseInfo: {
        search_interface_id: '',
        creater: '',
        compare_result: '',
        pagenum: 1,
        pagesize: 5
      },
      total: 0,
      interfaceList: [],
      projectSearchList: [],
      interfaceSearchList: [],
      matchAssertList: [],
      arithmeticAssertList: [],
      isAdd: true,
      interfaceForm: {
        project_id: null,
        interface_name: '',
        interface_agreement: 'HTTP',
        interface_way: 'GET',
        header: '',
        interface_address: '',
        request_param: [],
        response_param: [],
        type: ''
      },
      dialogTitle: '',
      interfaceFormRules: {
        project_id: [
          { required: true, message: '请选择所属域名', trigger: 'change' }
        ],
        interface_name: [
          { required: true, message: '请输入接口名称', trigger: 'blur' }
        ],
        interface_agreement: [
          { required: true, message: '请选择协议类型', trigger: 'change' }
        ],
        interface_way: [
          { required: true, message: '请选择请求方式', trigger: 'change' }
        ],
        interface_address: [
          { required: true, message: '请输入接口地址', trigger: 'blur' }
        ]
      },
      caseForm: {
        case_type: 'API',
        case_name: '',
        interface_id: null,
        interface_address: '',
        case_describe: '',
        case_level: '',
        is_init: false,
        init_database: null,
        init_sql: '',
        is_back: false,
        back_sql: '',
        is_encrypt: false,
        interface_data: '',
        is_assert: false,
        asserts: '',
        csv_file: null,
        header: null
      },
      caseFormRules: {
        interface_id: [
          { required: true, message: '请选择所属接口', trigger: 'blur' }
        ],
        case_name: [
          { required: true, message: '请输入案例名称', trigger: 'blur' }
        ]
      },
      dialogVisible: false,
      requestParamList: [],
      type: ''
    }
  },
  created() {
    this.queryPendingInterface()
    this.getProjectSearchList()
  },
  methods: {
    queryPendingInterface() {
      this.queryInfo.pagenum = 1
      this.getPendingInterfaceList()
    },
    async getPendingInterfaceList() {
      const { data: res } = await this.$http.get(this.model + '/pendinginterface_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取接口列表失败')
      this.interfaceList = res.data.data
      this.total = res.data.total
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getPendingInterfaceList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getPendingInterfaceList()
    },
    showDialog(info) {
      const objString = JSON.stringify(info)
      this.interfaceForm = JSON.parse(objString)
      this.interfaceForm.project_id = ''
      this.caseForm.case_name = this.interfaceForm.interface_name
      this.interfaceForm.remark = ''
      this.dialogVisible = true
    },
    addMatchAssert() {
      this.matchAssertList.push({
        assert_type: '1',
        return_value: '',
        assert_operator: '1',
        expect_result: '',
        operation_value: ''
      })
    },
    addArithmeticAssert() {
      this.arithmeticAssertList.push({
        assert_type: '2',
        return_value: '',
        assert_operator: '1',
        expect_result: '',
        operation_value: ''
      })
    },
    deleteCase(isInterface, index) {
      if (isInterface) {
        this.matchAssertList.splice(index, 1)
      } else {
        this.arithmeticAssertList.splice(index, 1)
      }
    },
    async submitInterfaceCase() {
      try {
        await this.$refs.interfaceFormRef.validate()
        this.caseForm.interface_id = this.interfaceForm.project_id + '-' + this.interfaceForm.interface_id
        this.interfaceForm.type = '2'
        this.$refs.caseFormRef.validate(async valid => {
          if (!valid) return false
          const asserts = [...this.matchAssertList, ...this.arithmeticAssertList]
          this.caseForm.asserts = asserts
          if (this.caseForm.is_assert) {
            if (this.caseForm.asserts.length === 0) return this.$message.error('断言为是，请填写断言内容')
          }
          const { data: res } = await this.$http.post(this.model + '/interface_update_add/', {
            interfaceForm: this.interfaceForm,
            caseForm: this.caseForm
          })
          // const { data: res1 } = await this.$http.put(this.model + '/' + this.interfaceForm.interface_id + '/interface_update/', this.interfaceForm)
          // const { data: res2 } = await this.$http.post(this.model1 + '/case_add/', this.caseForm)
          if (res.meta.status !== 200) return this.$message.error('新增接口案例失败')
          this.$message.success('新增接口案例成功')
          this.dialogVisible = false
          this.caseForm.asserts = ''
          this.caseForm.case_name = ''
          this.caseForm.interface_id = null
          this.caseForm.case_describe = ''
          this.caseForm.case_level = ''
          this.caseForm.is_assert = false
          this.caseForm.interface_data = ''
          this.matchAssertList = []
          this.arithmeticAssertList = []
          this.getPendingInterfaceList()
        })
      } catch (e) {

      }
    },
    async getProjectSearchList() {
      const { data: res } = await this.$http.get('/user_info/info/', { params: { type: 'projects' } })
      if (res.meta.status !== 200) return this.$message.error('获取域名查询列表失败')
      this.projectSearchList = res.data
    },
    closeDialog() {
      this.dialogVisible = false
      this.caseForm.asserts = ''
      this.caseForm.case_name = ''
      this.caseForm.interface_id = null
      this.caseForm.case_describe = ''
      this.caseForm.case_level = ''
      this.caseForm.is_assert = false
      this.caseForm.interface_data = ''
      this.matchAssertList = []
      this.arithmeticAssertList = []
    },
    queryCaseList(id) {
      window.sessionStorage.setItem('activePath', '/api-cases')
      this.$router.push({
        name: 'apiCase',
        query: { from: id }
        // params: { projectId: 'projectId', interfaceId: id }
      })
    },
    transferJson() {
      if (this.caseForm.interface_data) {
        this.caseForm.interface_data = this.caseForm.interface_data.replace(/:\s*@{/g, ':"*@{').replace(/:\s*\[@{/g, ':["*@{').replace(/:\s*{@{/g, ':{"*@{').replace(/:\s*\${/g, ':"*${').replace(/:\s*\[\${/g, ':["*${').replace(/:\s*{\${/g, ':{"*${')
        this.caseForm.interface_data = this.caseForm.interface_data.replace(/}@,/g, '}@*",').replace(/}@]/g, '}@*"]').replace(/}@}/g, '}@*"}').replace(/}\$,/g, '}$*",').replace(/}\$]/g, '}$*"]').replace(/}\$}/g, '}$*"}')
        this.caseForm.interface_data = JSON.parse(this.caseForm.interface_data)
        this.caseForm.interface_data = JSON.stringify(this.caseForm.interface_data, null, 4)
        this.caseForm.interface_data = this.caseForm.interface_data.replace(/"\*/g, '').replace(/\*"/g, '')
      }
    },
    updateInterface(id) {
      this.$confirm('<p>此操作将更新该接口的处理状态为: 已处理</p><p>是否继续?</p>', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }).then(() => {
        this.$http.put('/interface_info/' + id + '/pendinginterface_update/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error('手动处理失败')
          this.$message.success('手动处理成功')
          this.getPendingInterfaceList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        })
      })
    },
    toJson(text) {
      text = text.replace(/:\s*@{/g, ':"*@{').replace(/:\s*\[@{/g, ':["*@{').replace(/:\s*{@{/g, ':{"*@{').replace(/:\s*\${/g, ':"*${').replace(/:\s*\[\${/g, ':["*${').replace(/:\s*{\${/g, ':{"*${')
      text = text.replace(/}@,/g, '}@*",').replace(/}@]/g, '}@*"]').replace(/}@}/g, '}@*"}').replace(/}\$,/g, '}$*",').replace(/}\$]/g, '}$*"]').replace(/}\$}/g, '}$*"}')
      text = JSON.parse(text)
      text = JSON.stringify(text, null, 4)
      text = text.replace(/"\*/g, '').replace(/\*"/g, '')
      return text
    },
    compareJson(newtxt, oldtxt) {
      if (newtxt && oldtxt) {
        this.originalText = this.toJson(newtxt)
        this.newText = this.toJson(oldtxt)
        this.dialogVisible2 = true
      } else {
        this.$message({
          type: 'info',
          message: '数据为空'
        })
      }
    }
  }
}
</script>
