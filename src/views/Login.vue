<template>
  <div class="login_container">
    <div class="login_box" v-if="!isResetPassword">
      <div class="avater_box">
        <img src="../assets/logo.png" alt="">
      </div>
      <el-form v-if="!isResetPassword"
               ref="loginFormRef"
               class="login_form"
               :model="loginForm"
               :rules="loginFormRules"
               key="loginFormRef"
               @keyup.enter.native="login">
        <el-form-item prop="username">
          <el-input placeholder="请输入用户名" prefix-icon="iconfont icon-user" v-model="loginForm.username"></el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input prefix-icon="iconfont icon-3702mima"
                    placeholder="请输入密码"
                    v-model="loginForm.password"
                    :type="passwordType">
            <span slot="suffix" @click="showPwd">
              <i :class="passwordType === 'password' ? 'iconfont icon-Notvisible' : 'iconfont icon-browse'"></i>
            </span>
          </el-input>
        </el-form-item>
        <el-form-item class="btns">
          <el-button type="primary" @click="login">登录</el-button>
          <el-button type="info" @click="switchToReset">修改密码</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="reset_box" v-else>
      <div class="reset_avater_box">
        <img src="../assets/logo.png" alt="">
      </div>
      <!-- 重置密码表单 -->
      <el-form ref="resetFormRef"
               class="reset_form"
               :model="resetForm"
               :rules="resetFormRules"
               key="resetFormRef">
          <el-form-item prop="userName">
              <el-input prefix-icon="iconfont icon-user"
                       v-model="resetForm.userName"
                       placeholder="请输入用户名"
                       >
              </el-input>
          </el-form-item>
          <el-form-item prop="oldPassword">
              <el-input prefix-icon="iconfont icon-3702mima"
                       v-model="resetForm.oldPassword"
                       placeholder="请输入原密码"
                       :type="passwordType">
                  <span slot="suffix" @click="showPwd">
                      <i :class="passwordType === 'password' ? 'iconfont icon-Notvisible' : 'iconfont icon-browse'"></i>
                  </span>
              </el-input>
          </el-form-item>
          <el-form-item prop="newPassword">
              <el-input prefix-icon="iconfont icon-3702mima"
                       v-model="resetForm.newPassword"
                       placeholder="请输入新密码"
                       :type="passwordType">
                  <span slot="suffix" @click="showPwd">
                      <i :class="passwordType === 'password' ? 'iconfont icon-Notvisible' : 'iconfont icon-browse'"></i>
                  </span>
              </el-input>
          </el-form-item>
          <el-form-item prop="confirmPassword">
              <el-input prefix-icon="iconfont icon-3702mima"
                       v-model="resetForm.confirmPassword"
                       placeholder="请确认新密码"
                       :type="passwordType">
                  <span slot="suffix" @click="showPwd">
                      <i :class="passwordType === 'password' ? 'iconfont icon-Notvisible' : 'iconfont icon-browse'"></i>
                  </span>
              </el-input>
          </el-form-item>
          <el-form-item class="btns">
              <el-button type="primary" @click="confirmReset">确认修改</el-button>
              <el-button type="info" @click="backToLogin">返回登录</el-button>
          </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
// import sha1 from 'sha1'

export default {
  data() {
    // 验证确认密码是否匹配
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.resetForm.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    return {
      isResetPassword: false,
      loginForm: {
        username: '',
        password: ''
      },
      // 重置密码表单数据
      resetForm: {
        userName: '',
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      loginFormRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ]
      },
      // 重置密码表单验证规则
      resetFormRules: {
        userName: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        oldPassword: [
          { required: true, message: '请输入原密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, max: 12, message: '密码长度需为6-12个字符', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              const hasLetter = /[a-zA-Z]/.test(value) // 检查是否包含字母
              const hasNumber = /\d/.test(value) // 检查是否包含数字
              if (!hasLetter || !hasNumber) {
                callback(new Error('密码必须包含字母和数字的组合'))
              } else {
                callback() // 校验通过
              }
            },
            trigger: 'blur'
          }
        ],
        confirmPassword: [
          { required: true, message: '请确认新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' } // 自定义校验规则
        ]
      },
      passwordType: 'password'
    }
  },
  mounted() {
    // 在组件挂载时添加全局键盘事件监听
    document.addEventListener('keydown', this.handleKeyDown)
  },
  beforeDestroy() {
    // 在组件销毁前移除事件监听，避免内存泄漏
    document.removeEventListener('keydown', this.handleKeyDown)
  },
  methods: {
    reset() {
      this.$refs.loginFormRef.resetFields()
    },
    // 处理全局键盘事件
    handleKeyDown(event) {
      // 检查是否按下回车键 (keyCode 13) 且当前不是修改密码状态
      if (event.key === 'Enter' && !this.isResetPassword) {
        this.login()
      }
    },
    login() {
      this.$refs.loginFormRef.validate(valid => {
        if (!valid) return valid
        // this.loginForm.password = sha1(this.loginForm.password)
        this.$http.post('/user_info/login/', this.loginForm).then(reslut => {
          const { data: res } = reslut
          if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
          this.$message.success('登录成功')
          window.localStorage.setItem('token', res.data.token)
          window.localStorage.setItem('userId', res.data.id)
          window.localStorage.setItem('user_name', res.data.user_name)
          window.localStorage.setItem('role_id', res.data.role_id)
          window.localStorage.setItem('chinese_name', res.data.chinese_name)
          window.localStorage.setItem('staff_no', res.data.staff_no)
          // 重置重定向锁
          window.isRedirecting = false

          // 获取重定向路径并跳转
          const redirect = this.$route.query.url
          if (redirect) {
            sessionStorage.removeItem('redirect')
            location.href = redirect
          } else {
            this.$router.push('/home')
          }
        }).catch(error => {
          this.$message.error('接口异常')
          console.log(error)
        })
      })
    },
    // // 优化后的登录逻辑
    // login() {
    //   this.$refs.loginFormRef.validate(valid => {
    //     if (!valid) return
    //
    //     this.$http.post('/user_info/login/', this.loginForm).then(result => {
    //       const { data: res } = result
    //       if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
    //
    //       // 统一存储用户信息
    //       const userData = {
    //         token: res.data.token,
    //         userId: res.data.id,
    //         userName: res.data.user_name,
    //         roleId: res.data.role_id,
    //         chineseName: res.data.chinese_name,
    //         staffNo: res.data.staff_no
    //       }
    //       Object.entries(userData).forEach(([key, val]) => {
    //         localStorage.setItem(key, val)
    //       })
    //
    //       // 统一跳转逻辑
    //       const redirectUrl = this.$route.query.redirect
    //         ? decodeURIComponent(this.$route.query.redirect)
    //         : '/home'
    //
    //       // 使用路由跳转代替location.href
    //       this.$router.replace(redirectUrl).catch(() => {
    //         this.$router.replace('/home') // 容错处理
    //       })
    //     }).catch(error => {
    //       this.$message.error(error.response?.data?.message || '登录失败')
    //     })
    //   })
    // },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
    },
    // 切换到重置密码页面
    switchToReset() {
      this.isResetPassword = true
      this.resetForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    },
    // 返回登录页面
    backToLogin() {
      this.isResetPassword = false
      this.loginForm = {
        username: '',
        password: ''
      }
    },
    confirmReset() {
      this.$refs.resetFormRef.validate(async valid => {
        if (!valid) return
        try {
          // 这里添加重置密码的接口调用
          await this.resetPassword(this.resetForm)
          this.$message.success('密码修改成功')
          // 自动返回登录页
          this.backToLogin()
        } catch (error) {
          this.$message.error(error)
        }
      })
    },
    // 重置密码的接口调用方法
    async resetPassword(formData) {
      formData = {
        username: formData.userName,
        password: formData.oldPassword,
        new_password: formData.newPassword
      }
      // 调用重置密码接口
      const { data: res } = await this.$http.post('/user_info/user_password_reset/', formData)
      // 判断接口返回的状态
      if (res.meta.status !== 200) {
        // 抛出错误，确保进入 confirmReset 的 catch 逻辑
        throw new Error(res.meta.msg)
      }
    }
  }
}
</script>

<style lang="less" scoped>
  .login_container {
      background-color: #2b4b6b;
      height: 100%;
  }
  .login_box {
      width: 450px;
      height: 300px;
      background-color: #fff;
      border-radius: 3px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

      .avater_box {
          height: 130px;
          width: 130px;
          border: 1px solid #eee;
          border-radius: 50%;
          padding: 10px;
          box-shadow: 0 0 10px #ddd;
          position: absolute;
          left: 50%;
          transform: translate(-50%, -50%);
          background-color: #fff;
          img {
              height: 100%;
              width: 100%;
              border-radius: 50%;
              background-color: #eee;
          }
      }

      .login_form {
          position: absolute;
          top: 110px;
          padding: 0 20px;
          width: 100%;
          box-sizing: border-box;
      }

      .btns {
          display: flex;
          justify-content: flex-end;
      }
  }
  .reset_box {
      width: 450px;
      height: 420px;
      background-color: #fff;
      border-radius: 3px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

      .reset_avater_box {
          height: 130px;
          width: 130px;
          border: 1px solid #eee;
          border-radius: 50%;
          padding: 10px;
          box-shadow: 0 0 10px #ddd;
          position: absolute;
          left: 50%;
          transform: translate(-50%, -50%);
          background-color: #fff;
          img {
              height: 100%;
              width: 100%;
              border-radius: 50%;
              background-color: #eee;
          }
      }

      .reset_form {
          position: absolute;
          top: 110px;
          padding: 0 20px;
          width: 100%;
          box-sizing: border-box;
      }

      .btns {
          display: flex;
          justify-content: flex-end;
      }
  }
</style>
