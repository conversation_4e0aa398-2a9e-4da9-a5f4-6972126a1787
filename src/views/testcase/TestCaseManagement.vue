<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>测试用例库</el-breadcrumb-item>
      <el-breadcrumb-item>迭代用例管理</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="3">
          <el-select placeholder="请选择业务线"
                     v-model="queryInfo.business_id"
                     clearable
                     filterable>
            <el-option v-for="item in businessList"
                       :key="item.id"
                       :label="item.label"
                       :value="item.id">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-input placeholder="迭代名称"
                    v-model="queryInfo.iteration_name"
                    clearable
                    @keyup.enter.native="queryCaseReport"
          >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    @keyup.enter.native="queryCaseReport">
          </el-input>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="queryCaseReport">查 询</el-button>
          <el-button type="primary" @click="queryReset">重 置</el-button>
          <el-button type="warning" @click="handleImportXmind">导入XMind用例</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" style="height: 20px;"></el-col>
      </el-row>
      <el-row>
        <el-table :data="testcaseList" border>
          <el-table-column label="迭代名称" prop="iteration_name"  show-overflow-tooltip min-width="150px" align="center"></el-table-column>
          <el-table-column label="需求链接" prop="requirement_link" align="center" min-width="80px">
            <template v-slot="scope">
              <el-tooltip
                v-if="scope.row.requirement_link"
                :content="scope.row.requirement_link"
                placement="top">
                <a v-if="scope.row.requirement_link" :href="scope.row.requirement_link" target="_blank"
                 class="link">查看需求</a>
              </el-tooltip>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="业务线" prop="businessName" min-width="100px" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="冒烟结果" min-width="80px" align="center">
            <template slot-scope="scope">
              {{ scope.row.pass_count }} / {{ scope.row.total_count }}
            </template>
          </el-table-column>
          <el-table-column label="操作人" prop="operator" min-width="80px" align="center"></el-table-column>
          <el-table-column label="上传时间" prop="update_time" min-width="140px" align="center"></el-table-column>
          <el-table-column label="操作" min-width="220px" align="center">
            <template v-slot="slotProps">
              <el-button type="success" size="mini" @click="getSmokeCaseList(slotProps.row)">执行冒烟
              </el-button>
              <!--  <el-button type="success" size="mini" @click="getCoreCaseList(slotProps.row)">核心用例
                </el-button>
                <el-button type="danger" size="mini" @click="AIMatchCase(slotProps.row)">AI匹配用例
                </el-button>-->
              <!-- 下拉按钮：合并下载选项 -->
              <el-dropdown trigger="click">
                <el-button type="primary" size="mini">
                  下载用例 <i class="el-icon-arrow-down"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown" trigger="click">
                  <el-dropdown-item>
                    <el-button type="danger" size="mini" @click="exportCaseList(slotProps.row, case_type='1')">
                      下载冒烟
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="primary" size="mini" @click="exportCaseList(slotProps.row, case_type='2')">
                      下载核心
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="warning" size="mini" @click="exportCaseList(slotProps.row, case_type='3')">
                      下载普通
                    </el-button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <!-- 更多按钮：合并核心用例、AI匹配用例、删除 -->
              <el-dropdown trigger="click">
                <el-button type="warning" size="mini">
                  更多 <i class="el-icon-arrow-down"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown" trigger="click">
                  <el-dropdown-item>
                    <el-button type="success" size="mini" @click="getCoreCaseList(slotProps.row)" style="width: 100px">核心用例
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="danger" size="mini" @click="AIMatchCase(slotProps.row)" style="width: 100px">AI匹配用例
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-popconfirm
                      title="确定要删除此记录吗？"
                      confirm-button-text="确定"
                      cancel-button-text="取消"
                      icon="el-icon-warning"
                      @confirm="deleteImportCaseReport(slotProps.row.id)"
                    >
                      <el-button v-if="role_id ==='0' || role_id ==='1'" type="info" size="mini" slot="reference" style="width: 100px">删除
                      </el-button>
                    </el-popconfirm>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>

    <!-- 导入xmind弹框-->
    <el-dialog
      title="导入用例"
      :visible.sync="importXmindVisible"
      width="30%"
      @close="closeImportXmindDialog"
    >
      <el-form :model="importForm" :rules="importFormRules" ref="importForm">
        <!-- 业务线选择 -->
        <el-form-item label="业务线" prop="business_id">
          <el-select v-model="importForm.business_id" filterable placeholder="请选择">
            <el-option v-for="item in businessList"
                       :key="item.id"
                       :label="item.label"
                       :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 文件上传 -->
        <el-form-item>
          <el-upload
            :action="uploadPath"
            :headers="headers"
            class="upload-demo"
            ref="uploadRef"
            :auto-upload="true"
            :on-remove="handleRemove"
            :on-exceed="handleExceed"
            :on-success="onSuccess"
            :show-file-list="true"
            :limit="1"
            accept=".xmind"
          >
            <el-button type="primary">选择文件</el-button>
            <div v-if="importForm.file" style="margin-top: 10px">
              已选文件：{{ importForm.file.name }}
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeImportXmindDialog">取 消</el-button>
        <el-button type="primary" @click="submitXmind">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import { baseUrl } from '../../main'
export default {
  data() {
    return {
      model: 'test_case_info',
      user_name: window.localStorage.getItem('user_name'),
      chinese_name: window.localStorage.getItem('chinese_name'),
      staff_no: window.localStorage.getItem('staff_no'),
      role_id: window.localStorage.getItem('role_id'),
      importForm: {},
      case_type: '',
      queryInfo: {
        business_id: '',
        iteration_name: '',
        creater: '',
        pagenum: 1,
        pagesize: 5
      },
      total: 0,
      dialogTableVisible: true,
      importXmindVisible: false,
      testcaseList: [],
      businessList: [],
      CaseDetailForm: {},
      value: '',
      importFormRules: {
        business_id: [
          { required: true, message: '请选择业务线', trigger: 'change' }
        ],
        file: [
          { required: true, message: '请上传文件', trigger: 'change' }
        ]
      },
      headers: {
        Authorization: window.localStorage.getItem('token'),
        userId: window.localStorage.getItem('userId')
      }
    }
  },
  created() {
    this.getTestCaseList()
    this.getBusinessList()
  },
  computed: {
    uploadPath() {
      // 动态生成上传路径
      // const baseURl = axios.defaults.baseURL
      return `${baseUrl}user_info/upload_file/`
    }
  },
  methods: {
    // 设置每页条数
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getTestCaseList()
    },
    // 设置页数
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getTestCaseList()
    },
    // 用例导入列表查询方法
    queryCaseReport() {
      this.queryInfo.pagenum = 1
      this.getTestCaseList()
    },
    // 重置查询
    queryReset() {
      this.queryInfo = {
        business_id: '',
        iteration_name: '',
        creater: '',
        pagenum: 1,
        pagesize: 5
      }
      this.getTestCaseList()
    },
    // 获取用例导入列表
    async getTestCaseList() {
      const { data: res } = await this.$http.post(this.model + '/import_case_report_list/', this.queryInfo)
      if (res.meta.status !== 200) return this.$message.error('获取用例记录列表失败')
      this.total = res.data.total
      this.testcaseList = res.data.data
    },
    // AI匹配用例
    async AIMatchCase(row) {
      const objString = JSON.stringify(row)
      this.CaseDetailForm = JSON.parse(objString)
      const Data = {
        report_id: this.CaseDetailForm.id,
        core_case_id: null
      }
      const { data: res } = await this.$http.post(this.model + '/ai_match_case/', Data)
      if (res.meta.status !== 200) return this.$message.error('AI匹配失败')
      this.$message.success('操作成功，AI匹配中')
      await this.getTestCaseList()
    },
    // 删除导入记录
    async deleteImportCaseReport(id) {
      const { data: res } = await this.$http.delete(this.model + '/' + id + '/import_case_report_delete/')
      if (res.meta.status !== 200) return this.$message.error('删除失败')
      this.$message.success('删除成功')
      await this.getTestCaseList()
    },
    // 打开导入xmind弹框
    handleImportXmind() {
      this.importXmindVisible = true
    },
    // 关闭导入xmind弹框
    closeImportXmindDialog() {
      this.importForm = {}
      this.importXmindVisible = false
      // 通过 ref 调用 clearFiles 方法
      this.$refs.uploadRef.clearFiles()
    },
    // 获取业务线列表
    async getBusinessList() {
      const { data: res } = await this.$http.get('/user_info/info/', { params: { type: 'businesses' } })
      if (res.meta.status !== 200) return this.$message.error('获取域名查询列表失败')
      this.businessList = res.data
    },
    // 删除文件
    handleRemove(file) {
      this.importForm.xmind_url = null
      this.$forceUpdate()
    },
    // 文件上传成功处理
    onSuccess(response, file) {
      this.importForm.xmind_url = response.data.file_path
      if (response.meta.status !== 200) return this.$message.error('文件上传失败')
      this.$message.success('文件上传成功！')
    },
    // 文件超出个数限制时的钩子
    handleExceed(files, fileList) {
      this.$message.warning('请先删除已上传文件后重新上传!')
    },
    // 提交xmind
    submitXmind() {
      this.$refs.importForm.validate((valid) => {
        if (valid) {
          this.$http.post(this.model + '/import_xmind/', this.importForm)
            .then(response => {
              if (response.data.meta.status === 200) {
                this.$message.success('导入成功')
                this.getTestCaseList()
                this.importXmindVisible = false
                this.importForm = {}
              } else return this.$message.error('导入失败')
            })
            .catch(error => {
              this.$message.error('导入失败: ' + error.message)
            })
        } else {
          this.$message.error('请填写完整信息')
          return false
        }
      })
    },
    // 跳转到冒烟用例列表
    getSmokeCaseList(row) {
      this.$router.push({ path: 'smokeCaseList', query: { report_id: row.id } })
    },
    // 跳转到核心用例列表
    getCoreCaseList(row) {
      this.$router.push({ path: 'coreCaseList', query: { report_id: row.id, iteration_name: row.iteration_name } })
    },
    // 导出用例列表
    async exportCaseList(row, caseType) {
      const fileName = row.iteration_name
      // 定义枚举值映射
      const caseTypeMap = {
        1: '冒烟',
        2: '核心',
        3: '全部'
      }
      // 获取映射后的值
      const caseTypeValue = caseTypeMap[caseType]
      const response = await this.$http.post(this.model + '/export_case/', { report_id: row.id, case_type: caseType }, { responseType: 'blob' })
      const filename = fileName + caseTypeValue + '用例.xlsx'
      await this.downloadFile(response, filename)
    },
    // 下载文件
    async downloadFile(response, filename) {
      try {
        // 创建 Blob 对象
        const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        const downloadUrl = window.URL.createObjectURL(blob)
        // 创建一个隐藏的链接元素
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = filename
        document.body.appendChild(link)
        // 触发下载并移除链接元素
        link.click()
        link.remove()
        // 释放 URL 对象
        window.URL.revokeObjectURL(downloadUrl)
      } catch (error) {
        this.$message.error('导出失败')
      }
    }
  }
}
</script>
