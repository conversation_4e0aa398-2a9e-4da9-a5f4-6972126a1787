<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>用例管理</el-breadcrumb-item>
      <el-breadcrumb-item>需求管理</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="3">
          <el-input placeholder="迭代名称" v-model="queryInfo.iteration_name" clearable
                    @keyup.enter.native="queryCaseReport">
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-input placeholder="需求ID" v-model="queryInfo.requirement_id" clearable
                    @keyup.enter.native="queryCaseReport">
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-input placeholder="需求名称" v-model="queryInfo.requirement_name" clearable
                    @keyup.enter.native="queryCaseReport">
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-select placeholder="请选择业务线" v-model="queryInfo.business_id" clearable filterable>
            <el-option v-for="item in businessList" :key="item.id" :label="item.label" :value="item.id">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="10">
          <el-button type="primary" @click="queryCaseReport">查 询</el-button>
          <el-button type="primary" @click="queryReset" style="margin-left: 10px">重 置</el-button>
          <el-dropdown trigger="click">
            <el-button type="danger" style="margin-left: 10px">
              下载用例 <i class="el-icon-arrow-down"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown" trigger="click">
              <el-dropdown-item>
                <el-button type="success" size="mini" @click="getCoreCaseList(slotProps.row)"
                           style="width: 100px">核心用例
                </el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="danger" size="mini" @click="AIMatchCase(slotProps.row)"
                           style="width: 100px">冒烟用例
                </el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="info" size="mini" @click="AIMatchCase(slotProps.row)"
                           style="width: 100px">召回用例
                </el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="warning" size="mini" @click="AIMatchCase(slotProps.row)"
                           style="width: 100px">全部用例
                </el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button type="warning" @click="handleImportXmind" style="margin-left: 10px">推送已合并用例</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" style="height: 20px;"></el-col>
      </el-row>
      <el-row>
        <el-table :data="testcaseList" border>
          <el-table-column label="迭代名称" prop="iteration_name" show-overflow-tooltip min-width="150px"
                           align="center"></el-table-column>
          <el-table-column label="业务线" prop="businessName" min-width="100px" show-overflow-tooltip
                           align="center"></el-table-column>
          <el-table-column label="tapd链接" prop="requirement_link" align="center" min-width="80px">
            <template v-slot="scope">
              <el-tooltip v-if="scope.row.requirement_link" :content="scope.row.requirement_link" placement="top">
                <a v-if="scope.row.requirement_link" :href="scope.row.requirement_link" target="_blank"
                   class="link">点击跳转</a>
              </el-tooltip>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="需求ID" prop="requirement_ID" show-overflow-tooltip min-width="60px"
                           align="center"></el-table-column>
          <el-table-column label="需求名称" prop="requirement_name" show-overflow-tooltip min-width="100px"
                           align="center"></el-table-column>
          <el-table-column label="需求链接" prop="requirement_link" align="center" min-width="80px">
            <template v-slot="scope">
              <el-tooltip v-if="scope.row.requirement_link" :content="scope.row.requirement_link" placement="top">
                <a v-if="scope.row.requirement_link" :href="scope.row.requirement_link" target="_blank"
                   class="link">点击跳转</a>
              </el-tooltip>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="AI需求分析内容" min-width="100px" align="center">
            <template #default="scope">
              <el-popover
                placement="top"
                width="800"
                trigger="click"
                popper-class="dark-popover"
              >
                <div v-html="formatTooltipContent(scope.row.requirement_analyze_content)"
                     style="white-space: pre-line; font-size: 12px"></div>
                <div slot="reference"
                     class="cell-content"
                     style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden; min-width: 60px; cursor: pointer; color: #409EFF; text-align: center;">
                  点击查看
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="UI稿" prop="ui_link" align="center" min-width="80px">
            <template v-slot="scope">
              <el-tooltip
                v-if="scope.row.ui_link"
                :content="scope.row.ui_link"
                placement="top">
                <div
                  @click="openLink(scope.row.ui_link)"
                  class="cell-content"
                  style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden; min-width: 60px; cursor: pointer; color: #409EFF; text-align: center;">
                  点击查看
                </div>
              </el-tooltip>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="技术文档" prop="requirement_link" align="center" min-width="80px">
            <template v-slot="scope">
              <el-tooltip v-if="scope.row.requirement_link" :content="scope.row.requirement_link" placement="top">
                <a v-if="scope.row.requirement_link" :href="scope.row.requirement_link" target="_blank"
                   class="link">点击查看</a>
              </el-tooltip>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="用例数" prop="total_test_case" show-overflow-tooltip min-width="60px"
                           align="center"></el-table-column>
          <el-table-column label="召回率" min-width="70px" align="center">
            <template slot-scope="scope">
              {{ scope.row.recall_ai_test_case }} / {{ scope.row.total_ai_test_case }}
            </template>
          </el-table-column>
          <el-table-column label="AI用例占比" min-width="70px" align="center">
            <template slot-scope="scope">
              {{ scope.row.total_ai_test_case }} / {{ scope.row.total_test_case }}
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="250px" align="center">
            <template v-slot="slotProps">
              <el-button type="primary" size="mini" @click="checkMode(slotProps.row), generationModeVisible = true">AI生成用例</el-button>
              <el-button type="warning" size="mini" @click="getSmokeCaseList(slotProps.row)">查看用例
              </el-button>
              <el-button type="danger" size="mini" @click="getSmokeCaseList(slotProps.row)">匹配用例
              </el-button>
              <el-dropdown trigger="click">
                <el-button type="primary" size="mini">
                  查看AI分析結果 <i class="el-icon-arrow-down"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown" trigger="click">
                  <el-dropdown-item>
                    <el-button type="danger" size="mini" @click="exportCaseList(slotProps.row, case_type='1')">
                      需求分析內容
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="primary" size="mini" @click="exportCaseList(slotProps.row, case_type='2')">
                      测试点设计内容
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="warning" size="mini" @click="exportCaseList(slotProps.row, case_type='3')">
                      测试用例初稿
                    </el-button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum" :page-size="queryInfo.pagesize" :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper" :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!--生成测试用例步骤选择框-->
     <el-dialog
      title="生成测试用例方式"
      :visible.sync="generationModeVisible"
      width="50%"
      class="requirement-dialog"
      @close="resetGenerationForm"
    >
      <el-form label-width="100px" :model="generationModelForm" >
        <el-row :gutter="20"> <!-- 控制行内列的间距 -->
          <!-- 第一列：生成方式选择 -->
          <el-col :span="24">
            <el-form-item label="用例生成方式:" class="bold-label" prop="generationMode">
              <el-radio-group v-model="generationModelForm.generationMode" class="step-radio-group" @change="handleGenerationModeChange">
                <el-radio label="one-click" >一键生成</el-radio>
                <el-radio label="stepwise-generation" >逐步生成</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!-- 第二列：逐步生成节点 -->
          <el-col :span="24">
            <el-form-item label="逐步生成节点:" class="bold-label" prop="generationStep">
              <el-radio-group v-model="generationModelForm.generationStep" class="step-radio-group" :disabled="generationModelForm.generationMode !== 'stepwise-generation'" @change="handleStepChange">
                <el-radio label="requirement-analysis" >需求分析</el-radio>
                <el-radio label="test-design" >测试点设计</el-radio>
                <el-radio label="draft-generation" >用例初稿生成</el-radio>
                <el-radio label="case-review" >用例评审</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="generationModeVisible=false">取 消</el-button>
        <el-button type="primary" @click="generationModelCommit">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 录入需求弹框-->
    <el-dialog
      title="需求录入"
      :visible.sync="importRequirementVisible"
      width="70%"
      class="requirement-dialog"
      @close="importRequirementVisible=false"
    >
      <el-form label-width="100px" :model="importRequirementForm" :rules="importRequirementFormRules"
               ref="importRequirementFormRef" key="importRequirementFormRef">
      <!-- 业务线选择 -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属业务线" prop="business_id">
              <span>{{ importRequirementForm.business_name }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="需求ID" prop="requirement_id">
              <el-input v-model="importRequirementForm.requirement_id" placeholder="请输入需求ID"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="UI稿地址:" class="bold-label" prop="ui_link">
              <el-input
                  class="edit-input"
                  v-model="importRequirementForm.ui_link"
                  style="width: 750px; padding-left: 0px"
                  placeholder="请输入UI稿地址"
                ></el-input>
            </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="技术文档地址:" class="bold-label" prop="tech_link">
              <el-input
                  class="edit-input"
                  v-model="importRequirementForm.tech_link"
                  style="width: 750px; padding-left: 0px"
                  placeholder="请输入技术文档地址"
                ></el-input>
            </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="需求内容:" class="bold-label" prop="requirement_content">
            <el-input
              type="textarea"
              :rows="7"
              maxlength="3000"
              show-word-limit
              v-model="importRequirementForm.requirement_content"
              @paste.native="handlePastePic"
              ref="replyInput"
              placeholder="请输入需求内容或粘贴图片(支持jpg/png格式，单张图片不超过5MB,限制3张图片)"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
            <!-- 图片上传部分 -->
            <el-form-item label="上传图片:">
              <el-upload
                :action="uploadPath"
                list-type="picture-card"
                :multiple="true"
                :limit="3"
                :file-list="fileList"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :on-success="handleUploadSuccess"
                :on-exceed="handleExceed"
                :before-upload="beforeUpload"
                :on-change="handFileList"
                :auto-upload="true">
                <i class="el-icon-plus"></i>
                <div slot="tip" class="el-upload__tip">
                  支持jpg/png格式，单张图片不超过5MB
                </div>
              </el-upload>
            </el-form-item>
          </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importRequirementVisible=false">取 消</el-button>
        <el-button type="primary" @click="submitRequirement">下一步</el-button>
      </span>
    </el-dialog>
    <!-- AI分析结果弹框 -->
    <el-dialog
      title="需求分析"
      :visible.sync="showAnalyzeResultDialog"
      width="60%"
      class="requirement-dialog"
      @close="showAnalyzeResultDialog = false">
      <el-form label-width="100px" :model="analyzeResultForm" :rules="analyzeResultFormRules"
               ref="analyzeResultFormRef" key="analyzeResultFormRef">
        <el-form-item label="需求分析:" class="bold-label">
          <el-input
                type="textarea"
                :rows="15"
                maxlength="5000"
                show-word-limit
                v-model="analyzeResultForm.requirementAnalyzePrd"
                placeholder="请输入需求分析内容"
                :disabled="false"
                :readonly="false"
                ref="analyzeTextarea"
                style="pointer-events: auto !important;"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showAnalyzeResultDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmAnalyzeResult">下一步</el-button>
      </span>
    </el-dialog>
    <!--AI分析测试点-->
    <el-dialog
      title="测试点分析"
      :visible.sync="showTestPointDialog"
      width="60%"
      class="requirement-dialog"
      @close="showTestPointDialog = false">
      <el-form label-width="100px" :model="testPointForm" :rules="testPointFormRules"
               ref="testPointFormRef" key="testPointFormRef">
        <el-form-item label="测试点分析:" class="bold-label">
          <el-input
                type="textarea"
                :rows="15"
                maxlength="5000"
                show-word-limit
                v-model="testPointForm.testPoint"
                placeholder="请输入测试点内容"
                :disabled="false"
                :readonly="false"
                ref="analyzeTextarea"
                style="pointer-events: auto !important;"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showTestPointDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmTestPointResult">下一步</el-button>
      </span>
    </el-dialog>
    <!--AI生成用例初稿-->
    <el-dialog
      title="AI生成用例初稿预览"
      :visible.sync="showTestCaseFirstDialog"
      width="80%"
      @close="showTestCaseFirstDialog = false">
      <el-row>
        <el-col>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6" style="text-align: left;">
          <el-button type="primary" @click="handleAddCase">新 增</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="firstCaseList" border v-loading="getFirstCaseListLoading" >
          <el-table-column label="用例名称" prop="case_scene" min-width="120" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="所属系统" prop="system" min-width="100" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="模块" prop="module" min-width="100" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="前置条件" prop="premise" min-width="150" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="测试步骤" min-width="200" align="center">
            <template #default="scope">
                <!-- 调用 getTextContent 方法获取并显示文本 -->
              <el-tooltip placement="top">
                <div style="white-space: nowrap;text-overflow: ellipsis; width: 180px; overflow: hidden; text-align: left">{{ scope.row.test_steps }}</div>
                <div slot="content" v-html="formatTooltipContent(scope.row.test_steps)" style="white-space: pre-line"></div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="预期结果" min-width="200" align="center">
            <template #default="scope">
                <!-- 调用 getTextContent 方法获取并显示文本 -->
              <el-tooltip placement="top">
                <div style="white-space: nowrap;text-overflow: ellipsis; width: 180px; overflow: hidden; text-align: left">{{ scope.row.expected_result }}</div>
                <div slot="content" v-html="formatTooltipContent(scope.row.expected_result)" style="white-space: pre-line"></div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="130px" align="center">
            <template v-slot="slotProps">
                <el-button type="text" size="mini" @click="handleUpdateCase(slotProps.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleCaseListSizeChange"
          @current-change="handleCaseListCurrentChange"
          :current-page.sync="queryTestCaseInfo.pagenum"
          :page-size="queryTestCaseInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="case_list_total">
        </el-pagination>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showTestCaseListDialog = false, queryRecognizeRecordList()">关闭</el-button>
      </span>
    </el-dialog>
    <!--新增用例-->
    <el-dialog
      title="新增用例"
      :visible.sync="addTestCaseVisible"
      width="60%"
      @close="closeAddCaseDialog"
    >
      <el-form label-width="140px" :model="addCaseForm" :rules="addCaseFormRules" ref="addCaseFormRef" key="addCaseFormRef">
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="所属业务线" prop="business_id">
              <el-select placeholder="请选择业务线"
                         v-model="addCaseForm.business_id"
                         clearable
                         filterable>
                <el-option v-for="item in businessList"
                           :key="item.id"
                           :label="item.label"
                           :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属模块" prop="module">
              <el-input v-model="addCaseForm.module" placeholder="请输入所属模块" maxlength="50" show-word-limit/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="场景名称" prop="case_scene">
            <el-input
              v-model="addCaseForm.case_scene"
              type="textarea"
              :rows="3"
              placeholder="请输入场景名称"
              maxlength="500"
              show-word-limit
            />
        </el-form-item>
        <el-form-item label="前置条件" prop="premise">
            <el-input
              v-model="addCaseForm.premise"
              type="textarea"
              :rows="3"
              placeholder="请输入前置条件"
              maxlength="500"
              show-word-limit
            />
        </el-form-item>
        <el-form-item label="操作步骤" prop="test_steps">
            <el-input
              v-model="addCaseForm.test_steps"
              type="textarea"
              :rows="5"
              placeholder="请输入详细的操作步骤"
            />
        </el-form-item>
        <el-form-item label="预期结果" prop="expected_result">
          <el-input
            v-model="addCaseForm.expected_result"
            type="textarea"
            :rows="3"
            placeholder="请输入预期结果(0-500字符)"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="关键字" prop="keywords">
                <el-input v-model="addCaseForm.keywords" placeholder="请输入关键字" maxlength="50" show-word-limit/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="自动化场景ID" prop="case_scene_id">
                <el-input v-model="addCaseForm.case_scene_id" placeholder="请输入自动化场景ID" maxlength="100"
                          show-word-limit/>
              </el-form-item>
            </el-col>
          </el-row>
        <el-form-item label="迭代名称" prop="iteration_name">
          <el-input
            v-model="addCaseForm.iteration_name"
            type="textarea"
            :rows="3"
            placeholder="请输入迭代名称"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="addTestCase()">新 增</el-button>
            <el-button @click="closeAddCaseDialog">取 消</el-button>
          </el-form-item>
        </el-form>
    </el-dialog>
  </div>
</template>

<script>

import { baseUrl } from '../../main'
export default {
  data() {
    return {
      model: 'test_case_info',
      user_name: window.localStorage.getItem('user_name'),
      chinese_name: window.localStorage.getItem('chinese_name'),
      staff_no: window.localStorage.getItem('staff_no'),
      role_id: window.localStorage.getItem('role_id'),
      importForm: {},
      case_type: '',
      queryInfo: {
        business_id: '',
        iteration_name: '',
        requirement_id: '',
        requirement_name: '',
        creater: '',
        pagenum: 1,
        pagesize: 5
      },
      importRequirementForm: {
        business_id: '',
        bussiness_name: '',
        requirement_id: '',
        requirement_content: '',
        requirementAnalyzePrd: '',
        ui_link: '',
        tech_link: '',
        requirement_pic: [],
        intention: ''
      },
      generationModelForm: {
        generationMode: 'one-click', // 默认一键生成
        generationStep: ''
      },
      analyzeResultForm: {
        business_id: '',
        requirement_id: '',
        requirement_content: '',
        requirementAnalyzePrd: '',
        intention: ''
      },
      testPointForm: {
        business_id: '',
        requirement_id: '',
        intention: '',
        testPoint: ''
      },
      importRequirementVisible: false,
      showAnalyzeResultDialog: false, // 控制AI分析结果弹框显示
      showTestCaseListDialog: false, // 控制评审后的测试用例列表弹框的显示
      handleUpdateTestCaseDialogVisible: false, // 控制编辑用例弹框的显示
      showTestPointDialog: false, // 控制AI测试点分析弹框
      showTestCaseFirstDialog: false, // 用例初稿弹窗
      generationModeVisible: false, // 控制生成模式弹框的显示
      requirementDetailForm: {},
      addTestCaseVisible: false,
      addCaseForm: {},
      firstCaseList: [],
      getFirstCaseListLoading: false,
      // 参数映射表
      paramMap: {
        'one-click': '11',
        'stepwise-generation': {
          'requirement-analysis': '21',
          'test-design': '22',
          'draft-generation': '23',
          'case-review': '24'
        }
      },
      importRequirementFormRules: {
        business_id: [
          { required: true, message: '请选择业务线', trigger: 'change' }
        ],
        requirement_id: [
          { required: true, message: '请输入需求ID', trigger: 'change' }
        ],
        requirement_content: [
          { required: true, message: '请输入需求内容', trigger: 'change' }
        ]
      },
      queryTestCaseInfo: {
        system: '',
        module: '',
        status: '',
        recognize_record_id: '',
        pagenum: 1,
        pagesize: 10
      },
      total: 0,
      dialogTableVisible: true,
      importXmindVisible: false,
      testcaseList: [],
      businessList: [],
      CaseDetailForm: {},
      value: '',
      importFormRules: {
        business_id: [
          { required: true, message: '请选择业务线', trigger: 'change' }
        ],
        file: [
          { required: true, message: '请上传文件', trigger: 'change' }
        ]
      },
      headers: {
        Authorization: window.localStorage.getItem('token'),
        userId: window.localStorage.getItem('userId')
      }
    }
  },
  created() {
    this.getTestCaseList()
    this.getBusinessList()
  },
  computed: {
    uploadPath() {
      // 动态生成上传路径
      // const baseURl = axios.defaults.baseURL
      return `${baseUrl}user_info/upload_file/`
    }
  },
  methods: {
    // 设置每页条数
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getTestCaseList()
    },
    // 设置页数
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getTestCaseList()
    },
    // 用例导入列表查询方法
    queryCaseReport() {
      this.queryInfo.pagenum = 1
      this.getTestCaseList()
    },
    // 重置查询
    queryReset() {
      this.queryInfo = {
        business_id: '',
        iteration_name: '',
        creater: '',
        pagenum: 1,
        pagesize: 5
      }
      this.getTestCaseList()
    },
    // tooltip换行显示
    formatTooltipContent(content) {
      if (!content || typeof content !== 'string') {
        return ''
      }

      // 检查内容是否包含编号格式（如"1、"，"2："等）
      if (/\d+[、：:.]/.test(content)) {
        // 使用formatSteps函数处理带编号的内容
        const steps = this.formatSteps(content)
        // 将处理后的步骤数组转换为HTML格式，每个步骤一行
        return steps.join('<br>')
      } else {
        // 对于不包含编号的普通内容，仍然使用原来的处理方式
        return content.replace(/\n/g, '<br>')
      }
    },
    // 获取用例导入列表
    async getTestCaseList() {
      const { data: res } = await this.$http.post(this.model + '/import_case_report_list/', this.queryInfo)
      if (res.meta.status !== 200) return this.$message.error('获取用例记录列表失败')
      this.total = res.data.total
      this.testcaseList = res.data.data
    },
    // AI匹配用例
    async AIMatchCase(row) {
      const objString = JSON.stringify(row)
      this.CaseDetailForm = JSON.parse(objString)
      const Data = {
        report_id: this.CaseDetailForm.id,
        core_case_id: null
      }
      const { data: res } = await this.$http.post(this.model + '/ai_match_case/', Data)
      if (res.meta.status !== 200) return this.$message.error('AI匹配失败')
      this.$message.success('操作成功，AI匹配中')
      await this.getTestCaseList()
    },
    // 删除导入记录
    async deleteImportCaseReport(id) {
      const { data: res } = await this.$http.delete(this.model + '/' + id + '/import_case_report_delete/')
      if (res.meta.status !== 200) return this.$message.error('删除失败')
      this.$message.success('删除成功')
      await this.getTestCaseList()
    },
    // 打开导入xmind弹框
    handleImportXmind() {
      this.importXmindVisible = true
    },
    // 关闭导入xmind弹框
    closeImportXmindDialog() {
      this.importForm = {}
      this.importXmindVisible = false
      // 通过 ref 调用 clearFiles 方法
      this.$refs.uploadRef.clearFiles()
    },
    // 获取业务线列表
    async getBusinessList() {
      const { data: res } = await this.$http.get('/user_info/info/', { params: { type: 'businesses' } })
      if (res.meta.status !== 200) return this.$message.error('获取域名查询列表失败')
      this.businessList = res.data
    },
    // 删除文件
    handleRemove(file) {
      this.importForm.xmind_url = null
      this.$forceUpdate()
    },
    // 文件上传成功处理
    onSuccess(response, file) {
      this.importForm.xmind_url = response.data.file_path
      if (response.meta.status !== 200) return this.$message.error('文件上传失败')
      this.$message.success('文件上传成功！')
    },
    // 文件超出个数限制时的钩子
    handleExceed(files, fileList) {
      this.$message.warning('请先删除已上传文件后重新上传!')
    },
    // 提交xmind
    submitXmind() {
      this.$refs.importForm.validate((valid) => {
        if (valid) {
          this.$http.post(this.model + '/import_xmind/', this.importForm)
            .then(response => {
              if (response.data.meta.status === 200) {
                this.$message.success('导入成功')
                this.getTestCaseList()
                this.importXmindVisible = false
                this.importForm = {}
              } else return this.$message.error('导入失败')
            })
            .catch(error => {
              this.$message.error('导入失败: ' + error.message)
            })
        } else {
          this.$message.error('请填写完整信息')
          return false
        }
      })
    },
    // 跳转到冒烟用例列表
    getSmokeCaseList(row) {
      this.$router.push({ path: 'smokeCaseList', query: { report_id: row.id } })
    },
    // 跳转到核心用例列表
    getCoreCaseList(row) {
      this.$router.push({ path: 'coreCaseList', query: { report_id: row.id, iteration_name: row.iteration_name } })
    },
    // 导出用例列表
    async exportCaseList(row, caseType) {
      const fileName = row.iteration_name
      // 定义枚举值映射
      const caseTypeMap = {
        1: '冒烟',
        2: '核心',
        3: '全部'
      }
      // 获取映射后的值
      const caseTypeValue = caseTypeMap[caseType]
      const response = await this.$http.post(this.model + '/export_case/', { report_id: row.id, case_type: caseType }, { responseType: 'blob' })
      const filename = fileName + caseTypeValue + '用例.xlsx'
      await this.downloadFile(response, filename)
    },
    // 下载文件
    async downloadFile(response, filename) {
      try {
        // 创建 Blob 对象
        const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        const downloadUrl = window.URL.createObjectURL(blob)
        // 创建一个隐藏的链接元素
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = filename
        document.body.appendChild(link)
        // 触发下载并移除链接元素
        link.click()
        link.remove()
        // 释放 URL 对象
        window.URL.revokeObjectURL(downloadUrl)
      } catch (error) {
        this.$message.error('导出失败')
      }
    },
    // 打开生成用例方式选择框
    async checkMode(row) {
      this.importRequirementForm = {
        business_id: row.business_line,
        business_name: row.businessName,
        requirement_id: row.requirement_id,
        ui_link: row.ui_link,
        tech_link: row.tech_link
      }
    },
    // 选择用例生成方式提交
    generationModelCommit() {
      this.generationModeVisible = false
      const importData = {
        ...this.importRequirementForm
      }
      // 根据不同的选择跳转到不同的对话框
      switch (this.generationModelForm.generationMode) {
        case 'one-click':
          // 一键生成跳转到 importRequirementVisible
          this.importRequirementVisible = true
          this.importRequirementForm.intention = this.getCurrentParam()
          break
        case 'stepwise-generation':
          // 逐步生成根据步骤跳转
          switch (this.generationModelForm.generationStep) {
            case 'requirement-analysis':
              this.importRequirementVisible = true
              this.importRequirementForm.intention = this.getCurrentParam()
              break
            case 'test-design':
              this.showAnalyzeResultDialog = true
              this.analyzeResultForm.intention = this.getCurrentParam()
              break
            case 'draft-generation':
              this.showTestPointDialog = true
              this.testPointForm.intention = this.getCurrentParam()
              break
            case 'case-review':
              this.showTestCaseFirstDialog = true
              break
            default:
              console.warn('Unknown step:', this.generationModelForm.generationStep)
              this.importRequirementVisible = true
          }
          break

        default:
          console.warn('Unknown generation mode:', this.generationModelForm.generationMode)
          this.importRequirementVisible = true
      }
    },
    // 提交需求录入
    async submitRequirement() {
      this.$refs.importRequirementFormRef.validate(async valid => {
        if (!valid) return false
        const requirementData = {
          requirement_content: this.importRequirementForm.requirement_content
        }
        this.$loading({
          lock: true,
          text: 'AI正在需求分析,请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        const { data: res } = await this.$http.post(this.model + '/ai_get_analyze_prd/', requirementData)
        if (res.meta.status !== 200) return this.$message.error('AI分析失败,请重试')
        // 使用 Vue.set 确保响应性
        this.$set(this.importRequirementForm, 'requirementAnalyzePrd', res.data)
        this.$loading().close()
        // 关闭需求录入弹框
        this.importRequirementVisible = false
        // 显示需求分析结果弹框
        this.showAnalyzeResultDialog = true
        await this.queryRecognizeRecordList()
      })
    },
    // 确认AI分析结果
    async confirmAnalyzeResult() {
      this.$loading({
        lock: true,
        text: 'AI正在生成测试点,请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const { data: res } = await this.$http.post(this.model + '/ai_get_analyze_prd/', this.importRequirementForm)
      if (res.meta.status !== 200) return this.$message.error('AI分析失败,请重试')
      await this.queryTestCaseList(res.data.id)
      this.$loading().close()
      // 关闭需求分析弹框
      this.showAnalyzeResultDialog = false
      // 显示AI生成测试点弹框
      this.showTestPointDialog = true
    },
    // 确认AI获取测试点结果
    async confirmTestPointResult() {
      this.$loading({
        lock: true,
        text: 'AI正在生成测试用例,请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const { data: res } = await this.$http.post(this.model + '/ai_generate_testcases/', this.importRequirementForm)
      if (res.meta.status !== 200) return this.$message.error('AI生成失败,请重试')
      await this.queryTestCaseList(res.data.id)
      this.$loading().close()
      // 关闭测试点分析弹框
      this.showTestPointDialog = false
      // 打开测试用例初稿列表
      this.showTestCaseFirstDialog = true
    },
    // 打开用例初稿的新增弹框
    handleAddCase() {
      this.getFirstTestCaseList()
      this.addTestCaseVisible = true
    },
    // 关闭新增弹框
    closeAddCaseDialog() {
      this.addCaseForm = {}
      this.addTestCaseVisible = false
    },
    // 获取用例初稿列表
    async getFirstTestCaseList() {
      this.getFirstCaseListLoading = true
      const { data: res } = await this.$http.post(this.model + '/first_test_case_list/', this.queryInfo)
      if (res.meta.status !== 200) return this.$message.error('获取用例记录列表失败')
      this.total = res.data.total
      this.firstCaseList = res.data.data
      this.getFirstCaseListLoading = false
    },
    // 新增用例
    addTestCase() {
      this.$refs.addCaseFormRef.validate(async valid => {
        if (!valid) return false
        // 这里调用API提交表单数据
        const addData = {
          module: this.addCaseForm.module,
          premise: this.addCaseForm.premise,
          iteration_name: this.addCaseForm.iteration_name,
          case_scene: this.addCaseForm.case_scene,
          business_id: this.addCaseForm.business_id,
          case_scene_id: this.addCaseForm.case_scene_id,
          keywords: this.addCaseForm.keywords,
          test_steps: this.addCaseForm.test_steps,
          expected_result: this.addCaseForm.expected_result
        }
        const { data: res } = await this.$http.post(this.model + '/testcase_add/', addData)
        if (res.meta.status !== 200) return this.$message.error('处理失败')
        this.$message.success('新增成功')
        this.addTestCaseVisible = false
        await this.getAiTestCaseList()
      })
    },
    getCurrentParam() {
      const mode = this.generationModelForm.generationMode
      const step = this.generationModelForm.generationStep

      if (mode === 'one-click') {
        return this.paramMap['one-click']
      }
      // 逐步生成时，确保有默认步骤
      const validStep = step || 'requirement-analysis'
      return this.paramMap['stepwise-generation'][validStep]
    },
    handleGenerationModeChange(val) {
      if (val === 'stepwise-generation') {
        this.generationModelForm.generationStep = 'requirement-analysis' // 如果是逐步生成，默认选中需求分析
      } else {
        this.generationModelForm.generationStep = '' // 如果不是逐步生成，清空选择
      }
      this.importRequirementForm.intention = this.getCurrentParam()
    },
    handleStepChange(val) {
      this.importRequirementForm.intention = this.getCurrentParam()
    },
    // 关闭生成方式弹框后重置表单数据
    resetGenerationForm() {
      this.generationModelForm = {
        generationMode: 'one-click',
        generationStep: ''
      }
      this.generationModeVisible = false
    }
  }
}
</script>

<style>
.requirement-dialog {
  min-height: 600px;
  display: flex;
  flex-direction: column;
}

.requirement-dialog .el-dialog__body {
  flex: 1;
  overflow: auto;
  max-height: 70vh;
  padding: 20px;
}

.requirement-dialog .el-form-item {
  margin-bottom: 20px;
}

.requirement-dialog .el-textarea {
  min-height: 150px;
}

/* 确保 textarea 可编辑 */
.requirement-dialog .el-textarea__inner {
  pointer-events: auto !important;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  cursor: text !important;
  background-color: #fff !important;
  border: 1px solid #dcdfe6 !important;
}

.requirement-dialog .el-textarea__inner:focus {
  border-color: #409eff !important;
  outline: 0 !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
}

.requirement-dialog .el-textarea__inner:disabled,
.requirement-dialog .el-textarea__inner[readonly] {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #c0c4cc !important;
  cursor: not-allowed !important;
}

/* 调试样式 */
.debug-info {
  padding: 5px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
  margin-bottom: 10px;
}
.radio-options-container {
  display: flex;
  gap: 20px;
}

.radio-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s;
}

.radio-option:hover {
  background-color: #f5f7fa;
}
.radio-circle {
  width: 16px;
  height: 16px;
  border: 1px solid #dcdfe6;
  border-radius: 50%;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.radio-option.selected .radio-circle {
  border-color: #409eff;
}

.radio-inner {
  width: 8px;
  height: 8px;
  background-color: #409eff;
  border-radius: 50%;
}

.el-form-item__label {
  width:120px;
}

.bold-label {
  font-weight: bold;
}
.selected {
  /* 选中状态的样式 */
}

/* 新增样式 */
.option-group {
  margin-top: 10px;
  width: 100%;
}

.radio-options-subcontainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 5px;
}

.bold-label .el-form-item__label {
  white-space: nowrap;
}

</style>
