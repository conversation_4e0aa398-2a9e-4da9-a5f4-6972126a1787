<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>测试用例管理</el-breadcrumb-item>
      <el-breadcrumb-item>核心用例列表</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="4">
          <el-input placeholder="用例场景"
                    v-model="caseListQuery.case_scene"
                    clearable
                    @keyup.enter.native="queryList"
                    >
          </el-input>
        </el-col>
            <el-col :span="4">
          <el-input placeholder="需求标题"
                    v-model="caseListQuery.requirement"
                    clearable
                    @keyup.enter.native="queryList"
                    >
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-input placeholder="所属系统"
                    v-model="caseListQuery.system"
                    clearable
                    @keyup.enter.native="queryList"
                    >
          </el-input>
        </el-col>
         <el-col :span="4">
          <el-input placeholder="所属模块"
                    v-model="caseListQuery.module"
                    clearable
                    @keyup.enter.native="queryList"
                    >
          </el-input>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="queryList">查 询</el-button>
          <el-button type="primary" @click="queryReset">重 置</el-button>
          <el-button type="warning" @click="handleAddCase">新增用例</el-button>
          <el-button type="warning" @click="handlePushCase">推送已合并用例</el-button>
        </el-col>
        </el-row>
      <el-row>
        <el-table :data="caseList" border width="100%" :scroll-x="true" v-loading="getCaseListLoading">
        <el-table-column label="业务线" prop="businessName" min-width="60px" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="迭代名称" prop="iteration_name" show-overflow-tooltip min-width="80px" align="center"></el-table-column>
          <el-table-column label="用例场景" prop="case_scene" min-width="100px" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="需求标题" prop="requirement" min-width="100px" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="所属系统" prop="system" min-width="80px" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="模块" prop="module" min-width="80px" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="前置条件" prop="premise" min-width="120px" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="测试步骤" prop="test_steps" min-width="100px" align="center">
            <template #default="scope">
                <!-- 调用 getTextContent 方法获取并显示文本 -->
              <el-tooltip placement="top">
                <div style="white-space: nowrap;text-overflow: ellipsis; width: 120px; overflow: hidden; text-align: left">{{ scope.row.test_steps }}</div>
                <div slot="content" v-html="formatTooltipContent(scope.row.test_steps)" style="white-space: pre-line"></div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="预期结果" prop="expected_result" min-width="120px" align="center">
            <template #default="scope">
                <!-- 调用 getTextContent 方法获取并显示文本 -->
              <el-tooltip placement="top">
                <div style="white-space: nowrap;text-overflow: ellipsis; width: 120px; overflow: hidden; text-align: left">{{ scope.row.expected_result }}</div>
                <div slot="content" v-html="formatTooltipContent(scope.row.expected_result)" style="white-space: pre-line"></div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="150px" align="center">
            <template v-slot="slotProps">
              <el-tooltip class="item" effect="dark" content="编辑用例" placement="top" :enterable="false">
                <el-button type="warning" icon="el-icon-edit" size="mini" circle
                           @click="handleUpdateCoreCase(slotProps.row)"></el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="删除" placement="top" :enterable="false">
                <el-popconfirm
                  title="确定要删除此记录吗？"
                  confirm-button-text="确定"
                  cancel-button-text="取消"
                  icon="el-icon-warning"
                  @confirm="deleteImportCoreReport(slotProps.row.id)"
                >
                  <el-button v-if="role_id ==='0' || role_id ==='1'" type="danger" size="mini" icon="el-icon-delete"
                             slot="reference" circle></el-button>
                </el-popconfirm>
              </el-tooltip>
              <el-dropdown>
                <el-button type="primary" size="mini">
                  AI用例整合 <i class="el-icon-arrow-down"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item>
                    <el-button type="danger" size="mini" @click="aiMatchCase(slotProps.row)">与用例池匹配</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button type="primary" size="mini" @click="mergeCase(slotProps.row)">合并至用例池</el-button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="caseListQuery.pagenum"
          :page-size="caseListQuery.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!--编辑用例弹框-->
    <el-dialog
      :visible.sync="handleUpdateCoreCaseDialogVisible"
      title="编辑核心用例"
      width="80%">
      <el-divider content-position="center" style="margin-top: -30px">基本信息</el-divider>
      <el-form label-width="140px" :model="coreCaseDetailForm" :rules="coreCaseDetailFormRules"
               ref="coreCaseDetailFormRef" key="coreCaseDetailFormRef">
        <el-row>
          <el-col :span="9">
            <el-form-item label="业务线:" class="bold-label">
              <span>{{ coreCaseDetailForm.businessName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="15">
            <el-form-item label="迭代名称:" class="bold-label">
              <span>{{ coreCaseDetailForm.iteration_name }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="9">
            <el-form-item label="所属系统:" class="bold-label">
              <el-input v-model="coreCaseDetailForm.system" placeholder="请输入所属模块" maxlength="50"
                        show-word-limit/>
            </el-form-item>
          </el-col>
          <el-col :span="15">
            <el-form-item label="所属模块:" class="bold-label" prop="module">
              <el-input v-model="coreCaseDetailForm.module" placeholder="请输入所属模块" maxlength="50"
                        show-word-limit/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="需求标题:" class="bold-label" prop="requirement">
            <el-input
              type="textarea"
              :rows="2"
              maxlength="500"
              show-word-limit
              v-model="coreCaseDetailForm.requirement"
              placeholder="请输入需求标题"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="用例场景:" class="bold-label" prop="case_scene">
            <el-input
              type="textarea"
              :rows="2"
              maxlength="500"
              show-word-limit
              v-model="coreCaseDetailForm.case_scene"
              placeholder="请输入用例场景"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="前置条件:" class="bold-label" prop="premise">
            <el-input
              type="textarea"
              :rows="3"
              maxlength="1000"
              show-word-limit
              v-model="coreCaseDetailForm.premise"
              placeholder="请输入前置条件"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="测试步骤:" class="bold-label" prop="test_steps">
            <el-input
              type="textarea"
              :rows="5"
              maxlength="1000"
              show-word-limit
              v-model="coreCaseDetailForm.test_steps"
              placeholder="请输入测试步骤"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="预期结果:" class="bold-label" prop="expected_result">
            <el-input
              type="textarea"
              :rows="5"
              maxlength="1000"
              show-word-limit
              v-model="coreCaseDetailForm.expected_result"
              placeholder="请输入预期结果"></el-input>
          </el-form-item>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关键字" prop="keywords">
              <el-input v-model="coreCaseDetailForm.keyword" placeholder="请输入关键字" maxlength="50"
                        show-word-limit/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="自动化场景ID" prop="case_scene_id">
              <el-input v-model="coreCaseDetailForm.case_scene_id" placeholder="请输入自动化场景ID" maxlength="100"
                        show-word-limit/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="text-align: right">
            <span>
              <el-button @click="cancelEdit">取 消</el-button>
              <el-button type="primary" @click="submitUpdateCase()">保 存</el-button>
            </span>
        </el-row>
      </el-form>
    </el-dialog>
    <el-dialog
      :visible.sync="addtestcaseVisible"
      title="新增核心用例"
      width="80%">
      <el-form label-width="140px" :model="addCaseForm" :rules="addCaseFormRules" ref="addCaseFormRef"
               key="addCaseFormRef">
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="所属业务线" prop="business_id">
              <el-select placeholder="请选择业务线"
                         v-model="addCaseForm.business_id"
                         clearable
                         filterable>
                <el-option v-for="item in businessList"
                           :key="item.id"
                           :label="item.label"
                           :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属模块" prop="module">
              <el-input v-model="addCaseForm.module" placeholder="请输入所属模块" maxlength="50" show-word-limit/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="需求标题" prop="requirements">
          <el-input
            v-model="addCaseForm.requirements"
            placeholder="请输入需求标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="所属系统" prop="system">
          <el-input
            v-model="addCaseForm.system"
            placeholder="请输入所属系统"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="场景名称" prop="case_scene">
          <el-input
            v-model="addCaseForm.case_scene"
            type="textarea"
            :rows="3"
            placeholder="请输入场景名称"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="前置条件" prop="premise">
          <el-input
            v-model="addCaseForm.premise"
            type="textarea"
            :rows="3"
            placeholder="请输入前置条件"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="操作步骤" prop="test_steps">
          <el-input
            v-model="addCaseForm.test_steps"
            type="textarea"
            :rows="5"
            placeholder="请输入详细的操作步骤"
          />
        </el-form-item>
        <el-form-item label="预期结果" prop="expected_result">
          <el-input
            v-model="addCaseForm.expected_result"
            type="textarea"
            :rows="3"
            placeholder="请输入预期结果(0-500字符)"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关键字" prop="keywords">
              <el-input v-model="addCaseForm.keywords" placeholder="请输入关键字" maxlength="50" show-word-limit/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="自动化场景ID" prop="case_scene_id">
              <el-input v-model="addCaseForm.case_scene_id" placeholder="请输入自动化场景ID" maxlength="100"
                        show-word-limit/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item>
          <el-button type="primary" @click="addTestCase()">新 增</el-button>
          <el-button @click="closeAddCaseDialog">取 消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!--处理合并/废弃用例弹框-->
    <el-dialog
      title="合并用例"
      :visible.sync="handleMergeCaseDialogVisible"
      width="80%"
      @close="closeMergeCaseDialogVisible"
    >
      <!-- 上方基本信息展示 -->
      <el-form label-width="140px" :model="coreCaseDetailForm">
        <el-row :gutter="15">
          <el-col :span="4">
            <el-form-item  class="bold-label" label="用例ID：" prop="id">
              <span>{{ coreCaseDetailForm.id }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item class="bold-label" label="所属模块：" prop="module">
              <span>{{ coreCaseDetailForm.module }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="bold-label" label="场景名称：" prop="case_scene">
              <span>{{ coreCaseDetailForm.case_scene }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item class="bold-label" label="前置条件：" prop="premise">
          <span>{{ coreCaseDetailForm.premise }}</span>
        </el-form-item>
        <el-form-item class="bold-label" label="操作步骤：" prop="test_steps">
          <span v-html="formatTooltipContent(coreCaseDetailForm.test_steps)"></span>
        </el-form-item>
        <el-form-item class="bold-label" label="预期结果：" prop="expected_result">
          <span v-html="formatTooltipContent(coreCaseDetailForm.expected_result)"></span>
        </el-form-item>
      </el-form>
      <!-- 下方匹配用例表格 -->
      <div class="matched-cases">
        <h3>匹配的核心用例</h3>
        <el-table
          :data="matchedCases"
          border
          style="width: 100%"
        >
          <el-table-column prop="id" label="核心用例ID" width="120"/>
          <el-table-column prop="case_scene" label="场景名称" show-overflow-tooltip/>
          <el-table-column prop="premise" label="前置条件" show-overflow-tooltip/>
          <el-table-column label="测试步骤"  align="center">
            <template #default="scope">
              <!-- 调用 getTextContent 方法获取并显示文本 -->
              <el-tooltip placement="top">
                <div style="white-space: nowrap;text-overflow: ellipsis; width: 180px; overflow: hidden; text-align: left">
                  {{ scope.row.test_steps }}
                </div>
                <div slot="content" v-html="formatTooltipContent(scope.row.test_steps)"
                     style="white-space: pre-line"></div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="预期结果"  align="center">
            <template #default="scope">
              <el-tooltip placement="top">
                <div style="white-space: nowrap;text-overflow: ellipsis; width: 180px; overflow: hidden; text-align: left">
                  {{ scope.row.expected_result }}
                </div>
                <div slot="content" v-html="formatTooltipContent(scope.row.expected_result)"
                     style="white-space: pre-line"></div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="100px" align="center">
            <template v-slot="slotProps">
              <el-button
                type="primary"
                size="mini"
                @click="aiMergeCase(slotProps.row)"
              >合并
              </el-button>
              <el-button
                type="warning"
                size="mini"
                @click="handleObsolete(slotProps.row)"
              >不处理
              </el-button>
              <el-tooltip class="item" effect="dark" content="删除" placement="top" :enterable="false">
                <el-popconfirm
                  title="确定要从用例池中删除此用例吗？"
                  confirm-button-text="确定"
                  cancel-button-text="取消"
                  icon="el-icon-warning"
                  @confirm="handleDelete(slotProps.row.id)"
                >
                  <el-button type="danger" icon="el-icon-delete" size="mini" slot="reference"></el-button>
                </el-popconfirm>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <!-- 合并确认对话框 -->
    <el-dialog
      title="合并确认"
      :visible.sync="mergeConfirmVisible"
      width="80%"
      @close="closeMergeConfirmVisible"
    >
      <el-form label-width="100px" :model="mergeCaseDetailForm" :rules="mergeCaseDetailFormRules"
               ref="mergeCaseDetailFormRef" key="mergeCaseDetailFormRef">
        <el-row>
          <el-col :span="12">
            <el-form-item label="迭代核心ID：" class="bold-label" prop="test_case_id">
              <span>{{ mergeCaseDetailForm.source_info.test_case_id }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标准用例ID：" class="bold-label" prop="norm_case_id">
              <span>{{ mergeCaseDetailForm.source_info.norm_case_id }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="场景名称" class="bold-label" prop="case_scene">
            <el-input
              v-model="mergeCaseDetailForm.merged_content.case_scene"
              type="textarea"
              :rows="3"
              placeholder="请输入场景名称"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="前置条件" class="bold-label" prop="premise">
            <el-input
              v-model="formattedPremise"
              type="textarea"
              :rows="3"
              placeholder="请输入前置条件"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="操作步骤" class="bold-label" prop="test_steps">
            <el-input
              v-model="formattedTestSteps"
              type="textarea"
              :rows="5"
              placeholder="请输入详细的操作步骤"
              maxlength="5000"
              show-word-limit
            />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="预期结果" class="bold-label" prop="expected_result">
            <el-input
              v-model="formattedExpectedResult"
              type="textarea"
              :rows="3"
              placeholder="请输入预期结果(0-500字符)"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="confirmMerge">确 认</el-button>
            <el-button @click="closeMergeConfirmVisible">取 消</el-button>
        </span>
    </el-dialog>
   </div>
</template>

<script>
import { baseUrl } from '../../main'
export default {
  data() {
    return {
      model: 'test_case_info',
      caseList: [], // 核心用例列表
      matchedCases: [], // 匹配到的用例
      user_name: window.localStorage.getItem('user_name'), // 当前用户名
      chinese_name: window.localStorage.getItem('chinese_name'),
      staff_no: window.localStorage.getItem('staff_no'),
      role_id: window.localStorage.getItem('role_id'),
      report_id: this.$route.query.report_id, // 导入记录ID
      iteration_name: this.$route.query.iteration_name, // 迭代名称
      caseListQuery: { // 查询栏
        report_id: this.$route.query.report_id, // 导入记录ID
        requirement: '', // 需求标题
        case_scene: '', // 场景名称
        system: '', // 系统
        module: '', // 模块
        pagenum: 1,
        pagesize: 10
      },
      total: 0,
      getCaseListLoading: false, // 用例列表加载状态
      handleUpdateCoreCaseDialogVisible: false, // 用例详情对话框
      addtestcaseVisible: false, // 新增用例对话框
      handleMergeCaseDialogVisible: false, // 用例合并对话框
      mergeConfirmVisible: false, // 用例合并确认对话框
      // 合并用例表单
      mergeCaseDetailForm: {
        source_info: {
          test_case_id: '',
          norm_case_id: ''
        },
        merged_content: {
          case_scene: '',
          premise: '',
          test_steps: '',
          expected_result: ''
        }
      },
      loading: false,
      addCaseForm: {}, // 用例详情表单
      businessList: [], // 业务线列表
      coreCaseDetailForm: {}, // 用例详情表单
      selectedRows: [],
      //  合并用例表单验证规则
      mergeCaseDetailFormRules: {
        'merged_content.case_scene': [
          { required: true, message: '请输入场景名称', trigger: 'blur' },
          { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
        ],
        'merged_content.test_steps': [
          { required: true, message: '请输入操作步骤', trigger: 'blur' }
        ],
        'merged_content.expected_result': [
          { required: true, message: '请输入预期结果', trigger: 'blur' },
          { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
        ]
      },
      //  新增用例表单验证规则
      addCaseFormRules: {
        keyword: [
          { required: true, message: '请输入关键字', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        case_scene: [
          { required: true, message: '请输入场景名称', trigger: 'blur' },
          { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
        ],
        business_id: [
          { required: true, message: '请选择所属业务线', trigger: 'change' }
        ],
        module: [
          { required: true, message: '请输入所属模块', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        test_steps: [
          { required: true, message: '请输入操作步骤', trigger: 'blur' }
        ],
        expected_result: [
          { required: true, message: '请输入预期结果', trigger: 'blur' },
          { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
        ]
      },
      // 编辑用例表单验证规则
      coreCaseDetailFormRules: {
        test_steps: [
          { required: true, message: '请输入测试步骤', trigger: 'change' }
        ],
        expected_result: [
          { required: true, message: '请输入预期结果', trigger: 'change' }
        ],
        requirement: [
          { required: true, message: '请输入需求标题', trigger: 'change' }
        ],
        module: [
          { required: true, message: '请输入所属模块', trigger: 'change' }
        ],
        case_scene: [
          { required: true, message: '请输入用例场景', trigger: 'change' }
        ]
      }
    }
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() {
    this.getCaseList()
    this.getBusinessList()
  },
  activated() {
    // 监听键盘事件
    window.addEventListener('keydown', this.handleKeydown)
    this.queryReset()
  },
  deactivated() {
    // 清理事件监听器
    window.removeEventListener('keydown', this.handleKeydown)
  },
  computed: {
    // 前置条件
    formattedPremise: {
      get() {
        return this.formatText(this.mergeCaseDetailForm.merged_content.premise)
      },
      set(value) {
        this.mergeCaseDetailForm.merged_content.premise = this.unformatText(value)
      }
    },
    // 测试步骤
    formattedTestSteps: {
      get() {
        return this.formatText(this.mergeCaseDetailForm.merged_content.test_steps)
      },
      set(value) {
        this.mergeCaseDetailForm.merged_content.test_steps = this.unformatText(value)
      }
    },
    // 预期结果
    formattedExpectedResult: {
      get() {
        return this.formatText(this.mergeCaseDetailForm.merged_content.expected_result)
      },
      set(value) {
        this.mergeCaseDetailForm.merged_content.expected_result = this.unformatText(value)
      }
    }
  },
  methods: {
    //  查询
    queryList() {
      this.caseListQuery.pagenum = 1
      this.getCaseList()
    },
    // 翻页
    handleSizeChange(newSize) {
      this.caseListQuery.pagesize = newSize
      this.getCaseList()
    },
    handleCurrentChange(newPage) {
      this.caseListQuery.pagenum = newPage
      this.getCaseList()
    },
    // 获取核心用例列表
    async getCaseList() {
      this.getCaseListLoading = true
      const { data: res } = await this.$http.post(this.model + '/core_case_list/', this.caseListQuery)
      if (res.meta.status !== 200) return this.$message.error('获取核心用例列表失败')
      this.total = res.data.total
      this.caseList = res.data.data.map(item => ({
        ...item
      }))
      this.getCaseListLoading = false
    },
    // 获取业务线列表
    async getBusinessList() {
      const { data: res } = await this.$http.get('/user_info/info/', { params: { type: 'businesses' } })
      if (res.meta.status !== 200) return this.$message.error('获取业务线列表失败')
      this.businessList = res.data
    },
    // 取消编辑弹框
    cancelEdit() {
      this.coreCaseDetailForm = {}
      this.handleUpdateCoreCaseDialogVisible = false
    },
    // 编辑冒烟用例
    async handleUpdateCoreCase(row) {
      const objString = JSON.stringify(row)
      this.coreCaseDetailForm = JSON.parse(objString)
      this.handleUpdateCoreCaseDialogVisible = true
    },
    // 删除核心用例
    async deleteImportCoreReport(id) {
      const { data: res } = await this.$http.delete(this.model + '/' + id + '/import_core_report_delete/')
      if (res.meta.status !== 200) return this.$message.error('删除失败')
      this.$message.success('删除成功')
      await this.getCaseList()
    },
    // AI匹配用例
    async aiMatchCase(row) {
      const objString = JSON.stringify(row)
      this.coreCaseDetailForm = JSON.parse(objString)
      const Data = {
        report_id: null,
        core_case_id: this.coreCaseDetailForm.id
      }
      const { data: res } = await this.$http.post(this.model + '/ai_match_case/', Data)
      if (res.meta.status !== 200) return this.$message.error('AI匹配失败')
      this.$message.success('操作成功，AI匹配中')
      await this.getCaseList()
    },
    // 单个用例合并处理
    async mergeCase(row) {
      const objString = JSON.stringify(row)
      this.coreCaseDetailForm = JSON.parse(objString)
      const id = this.coreCaseDetailForm.id
      await this.getRelatedCases(id)
      this.handleMergeCaseDialogVisible = true
    },
    // 查询核心用例匹配的关联用例
    async getRelatedCases(id) {
      try {
        // const caseId = row.id // 直接从 row 获取 id
        const { data: res } = await this.$http.get(this.model + '/get_related_cases/', { params: { id: id } })
        if (Array.isArray(res)) {
          this.matchedCases = res
        } else if (res && res.meta && res.meta.status === 200) {
          // 如果后端返回的是 { meta: { status: 200 }, data: [...] }
          this.matchedCases = res.data || []
        } else {
          // 其他情况视为失败
          this.$message.error('查询失败')
          this.matchedCases = []
        }
      } catch (error) {
        console.error('获取关联用例失败:', error)
        this.$message.error('获取关联用例失败')
        this.matchedCases = []
      }
    },
    // 关闭合并处理弹框
    closeMergeCaseDialogVisible() {
      this.coreCaseDetailForm = {}
      this.handleMergeCaseDialogVisible = false
    },
    // 打开合并确认框
    // async handleMerge(row) {
    //   try {
    //     // 1. 等待合并操作完成
    //     await this.aiMergeCase(this.coreCaseDetailForm.id, row.id)
    //     // 2. 只有成功时才打开弹窗
    //     this.mergeConfirmVisible = true
    //   } catch (error) {
    //     // 3. 错误已在 aiMergeCase 或拦截器中处理，这里只需确保不打开弹窗
    //     console.error('合并失败:', error)
    //   } finally {
    //     // 4. 无论成功失败都关闭 Loading（确保不会卡住界面）
    //     this.$loading().close()
    //   }
    // },
    // 调用合并方法合并用例-结果展示在合并确认页面
    async aiMergeCase(row) {
      const caseId = this.coreCaseDetailForm.id
      const normTestcaseId = row.id
      const loadingInstance = this.$loading({
        lock: true,
        text: 'AI正在合并用例,请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      try {
        const { data: res } = await this.$http.post(this.model + '/merge_case/', {
          test_case_id: caseId,
          norm_case_id: normTestcaseId
        })
        if (res.meta.status !== 200) return this.$message.error('合并出错，请稍后再试')
        this.mergeCaseDetailForm = res.data
        this.mergeConfirmVisible = true
      } catch (error) {
        console.error('合并请求失败:', error)
        this.$message.error('请求失败，请检查网络或稍后重试')
      } finally {
        loadingInstance.close()
      }
    },
    // 关闭合并确认弹框
    closeMergeConfirmVisible() {
      this.mergeCaseDetailForm = {
        source_info: {},
        merged_content: {}
      }
      this.mergeConfirmVisible = false
    },
    // 提交合并
    async confirmMerge() {
      this.$refs.mergeCaseDetailFormRef.validate(async valid => {
        if (!valid) return false
        // 这里调用API提交表单数据
        const Data = {
          case_id: this.mergeCaseDetailForm.source_info.test_case_id,
          norm_testcase_id: this.mergeCaseDetailForm.source_info.norm_case_id,
          iteration_name: this.iteration_name,
          premise: this.mergeCaseDetailForm.merged_content.premise,
          case_scene: this.mergeCaseDetailForm.merged_content.case_scene,
          test_steps: this.mergeCaseDetailForm.merged_content.test_steps,
          expected_result: this.mergeCaseDetailForm.merged_content.expected_result
        }
        const { data: res } = await this.$http.post(this.model + '/merge_case_confirm/', Data)
        if (res.meta.status !== 200) return this.$message.error('处理失败')
        this.$message.success('处理成功')
        this.mergeConfirmVisible = false
        await this.getRelatedCases(this.coreCaseDetailForm.id)
        this.mergeCaseDetailForm = {
          source_info: {},
          merged_content: {}
        }
      })
    },
    // 处理删除
    async handleDelete(caseId) {
      const { data: res } = await this.$http.delete(this.model + '/testcase_delete/', { params: { id: caseId } })
      if (res.meta.status !== 200) return this.$message.error('删除失败')
      this.$message.success('删除成功')
      await this.getRelatedCases(this.coreCaseDetailForm.id)
    },
    // 不处理操作
    handleObsolete(row) {
      const coreCaseId = this.coreCaseDetailForm.id
      const matchedCaseId = row.id
      this.markCaseAsObsolete(coreCaseId, matchedCaseId)
      // 刷新匹配用例列表
      this.getRelatedCases(this.coreCaseDetailForm.id)
    },
    // 标记用例为不处理
    async markCaseAsObsolete(coreCaseId, matchedCaseId) {
      try {
        const response = await this.$http.post(this.model + '/obsolete_case/', {
          case_id: coreCaseId,
          norm_testcase_id: matchedCaseId
        })
        if (response.data.code === 200) {
          this.$message.success('已标记为不处理')
        } else {
          this.$message.error(response.data.message || '操作失败')
        }
      } catch (error) {
        this.$message.error('请求出错: ' + error.message)
      }
    },
    // 推送用例
    async handlePushCase() {
      const { data: res } = await this.$http.post(this.model + '/push_merge_case/', {
        case_report_id: this.report_id,
        iteration_name: this.iteration_name
      })
      if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
      this.$message.success(`推送成功:${res.meta.msg.count}条数据`)
    },
    // 打开新增弹框
    handleAddCase() {
      this.addtestcaseVisible = true
    },
    // 关闭新增弹框
    closeAddCaseDialog() {
      this.addCaseForm = {}
      this.addtestcaseVisible = false
    },
    // 新增用例
    addTestCase() {
      this.$refs.addCaseFormRef.validate(async valid => {
        if (!valid) return false
        const addData = {
          module: this.addCaseForm.module,
          premise: this.addCaseForm.premise,
          case_scene: this.addCaseForm.case_scene,
          business_id: this.addCaseForm.business_id,
          case_scene_id: this.addCaseForm.case_scene_id,
          keywords: this.addCaseForm.keywords,
          test_steps: this.addCaseForm.test_steps,
          expected_result: this.addCaseForm.expected_result,
          system: this.addCaseForm.system,
          requirements: this.addCaseForm.requirements,
          case_report_id: this.$route.query.report_id,
          iteration_name: this.$route.query.iteration_name
        }
        const { data: res } = await this.$http.post(this.model + '/corecase_add/', addData)
        if (res.meta.status !== 200) return this.$message.error('处理失败')
        this.$message.success('处理成功')
        this.addtestcaseVisible = false
        this.addCaseForm = {}
        await this.getCaseList()
      })
    },
    // 保存编辑用例
    async submitUpdateCase() {
      this.$refs.coreCaseDetailFormRef.validate(async valid => {
        if (!valid) return false
        let selectedIds = []
        if (this.selectedRows.length === 0 && this.coreCaseDetailForm) {
          selectedIds = [this.coreCaseDetailForm.id]
        } else {
          selectedIds = this.selectedRows.map(row => row.id)
        }
        const updateData = {
          id: selectedIds,
          system: this.coreCaseDetailForm.system,
          module: this.coreCaseDetailForm.module,
          requirement: this.coreCaseDetailForm.requirement,
          case_scene: this.coreCaseDetailForm.case_scene,
          expected_result: this.coreCaseDetailForm.expected_result,
          test_steps: this.coreCaseDetailForm.test_steps,
          premise: this.coreCaseDetailForm.premise,
          case_scene_id: this.coreCaseDetailForm.case_scene_id,
          keyword: this.coreCaseDetailForm.keyword
        }
        const { data: res } = await this.$http.post(this.model + '/update_core_case/', updateData)
        if (res.meta.status !== 200) return this.$message.error('处理失败')
        this.$message.success('处理成功')
        this.coreCaseDetailForm = {}
        this.handleUpdateCoreCaseDialogVisible = false
        await this.getCaseList()
      })
    },
    // 重置按钮方法
    queryReset() {
      this.caseListQuery = { // 查询栏
        report_id: this.$route.query.report_id, // 导入记录ID
        requirement: '', // 需求标题
        case_scene: '', // 场景名称
        system: '', // 系统
        module: '', // 模块
        pagenum: 1,
        pagesize: 10
      }
      this.getCaseList()
    },
    // tooltip换行显示
    formatTooltipContent(content) {
      return content && content.replace(/\n/g, '<br>')
    },
    // 格式化显示（1.xxx,2.yyy → 1.xxx\n2.yyy）
    formatText(content) {
      return content?.replace(/,(\d+\.)/g, ',\n$1') || ''
    },
    // 反向格式化存储（1.xxx\n2.yyy → 1.xxx,2.yyy）
    unformatText(content) {
      return content?.replace(/\n(\d+\.)/g, ',$1') || ''
    }
  }
}
</script>

<style>
.edit-input .el-input__inner {
  width: 100%;
  padding: 0 7px;
}
.edit-input .el-input__suffix{
  left: 50px;
}
.bold-label .el-form-item__label {
  font-weight: bold;
}

/* 鼠标悬浮效果 */
.el-table__body tr.el-table__row:hover > td,
.el-table__body tr.el-table__row--striped:hover > td {
  background-color: #ecf5ff !important;
}
.custom-button-group .el-button {
  margin: 4px !important; /* 移除按钮之间的间距 */
}

.el-dialog__body {
  padding-top: 0;
}

/* 设置弹框背景透明 */
.transparent-dialog .el-dialog {
  background-color: rgba(255, 255, 255, 0.13); /* 半透明背景 */
  box-shadow: none; /* 去掉阴影 */
  margin: 0; /* 去掉外边距 */
  position: fixed; /* 设为固定定位 */
  top: 0; /* 顶部为0 */
}
.transparent-dialog .el-dialog__header,
.transparent-dialog .el-dialog__body,
.transparent-dialog .el-dialog__footer {
  background-color: transparent; /* 内部元素背景透明 */
  margin: 0; /* 去掉内部元素的外边距 */
  padding: 0; /* 去掉内部元素的内边距 */
}

</style>

<style scoped>
/* 斑马纹样式 */
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #f5f7fa; /* 浅蓝色 */
}

/* 默认行颜色 */
::v-deep .el-table__body tr td {
  background: white; /* 白色 */
}

/* 鼠标悬停颜色 */
::v-deep .el-table__body tr:hover > td {
  background: #e6f7ff !important; /* 悬停时的浅蓝色 */
}

</style>
