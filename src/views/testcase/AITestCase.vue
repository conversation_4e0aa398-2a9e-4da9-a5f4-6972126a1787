<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>测试用例库</el-breadcrumb-item>
      <el-breadcrumb-item>AI测试用例库</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="4">
          <el-select placeholder="请选择业务线"
                     v-model="queryInfo.business_id"
                     clearable
                     filterable>
            <el-option v-for="item in businessList"
                       :key="item.id"
                       :label="item.label"
                       :value="item.id">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-input placeholder="迭代名称"
                    v-model="queryInfo.iteration_name"
                    clearable>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-input placeholder="所属模块"
                    v-model="queryInfo.module"
                    clearable>
          </el-input>
        </el-col>
         <el-col :span="8">
          <el-input placeholder="场景名称"
                    v-model="queryInfo.case_scene"
                    clearable>
          </el-input>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" style="height: 20px;"></el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="3">
          <el-input placeholder="关键字"
                    v-model="queryInfo.keywords"
                    clearable>
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-input placeholder="自动化场景ID"
                    v-model="queryInfo.case_scene_id"
                    clearable>
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-input placeholder="更新人"
                    v-model="queryInfo.modifier"
                    clearable>
          </el-input>
        </el-col>
        <el-col :span="7">
           <el-date-picker
              v-model="queryDate"
              :clearable="true"
              type="daterange"
              align="right"
              style="width: 300px"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd"
              disabledDate="false">
            </el-date-picker>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="queryTestCase">查 询</el-button>
          <el-button type="primary" @click="queryReset">重 置</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" style="height: 20px;"></el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-button v-if="staff_no === '3113'" type="primary" @click="vectorizeTestCases">初始向量化</el-button>
          <el-button type="primary" @click="handleAddCase">新 增</el-button>
          <el-button type="warning" @click="handleImport">导入用例</el-button>
          <el-button type="success" @click="handleExport">导出</el-button>
          <el-button
            type="danger"
            :disabled="!selectedRows.length"
            @click="handleBatchDelete"
          >批量删除
          </el-button>
        </el-col>
<!--        <el-col :span="6" style="text-align: right">-->
<!--          <el-button type="primary" @click="handleAIMerge">AI合并核心用例</el-button>-->
<!--        </el-col>-->
      </el-row>
    <!--列表字段-->
      <el-row>
        <el-table :data="normCaseList" border width="100%" :scroll-x="true" v-loading="getCaseListLoading" @selection-change="handleSelectionCase">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="所属业务线" prop="businessName" min-width="60px" align="center"></el-table-column>
          <el-table-column label="迭代名称" prop="iteration_name" show-overflow-tooltip min-width="120px" align="center"></el-table-column>
          <el-table-column label="模块" prop="module" min-width="80px" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="关键字" prop="keywords" min-width="80px" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="用例场景" prop="case_scene" min-width="100px" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="测试步骤" min-width="120px" align="center">
            <template #default="scope">
                <!-- 调用 getTextContent 方法获取并显示文本 -->
              <el-tooltip placement="top">
                <div style="white-space: nowrap;text-overflow: ellipsis; width: 180px; overflow: hidden; text-align: left">{{ scope.row.test_steps }}</div>
                <div slot="content" v-html="formatTooltipContent(scope.row.test_steps)" style="white-space: pre-line"></div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="预期结果" min-width="120px" align="center">
            <template #default="scope">
              <el-tooltip placement="top">
                <div style="white-space: nowrap;text-overflow: ellipsis; width: 180px; overflow: hidden; text-align: left">{{ scope.row.expected_result }}</div>
                <div slot="content" v-html="formatTooltipContent(scope.row.expected_result)" style="white-space: pre-line"></div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="自动化场景ID" prop="case_scene_id" min-width="80px" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="修改人员" prop="modifier" min-width="80px" align="center"></el-table-column>
          <el-table-column label="更新时间" prop="modify_time" min-width="80px" align="center"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="120px" align="center">
            <template v-slot="slotProps">
              <el-tooltip class="item" effect="dark" content="编辑用例" placement="top" :enterable="false">
                <el-button type="success" icon="el-icon-edit" size="mini" circle @click="handleUpdateTestCase(slotProps.row)"></el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="删除" placement="top" :enterable="false">
                <el-popconfirm
                  title="确定要删除此记录吗？"
                  confirm-button-text="确定"
                  cancel-button-text="取消"
                  icon="el-icon-warning"
                  @confirm="deleteTestCase(slotProps.row.id)"
                >
                  <el-button  type="warning" icon="el-icon-delete" size="mini" circle slot="reference"></el-button>
                </el-popconfirm>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!--新增用例-->
    <el-dialog
      title="新增用例"
      :visible.sync="addTestCaseVisible"
      width="60%"
      @close="closeAddCaseDialog"
    >
      <el-form label-width="140px" :model="addCaseForm" :rules="addCaseFormRules" ref="addCaseFormRef" key="addCaseFormRef">
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="所属业务线" prop="business_id">
              <el-select placeholder="请选择业务线"
                         v-model="addCaseForm.business_id"
                         clearable
                         filterable>
                <el-option v-for="item in businessList"
                           :key="item.id"
                           :label="item.label"
                           :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属模块" prop="module">
              <el-input v-model="addCaseForm.module" placeholder="请输入所属模块" maxlength="50" show-word-limit/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="场景名称" prop="case_scene">
            <el-input
              v-model="addCaseForm.case_scene"
              type="textarea"
              :rows="3"
              placeholder="请输入场景名称"
              maxlength="500"
              show-word-limit
            />
        </el-form-item>
        <el-form-item label="前置条件" prop="premise">
            <el-input
              v-model="addCaseForm.premise"
              type="textarea"
              :rows="3"
              placeholder="请输入前置条件"
              maxlength="500"
              show-word-limit
            />
        </el-form-item>
        <el-form-item label="操作步骤" prop="test_steps">
            <el-input
              v-model="addCaseForm.test_steps"
              type="textarea"
              :rows="5"
              placeholder="请输入详细的操作步骤"
            />
        </el-form-item>
        <el-form-item label="预期结果" prop="expected_result">
          <el-input
            v-model="addCaseForm.expected_result"
            type="textarea"
            :rows="3"
            placeholder="请输入预期结果(0-500字符)"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="关键字" prop="keywords">
                <el-input v-model="addCaseForm.keywords" placeholder="请输入关键字" maxlength="50" show-word-limit/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="自动化场景ID" prop="case_scene_id">
                <el-input v-model="addCaseForm.case_scene_id" placeholder="请输入自动化场景ID" maxlength="100"
                          show-word-limit/>
              </el-form-item>
            </el-col>
          </el-row>
        <el-form-item label="迭代名称" prop="iteration_name">
          <el-input
            v-model="addCaseForm.iteration_name"
            type="textarea"
            :rows="3"
            placeholder="请输入迭代名称"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="addTestCase()">新 增</el-button>
            <el-button @click="closeAddCaseDialog">取 消</el-button>
          </el-form-item>
        </el-form>
    </el-dialog>
    <!--编辑用例-->
    <el-dialog
      title="编辑用例"
      :visible.sync="handleUpdateTestCaseDialogVisible"
      width="60%"
      @close="closeUpdateCaseDialog"
    >
      <el-form label-width="140px" :model="testCaseDetailForm" :rules="testCaseDetailFormRules" ref="testCaseDetailFormRef" key="testCaseDetailFormRef">
        <el-row :gutter="15">
          <el-col :span="12">
            <el-form-item label="所属业务线" prop="business_line">
              <span>{{ testCaseDetailForm.businessName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属模块" prop="module">
              <el-input v-model="testCaseDetailForm.module" placeholder="请输入所属模块" maxlength="50" show-word-limit/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="场景名称" prop="case_scene">
            <el-input
              v-model="testCaseDetailForm.case_scene"
              type="textarea"
              :rows="3"
              placeholder="请输入场景名称"
              maxlength="500"
              show-word-limit
            />
        </el-form-item>
        <el-form-item label="前置条件" prop="premise">
            <el-input
              v-model="testCaseDetailForm.premise"
              type="textarea"
              :rows="3"
              placeholder="请输入前置条件"
              maxlength="500"
              show-word-limit
            />
        </el-form-item>
        <el-form-item label="操作步骤" prop="test_steps">
            <el-input
              v-model="testCaseDetailForm.test_steps"
              type="textarea"
              :rows="5"
              placeholder="请输入详细的操作步骤"
            />
        </el-form-item>
        <el-form-item label="预期结果" prop="expected_result">
          <el-input
            v-model="testCaseDetailForm.expected_result"
            type="textarea"
            :rows="3"
            placeholder="请输入预期结果(0-500字符)"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="关键字" prop="keywords">
                <el-input v-model="testCaseDetailForm.keywords" placeholder="请输入关键字" maxlength="50" show-word-limit/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="自动化场景ID" prop="case_scene_id">
                <el-input v-model="testCaseDetailForm.case_scene_id" placeholder="请输入自动化场景ID" maxlength="100"
                          show-word-limit/>
              </el-form-item>
            </el-col>
          </el-row>
        <el-form-item label="迭代名称" prop="iteration_name">
          <el-input
            v-model="testCaseDetailForm.iteration_name"
            type="textarea"
            :rows="3"
            placeholder="请输入迭代名称"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="updateTestCase()">保 存</el-button>
            <el-button @click="closeUpdateCaseDialog">取 消</el-button>
          </el-form-item>
        </el-form>
    </el-dialog>
    <!--导入弹框-->
    <el-dialog
      title="导入用例"
      :visible.sync="importTestCaseVisible"
      width="30%"
      @close="closeTestCaseDialog"
    >
      <el-form :model="importForm" :rules="importFormRules" ref="importForm">
        <div style="margin-bottom: 40px;"></div>
        <!-- 文件上传 -->
        <el-form-item>
          <el-upload
            :action="uploadPath"
            :headers="headers"
            class="upload-demo"
            ref="uploadRef"
            :auto-upload="true"
            :on-remove="handleRemove"
            :on-exceed="handleExceed"
            :on-success="onSuccess"
            :show-file-list="true"
            :limit="1"
          >
            <el-button type="primary">选择文件</el-button>
            <div v-if="importForm.file" style="margin-top: 10px">
              已选文件：{{ importForm.file.name }}
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeTestCaseDialog">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script >
import { baseUrl } from '../../main'
export default {
  data() {
    return {
      model: 'test_case_info',
      user_name: window.localStorage.getItem('user_name'),
      chinese_name: window.localStorage.getItem('chinese_name'),
      staff_no: window.localStorage.getItem('staff_no'),
      role_id: window.localStorage.getItem('role_id'),
      normCaseList: [],
      addTestCaseVisible: false,
      handleUpdateTestCaseDialogVisible: false,
      queryInfo: {
        business_id: '',
        iteration_name: '',
        modify_time: '',
        modifier: '',
        case_scene_id: '',
        keywords: '',
        module: '',
        case_scene: '',
        creator: '',
        start_date: '',
        end_date: '',
        pagenum: 1,
        pagesize: 10
      },
      addCaseForm: {},
      testCaseDetailForm: {},
      total: 0,
      businessList: [],
      queryDate: [],
      value: '',
      getCaseListLoading: false,
      selectedRows: [],
      importTestCaseVisible: false,
      importForm: {},
      importFormRules: {
        file: [
          { required: true, message: '请上传文件', trigger: 'change' }
        ]
      },
      addCaseFormRules: {
        keyword: [
          { required: true, message: '请输入关键字', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        case_scene: [
          { required: true, message: '请输入场景名称', trigger: 'blur' },
          { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
        ],
        businessLine: [
          { required: true, message: '请选择所属业务线', trigger: 'change' }
        ],
        module: [
          { required: true, message: '请输入所属模块', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        test_steps: [
          { required: true, message: '请输入操作步骤', trigger: 'blur' }
        ],
        iteration_name: [
          { required: true, message: '请输入迭代名称', trigger: 'blur' },
          { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
        ],
        expected_result: [
          { required: true, message: '请输入预期结果', trigger: 'blur' },
          { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
        ]
      },
      testCaseDetailFormRules: {
        keyword: [
          { required: true, message: '请输入关键字', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        case_scene: [
          { required: true, message: '请输入场景名称', trigger: 'blur' },
          { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
        ],
        businessLine: [
          { required: true, message: '请选择所属业务线', trigger: 'change' }
        ],
        module: [
          { required: true, message: '请输入所属模块', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        test_steps: [
          { required: true, message: '请输入操作步骤', trigger: 'blur' }
        ],
        iteration_name: [
          { required: true, message: '请输入迭代名称', trigger: 'blur' },
          { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
        ],
        expected_result: [
          { required: true, message: '请输入预期结果', trigger: 'blur' },
          { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
        ]
      },
      headers: {
        Authorization: window.localStorage.getItem('token'),
        userId: window.localStorage.getItem('userId')
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 31)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一年',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created() {
    this.getAiTestCaseList()
    this.getBusinessList()
  },
  computed: {
    uploadPath() {
      // 动态生成上传路径
      return `${baseUrl}user_info/upload_file/`
    }
  },
  watch: {
    // 监听 dateRange 的变化，自动更新 workChatInfo
    queryDate(newVal) {
      if (newVal && newVal.length >= 1) {
        this.queryInfo.start_date = newVal[0]
        this.queryInfo.end_date = newVal[1]
      } else {
        this.queryInfo.start_date = ''
        this.queryInfo.end_date = ''
      }
    }
  },
  methods: {
    // 设置每页条数
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getAiTestCaseList()
    },
    // 设置页数
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getAiTestCaseList()
    },
    // 将勾选的数据进行保存
    handleSelectionCase(selection) {
      this.selectedRows = selection
    },
    // AI用例池列表查询方法
    queryTestCase() {
      this.queryInfo.pagenum = 1
      this.getAiTestCaseList()
    },
    // 重置查询
    queryReset() {
      this.queryDate = []
      this.queryInfo = {
        business_id: '',
        iteration_name: '',
        modify_time: '',
        modifier: '',
        case_scene_id: '',
        keywords: '',
        module: '',
        case_scene: '',
        pagenum: 1,
        pagesize: 5
      }
      this.getAiTestCaseList()
    },
    // 打开导入弹框
    handleImport() {
      this.importTestCaseVisible = true
    },
    // 关闭导入弹框
    closeTestCaseDialog() {
      this.importForm = {}
      this.importTestCaseVisible = false
      // 通过 ref 调用 clearFiles 方法
      this.$refs.uploadRef.clearFiles()
    },
    // 提交导入文件
    submit() {
      this.$refs.importForm.validate((valid) => {
        if (valid) {
          this.$http.post(this.model + '/import_case_excel/', this.importForm)
            .then(response => {
              if (response.data.meta.status === 200) {
                this.$message.success('导入成功')
                this.getAiTestCaseList()
                this.importTestCaseVisible = false
                this.importForm = {}
              } else return this.$message.error('导入失败')
            })
            .catch(error => {
              this.$message.error('导入失败: ' + error.message)
            })
        } else {
          this.$message.error('请填写完整信息')
          return false
        }
      })
    },
    // 导出用例列表
    async handleExport() {
      try {
        // 准备导出参数，去除分页参数
        const exportParams = {
          ...this.queryInfo,
          // 如果有时间范围字段需要特殊处理
          start_date: this.queryInfo.start_date || '',
          end_date: this.queryInfo.end_date || ''
        }
        // 移除分页参数，因为导出通常是全部数据
        delete exportParams.pagenum
        delete exportParams.pagesize
        // 调用导出API
        // const response = await this.$http.post(this.model + '/export_test_case/', exportParams , { responseType: 'blob' })
        const response = await this.$http({
          method: 'post',
          url: `${this.model}/export_test_case/`,
          data: exportParams,
          responseType: 'blob',
          headers: {
            'Content-Type': 'application/json'
          }
        })
        // 生成文件名，可以更具业务需求调整
        const filename = `测试用例_${new Date().toLocaleDateString()}.xlsx`
        // 下载文件
        await this.downloadFile(response, filename)
        this.$message.success('导出成功')
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('导出失败: ' + (error.message || error))
        } else {
          this.$message.info('已取消导出')
        }
      }
    },
    // 获取用例列表
    async getAiTestCaseList() {
      this.getCaseListLoading = true
      const { data: res } = await this.$http.post(this.model + '/AI_test_case_list/', this.queryInfo)
      if (res.meta.status !== 200) return this.$message.error('获取用例记录列表失败')
      this.total = res.data.total
      this.normCaseList = res.data.data
      this.getCaseListLoading = false
    },
    // 新增用例
    addTestCase() {
      this.$refs.addCaseFormRef.validate(async valid => {
        if (!valid) return false
        // 这里调用API提交表单数据
        const addData = {
          module: this.addCaseForm.module,
          premise: this.addCaseForm.premise,
          iteration_name: this.addCaseForm.iteration_name,
          case_scene: this.addCaseForm.case_scene,
          business_id: this.addCaseForm.business_id,
          case_scene_id: this.addCaseForm.case_scene_id,
          keywords: this.addCaseForm.keywords,
          test_steps: this.addCaseForm.test_steps,
          expected_result: this.addCaseForm.expected_result
        }
        const { data: res } = await this.$http.post(this.model + '/testcase_add/', addData)
        if (res.meta.status !== 200) return this.$message.error('处理失败')
        this.$message.success('新增成功')
        this.addTestCaseVisible = false
        await this.getAiTestCaseList()
      })
    },
    // 打开新增弹框
    handleAddCase() {
      this.addTestCaseVisible = true
    },
    // 关闭新增弹框
    closeAddCaseDialog() {
      this.addCaseForm = {}
      this.addTestCaseVisible = false
    },
    // 编辑单个用例
    async handleUpdateTestCase(row) {
      const objString = JSON.stringify(row)
      this.testCaseDetailForm = JSON.parse(objString)
      this.handleUpdateTestCaseDialogVisible = true
    },
    // 编辑单个用例提交
    updateTestCase() {
      this.$refs.testCaseDetailFormRef.validate(async valid => {
        if (!valid) return false
        // 这里调用API提交表单数据
        const UpdateData = {
          id: this.testCaseDetailForm.id,
          module: this.testCaseDetailForm.module,
          premise: this.testCaseDetailForm.premise,
          iteration_name: this.testCaseDetailForm.iteration_name,
          case_scene: this.testCaseDetailForm.case_scene,
          business_line: this.testCaseDetailForm.business_line,
          case_scene_id: this.testCaseDetailForm.case_scene_id,
          keywords: this.testCaseDetailForm.keywords,
          test_steps: this.testCaseDetailForm.test_steps,
          expected_result: this.testCaseDetailForm.expected_result
        }
        const { data: res } = await this.$http.post(this.model + '/testcase_update/', UpdateData)
        if (res.meta.status !== 200) return this.$message.error('处理失败')
        this.$message.success('用例编辑成功')
        this.handleUpdateTestCaseDialogVisible = false
        await this.getAiTestCaseList()
      })
    },
    // 关闭编辑弹框
    closeUpdateCaseDialog() {
      this.testCaseDetailForm = {}
      this.handleUpdateTestCaseDialogVisible = false
    },
    // 删除用例
    async deleteTestCase(caseId) {
      const { data: res } = await this.$http.delete(this.model + '/testcase_delete/', { params: { id: caseId } })
      if (res.meta.status !== 200) return this.$message.error('删除失败')
      this.$message.success('删除成功')
      await this.getAiTestCaseList()
    },
    // 批量删除方法
    handleBatchDelete() {
      if (!this.selectedRows.length) {
        this.$message.warning('请至少选择一条数据')
        return
      }

      this.$confirm('确认删除选中的用例吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const ids = this.selectedRows.map(row => row.id).join(',')
          // 调用删除API
          await this.$http.delete(this.model + '/testcase_delete/', { params: { ids } })

          this.$message.success('删除成功')
          this.getAiTestCaseList()
          this.selectedRows = []
        } catch (error) {
          this.$message.error('删除失败: ' + error.message)
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    // 触发合并用例推送入库
    // 获取业务线列表
    async getBusinessList() {
      const { data: res } = await this.$http.get('/user_info/info/', { params: { type: 'businesses' } })
      if (res.meta.status !== 200) return this.$message.error('获取域名查询列表失败')
      this.businessList = res.data
    },
    // 初始向量化
    async vectorizeTestCases() {
      this.$loading({
        lock: true,
        text: 'AI正在初始向量化,请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const { data: res } = await this.$http.get(this.model + '/vectorize_test_cases/')
      if (res.meta.status !== 200) return this.$message.error('初始向量化失败')
      this.$loading().close()
      this.$message.success('初始向量化成功')
    },
    // tooltip换行显示
    formatTooltipContent(content) {
      return content && content.replace(/\n/g, '<br>')
    },
    // 删除文件
    handleRemove(file) {
      this.importForm.file_url = null
      this.$forceUpdate()
    },
    // 文件上传成功处理
    onSuccess(response, file) {
      this.importForm.file_url = response.data.file_path
      if (response.meta.status !== 200) return this.$message.error('文件上传失败')
      this.$message.success('文件上传成功！')
    },
    // 文件超出个数限制时的钩子
    handleExceed(files, fileList) {
      this.$message.warning('请先删除已上传文件后重新上传!')
    },
    // 下载文件
    async downloadFile(response, filename) {
      try {
        // 创建 Blob 对象
        const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        const downloadUrl = window.URL.createObjectURL(blob)
        // 创建一个隐藏的链接元素
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = filename
        document.body.appendChild(link)
        // 触发下载并移除链接元素
        link.click()
        link.remove()
        // 释放 URL 对象
        window.URL.revokeObjectURL(downloadUrl)
      } catch (error) {
        this.$message.error('导出失败')
      }
    }
  }
}
</script>

<style scoped>
.testcase-add-container {
  padding: 20px;
}
.box-card {
  max-width: 1000px;
  margin: 0 auto;
}
.el-select {
  width: 100%;
}
</style>
