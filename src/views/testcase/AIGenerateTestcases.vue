<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>测试用例库</el-breadcrumb-item>
      <el-breadcrumb-item>AI生成测试用例</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="4">
          <el-select placeholder="请选择业务线"
                     v-model="queryInfo.business_id"
                     clearable
                     filterable>
            <el-option v-for="item in businessList"
                       :key="item.id"
                       :label="item.label"
                       :value="item.id">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-input placeholder="迭代名称"
                    v-model="queryInfo.iteration_name"
                    clearable
                    @keyup.enter.native="queryRecognizeRecordList"
          >
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-input placeholder="需求ID"
                    v-model="queryInfo.requirement_id"
                    clearable
                    @keyup.enter.native="queryRecognizeRecordList"
          >
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.create_person"
                    clearable
                    @keyup.enter.native="queryRecognizeRecordList">
          </el-input>
        </el-col>
        <el-col :span="7">
          <el-button type="primary" @click="queryRecognizeRecordList">查 询</el-button>
          <el-button type="primary" @click="queryReset">重 置</el-button>
          <el-button type="warning" @click="handleImportRequirement">AI生成用例</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" style="height: 20px;"></el-col>
      </el-row>
      <el-row>
        <el-table :data="recognizeRecordList" border>
          <el-table-column label="迭代名称" prop="iteration_name"  show-overflow-tooltip min-width="170px" align="center"></el-table-column>
          <el-table-column label="业务线" prop="business_name" min-width="90px" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="需求ID" prop="requirement_id"  show-overflow-tooltip min-width="75px" align="center"></el-table-column>
          <el-table-column label="需求内容" min-width="80px" align="center">
            <template #default="scope">
              <el-popover
                placement="top"
                width="800"
                trigger="click"
                popper-class="dark-popover"
              >
                <div v-html="formatTooltipContent(scope.row.requirement_content)"
                     style="white-space: pre-line; font-size: 12px"></div>
                <div slot="reference"
                     class="cell-content"
                     style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden; min-width: 60px; cursor: pointer; color: #409EFF; text-align: center;">
                  点击查看
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="AI需求分析内容" min-width="105px" align="center">
            <template #default="scope">
              <el-popover
                placement="top"
                width="800"
                trigger="click"
                popper-class="dark-popover"
              >
                <div v-html="formatTooltipContent(scope.row.requirement_analyze_content)"
                     style="white-space: pre-line; font-size: 12px"></div>
                <div slot="reference"
                     class="cell-content"
                     style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden; min-width: 60px; cursor: pointer; color: #409EFF; text-align: center;">
                  点击查看
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="需求图片" min-width="90px" align="center">
            <template slot-scope="scope">
              <template v-if="hasImage(scope.row.requirement_pic)">
                <el-link @click="previewImages(scope.row.requirement_pic)" style="font-size: 12px;">查看图片<i class="el-icon-view el-icon--right"></i> </el-link>
              </template>
              <template v-else>
                <span>-</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="UI稿" prop="ui_link" align="center" min-width="80px">
            <template v-slot="scope">
              <el-tooltip
                v-if="scope.row.ui_link"
                :content="scope.row.ui_link"
                placement="top">
                <div
                  @click="openLink(scope.row.ui_link)"
                  class="cell-content"
                  style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden; min-width: 60px; cursor: pointer; color: #409EFF; text-align: center;">
                  点击查看
                </div>
              </el-tooltip>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="技术文档" prop="tech_link" align="center" min-width="80px">
            <template v-slot="scope">
              <el-tooltip
                v-if="scope.row.tech_link"
                :content="scope.row.tech_link"
                placement="top">
                <div
                  @click="openLink(scope.row.tech_link)"
                  class="cell-content"
                  style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden; min-width: 60px; cursor: pointer; color: #409EFF; text-align: center;">
                  点击查看
                </div>
              </el-tooltip>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="召回率" min-width="70px" align="center">
            <template slot-scope="scope">
              {{ scope.row.recall_ai_test_case }} / {{ scope.row.total_ai_test_case }}
            </template>
          </el-table-column>
          <el-table-column label="创建人" prop="create_person" min-width="80px" align="center"></el-table-column>
          <el-table-column label="上传时间" prop="create_time" min-width="150px" align="center"></el-table-column>
          <el-table-column label="操作" min-width="140px" align="center">
            <template v-slot="slotProps">
              <el-button type="primary" size="mini" @click="queryTestCaseList(slotProps.row.id), showTestCaseListDialog = true">查看生成用例</el-button>
              <el-button type="warning" size="mini" @click="exportRecallCase(slotProps.row)">下载召回用例</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>

    <!-- 录入需求弹框-->
    <el-dialog
      title="需求录入"
      :visible.sync="importRequirementVisible"
      width="60%"
      class="requirement-dialog"
      @close="importRequirementVisible=false"
    >
      <el-form label-width="100px" :model="importRequirementForm" :rules="importRequirementFormRules"
               ref="importRequirementFormRef" key="importRequirementFormRef">
      <!-- 业务线选择 -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="业务线" prop="business_id">
              <el-select v-model="importRequirementForm.business_id" filterable placeholder="请选择">
                <el-option v-for="item in businessList"
                           :key="item.id"
                           :label="item.label"
                           :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="需求ID" prop="requirement_id">
              <el-input v-model="importRequirementForm.requirement_id" placeholder="请输入需求ID"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="UI稿地址:" class="bold-label" prop="ui_link">
              <el-input
                  class="edit-input"
                  v-model="importRequirementForm.ui_link"
                  style="width: 750px; padding-left: 0px"
                  placeholder="请输入UI稿地址"
                ></el-input>
            </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="技术文档地址:" class="bold-label" prop="tech_link">
              <el-input
                  class="edit-input"
                  v-model="importRequirementForm.tech_link"
                  style="width: 750px; padding-left: 0px"
                  placeholder="请输入技术文档地址"
                ></el-input>
            </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="需求内容:" class="bold-label" prop="requirement_content">
            <el-input
              type="textarea"
              :rows="7"
              maxlength="3000"
              show-word-limit
              v-model="importRequirementForm.requirement_content"
              @paste.native="handlePastePic"
              ref="replyInput"
              placeholder="请输入需求内容或粘贴图片(支持jpg/png格式，单张图片不超过5MB,限制3张图片)"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
            <!-- 图片上传部分 -->
            <el-form-item label="上传图片:">
              <el-upload
                :action="uploadPath"
                list-type="picture-card"
                :multiple="true"
                :limit="3"
                :file-list="fileList"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :on-success="handleUploadSuccess"
                :on-exceed="handleExceed"
                :before-upload="beforeUpload"
                :on-change="handFileList"
                :auto-upload="true">
                <i class="el-icon-plus"></i>
                <div slot="tip" class="el-upload__tip">
                  支持jpg/png格式，单张图片不超过5MB
                </div>
              </el-upload>
            </el-form-item>
          </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importRequirementVisible=false">取 消</el-button>
        <el-button type="primary" @click="submitRequirement">下一步</el-button>
      </span>
    </el-dialog>
    <!-- AI分析结果弹框 -->
    <el-dialog
      title="需求分析"
      :visible.sync="showAnalyzeResultDialog"
      width="60%"
      class="requirement-dialog"
      @close="showAnalyzeResultDialog = false">
      <el-form label-width="100px" :model="importRequirementForm" :rules="importRequirementFormRules"
               ref="importRequirementFormRef" key="importRequirementFormRef">
        <el-form-item label="需求分析:" class="bold-label">
          <el-input
                type="textarea"
                :rows="15"
                maxlength="5000"
                show-word-limit
                v-model="importRequirementForm.requirementAnalyzePrd"
                placeholder="请输入需求分析内容"
                :disabled="false"
                :readonly="false"
                ref="analyzeTextarea"
                @focus="handleTextareaFocus"
                @blur="handleTextareaBlur"
                @input="handleTextareaInput"
                style="pointer-events: auto !important;"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showAnalyzeResultDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmAnalyzeResult">下一步</el-button>
      </span>
    </el-dialog>
    <!-- AI生成测试用例列表弹框 -->
    <el-dialog
      title="AI生成用例预览"
      :visible.sync="showTestCaseListDialog"
      width="80%"
      @close="showTestCaseListDialog = false">
      <el-row>
        <el-col>
          <el-dropdown trigger="click">
            <el-button style="border-color: #409EFF;color: #409EFF;" size="small">
              批量修改召回状态 <i class="el-icon-arrow-down"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <el-button style="color: #409EFF;" size="small" @click="batchUpdateCaseStatus('1')">批量召回</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button style="color: #409EFF;" size="small" @click="batchUpdateCaseStatus('0')">批量取消召回</el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="testCaseList" border @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="用例名称" prop="case_scene" min-width="120" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="所属系统" prop="system" min-width="100" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="模块" prop="module" min-width="100" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="前置条件" prop="premise" min-width="150" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="测试步骤" min-width="200" align="center">
            <template #default="scope">
                <!-- 调用 getTextContent 方法获取并显示文本 -->
              <el-tooltip placement="top">
                <div style="white-space: nowrap;text-overflow: ellipsis; width: 180px; overflow: hidden; text-align: left">{{ scope.row.test_steps }}</div>
                <div slot="content" v-html="formatTooltipContent(scope.row.test_steps)" style="white-space: pre-line"></div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="预期结果" min-width="200" align="center">
            <template #default="scope">
                <!-- 调用 getTextContent 方法获取并显示文本 -->
              <el-tooltip placement="top">
                <div style="white-space: nowrap;text-overflow: ellipsis; width: 180px; overflow: hidden; text-align: left">{{ scope.row.expected_result }}</div>
                <div slot="content" v-html="formatTooltipContent(scope.row.expected_result)" style="white-space: pre-line"></div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="召回状态" min-width="80" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.status==='1'" style="color: #67C23A;">已召回</span>
              <span v-else style="color: #f35d58;">未召回</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="130px" align="center">
            <template v-slot="slotProps">
                <el-button type="text" size="mini" @click="handleUpdateCase(slotProps.row)">编辑</el-button>
                <el-button v-if="slotProps.row.status==='0'" type="text" size="mini" @click="submitUpdateCaseStatus(slotProps.row, '1')">召回</el-button>
                <el-button v-if="slotProps.row.status==='1'" type="text" size="mini" @click="submitUpdateCaseStatus(slotProps.row, '0')">取消召回</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleCaseListSizeChange"
          @current-change="handleCaseListCurrentChange"
          :current-page.sync="queryTestCaseInfo.pagenum"
          :page-size="queryTestCaseInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="case_list_total">
        </el-pagination>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showTestCaseListDialog = false, queryRecognizeRecordList()">关闭</el-button>
      </span>
    </el-dialog>
    <!--编辑用例弹框-->
    <el-dialog
        :visible.sync="handleUpdateTestCaseDialogVisible"
        title = "编辑用例"
        width="80%">
        <el-divider content-position="center" style="margin-top: -30px">基本信息</el-divider>
        <el-form label-width="140px" :model="testCaseDetailForm" :rules="testCaseDetailFormRules" ref="testCaseDetailFormRef" key="testCaseDetailFormRef">
          <el-row>
            <el-col :span="8">
              <el-form-item label="用例场景" prop="case_scene">
                <el-input v-model="testCaseDetailForm.case_scene" placeholder="请输入用例场景"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属系统" prop="system">
                <el-input v-model="testCaseDetailForm.system" placeholder="请输入所属系统"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="模块" prop="module">
                <el-input v-model="testCaseDetailForm.module" placeholder="请输入模块"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item label="前置条件:" class="bold-label" prop="premise">
              <el-input
                type="textarea"
                :rows="3"
                maxlength="1000"
                show-word-limit
                v-model="testCaseDetailForm.premise"
                placeholder="请输入前置条件"></el-input>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="测试步骤:" class="bold-label" prop="test_steps">
              <el-input
                type="textarea"
                :rows="5"
                maxlength="1000"
                show-word-limit
                v-model="testCaseDetailForm.test_steps"
                placeholder="请输入测试步骤"></el-input>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="预期结果:" class="bold-label" prop="expected_result">
              <el-input
                type="textarea"
                :rows="5"
                maxlength="1000"
                show-word-limit
                v-model="testCaseDetailForm.expected_result"
                placeholder="请输入预期结果"></el-input>
            </el-form-item>
          </el-row>
          <el-row style="text-align: right">
            <span>
              <el-button @click="cancelCaseEdit">取 消</el-button>
              <el-button type="primary" @click="submitUpdateCase()">保 存</el-button>
            </span>
          </el-row>
        </el-form>
      </el-dialog>
    <!--图片预览弹框-->
    <el-dialog
      :visible.sync="dialogVisible"
      width="100%"
      class="transparent-dialog">
      <div style="position: relative; height: 620px;">
        <!-- 图片展示区域 -->
        <div v-if="previewImagesList.length > 0" style="height: 100%; display: flex; align-items: center; justify-content: center;">
          <!-- 页码提示 -->
          <div style="position: absolute; top: 0px; left: 50%; transform: translateX(-50%); color: rgb(255,255,255); font-weight: bold; font-size: 24px;">
            {{ currentImageIndex + 1 }} / {{ previewImagesList.length }}
          </div>
          <img
            :src="currentImageSrc"
            style="max-width: 90%; max-height: 550px; display: block;"
          >
          <!-- 分页控制器 -->
          <div>
            <el-button
              circle
              size="mini"
              :disabled="currentImageIndex === 0"
              @click="previousImage"
              style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%);"
            >
              <i class="el-icon-arrow-left" style="font-size: 30px; font-weight: bold;"></i>
            </el-button>
            <el-button
              circle
              size="mini"
              :disabled="currentImageIndex === previewImagesList.length - 1"
              @click="nextImage"
              style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-weight: bold;"
            >
              <i class="el-icon-arrow-right" style="font-size: 30px; font-weight: bold;"></i>
            </el-button>
          </div>
        </div>
        <div v-else style="text-align: center; padding: 50px;">
          暂无可用图片
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { baseUrl } from '../../main'
export default {
  data() {
    return {
      model: 'test_case_info',
      user_name: window.localStorage.getItem('user_name'),
      chinese_name: window.localStorage.getItem('chinese_name'),
      staff_no: window.localStorage.getItem('staff_no'),
      role_id: window.localStorage.getItem('role_id'),
      importRequirementForm: {
        business_id: '',
        requirement_id: '',
        requirement_content: '',
        requirementAnalyzePrd: '',
        ui_link: '',
        tech_link: '',
        requirement_pic: []
      },
      testCaseDetailForm: {},
      requirementAnalyzePrd: '',
      case_type: '',
      queryInfo: {
        business_id: '',
        iteration_name: '',
        creater: '',
        pagenum: 1,
        pagesize: 5
      },
      queryTestCaseInfo: {
        system: '',
        module: '',
        status: '',
        recognize_record_id: '',
        pagenum: 1,
        pagesize: 10
      },
      total: 0,
      case_list_total: 0,
      dialogTableVisible: true,
      importRequirementVisible: false,
      showAnalyzeResultDialog: false, // 控制AI分析结果弹框显示
      showTestCaseListDialog: false, // 控制测试用例列表弹框的显示
      handleUpdateTestCaseDialogVisible: false, // 控制编辑用例弹框的显示
      recognizeRecordList: [],
      businessList: [],
      testCaseList: [],
      value: '',
      dialogVisible: false, // 控制图片预览弹框的显示
      previewImagesList: [], // 存储当前预览图片的地址
      currentImageIndex: 0,
      fileList: [],
      selectedRows: [],
      importRequirementFormRules: {
        business_id: [
          { required: true, message: '请选择业务线', trigger: 'change' }
        ],
        requirement_id: [
          { required: true, message: '请输入需求ID', trigger: 'change' }
        ],
        requirement_content: [
          { required: true, message: '请输入需求内容', trigger: 'change' }
        ]
      },
      testCaseDetailFormRules: {
        case_scene: [
          { required: true, message: '请输入用例场景', trigger: 'change' }
        ],
        test_steps: [
          { required: true, message: '请输入测试步骤', trigger: 'change' }
        ],
        expected_result: [
          { required: true, message: '请输入预期结果', trigger: 'change' }
        ]
      },
      headers: {
        Authorization: window.localStorage.getItem('token'),
        userId: window.localStorage.getItem('userId')
      }
    }
  },
  created() {
    this.getRecognizeRecordList()
    this.getBusinessList()
  },
  computed: {
    uploadPath() {
      // 动态生成上传路径
      return `${baseUrl}question/question_attachment_upload/`
    },
    // 当前图片的 src
    currentImageSrc() {
      const currentImage = this.previewImagesList[this.currentImageIndex]
      return currentImage
    }
  },
  methods: {
    // 设置生成记录列表每页条数
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getRecognizeRecordList()
    },
    // 设置生成记录列表页数
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getRecognizeRecordList()
    },
    // 设置ai用例列表每页条数
    handleCaseListSizeChange(newSize) {
      this.queryTestCaseInfo.pagesize = newSize
      this.getAITestCaseList()
    },
    // 设置ai用例列表页数
    handleCaseListCurrentChange(newPage) {
      this.queryTestCaseInfo.pagenum = newPage
      this.getAITestCaseList()
    },
    // AI生成用例记录查询方法
    queryRecognizeRecordList() {
      this.queryInfo.pagenum = 1
      this.getRecognizeRecordList()
    },
    // 重置查询
    queryReset() {
      this.queryInfo = {
        business_id: '',
        iteration_name: '',
        creater: '',
        pagenum: 1,
        pagesize: 5
      }
      this.getRecognizeRecordList()
    },
    // 获取业务线列表
    async getBusinessList() {
      const { data: res } = await this.$http.get('/user_info/info/', { params: { type: 'businesses' } })
      if (res.meta.status !== 200) return this.$message.error('获取域名查询列表失败')
      this.businessList = res.data
    },
    // 获取AI生成用例记录列表
    async getRecognizeRecordList() {
      const { data: res } = await this.$http.post(this.model + '/ai_recognize_record_list/', this.queryInfo)
      if (res.meta.status !== 200) return this.$message.error('获取AI生成用例记录列表失败')
      this.total = res.data.total
      this.recognizeRecordList = res.data.data
    },
    // 打开导入需求录入弹框
    handleImportRequirement() {
      // 重置表单数据
      this.importRequirementForm = {
        business_id: '',
        requirement_id: '',
        requirement_content: '',
        requirementAnalyzePrd: '',
        ui_link: '',
        tech_link: '',
        requirement_pic: []
      }
      this.fileList = []
      this.importRequirementVisible = true
    },
    // // 关闭导入需求录入弹框
    // closeImportRequirementDialog() {
    //   this.importRequirementForm = {}
    //   this.importRequirementVisible = false
    //   // 通过 ref 调用 clearFiles 方法
    //   this.$refs.uploadRef.clearFiles()
    // },
    // 提交需求录入
    async submitRequirement() {
      this.$refs.importRequirementFormRef.validate(async valid => {
        if (!valid) return false
        const requirementData = {
          requirement_content: this.importRequirementForm.requirement_content
        }
        this.$loading({
          lock: true,
          text: 'AI正在需求分析,请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        const { data: res } = await this.$http.post(this.model + '/ai_get_analyze_prd/', requirementData)
        if (res.meta.status !== 200) return this.$message.error('AI分析失败,请重试')
        // 使用 Vue.set 确保响应性
        this.$set(this.importRequirementForm, 'requirementAnalyzePrd', res.data)
        this.$loading().close()
        // 关闭需求录入弹框
        this.importRequirementVisible = false
        // 显示需求分析结果弹框
        this.showAnalyzeResultDialog = true
        await this.queryRecognizeRecordList()
      })
    },
    // 确认AI分析结果
    async confirmAnalyzeResult() {
      this.$loading({
        lock: true,
        text: 'AI正在生成测试用例,请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const { data: res } = await this.$http.post(this.model + '/ai_generate_testcases/', this.importRequirementForm)
      if (res.meta.status !== 200) return this.$message.error('AI分析失败,请重试')
      await this.queryTestCaseList(res.data.id)
      this.$loading().close()
      // 关闭需求分析弹框
      this.showAnalyzeResultDialog = false
      // 显示AI生成用例列表弹框
      this.showTestCaseListDialog = true
    },
    // 查看AI生成用例列表
    async queryTestCaseList(id) {
      this.queryTestCaseInfo.recognize_record_id = id
      this.queryTestCaseInfo.pagenum = 1
      await this.getAITestCaseList()
    },
    // 获取AI生成用例列表
    async getAITestCaseList() {
      const { data: res } = await this.$http.post(this.model + '/ai_generate_test_case_list/', this.queryTestCaseInfo)
      if (res.meta.status !== 200) return this.$message.error('列表获取失败')
      this.case_list_total = res.data.total
      this.testCaseList = res.data.data
    },
    // 编辑用例
    async handleUpdateCase(row) {
      const { data: res } = await this.$http.get(this.model + '/get_case_detail/', { params: { test_case_id: row.id } })
      const objString = JSON.stringify(res.data)
      this.testCaseDetailForm = JSON.parse(objString)
      this.handleUpdateTestCaseDialogVisible = true
    },
    // 保存编辑用例
    async submitUpdateCase() {
      this.$refs.testCaseDetailFormRef.validate(async valid => {
        if (!valid) return false
        const { data: res } = await this.$http.post(this.model + '/update_ai_test_case/', this.testCaseDetailForm)
        if (res.meta.status !== 200) return this.$message.error('处理失败')
        this.$message.success('处理成功')
        this.testCaseDetailForm = {}
        this.handleUpdateTestCaseDialogVisible = false
        await this.getAITestCaseList()
      })
    },
    // 更新用例召回状态
    async submitUpdateCaseStatus(row, status) {
      const { data: res } = await this.$http.post(this.model + '/update_ai_test_case_status/', { id: [row.id], status: status })
      if (res.meta.status !== 200) return this.$message.error('召回状态更新失败')
      this.$message.success('召回状态更新成功')
      await this.getAITestCaseList()
    },
    // 将勾选的数据进行保存
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    // 批量更新用例召回状态
    async batchUpdateCaseStatus(status) {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要召回或取消召回的用例')
        return
      }
      // 获取选中行的 id
      const selectedIds = this.selectedRows.map((row) => row.id)
      const { data: res } = await this.$http.post(this.model + '/update_ai_test_case_status/', {
        id: selectedIds,
        status: status
      })
      if (res.meta.status !== 200) return this.$message.error('召回状态更新失败')
      this.$message.success('召回状态更新成功')
      await this.getAITestCaseList()
    },
    // 取消用例编辑
    cancelCaseEdit() {
      this.smokeCaseDetailForm = {}
      this.handleUpdateTestCaseDialogVisible = false
    },
    // 导出AI生成用例记录维度召回用例
    async exportRecallCase(row) {
      if (!row.recall_ai_test_case) return this.$message.warning('无召回用例')
      const fileName = row.iteration_name + '-' + row.requirement_id
      const response = await this.$http.get(this.model + '/export_to_xmind_by_record/', {
        params: { recognize_record_id: row.id },
        responseType: 'blob' // 正确设置responseType
      })
      const filename = fileName + '召回用例.xmind'
      await this.downloadFile(response, filename)
    },
    // 下载文件
    async downloadFile(response, filename) {
      try {
        // 创建 Blob 对象，使用正确的MIME类型
        const blob = new Blob([response.data], { type: 'application/zip' })
        const downloadUrl = window.URL.createObjectURL(blob)
        // 创建一个隐藏的链接元素
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = filename
        document.body.appendChild(link)
        // 触发下载并移除链接元素
        link.click()
        link.remove()
        // 释放 URL 对象
        window.URL.revokeObjectURL(downloadUrl)
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    },
    // tooltip换行显示
    formatTooltipContent(content) {
      if (!content || typeof content !== 'string') {
        return ''
      }

      // 检查内容是否包含编号格式（如"1、"，"2："等）
      if (/\d+[、：:.]/.test(content)) {
        // 使用formatSteps函数处理带编号的内容
        const steps = this.formatSteps(content)
        // 将处理后的步骤数组转换为HTML格式，每个步骤一行
        return steps.join('<br>')
      } else {
        // 对于不包含编号的普通内容，仍然使用原来的处理方式
        return content.replace(/\n/g, '<br>')
      }
    },
    // 将带有编号的字符串按编号分割为数组
    formatSteps(content) {
      // 如果 content 为空或不是字符串，返回空数组
      if (!content || typeof content !== 'string') {
        return []
      }
      // 按数字+分隔符（、:.;）分割
      const steps = content
        .split(/(?=\d+[、：:.])/g) // 在数字+分隔符前分割
        .map(step => step.trim()) // 移除每项两端的空格
        .filter(step => step) // 移除空项
      return steps
    },
    // 打开链接
    openLink(url) {
      if (url) {
        window.open(url, '_blank')
      }
    },
    // 判断列表是否有图片
    hasImage(contentList) {
      // 判断 contentList 是否有值
      return contentList?.length > 0
    },
    // 预览图片
    handlePreview() {
      const previewList = this.importRequirementForm.requirement_pic.map(fileUrl => {
        return fileUrl
      })
      this.previewImages(previewList)
    },
    // 预览图片
    previewImages(contentList) {
      this.previewImagesList = contentList
      this.dialogVisible = true
      this.currentImageIndex = 0 // 每次打开重置为第一张
    },
    // 查看图片-上一张
    previousImage() {
      if (this.currentImageIndex > 0) {
        this.currentImageIndex--
      }
    },
    // 查看图片-下一张
    nextImage() {
      if (this.currentImageIndex < this.previewImagesList.length - 1) {
        this.currentImageIndex++
      }
    },
    // 键盘事件监听器,实现左右键切换图片
    handleKeydown(event) {
      if (event.key === 'ArrowLeft') {
        this.previousImage()
        event.preventDefault() // 阻止默认行为
        event.stopPropagation() // 阻止事件冒泡
      } else if (event.key === 'ArrowRight') {
        this.nextImage()
        event.preventDefault() // 阻止默认行为
        event.stopPropagation() // 阻止事件冒泡
      }
    },
    // 上传成功回调
    handleUploadSuccess(response, file, fileList) {
      if (response.meta.status === 200) {
        // 确保使用Vue的响应式数组更新
        this.$set(this.importRequirementForm, 'requirement_pic', [
          ...(this.importRequirementForm.requirement_pic ? this.importRequirementForm.requirement_pic : []),
          response.data.fileUrl
        ])
        // 更新fileList中的URL为实际的服务器URL
        const index = this.fileList.findIndex(f => f.uid === file.uid)
        if (index !== -1) {
          this.$set(this.fileList, index, {
            ...this.fileList[index],
            url: response.data.fileUrl,
            status: 'success',
            percentage: 100 // 上传成功设置为100%
          })
        }
      }
    },
    // 文件状态改变时的钩子
    handFileList(file, fileList) {
      this.fileList = fileList
    },
    // 删除文件回调
    handleRemove(file) {
      const index = this.fileList.indexOf(file)
      if (index > -1) {
        this.importRequirementForm.requirement_pic.splice(index, 1)
        this.fileList.splice(index, 1)
      }
      // 更新文件列表
      this.fileList = this.fileList.filter(f => f.uid !== file.uid)
    },
    // 文件超出限制回调
    handleExceed(files, fileList) {
      this.$message.warning('最多只允许上传3张图片')
    },
    // 上传前校验：限制为 PNG 和 JPG 格式，并检查大小
    beforeUpload(file) {
      // 只允许的 MIME 类型
      const validTypes = ['image/png', 'image/jpeg']
      const isValidType = validTypes.includes(file.type)

      // 只允许的扩展名
      const fileName = file.name.toLowerCase()
      const validExtensions = ['.png', '.jpeg', '.jpg']
      const isValidExtension = validExtensions.some(ext => fileName.endsWith(ext))

      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isValidType) {
        this.$message.error('文件格式无效，只能上传 PNG 或 JPG 格式的图片!')
        return false
      }
      if (!isValidExtension) {
        this.$message.error('文件扩展名必须为 .png、.jpeg 或 .jpg!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('图片大小不能超过5MB!')
        return false
      }
      return isValidType && isValidExtension && isLt5M
    },
    // 处理粘贴事件
    async handlePastePic(event) {
      // 获取剪贴板数据，兼容不同浏览器的事件对象
      const items = (event.clipboardData || event.originalEvent.clipboardData).items
      // 用于存储检测到的图片文件
      const imageFiles = []
      // 遍历剪贴板中的所有项
      for (const item of items) {
        // 检查是否为图片类型（MIME类型以'image'开头）
        if (item.type.indexOf('image') !== -1) {
          // 将剪贴板项转换为文件对象
          const file = item.getAsFile()
          // 在上传前校验文件（大小、格式等），beforeUpload 返回布尔值
          const isValid = await this.beforeUpload(file)
          if (isValid) {
            // 如果文件有效，添加到图片文件数组
            imageFiles.push(file)
          }
        }
      }
      // 如果检测到有效的图片文件
      if (imageFiles.length > 0) {
        event.preventDefault() // 阻止默认粘贴行为
        // 调用上传方法处理这些图片文件
        this.uploadPastedImages(imageFiles)
      }
    },
    // 处理粘贴的图片并自动上传
    uploadPastedImages(files) {
      // 遍历所有图片文件
      files.forEach(async (file, index) => {
        // 检查当前文件列表是否已达到上限（3个）
        if (this.fileList.length >= 3) {
          // 如果超过限制，触发超出处理函数
          this.handleExceed()
          return
        }

        // 创建临时文件对象，用于在 el-upload 组件中显示
        const tempFile = {
          name: file.name || `pasted-image-${index}.png`, // 提供默认文件名
          url: URL.createObjectURL(file),
          raw: file,
          status: 'uploading',
          uid: Date.now() + index,
          percentage: 0 // 初始化进度为0
        }

        // 使用 Vue.set 添加文件到 fileList，确保响应式更新
        this.$set(this.fileList, this.fileList.length, tempFile)

        // 自动上传逻辑
        try {
          // 创建 FormData 对象用于文件上传
          const formData = new FormData()
          // 将文件添加到 FormData，键名为 'file'
          formData.append('file', file)
          // 调用上传文件方法
          const response = await this.$http.post(this.uploadPath, formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent) => {
              // 更新上传进度
              const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
              const fileIndex = this.fileList.findIndex(f => f.uid === tempFile.uid)
              if (fileIndex !== -1) {
                this.$set(this.fileList[fileIndex], 'percentage', percent)
              }
            }
          })

          if (response.data.meta.status === 200) {
            // 调用成功处理函数，更新文件状态和URL
            this.handleUploadSuccess(response.data, tempFile, this.fileList)
          }
        } catch (error) {
          console.error('图片上传失败:', error)
          const fileIndex = this.fileList.findIndex(f => f.uid === tempFile.uid)
          if (fileIndex !== -1) {
            this.$set(this.fileList[fileIndex], 'status', 'failed')
            this.$set(this.fileList[fileIndex], 'percentage', 0)
          }
          this.$message.error('图片上传失败')
        }
      })
    }
  }
}
</script>

<style>
.requirement-dialog {
  min-height: 600px;
  display: flex;
  flex-direction: column;
}

.requirement-dialog .el-dialog__body {
  flex: 1;
  overflow: auto;
  max-height: 70vh;
  padding: 20px;
}

.requirement-dialog .el-form-item {
  margin-bottom: 20px;
}

.requirement-dialog .el-textarea {
  min-height: 150px;
}

/* 确保 textarea 可编辑 */
.requirement-dialog .el-textarea__inner {
  pointer-events: auto !important;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  cursor: text !important;
  background-color: #fff !important;
  border: 1px solid #dcdfe6 !important;
}

.requirement-dialog .el-textarea__inner:focus {
  border-color: #409eff !important;
  outline: 0 !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
}

.requirement-dialog .el-textarea__inner:disabled,
.requirement-dialog .el-textarea__inner[readonly] {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #c0c4cc !important;
  cursor: not-allowed !important;
}

/* 调试样式 */
.debug-info {
  padding: 5px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
  margin-bottom: 10px;
}
</style>
