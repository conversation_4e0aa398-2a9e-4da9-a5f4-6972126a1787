<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>自动化监控</el-breadcrumb-item>
      <el-breadcrumb-item>待处理列表</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>

      <!-- 查询条件 -->
      <el-row :gutter="10">
         <el-col :span="3">
          <el-input placeholder="场景ID"
                    v-model="detailMonitoringListQuery.scene_id"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-input placeholder="场景名称"
                    v-model="detailMonitoringListQuery.scene_name"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-input placeholder="场景负责人"
                    v-model="detailMonitoringListQuery.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-select placeholder="请选择业务线"
                     v-model="detailMonitoringListQuery.businessId"
                     clearable
                     filterable>
            <el-option v-for="item in businessList"
                        :key="item.id"
                        :label="item.label"
                        :value="item.id">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select placeholder="用例是否调整"
                     v-model="detailMonitoringListQuery.is_repair"
                     clearable>
            <el-option value="0" label="否"></el-option>
            <el-option value="1" label="是"></el-option>
            <el-option value="2" label="无需调整"></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select placeholder="错误类型"
                     v-model="detailMonitoringListQuery.fail_type"
                     clearable>
            <el-option value="1" label="多线程并发"></el-option>
            <el-option value="2" label="服务问题"></el-option>
            <el-option value="3" label="配置问题"></el-option>
            <el-option value="4" label="数据问题"></el-option>
            <el-option value="5" label="新功能影响"></el-option>
            <el-option value="6" label="用例设计问题"></el-option>
            <el-option value="7" label="BUG"></el-option>
            <el-option value="8" label="其他原因"></el-option>
          </el-select>
        </el-col>
        <el-col :span="5">
          <el-button type="primary" @click="queryList">查 询</el-button>
          <el-button type="primary" @click="queryReset">重 置</el-button>
        </el-col>
      </el-row>
      <el-row style="margin-top: 20px;">
        <el-col>
          <el-button @click="batchEdit" style="border-color: #409EFF;color: #409EFF;" size="small">批量维护失败原因</el-button>
        </el-col>
      </el-row>
      <!-- 列表 -->
      <el-row>
        <el-table :data="dailyMonitoringDetailList" border style="width: 100%;" :scroll-x="true" v-loading="getDailyMonitoringListLoading" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="任务名称" prop="task_name" min-width="120px" show-overflow-tooltip align="center"></el-table-column>
          <el-table-column label="场景ID" prop="scene_id" min-width="80px" align="center"></el-table-column>
          <el-table-column label="场景名称" prop="scene_name" min-width="120px" show-overflow-tooltip>
            <template v-slot="slotProps">
              <el-link type="primary" v-if="slotProps.row.creater==user_name" @click="showDialog(false,slotProps.row.scene_id, 'update')">{{ slotProps.row.scene_name }}</el-link>
              <el-link type="primary" v-else @click="showDialog(false,slotProps.row.scene_id, 'check')">{{ slotProps.row.scene_name }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="业务线" prop="businessName" min-width="100px" align="center"></el-table-column>
          <el-table-column label="运行结果" prop="run_result" min-width="90px" align="center">
            <template v-slot="slotProps">
              <span v-if="slotProps.row.run_result==='fail'">失败</span>
              <span v-else>错误</span>
            </template>
          </el-table-column>
          <el-table-column label="累计报错次数" prop="scene_error_count" min-width="100px" align="center"></el-table-column>
          <el-table-column label="创建人" prop="creater" min-width="90px" align="center"></el-table-column>
          <el-table-column label="用例是否调整" prop="is_repair" min-width="100px" align="center">
            <template v-slot="slotProps">
              <span v-if="slotProps.row.is_repair==='1'">是</span>
              <span v-if="slotProps.row.is_repair==='0'">否</span>
              <span v-if="slotProps.row.is_repair==='2'">无需调整</span>
            </template>
          </el-table-column>
          <el-table-column label="错误类型" prop="fail_type" min-width="100px" align="center">
            <template v-slot="slotProps">
              <span v-if="slotProps.row.fail_type==='1'">多线程并发</span>
              <span v-if="slotProps.row.fail_type==='2'">服务问题</span>
              <span v-if="slotProps.row.fail_type==='3'">配置问题</span>
              <span v-if="slotProps.row.fail_type==='4'">数据问题</span>
              <span v-if="slotProps.row.fail_type==='5'">新功能影响</span>
              <span v-if="slotProps.row.fail_type==='6'">用例设计问题</span>
              <span v-if="slotProps.row.fail_type==='7'">BUG</span>
              <span v-if="slotProps.row.fail_type==='8'">其他原因</span>
            </template>
          </el-table-column>
          <el-table-column label="原因及优化方案" min-width="120px">
            <template #default="scope">
                <!-- 调用 getTextContent 方法获取并显示文本 -->
              <el-tooltip placement="top">
                <div style="white-space: nowrap;text-overflow: ellipsis; width: 120px; overflow: hidden">{{ scope.row.repair_plan }}</div>
                <div slot="content" v-html="formatTooltipContent(scope.row.repair_plan)" style="white-space: pre-line"></div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" min-width="240px">
            <template v-slot="slotProps">
              <el-tooltip class="item" effect="dark" content="报告执行详情" placement="top" :enterable="false" >
                <el-button icon="el-icon-message" size="mini" circle @click="getDetail(slotProps.row.scene_id, slotProps.row.parent_id)"></el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="维护失败原因" placement="top" :enterable="false">
                <el-button type="primary" icon="el-icon-thumb" size="mini" circle @click="handleCaseResult(slotProps.row)"></el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="编辑场景" placement="top" :enterable="false">
                <el-button type="danger" icon="el-icon-edit" circle @click="showDialog(false,slotProps.row.scene_id, 'update')" size="mini"></el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="关联任务" placement="top" :enterable="false" >
                <el-button icon="el-icon-paperclip" type="warning" size="mini" circle @click="showTaskList(slotProps.row.scene_id,slotProps.row.scene_name)"></el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="运行场景" placement="top" :enterable="false">
                <el-button type="success" icon="el-icon-caret-right" circle @click="showSceneRunDialog(slotProps.row.scene_id)" size="mini"></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-row>

      <!-- 分页控件 -->
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="detailMonitoringListQuery.pagenum"
          :page-size="detailMonitoringListQuery.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>

        <!--批量处理用例弹框-->
        <el-dialog
        :visible.sync="batchEditDialogVisible"
        title = "批量维护失败原因"
        width="60%">
        <el-divider content-position="center" style="margin-top: -30px">基本信息</el-divider>
        <el-form label-width="140px" :model="caseResultDetailForm" :rules="caseResultDetailFormRules" ref="caseResultDetailFormRef" key="caseResultDetailFormRef">
          <el-row>
            <el-col :span="8">
              <el-form-item label="监控日期:" class="bold-label">
                <span>{{ date }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="错误类型:" class="bold-label" prop="fail_type">
                <el-select
                  v-model="caseResultDetailForm.fail_type"
                  size="mini"
                  style="width: 150px;"
                >
                  <el-option value="1" label="多线程并发"></el-option>
                  <el-option value="2" label="服务问题"></el-option>
                  <el-option value="3" label="配置问题"></el-option>
                  <el-option value="4" label="数据问题"></el-option>
                  <el-option value="5" label="新功能影响"></el-option>
                  <el-option value="6" label="用例设计问题"></el-option>
                  <el-option value="7" label="BUG"></el-option>
                  <el-option value="8" label="其他原因"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用例是否已调整:" class="bold-label" prop="is_repair">
                <el-select
                  v-model="caseResultDetailForm.is_repair"
                  size="mini"
                  style="width: 120px;"
                >
                  <el-option value="0" label="否"></el-option>
                  <el-option value="1" label="是"></el-option>
                  <el-option value="2" label="无需调整"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item label="tapd链接:" class="bold-label" prop="tapd_link" v-if="caseResultDetailForm.fail_type==='7'">
              <el-input placeholder="请输入BUG的tapd链接"
                    v-model="caseResultDetailForm.tapd_link"
                    clearable
                    >
              </el-input>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="原因及优化方案:" class="bold-label" prop="repair_plan">
              <el-input
                type="textarea"
                :rows="5"
                maxlength="1000"
                show-word-limit
                v-model="caseResultDetailForm.repair_plan"
                placeholder="请输入原因及优化方案"></el-input>
            </el-form-item>
          </el-row>
          <el-row style="text-align: right">
            <span>
              <el-button @click="cancelBatchEdit">取 消</el-button>
              <el-button type="primary" @click="submitHandleQuestion()">保 存</el-button>
            </span>
          </el-row>
        </el-form>
      </el-dialog>

    <!--处理用例弹框-->
    <el-dialog
        :visible.sync="handleCaseResultDialogVisible"
        title = "维护失败原因"
        width="60%">
        <el-divider content-position="center" style="margin-top: -30px">基本信息</el-divider>
        <el-form label-width="140px" :model="caseResultDetailForm" :rules="caseResultDetailFormRules" ref="caseResultDetailFormRef" key="caseResultDetailFormRef">
          <el-row>
            <el-col :span="8">
              <el-form-item label="监控日期:" class="bold-label">
                <span>{{ date }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="任务名称:" class="bold-label">
                <span>{{ caseResultDetailForm.task_name }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="业务线:" class="bold-label">
                <span>{{ caseResultDetailForm.businessName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="场景ID:" class="bold-label">
                <span>{{ caseResultDetailForm.scene_id }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="场景名称:" class="bold-label">
                <span>{{ caseResultDetailForm.scene_name }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="运行结果:" class="bold-label">
                <span v-if="caseResultDetailForm.run_result === 'fail'" >错误</span>
                <span v-if="caseResultDetailForm.run_result === 'error'">失败</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="错误类型:" class="bold-label" prop="fail_type">
                <el-select
                  v-model="caseResultDetailForm.fail_type"
                  size="mini"
                  style="width: 150px;"
                >
                  <el-option value="1" label="多线程并发"></el-option>
                  <el-option value="2" label="服务问题"></el-option>
                  <el-option value="3" label="配置问题"></el-option>
                  <el-option value="4" label="数据问题"></el-option>
                  <el-option value="5" label="新功能影响"></el-option>
                  <el-option value="6" label="用例设计问题"></el-option>
                  <el-option value="7" label="BUG"></el-option>
                  <el-option value="8" label="其他原因"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用例是否已调整:" class="bold-label" prop="is_repair">
                <el-select
                  v-model="caseResultDetailForm.is_repair"
                  size="mini"
                  style="width: 120px;"
                >
                  <el-option value="0" label="否"></el-option>
                  <el-option value="1" label="是"></el-option>
                  <el-option value="2" label="无需调整"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item label="tapd链接:" class="bold-label" prop="tapd_link" v-if="caseResultDetailForm.fail_type==='7'">
              <el-input placeholder="请输入BUG的tapd链接"
                    v-model="caseResultDetailForm.tapd_link"
                    clearable
                    >
              </el-input>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="原因及优化方案:" class="bold-label" prop="repair_plan">
              <el-input
                type="textarea"
                :rows="5"
                maxlength="1000"
                show-word-limit
                v-model="caseResultDetailForm.repair_plan"
                placeholder="请输入原因及优化方案"></el-input>
            </el-form-item>
          </el-row>
          <el-row style="text-align: right">
            <span>
              <el-button @click="cancelEdit">取 消</el-button>
              <el-button type="primary" @click="submitHandleQuestion()">保 存</el-button>
            </span>
          </el-row>
        </el-form>
      </el-dialog>

    <!-- 编辑场景抽屉 -->
    <EditSceneDrawer
      ref="editSceneDrawerRef"
      :visible.sync="dialogVisible"
      :title="title"
      :type="type"
      :scene-data="sceneForm"
      :business-list="businessList"
      :loading="addLoading"
      :model="model"
      :table-title="'案例组成'"
      :show-add-case-button="true"
      :table-id="'crTable'"
      :table-border="false"
      :table-style="'width: 100%'"
      @close="sceneDrawerClosed"
      @submit="handleSceneSubmit"
      @run-scene="showSceneRunDialog"
      @add-case="handleAddCase"
      @edit-case="handleEditCase"
      @copy-case="handleCopyCase"

      @opened="addDrawerOpened"
      @refresh-list="queryList"
    >
      <template #table-columns="{ type }">
        <el-table-column type="index" />
        <el-table-column prop="relation_case_id" min-width="100px" label="案例ID" />
        <el-table-column prop="relation_case_name" min-width="120px" label="案例名称" />
        <el-table-column prop="relation_case_type" min-width="80px" label="案例类型" />
        <el-table-column prop="is_get_param" min-width="80px" label="是否提取参数">
          <template v-slot="slotProps">
            <span v-if="slotProps.row.is_get_param">是</span>
            <span v-else>否</span>
          </template>
        </el-table-column>
        <el-table-column prop="response_param" min-width="100px" label="返回参数" />
        <el-table-column prop="response_param_alias" min-width="100px" label="返回参数别名" />
        <el-table-column prop="reset_param" min-width="80px" label="重设请求数据">
          <template v-slot="slotProps">
            <span v-if="slotProps.row.reset_param">是</span>
            <span v-else>否</span>
          </template>
        </el-table-column>
        <el-table-column prop="instructions" min-width="100px" label="备注说明" show-overflow-tooltip />
        <el-table-column prop="is_check" min-width="100px" label="是否数据越权">
          <template v-slot="slotProps">
            <el-switch
              v-model="slotProps.row.is_check"
              :disabled="type === 'check'"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column min-width="170px" label="操作" v-if="type !== 'check'">
          <template v-slot="slotProps">
            <el-button type="text" @click="handleEditCase({ caseInfo: slotProps.row, index: slotProps.$index })">编辑</el-button>
            <el-button type="text" @click="handleAddCase(slotProps.$index)">添加</el-button>
            <el-button type="text" @click="handleCopyCase(slotProps.row, slotProps.$index)">复制</el-button>
            <el-button type="text" @click="handleDeleteCase(slotProps.$index)">删除</el-button>
            <el-button type="text" @click="slotProps.row.is_enable = !slotProps.row.is_enable">
              <span v-if="slotProps.row.is_enable">禁用</span>
              <span v-else style="color:red">启用</span>
            </el-button>
          </template>
        </el-table-column>
      </template>
    </EditSceneDrawer>

    <!-- 环境选择对话框 -->
    <el-dialog :visible.sync="enviromentDialogVisible"
                width="30%"
                @close="enviromentDialogClosed"
                v-loading="loading"
                element-loading-text="场景运行中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.8)"
                :close-on-click-modal="false">
      <el-form label-width="130px"
               :model="sceneRunForm"
               ref="sceneRunFormRef"
               :rules="sceneRunFormRules">
        <el-form-item label="请选择运行环境" prop="env_type">
          <el-select v-model="sceneRunForm.env_type">
            <el-option label="测试环境" value="1"></el-option>
            <el-option label="预发环境" value="2"></el-option>
            <el-option label="生产环境" value="3"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="enviromentDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmitSceneRun">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 展示运行结果对话框 -->
    <el-dialog :visible.sync="resultDialogVisible"
               width="60%"
               :close-on-click-modal="false"
               @close="resultDialogClosed">
      <template slot="title">
        <p style="font-weight: 600;font-size: 16px">{{ resultForm.msg }}</p>
      </template>
      <template v-if="resultForm.code">
        <div v-if="resultForm.code!='0000'" style="color: red;font-size:15px;font-weight: bold;margin-bottom: 10px;">{{resultForm.error}}</div>
        <div v-else style="color: #67C23A;font-size:15px;font-weight: bold;margin-bottom: 10px;">场景执行完成！</div>
      </template>
      <el-collapse>
        <el-collapse-item v-for="(item, index) in resultForm.data" :key="index">
          <template slot="title">
            <span v-if="item.assert==='0'" style="color: red;font-size:15px;">{{ item.title }}</span>
            <span v-else style="font-size:15px;">{{ item.title }}</span>
          </template>
          <template slot="name">
            {{ index }}
          </template>
            <div v-for="(detail, index) in item.detail"
                 style="color: #505050;"
                 :key="index">{{ detail }}</div>
        </el-collapse-item>
        </el-collapse>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="resultDialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 任务选择对话框 -->
    <el-dialog :visible.sync="taskDialogVisible"
                width="40%"
                :title = "title"
                @close = "taskListClosed">
      <el-form label-width="140px"
               :model="sceneRunForm"
               ref="sceneRunFormRef"
               :rules="sceneRunFormRules">
        <el-form-item label="请选择需添加的任务">
          <el-select v-model="selectedTaskList" multiple filterable>
            <el-option
              v-for="task in taskList"
              :key="task.task_id"
              :label="`${task.task_id}-${task.task_name}`"
              :value="task.task_id">
          </el-option>
          </el-select>
        </el-form-item>
        <template>
          <div>
            <h3>该场景已关联任务</h3>
            <el-table
              :data="selected_tasks"
              style="width: 100%">
              <el-table-column
                prop="task_name"
                label="任务名称"
                width="450">
              </el-table-column>
              <el-table-column
                label="操作"
                width="80">
                <template v-slot="slotProps">
                  <el-button @click="deleteSelectedTask(slotProps.row)" type="text" size="small">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="taskListClosed">取 消</el-button>
        <el-button type="primary" @click="submitTaskList">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import EditSceneDrawer from '../../components/EditSceneDrawer.vue'

export default {
  components: {
    EditSceneDrawer
  },
  data() {
    return {
      task_model: 'task_info',
      model: 'scene_info',
      dailyMonitoringDetailList: [], // 日常监控列表
      user_name: window.localStorage.getItem('user_name'),
      chinese_name: window.localStorage.getItem('chinese_name'),
      staff_no: window.localStorage.getItem('staff_no'),
      role_id: window.localStorage.getItem('role_id'),
      headers: {
        Authorization: window.sessionStorage.getItem('token'),
        userId: window.sessionStorage.getItem('userId')
      },
      environment: this.$route.query.environment,
      date: this.$route.query.date,
      detailMonitoringListQuery: {
        date: this.$route.query.date,
        environment: this.$route.query.environment,
        creater: window.localStorage.getItem('user_name'),
        is_repair: '',
        pagenum: 1,
        pagesize: 20
      },
      sceneForm: {
        scene_name: '',
        business_id: 0,
        scene_describe: '',
        isShowPreparam: false,
        before_param: '',
        relation: []
      },
      caseHandleForm: {},
      caseResultDetailForm: {
        monitoring_record_id: '',
        fail_type: '',
        repair_plan: '',
        is_repair: '',
        tapd_link: ''
      },
      dialogVisible: false,
      enviromentDialogVisible: false,
      handleCaseResultDialogVisible: false,
      sceneRunForm: {
        scene_id: '',
        env_type: '1'
      },
      type: '',
      title: '',
      addIndex: 0,
      counterKey: 0,
      loading: false,
      addLoading: false,
      preparamList: [],
      caseParamList: [{}],
      businessList: [],
      sqlForm: {},
      resultDialogVisible: false,
      resultForm: {},
      taskDialogVisible: false,
      selectedTaskList: [],
      taskList: [],
      selected_tasks: [],
      selectedTaskParam: {
        task_id: [],
        scene_id: '',
        scene_name: ''
      },
      getDailyMonitoringListLoading: false,
      total: 0,
      sceneRunFormRules: {
        env_type: [
          { required: true, message: '请选择运行环境', trigger: 'blur' }
        ]
      },
      sceneFormRules: {
        scene_name: [
          { required: true, message: '请输入场景名称', trigger: 'blur' }
        ]
      },
      caseResultDetailFormRules: {
        repair_plan: [
          { required: true, message: '请输入原因及优化方案', trigger: 'blur' }
        ],
        fail_type: [
          { required: true, message: '请选择错误类型', trigger: 'blur' }
        ],
        is_repair: [
          { required: true, message: '请选择是否已调整', trigger: 'blur' }
        ],
        tapd_link: [
          { required: true, message: '请输入bug的tapd链接', trigger: 'blur' }
        ]
      },
      batchEditDialogVisible: false,
      selectedRows: []
    }
  },
  mounted() {
    this.getMonitoringDetail()
    this.getBusinessList()
  },
  activated() {
    this.queryReset()
  },
  methods: {
    queryList() {
      this.detailMonitoringListQuery.pagenum = 1
      this.getMonitoringDetail()
    },
    // 重置按钮方法
    queryReset() {
      this.detailMonitoringListQuery = {
        date: this.date,
        environment: this.environment,
        creater: this.user_name,
        is_repair: '',
        pagenum: 1,
        pagesize: 20
      }
      this.getMonitoringDetail()
    },
    // 获取业务线列表
    async getBusinessList() {
      const { data: res } = await this.$http.get('/user_info/info/', { params: { type: 'businesses' } })
      if (res.meta.status !== 200) {
        if (res.meta.status === 'HD1003' || res.meta.status === 'HD1004') {
          console.log('登录失效')
        } else return this.$message.error('获取域名查询列表失败')
      }
      this.businessList = res.data
    },
    // 自动化监控详情
    async getMonitoringDetail() {
      this.detailMonitoringListQuery.environment = this.environment = this.$route.query.environment
      this.detailMonitoringListQuery.date = this.date = this.$route.query.date
      this.getDailyMonitoringListLoading = true
      const { data: res } = await this.$http.post(this.task_model + '/get_daily_monitoring_detail_list/', this.detailMonitoringListQuery)
      if (res.meta.status !== 200) {
        if (res.meta.status === 'HD1003' || res.meta.status === 'HD1004') {
          console.log('登录失效,请重新登录')
        } else return this.$message.error('获取日常监控结果列表失败')
      }
      this.total = res.data.total
      this.dailyMonitoringDetailList = res.data.data.map(item => ({
        ...item
      }))
      this.getDailyMonitoringListLoading = false
    },
    // 批量维护失败原因发放
    async batchEdit() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择要编辑的行')
        return
      }
      console.log('选中的行:', this.selectedRows)
      this.batchEditDialogVisible = true
    },
    resetBatchEditForm() {
      this.caseResultDetailForm = {
        monitoring_record_id: '',
        fail_type: '',
        repair_plan: '',
        is_repair: '',
        tapd_link: ''
      }
    },
    cancelBatchEdit() {
      this.resetBatchEditForm()
      this.batchEditDialogVisible = false
    },
    cancelEdit() {
      this.resetBatchEditForm()
      this.handleCaseResultDialogVisible = false
    },
    // 翻页
    handleSizeChange(newSize) {
      this.detailMonitoringListQuery.pagesize = newSize
      this.getMonitoringDetail()
    },
    handleCurrentChange(newPage) {
      this.detailMonitoringListQuery.pagenum = newPage
      this.getMonitoringDetail()
    },
    // 处理用例
    async handleCaseResult(row) {
      const objString = JSON.stringify(row)
      this.caseResultDetailForm = JSON.parse(objString)
      this.handleCaseResultDialogVisible = true
    },
    // 提交去处理编辑结果
    async submitHandleQuestion() {
      this.$refs.caseResultDetailFormRef.validate(async valid => {
        if (!valid) return false

        // 获取所有选中的行的 recordIds
        let recordIds = []
        if (this.selectedRows.length === 0 && this.caseResultDetailForm) {
          recordIds = [this.caseResultDetailForm.monitoring_record_id]
        } else {
          recordIds = this.selectedRows.map(row => row.monitoring_record_id)
        }
        this.caseHandleForm = {
          fail_type: this.caseResultDetailForm.fail_type,
          repair_plan: this.caseResultDetailForm.repair_plan,
          is_repair: this.caseResultDetailForm.is_repair,
          tapd_link: this.caseResultDetailForm.tapd_link
        }
        const { data: res } = await this.$http.post(
          this.task_model + '/update_daily_monitoring_fail_reason/', { caseHandleForm: this.caseHandleForm, record_ids: recordIds })
        if (res.meta.status !== 200) return this.$message.error('处理失败')
        this.$message.success('处理成功')
        this.caseHandleForm = {}
        this.caseResultDetailForm = {}
        this.handleCaseResultDialogVisible = false
        this.batchEditDialogVisible = false
        await this.queryList()
      })
    },
    // 查看此用例在报告中的运行详细
    async getDetail(sceneId, parentId) {
      const { data: res } = await
      this.$http.get(this.task_model + '/task_report_detail/?scene_id=' + sceneId + '&parent_id=' + parentId)
      if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
      this.resultForm = res.data
      this.enviromentDialogVisible = false
      this.resultDialogVisible = true
    },
    // 打开场景编辑页面
    async showDialog(isAdd, id, type) {
      this.type = type
      if (this.type === 'update') {
        this.title = '编辑场景'
      } else if (this.type === 'check') {
        this.title = '查看场景'
      }
      await this.$refs.editSceneDrawerRef.showDialog(isAdd, id, type)
    },

    // 打开场景运行弹窗
    async showSceneRunDialog(id) {
      this.sceneRunForm.scene_id = id
      this.enviromentDialogVisible = true
    },
    // 关闭选择环境弹窗
    enviromentDialogClosed() {
      this.$refs.sceneRunFormRef.resetFields()
    },
    // 关闭运行结果弹框
    resultDialogClosed() {
      this.resultForm = {}
    },
    // 关闭场景编辑抽屉
    sceneDrawerClosed() {
      this.sceneForm = {
        scene_name: '',
        business_id: 0,
        scene_describe: '',
        isShowPreparam: false,
        before_param: '',
        relation: []
      }
      this.preparamList = []
    },
    // 处理场景提交
    handleSceneSubmit({ sceneData, flag }) {
      this.$refs.editSceneDrawerRef.submitScene(flag)
    },
    // 处理添加案例
    handleAddCase(index) {
      this.$refs.editSceneDrawerRef.showAddCaseDrawer(index)
    },
    // 处理编辑案例
    handleEditCase({ caseInfo, index }) {
      this.$refs.editSceneDrawerRef.showEditCaseDrawer(caseInfo, index)
    },
    // 处理复制案例
    handleCopyCase(caseInfo, index) {
      this.$refs.editSceneDrawerRef.handleCopyCase(caseInfo, index)
    },
    // 处理删除案例
    handleDeleteCase(index) {
      this.$refs.editSceneDrawerRef.deleteCurrentCase(index)
    },
    // 处理提交场景运行
    handleSubmitSceneRun() {
      // 将环境选择数据传递给EditSceneDrawer
      this.$refs.editSceneDrawerRef.sceneRunForm = this.sceneRunForm
      this.$refs.editSceneDrawerRef.submitSceneRun()
      this.enviromentDialogVisible = false
    },
    // 打开动画结束时的回调
    addDrawerOpened() {
      // 拖拽排序现在由EditSceneDrawer组件处理
    },
    // tooltip换行显示
    formatTooltipContent(content) {
      return content && content.replace(/\n/g, '<br>')
    },
    async showTaskList(sceneId, sceneName) {
      this.title = sceneId.toString() + '-' + sceneName
      const { data: res } = await this.$http.get('task_info/task_list/?channel=3&scene_id=' + sceneId)
      this.taskList = res.data.tasks
      this.selected_tasks = res.data.selected_tasks
      if (res.meta.status !== 200) return this.$message.error('任务列表获取失败')
      this.selectedTaskParam.scene_id = sceneId.toString()
      this.selectedTaskParam.scene_name = sceneName
      this.taskDialogVisible = true
    },
    async deleteSelectedTask(index) {
      this.$confirm('是否确认将场景从任务中移除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { data: res } = await this.$http.get('/task_info/delete_task_scene?task_id=' + index.task_id + '&scene_id=' + this.selectedTaskParam.scene_id)
        if (res.meta.status === 200) {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          await this.showTaskList(this.selectedTaskParam.scene_id, this.selectedTaskParam.scene_name)
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async submitTaskList() {
      if (this.selectedTaskList.length !== 0) {
        this.selectedTaskParam = { ...this.selectedTaskParam, task_id: this.selectedTaskList }
        const { data: res } = await this.$http.post('task_info/add_scene_to_more_task/', this.selectedTaskParam)
        if (res.meta.status !== 200) return this.$message.error('操作失败')
        this.$message.success('添加成功')
      } else {
        this.$message.success('操作成功')
      }
      this.selectedTaskParam = []
      this.selectedTaskList = []
      this.taskDialogVisible = false
      await this.getMonitoringDetail()
    },
    taskListClosed() {
      this.selectedTaskParam = []
      this.selectedTaskList = []
      this.taskDialogVisible = false
    },
    // 将选中的行数据存储到 selectedRows 中
    handleSelectionChange(selection) {
      this.selectedRows = selection
    }
  }
}
</script>

<style>
.edit-input .el-input__inner {
  width: 100%;
  padding: 0 7px;
}
.edit-input .el-input__suffix{
  left: 50px;
}
.bold-label .el-form-item__label {
  font-weight: bold;
}

/* 鼠标悬浮效果 */
.el-table__body tr.el-table__row:hover > td,
.el-table__body tr.el-table__row--striped:hover > td {
  background-color: #ecf5ff !important;
}
.custom-button-group .el-button {
  margin: 4px !important; /* 移除按钮之间的间距 */
}

.el-dialog__body {
  padding-top: 0;
}
</style>

<style scoped>
/* 斑马纹样式 */
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #f5f7fa; /* 浅蓝色 */
}

/* 默认行颜色 */
::v-deep .el-table__body tr td {
  background: white; /* 白色 */
}

/* 鼠标悬停颜色 */
::v-deep .el-table__body tr:hover > td {
  background: #e6f7ff !important; /* 悬停时的浅蓝色 */
}

</style>
