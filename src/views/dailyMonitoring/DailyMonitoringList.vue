<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>自动化监控</el-breadcrumb-item>
      <el-breadcrumb-item>日常监控</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="5">
          <el-select placeholder="是否存在待处理用例"
                     v-model="monitoringListQuery.is_pending"
                     clearable>
            <el-option value="0" label="否"></el-option>
            <el-option value="1" label="是"></el-option>
          </el-select>
        </el-col>
        <el-col :span="7">
           <el-date-picker
              v-model="queryDate"
              :clearable="true"
              type="daterange"
              align="right"
              style="width: 300px"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd"
              disabledDate="false">
            </el-date-picker>
          </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="queryList">查 询</el-button>
          <el-button type="primary" @click="queryReset">重 置</el-button>
          <el-button type="warning" @click="sendWeekly">发送周报</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-tabs v-model="monitoringListQuery.environment"
                 class="test"
                 @tab-click="getDailyMonitoringList">
          <el-tab-pane label="测试环境" name="1"></el-tab-pane>
          <el-tab-pane label="生产环境" name="3"></el-tab-pane>
        </el-tabs>
      </el-row>
      <el-row>
        <el-table :data="dailyMonitoringList" border width="100%" v-loading="getDailyMonitoringListLoading">
          <el-table-column label="监控日期" prop="date" min-width="60px" align="center"></el-table-column>
          <el-table-column label="用例总数" prop="test_all" min-width="50px" align="center">
            <template #default="{ row }">
              <span style="color: #5e625f; font-weight: bold; font-size: 13px">{{ row.test_all }}</span>
            </template>
          </el-table-column>
          <el-table-column label="成功用例数" prop="test_pass" min-width="50px" align="center">
            <template #default="{ row }">
              <span style="color: #13a964; font-weight: bold; font-size: 13px">{{ row.test_pass }}</span>
            </template>
          </el-table-column>
          <el-table-column label="失败用例数" prop="test_fail" min-width="50px" align="center">
            <template #default="{ row }">
              <span :style="{
                color: parseInt(row.test_fail) > 0 ? '#FF9C99' : 'inherit',
                fontWeight: parseInt(row.test_fail) > 0 ? 'bold' : 'normal',
                fontSize: parseInt(row.test_fail) > 0 ? '13px' : 'inherit' }">
                {{ row.test_fail }}</span>
            </template>
          </el-table-column>
          <el-table-column label="错误用例数" prop="test_error" min-width="50px" align="center">
            <template #default="{ row }">
              <span :style="{
                color: parseInt(row.test_error) > 0 ? '#FF9C99' : 'inherit',
                fontWeight: parseInt(row.test_error) > 0 ? 'bold' : 'normal',
                fontSize: parseInt(row.test_error) > 0 ? '13px' : 'inherit' }">
                {{ row.test_error }}</span>
            </template>
          </el-table-column>
          <el-table-column label="故障率" prop="failure_rate" min-width="50px" align="center">
            <template #default="{ row }">
              <span :style="{ color: parseFloat(row.failure_rate) >= 10 ? 'red' : 'inherit' }">
                {{ row.failure_rate }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="未处理用例数" prop="pending_case_total" min-width="50px" align="center">
            <template #default="{ row }">
              <span style="color: #5e625f; font-weight: bold; font-size: 13px">{{ row.pending_case_total }}</span>
            </template>
          </el-table-column>
          <el-table-column label="未处理负责人" prop="creaters" min-width="150px" align="center"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="60px" align="center">
            <template v-slot="slotProps">
              <el-tooltip class="item" effect="dark" content="处理失败用例" placement="top" :enterable="false">
                <el-button type="primary" icon="el-icon-thumb" size="mini" circle @click="getMonitoringDetail(slotProps.row)"></el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="一键处理失败用例" placement="top" :enterable="false">
                <el-popconfirm
                  title="确定要一键处理本次监控结果吗？"
                  confirm-button-text="确定"
                  cancel-button-text="取消"
                  icon="el-icon-warning"
                  @confirm="handleAllCase(slotProps.row.record_ids)"
                >
                  <el-button circle v-if="role_id ==='0'" :disabled="slotProps.row.pending_case_total ===0" icon="el-icon-check" type="warning" size="mini" slot="reference"></el-button>
                </el-popconfirm>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="monitoringListQuery.pagenum"
          :page-size="monitoringListQuery.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'task_info',
      queryDate: [],
      dailyMonitoringList: [], // 日常监控列表
      user_name: window.localStorage.getItem('user_name'),
      chinese_name: window.localStorage.getItem('chinese_name'),
      staff_no: window.localStorage.getItem('staff_no'),
      role_id: window.localStorage.getItem('role_id'),
      monitoringListQuery: { // 查询栏
        environment: '1',
        start_date: '',
        end_date: '',
        is_pending: '',
        pagenum: 1,
        pagesize: 20
      },
      detailMonitoringListQuery: {
        environment: 1,
        date: '',
        pagenum: 1,
        pagesize: 20
      },
      total: 0,
      getDailyMonitoringListLoading: false,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 31)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一年',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created() {
    const now = new Date()
    const starttime = new Date(now.setMonth(now.getMonth() - 3)).toLocaleDateString().replace(/\//g, '-')
    const endtime = new Date().toLocaleDateString().replace(/\//g, '-')
    this.monitoringListQuery.start_date = starttime
    this.monitoringListQuery.end_date = endtime
    this.getDailyMonitoringList()
  },
  activated() {
    this.queryReset()
  },
  watch: {
    // 监听 dateRange 的变化，自动更新 workChatInfo
    queryDate(newVal) {
      if (newVal && newVal.length >= 1) {
        this.monitoringListQuery.start_date = newVal[0]
        this.monitoringListQuery.end_date = newVal[1]
      } else {
        this.monitoringListQuery.start_date = ''
        this.monitoringListQuery.end_date = ''
      }
    }
  },
  methods: {
    queryList() {
      this.monitoringListQuery.pagenum = 1
      this.getDailyMonitoringList()
    },
    // 翻页
    handleSizeChange(newSize) {
      this.monitoringListQuery.pagesize = newSize
      this.getDailyMonitoringList()
    },
    handleCurrentChange(newPage) {
      this.monitoringListQuery.pagenum = newPage
      this.getDailyMonitoringList()
    },
    // 获取日常监控结果列表
    async getDailyMonitoringList() {
      console.log('role_id', this.role_id)
      this.getDailyMonitoringListLoading = true
      const { data: res } = await this.$http.post(this.model + '/get_daily_monitoring_list/', this.monitoringListQuery)
      if (res.meta.status !== 200) return this.$message.error('获取日常监控结果列表失败')
      this.total = res.data.total
      this.dailyMonitoringList = res.data.data.map(item => ({
        ...item
      }))
      this.getDailyMonitoringListLoading = false
    },
    // 一键处理所有用例
    async handleAllCase(recordIds) {
      recordIds = recordIds.split(',')
      const { data: res } = await this.$http.post(this.model + '/batch_update_daily_monitoring_result/', { record_ids: recordIds })
      if (res.meta.status !== 200) return this.$message.error('批量处理失败')
      this.$message.success('处理成功')
      await this.getDailyMonitoringList()
    },
    // 获取最近一个月的时间范围
    getLastMonthRange() {
      const now = new Date()
      const lastMonth = new Date()
      lastMonth.setMonth(now.getMonth() - 3) // 向前推一个月
      // 格式化为 yyyy-MM-dd 格式
      const formatDate = (date) => {
        const y = date.getFullYear()
        const m = (date.getMonth() + 1).toString().padStart(2, '0')
        const d = date.getDate().toString().padStart(2, '0')
        return `${y}-${m}-${d}`
      }
      return [formatDate(lastMonth), formatDate(now)]
    },
    // 自动化监控详情
    async getMonitoringDetail(row) {
      this.$router.push({ path: '/daily-monitoring-detail-list', query: { date: row.date, environment: this.monitoringListQuery.environment } })
    },
    // 发送周报
    async sendWeekly() {
      const { data: res } = await this.$http.post('test_plat_report/send_weekly_report')
      if (res.meta.status !== 200) return this.$message.error('发送失败')
      this.$message.success('发送成功')
    },
    // 重置按钮方法
    queryReset() {
      this.queryDate = this.getLastMonthRange()
      this.monitoringListQuery = {
        start_date: this.queryDate[0],
        end_date: this.queryDate[1],
        is_pending: '',
        environment: this.monitoringListQuery.environment,
        pagenum: 1,
        pagesize: 20
      }
      this.getDailyMonitoringList()
    }
  }
}
</script>

<style>
.edit-input .el-input__inner {
  width: 100%;
  padding: 0 7px;
}
.edit-input .el-input__suffix{
  left: 50px;
}
.bold-label .el-form-item__label {
  font-weight: bold;
}

/* 鼠标悬浮效果 */
.el-table__body tr.el-table__row:hover > td,
.el-table__body tr.el-table__row--striped:hover > td {
  background-color: #ecf5ff !important;
}
.custom-button-group .el-button {
  margin: 4px !important; /* 移除按钮之间的间距 */
}

.el-dialog__body {
  padding-top: 0;
}
</style>

<style scoped>
/* 斑马纹样式 */
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #f5f7fa; /* 浅蓝色 */
}

/* 默认行颜色 */
::v-deep .el-table__body tr td {
  background: white; /* 白色 */
}

/* 鼠标悬停颜色 */
::v-deep .el-table__body tr:hover > td {
  background: #e6f7ff !important; /* 悬停时的浅蓝色 */
}

</style>
