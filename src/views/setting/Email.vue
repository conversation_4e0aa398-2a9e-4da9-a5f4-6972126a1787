<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>数据管理</el-breadcrumb-item>
      <el-breadcrumb-item>邮件模板</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="5">
          <el-input placeholder="请输入模板ID"
                    v-model="queryInfo.search_email_model_id"
                    clearable>
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入模板名称"
                    v-model="queryInfo.search_email_model_name"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryEmail">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">
          <el-button class="add" type="primary" @click="showDialog(true, {}, 'add')">添加模板</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="emailList" border>
          <el-table-column label="ID" prop="email_model_id" min-width="50px"></el-table-column>
          <el-table-column label="模板名称" prop="email_model_name" min-width="120px">
            <template v-slot="slotProps">
              <el-link type="primary" v-if="slotProps.row.creater==user_name" @click="showDialog(false, slotProps.row, 'update')">{{ slotProps.row.email_model_name }}</el-link>
              <el-link type="primary" v-else @click="showDialog(false,slotProps.row, 'check')">{{ slotProps.row.email_model_name }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="邮件主题" prop="email_subject" show-overflow-tooltip min-width="120px"></el-table-column>
          <el-table-column label="收件人" prop="email_TO" show-overflow-tooltip min-width="120px"></el-table-column>
          <el-table-column label="抄送人" prop="email_CC" show-overflow-tooltip min-width="120px"></el-table-column>
          <el-table-column label="邮件内容" prop="email_content" show-overflow-tooltip min-width="120px"></el-table-column>
          <el-table-column label="创建人" prop="creater" min-width="100px"></el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="120px"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="120px">
            <template v-slot="slotProps">
              <el-button type="primary" v-if="slotProps.row.creater==user_name" icon="el-icon-edit" size="mini" @click="showDialog(false, slotProps.row, 'update')"></el-button>
              <el-button type="danger" v-if="slotProps.row.creater==user_name || role_id==0" icon="el-icon-delete" size="mini" @click="removeEmailById(slotProps.row.email_model_id)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 新增编辑对话框 -->
    <el-dialog :visible.sync="dialogVisible"
                :title="dialogTitle"
                width="50%"
                @close="DialogClosed"
                :close-on-click-modal="false">
      <el-form :model="emailForm"
                label-width="100px"
                :rules="emailFormRules"
                ref="emailFormRef">
        <el-row>
          <el-col :span="12">
            <el-form-item label="模板名称" prop="email_model_name">
              <el-input v-model="emailForm.email_model_name" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮件主题" prop="email_subject">
              <el-input v-model="emailForm.email_subject" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="收件人" prop="email_TO">
              <el-input v-model="emailForm.email_TO" placeholder="多人时以英文分号隔开"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="抄送人" prop="email_CC">
              <el-input v-model="emailForm.email_CC" placeholder="多人时以英文分号隔开"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="邮件内容">
            <el-input v-model="emailForm.email_content"></el-input>
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="type !== 'check'">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitEmail">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    // 验证邮箱的规则
    var checkEmail = (rule, value, cb) => {
      // 验证邮箱的正则表达式
      const regEmail = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(\.[a-zA-Z0-9_-])+/
      if (!value) return cb()
      if (value.indexOf(';') === -1) {
        if (regEmail.test(value)) {
          // 合法的邮箱
          return cb()
        }
      } else {
        const valueArry = value.split(';')
        valueArry.forEach(item => {
          if (!regEmail.test(item)) {
            // 非法的邮箱
            cb(new Error('请输入合法的邮箱'))
          }
        })
        return cb()
      }
      cb(new Error('请输入合法的邮箱'))
    }
    return {
      model: 'email_model_info',
      user_name: window.localStorage.getItem('user_name'),
      role_id: window.localStorage.getItem('role_id'),
      queryInfo: {
        search_email_model_id: null,
        search_email_model_name: '',
        creater: window.localStorage.getItem('user_name'),
        pagenum: 1,
        pagesize: 5
      },
      emailList: [],
      isAdd: true,
      dialogVisible: false,
      emailForm: {},
      emailFormRules: {
        email_model_name: [
          { required: true, message: '请输入模板名称', trigger: 'blur' }
        ],
        email_subject: [
          { required: true, message: '请输入邮件主题', trigger: 'blur' }
        ],
        email_TO: [
          { required: true, message: '请输入收件人', trigger: 'blur' },
          { validator: checkEmail, trigger: 'blur' }
        ],
        email_CC: [
          { validator: checkEmail, trigger: 'blur' }
        ]
      },
      total: 0,
      dialogTitle: '',
      type: ''
    }
  },
  created() {
    this.getEmailList()
  },
  methods: {
    queryEmail() {
      this.queryInfo.pagenum = 1
      this.getEmailList()
    },
    async getEmailList() {
      const { data: res } = await this.$http.get(this.model + '/email_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取邮件列表失败')
      this.emailList = res.data.emails
      this.total = res.data.total
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getEmailList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getEmailList()
    },
    DialogClosed() {
      this.$refs.emailFormRef.resetFields()
    },
    showDialog(isAdd, email, type) {
      this.type = type
      if (this.type === 'add') {
        this.dialogTitle = '新增邮件模板'
      } else if (this.type === 'update') {
        this.dialogTitle = '编辑邮件模板'
      } else if (this.type === 'check') {
        this.dialogTitle = '查看邮件模板'
      }
      this.isAdd = isAdd
      if (!this.isAdd) {
        const objString = JSON.stringify(email)
        this.emailForm = JSON.parse(objString)
      }
      this.dialogVisible = true
    },
    submitEmail() {
      this.$refs.emailFormRef.validate(async valid => {
        if (!valid) return false
        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/email_add/', this.emailForm)
          if (res.meta.status !== 200) return this.$message.error('新增邮件模板失败')
          this.$message.success('新增邮件模板成功')
          this.getEmailList()
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.emailForm.email_model_id + '/email_update/', this.emailForm)
          if (res.meta.status !== 200) return this.$message.error('编辑邮件模板失败')
          this.$message.success('编辑邮件模板成功')
          this.getEmailList()
        }
        this.dialogVisible = false
      })
    },
    removeEmailById(id) {
      this.$confirm('此操作将永久删除该邮件模板, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log('id====>', id)
        this.$http.delete(this.model + '/' + id + '/email_delete/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error('删除邮件模板失败')
          this.$message.success('删除邮件模板成功')
          this.getEmailList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>
