<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>数据管理</el-breadcrumb-item>
      <el-breadcrumb-item>测试参数</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="5">
          <el-input placeholder="请输入参数ID"
                    v-model="queryInfo.parameterId"
                    clearable>
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入参数名称"
                    v-model="queryInfo.parameterName"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryParam">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">
          <el-button class="add" type="primary" @click="showDialog(true, {}, 'add')">添加参数</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="paramList" border>
          <el-table-column label="ID" prop="parameter_id" min-width="50px"></el-table-column>
          <el-table-column label="参数名称" prop="parameter_name" min-width="100px" show-overflow-tooltip>
            <template v-slot="slotProps">
              <el-link type="primary" v-if="slotProps.row.creater==user_name" @click="showDialog(false,slotProps.row,'update')">{{ slotProps.row.parameter_name }}</el-link>
              <el-link type="primary" v-else @click="showDialog(false,slotProps.row, 'check')">{{ slotProps.row.parameter_name }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="参数类型" prop="parameter_type" min-width="70px" show-overflow-tooltip>
            <template v-slot="slotProps">
              <span v-if="slotProps.row.parameter_type==='1'">字符串</span>
              <span v-else-if="slotProps.row.parameter_type==='2'">函数</span>
              <span v-else-if="slotProps.row.parameter_type==='3'">数据库</span>
              <span v-else-if="slotProps.row.parameter_type==='4'">Redis</span>
            </template>
          </el-table-column>
          <el-table-column label="测试参数" prop="test_parameter" show-overflow-tooltip min-width="100px"></el-table-column>
          <el-table-column label="预发参数" prop="uat_parameter" show-overflow-tooltip min-width="100px"></el-table-column>
          <el-table-column label="生产参数" prop="produce_parameter" show-overflow-tooltip min-width="100px"></el-table-column>
          <el-table-column label="备注" prop="remark" show-overflow-tooltip min-width="100px"></el-table-column>
          <el-table-column label="创建人" prop="creater" min-width="100px"></el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="120px"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="120px">
            <template v-slot="slotProps">
              <el-button type="primary" icon="el-icon-edit" size="mini" @click="showDialog(false,slotProps.row,'update')"></el-button>
              <el-button type="danger" v-if="slotProps.row.creater==user_name || role_id==0" icon="el-icon-delete" size="mini" @click="removeParamById(slotProps.row.parameter_id)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 新增编辑对话框 -->
    <el-dialog :visible.sync="dialogVisible"
                :title="title"
                width="50%"
                @close="DialogClosed"
                :close-on-click-modal="false">
      <el-form :model="paramForm"
                label-width="100px"
                :rules="paramFormRules"
                ref="paramFormRef">
        <el-row>
          <el-col :span="12">
            <el-form-item label="参数名称" prop="parameter_name">
              <el-input v-model="paramForm.parameter_name">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="参数类型" prop="parameter_type">
              <el-select v-model="paramForm.parameter_type">
                <el-option value="1" label="字符串"></el-option>
                <el-option value="2" label="函数"></el-option>
                <el-option value="3" label="数据库"></el-option>
                <el-option value="4" label="Redis"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="paramForm.parameter_type=='3'">
          <el-form-item label="数据库" prop="database_id">
            <el-select v-model="paramForm.database_id" filterable>
              <el-option v-for="item in databaseList"
                           :label="item.label"
                           :value="item.id"
                           :key="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
                <el-row v-if="paramForm.parameter_type=='4'">
          <el-form-item label="数据库" prop="database_id">
            <el-select v-model="paramForm.database_id" filterable>
              <el-option v-for="item in redisList"
                           :label="item.label"
                           :value="item.id"
                           :key="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="测试参数" prop="test_parameter">
            <el-input v-model="paramForm.test_parameter"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="预发参数" prop="uat_parameter">
            <el-input v-model="paramForm.uat_parameter"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="生产参数" prop="produce_parameter">
            <el-input v-model="paramForm.produce_parameter"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="paramForm.remark"></el-input>
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="type !== 'check'">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitParam">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'parameter_info',
      user_name: window.localStorage.getItem('user_name'),
      role_id: window.localStorage.getItem('role_id'),
      queryInfo: {
        parameterId: null,
        parameterName: '',
        creater: window.localStorage.getItem('user_name'),
        pagenum: 1,
        pagesize: 5
      },
      paramList: [],
      total: 0,
      dialogVisible: false,
      title: '',
      paramForm: {
        parameter_name: '',
        parameter_type: '',
        database_msg: null,
        test_parameter: '',
        uat_parameter: '',
        produce_parameter: '',
        remark: ''
      },
      paramFormRules: {
        parameter_name: [
          { required: true, message: '请输入参数名称', trigger: 'blur' }
        ],
        parameter_type: [
          { required: true, message: '请选择参数类型', trigger: 'blur' }
        ],
        test_parameter: [
          { required: true, message: '请输入测试参数', trigger: 'blur' }
        ],
        uat_parameter: [
          { required: true, message: '请输入预发参数', trigger: 'blur' }
        ],
        produce_parameter: [
          { required: true, message: '请输入生产参数', trigger: 'blur' }
        ]
      },
      isAdd: true,
      databaseList: [],
      redisList: [],
      type: ''
    }
  },
  created() {
    this.getParamList()
    this.getDatabaseList()
    this.getRedisList()
  },
  methods: {
    queryParam() {
      this.queryInfo.pagenum = 1
      this.getParamList()
    },
    async getDatabaseList() {
      const { data: res } = await this.$http.get('/user_info/info/', { params: { type: 'databases' } })
      if (res.meta.status !== 200) return this.$message.error('获取数据库信息失败')
      this.databaseList = res.data
    },
    async getRedisList() {
      const { data: res } = await this.$http.get('user_info/info/', { params: { type: 'redis' } })
      if (res.meta.status !== 200) return this.$message.error('获取Redis信息失败')
      this.redisList = res.data
    },
    async getParamList() {
      const { data: res } = await this.$http.get(this.model + '/parameter_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取参数列表失败')
      console.log('数据库列表==>', res)
      this.total = res.data.total
      this.paramList = res.data.parameters
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getParamList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getParamList()
    },
    DialogClosed() {
      this.$refs.paramFormRef.resetFields()
      this.paramForm = {
        parameter_name: '',
        parameter_type: '',
        database_msg: null,
        test_parameter: '',
        uat_parameter: '',
        produce_parameter: '',
        remark: ''
      }
    },
    showDialog(isAdd, param, type) {
      this.type = type
      if (this.type === 'add') {
        this.title = '新增参数'
      } else if (this.type === 'update') {
        this.title = '编辑参数'
      } else if (this.type === 'check') {
        this.title = '查看参数'
      }
      this.isAdd = isAdd
      if (!isAdd) {
        const objString = JSON.stringify(param)
        this.paramForm = JSON.parse(objString)
      }
      this.dialogVisible = true
    },
    submitParam() {
      this.$refs.paramFormRef.validate(async valid => {
        if (!valid) return false
        if (this.isAdd) {
          console.log('提交参数====》', this.paramForm)
          const { data: res } = await this.$http.post(this.model + '/parameter_add/', this.paramForm)
          if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
          this.$message.success('新增参数成功')
          this.getParamList()
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.paramForm.parameter_id + '/parameter_update/', this.paramForm)
          if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
          this.$message.success('编辑参数成功')
          this.getParamList()
        }
        this.dialogVisible = false
      })
    },
    removeParamById(id) {
      this.$confirm('此操作将永久删除该参数, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log('id====>', id)
        this.$http.delete(this.model + '/' + id + '/parameter_delete/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error('删除参数失败')
          this.$message.success('删除参数成功')
          this.getParamList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>
