<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>数据管理</el-breadcrumb-item>
      <el-breadcrumb-item>测试任务</el-breadcrumb-item>
      <el-breadcrumb-item>任务报表</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row>
        <el-table :data="reportList" border>
          <el-table-column label="ID" prop="taskReportId" min-width="50px" fixed="left"></el-table-column>
          <el-table-column label="任务ID" prop="taskId" min-width="100px" show-overflow-tooltip fixed="left"></el-table-column>
          <el-table-column label="任务名称" prop="taskName" min-width="150px" show-overflow-tooltip fixed="left"></el-table-column>
          <el-table-column label="运行环境" prop="runEnvironment" show-overflow-tooltip min-width="100px">
            <template v-slot="scopeProps">
              <span v-if="scopeProps.row.runEnvironment=='1'">测试环境</span>
              <span v-else-if="scopeProps.row.runEnvironment=='2'">预发环境</span>
              <span v-else>生产环境</span>
            </template>
          </el-table-column>
          <el-table-column label="任务类型" prop="taskType" show-overflow-tooltip min-width="100px">
            <template v-slot="scopeProps">
              <span v-if="scopeProps.row.taskType=='1'">接口案例</span>
              <span v-else-if="scopeProps.row.taskType=='2'">接口场景</span>
              <span v-else-if="scopeProps.row.taskType=='3'">Web-UI</span>
              <span v-else>Android-UI</span>
            </template>
          </el-table-column>
          <el-table-column label="案例总数" prop="caseNum" show-overflow-tooltip min-width="100px"></el-table-column>
          <el-table-column label="案例成功数" prop="caseSuccess" min-width="120px"></el-table-column>
          <el-table-column label="案例失败数" prop="caseFail" min-width="120px"></el-table-column>
          <el-table-column label="案例错误数" prop="caseError" min-width="120px"></el-table-column>
          <el-table-column label="任务耗时" prop="totalTime" min-width="120px"></el-table-column>
          <el-table-column label="报告产生时间" prop="createTime" min-width="150px"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="150px">
            <template v-slot="slotProps">
              <el-tooltip class="item" effect="dark" content="查看测试报告详情" placement="top" :enterable="false">
                <el-button type="warning" icon="el-icon-document-checked" size="mini" @click="showReport(slotProps.row.taskReportId)"></el-button>
              </el-tooltip>
               <el-tooltip class="item" effect="dark" content="手动重试" placement="top" :enterable="false">
                <el-button type="warning" icon="el-icon-refresh-right" :disabled="slotProps.row.caseError =='0' && slotProps.row.caseFail == '0'"
                           size="mini"  @click="retry(slotProps.row.taskId,slotProps.row.taskReportId)">
                </el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      queryInfo: {
        task_id: null,
        pagenum: 1,
        pagesize: 10
      },
      reportList: [],
      total: 0
    }
  },
  created() {
    this.getReportList()
  },
  activated() {
    this.getReportList()
  },
  methods: {
    async getReportList() {
      this.queryInfo.task_id = this.$route.query.taskId
      const { data: res } = await this.$http.get('/task_info/task_report_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
      this.reportList = res.data.task_reports
      this.total = res.data.total
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getReportList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getReportList()
    },
    async showReport(id) {
      window.open(this.$http.defaults.baseURL + 'task_info/task_report/?task_id=' + id)
    },
    async retry(taskId, taskReportId) {
      console.log(taskId, taskReportId)
      const { data: res } = await this.$http.get('/task_info/manual_retry_task/', { params: { task_id: parseInt(taskId), task_report_id: parseInt(taskReportId) } })
      if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
      this.$message.success(res.meta.msg)
    }
  }
}
</script>
