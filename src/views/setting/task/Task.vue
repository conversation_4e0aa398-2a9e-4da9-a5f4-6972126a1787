<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>数据管理</el-breadcrumb-item>
      <el-breadcrumb-item>测试任务</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="3">
          <el-select placeholder="请选择运行环境"
                     v-model="queryInfo.search_environment"
                     clearable>
            <el-option value="1" label="测试环境"></el-option>
            <el-option value="2" label="预发环境"></el-option>
            <el-option value="3" label="生产环境"></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select placeholder="请选择任务类型"
                     v-model="queryInfo.search_task_type"
                     clearable>
            <el-option value="API" label="API"></el-option>
            <el-option value="UI" label="UI"></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-input placeholder="请输入任务ID"
                    v-model="queryInfo.search_task_id"
                    clearable>
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入任务名称"
                    v-model="queryInfo.search_task_name"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryTask">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">
          <el-button class="add" type="primary" @click="showDialog(true, {})">添加任务</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="taskList" border>
          <el-table-column label="ID" prop="task_id" min-width="50px" fixed="left"></el-table-column>
          <el-table-column label="任务名称" prop="task_name" min-width="150px" show-overflow-tooltip fixed="left">
            <template v-slot="slotProps">
              <el-link type="primary" v-if="primary_tester.indexOf(user_name) !== -1 || slotProps.row.creater==user_name" @click="showDialog(false,slotProps.row)">{{ slotProps.row.task_name }}</el-link>
              <span v-else>{{ slotProps.row.task_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="运行环境" min-width="100px">
            <template v-slot="scopeProps">
              <span v-if="scopeProps.row.environment=='1'">测试环境</span>
              <span v-else-if="scopeProps.row.environment=='2'">预发环境</span>
              <span v-else>生产环境</span>
            </template>
          </el-table-column>
          <el-table-column label="任务类型" prop="task_type" show-overflow-tooltip min-width="100px"></el-table-column>
          <el-table-column label="细分类型" prop="api_type" show-overflow-tooltip min-width="100px"></el-table-column>
          <el-table-column label="crontab表达式" prop="cron" show-overflow-tooltip min-width="100px"></el-table-column>
          <el-table-column label="是否开启" min-width="100px">
            <template v-slot="scopeProps">
              <span v-if="scopeProps.row.is_open" type="success">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column label="发送邮件" prop="is_send_email" show-overflow-tooltip min-width="100px">
            <template v-slot="scopeProps">
              <span v-if="scopeProps.row.is_send_email">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column label="运行状态" min-width="100px">
            <template v-slot="scopeProps">
              <el-tag v-if="scopeProps.row.task_status=='C'" type="success">未运行</el-tag>
              <el-tag v-else-if="scopeProps.row.task_status=='R'" type="warning">运行中</el-tag>
              <el-tag v-else-if="scopeProps.row.task_status=='S'">已完成</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建人" prop="creater" min-width="120px"></el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="150px"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="260px">
            <template v-slot="slotProps">
              <el-button type="primary" v-if="primary_tester.indexOf(user_name) !== -1 || slotProps.row.creater==user_name" icon="el-icon-edit" size="mini" @click="showDialog(false,slotProps.row,'update')"></el-button>
              <el-tooltip class="item" effect="dark" content="查看测试报告列表" placement="top" :enterable="false">
                <el-button type="warning" icon="el-icon-document-checked" size="mini" @click="goReportPage(slotProps.row.task_id)"></el-button>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="运行测试任务" placement="top" :enterable="false">
                <el-button type="success" icon="el-icon-caret-right" size="mini" @click="runTask(slotProps.row.task_id)"></el-button>
              </el-tooltip>
            <el-button type="danger" v-if="slotProps.row.creater==user_name" icon="el-icon-delete" size="mini" @click="removeTaskById(slotProps.row.task_id)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 新增编辑对话框 -->
    <el-dialog :visible.sync="dialogVisible"
                :title="dialogTitle"
                width="65%"
                @close="DialogClosed"
                :close-on-click-modal="false"
                >
      <el-form :model="taskForm"
                label-width="100px"
                :rules="taskFormRules"
                ref="taskFormRef">
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务名称" prop="task_name">
              <el-input v-model="taskForm.task_name" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="运行环境" prop="environment" label-width="120px">
              <el-select v-model="taskForm.environment">
                <el-option value="1" label="测试环境"></el-option>
                <el-option value="2" label="预发环境"></el-option>
                <el-option value="3" label="生产环境"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务类型" prop="task_type">
              <template>
                <el-radio-group v-model="taskForm.task_type" :disabled="!isAdd" @change="taskTypeChange">
                  <el-radio label="API">API</el-radio>
                  <el-radio label="UI">UI</el-radio>
                </el-radio-group>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="细分类型" prop="api_type" label-width="120px">
              <template v-if="taskForm.task_type=='API'">
                <el-radio-group v-model="taskForm.api_type" @change="getCaseList" :disabled="!isAdd">
                  <el-radio label="CASE">CASE</el-radio>
                  <el-radio label="SCENE">SCENE</el-radio>
                </el-radio-group>
              </template>
              <template v-else>
                <el-radio-group v-model="taskForm.api_type" @change="getCaseList" :disabled="!isAdd">
                  <el-radio label="WEB">WEB</el-radio>
                  <el-radio label="Android">Android</el-radio>
                </el-radio-group>
              </template>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开启任务" prop="is_open">
              <el-switch v-model="taskForm.is_open" active-color="#13ce66">
              </el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="crontab表达式" prop="cron" label-width="120px">
              <el-input v-model="taskForm.cron" placeholder="请输入内容,如：0/5 * * * *"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="案例" prop="case">
            <tree-transfer :from_data="fromData"
                           :to_data="taskForm.case"
                           openAll
                           height="500px"
                           filter
                           :title="caseTitle"
                           @add-btn="addCase"
                           @remove-btn='removeCase'
                           :mode="mode"
                           ref="caseTreeRef"
                           :defaultCheckedKeys="defaultCase"
                           defaultTransfer
                           v-loading="transferLoading"
                           @mouseover.native="addTitle">
            </tree-transfer>
          </el-form-item>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否发送邮件">
              <el-switch v-model="taskForm.is_send_email" active-color="#13ce66">
                </el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="taskForm.is_send_email">
            <el-form-item label="邮件模板">
              <el-select v-model="taskForm.email_model">
                <el-option v-for="item in emailList"
                           :key="item.id"
                           :label="item.label"
                           :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="taskForm.remark"></el-input>
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitTask">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    // 验证cron规则
    var checkCron = (rule, value, cb) => {
      // 验证邮箱的正则表达式
      const regCron = /^((((\d+,)+\d+|(\d+(\/|-|#)\d+)|\d+L?|\*(\/\d+)?|L(-\d+)?|\?|[A-Z]{3}(-[A-Z]{3})?) ?){5})$/
      if (regCron.test(value)) {
        // 合法的邮箱
        return cb()
      }
      cb(new Error('请输入合法的crontab表达式'))
    }
    return {
      model: 'task_info',
      user_name: window.localStorage.getItem('user_name'),
      queryInfo: {
        search_task_id: null,
        search_task_name: '',
        search_environment: '1',
        search_task_type: 'API',
        creater: window.localStorage.getItem('user_name'),
        pagenum: 1,
        pagesize: 5
      },
      taskList: [],
      total: 0,
      dialogVisible: false,
      caseTitle: ['待选案例', '已选案例'],
      taskForm: {
        task_name: '',
        environment: '',
        task_type: 'API',
        api_type: 'CASE',
        is_open: false,
        case_pool: [],
        case: [],
        run_time: '0000',
        remark: '',
        is_send_email: false,
        email_model: '',
        cron: '6 6 6 * *'
      },
      isAdd: false,
      mode: 'transfer', // transfer addressList
      fromData: [],
      dialogTitle: '',
      taskFormRules: {
        task_name: [
          { required: true, message: '请输入任务名称', trigger: 'blur' }
        ],
        environment: [
          { required: true, message: '请选择任务环境', trigger: 'blur' }
        ],
        task_type: [
          { required: true, message: '请选择任务类型', trigger: 'blur' }
        ],
        api_type: [
          { required: true, message: '请选择细分类型', trigger: 'blur' }
        ],
        cron: [
          { required: true, message: '请输入crontab表达式', trigger: 'blur' },
          { validator: checkCron, trigger: 'blur' }
        ]
      },
      emailList: [],
      defaultCase: [],
      transferLoading: false,
      primary_tester: ['huangjiaojiao', 'huwei', 'hupeng', 'zhouqianwen', 'zhaomengzi', 'limingli', 'zhanglin']
    }
  },
  created() {
    this.getTaskList()
    this.getEmailList()
  },
  methods: {
    queryTask() {
      this.queryInfo.pagenum = 1
      this.getTaskList()
    },
    async getTaskList() {
      const { data: res } = await this.$http.get(this.model + '/task_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取任务列表失败')
      console.log('任务数据===》', res)
      this.taskList = res.data.tasks
      this.total = res.data.total
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getTaskList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getTaskList()
    },
    DialogClosed() {
      this.$refs.taskFormRef.resetFields()
      this.taskForm = {
        task_name: '',
        environment: '',
        task_type: 'API',
        api_type: 'CASE',
        is_open: false,
        case_pool: [],
        case: [],
        run_time: '0000',
        remark: '',
        is_send_email: false,
        email_model: '',
        cron: '6 6 6 * *'
      }
    },
    showDialog(isAdd, task) {
      this.isAdd = isAdd
      if (this.isAdd) {
        this.dialogTitle = '新增任务'
        this.getCaseList('CASE')
      } else {
        this.dialogTitle = '编辑任务'
        const objString = JSON.stringify(task)
        this.taskForm = JSON.parse(objString)
        if (this.taskForm.is_send_email) {
          this.taskForm.email_model = parseInt(this.taskForm.email_model)
        }
        this.getCaseList(this.taskForm.api_type)
      }
      this.dialogVisible = true
    },
    submitTask() {
      this.$refs.taskFormRef.validate(async valid => {
        if (!valid) return false
        if (this.taskForm.case_pool.length === 0) {
          this.$message.error('请选择任务内容')
          return false
        }
        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/task_add/', this.taskForm)
          if (res.meta.status !== 200) return this.$message.error('新增任务失败')
          this.$message.success('新增任务成功')
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.taskForm.task_id + '/task_update/', this.taskForm)
          if (res.meta.status !== 200) return this.$message.error('编辑任务失败')
          this.$message.success('编辑任务成功')
        }
        this.getTaskList()
        this.getEmailList()
        this.dialogVisible = false
      })
    },
    removeTaskById(id) {
      this.$confirm('此操作将永久删除该任务, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log('id====>', id)
        this.$http.delete(this.model + '/' + id + '/task_delete/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error('删除任务失败')
          this.$message.success('删除任务成功')
          this.getTaskList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async getCaseList(value) {
      this.transferLoading = true
      if (value === 'CASE') {
        const { data: res } = await this.$http.get('interface_info/get_api_list/?api_type=cases')
        if (res.meta.status !== 200) return this.$message.error('获取接口案例信息失败')
        this.fromData = res.data
      } else if (value === 'SCENE') {
        const { data: res } = await this.$http.get('interface_info/get_api_list/?api_type=scenes')
        if (res.meta.status !== 200) return this.$message.error('获取场景信息失败')
        this.fromData = res.data
      } else if (value === 'WEB') {
        const { data: res } = await this.$http.get('user_info/info/', { params: { type: 'webCases' } })
        if (res.meta.status !== 200) return this.$message.error('获取web案例信息失败')
        this.fromData = res.data
      } else if (value === 'Android') {
        const { data: res } = await this.$http.get('user_info/info/', { params: { type: 'appCases' } })
        if (res.meta.status !== 200) return this.$message.error('获取安卓案例信息失败')
        this.fromData = res.data
      }
      if (this.isAdd) {
        this.taskForm.case = []
        this.taskForm.case_pool = []
      } else {
        if (this.taskForm.api_type === 'CASE' || this.taskForm.api_type === 'SCENE') {
          this.defaultCase = this.taskForm.defaultCase
        } else {
          this.defaultCase = this.taskForm.defaultCase.map(Number)
        }
      }
      this.transferLoading = false
    },
    addCase(fromData, toData) {
      console.log('toData========>', toData)
      this.taskForm.case_pool = []
      if (this.taskForm.api_type === 'CASE' || this.taskForm.api_type === 'SCENE') {
        toData.forEach(item => {
          this.getLeafKeys(item, this.taskForm.case_pool)
        })
      } else {
        toData.forEach(item => {
          this.taskForm.case_pool.push(item.id.toString())
        })
      }
      console.log('添加数据变化==>', this.taskForm.case_pool)
    },
    removeCase(fromData, toData, obj) {
      this.taskForm.case_pool = []
      toData.forEach(item => {
        this.getLeafKeys(item, this.taskForm.case_pool)
      })
      console.log('删除时数据变化==>', this.taskForm.case_pool)
    },
    getLeafKeys(node, arry) {
      if (!node.children) return arry.push(node.value)
      node.children.forEach(item => {
        this.getLeafKeys(item, arry)
      })
    },
    async getEmailList() {
      const { data: res } = await this.$http.get('/user_info/info/', { params: { type: 'emails' } })
      if (res.meta.status !== 200) return this.$message.error('获取邮件列表失败')
      this.emailList = res.data
    },
    async showReport(id) {
      window.open(this.$http.defaults.baseURL + this.model + '/task_report/?task_id=' + id)
    },
    async runTask(id) {
      const { data: res } = await this.$http.get(this.model + '/task_run/', { params: { task_id: parseInt(id) } })
      if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
      this.$message.success(res.meta.msg)
    },
    taskTypeChange(value) {
      if (value === 'API') {
        this.taskForm.api_type = 'CASE'
        this.getCaseList('CASE')
      } else if (value === 'UI') {
        this.taskForm.api_type = 'WEB'
        this.getCaseList('WEB')
      }
    },
    goReportPage(taskId) {
      this.$router.push({ path: '/report', query: { taskId: taskId } })
    },
    addTitle(e) {
      const targetElement = e.target
      if (targetElement.title) return
      targetElement.title = targetElement.innerText
    }
  }
}
</script>
