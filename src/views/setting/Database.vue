<template>
  <div>
    <!-- 面包屑 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>数据管理</el-breadcrumb-item>
      <el-breadcrumb-item>数据库</el-breadcrumb-item>
    </el-breadcrumb>
    <!-- 卡片 -->
    <el-card>
      <!-- 查询条件 -->
      <el-row :gutter="20">
        <el-col :span="5">
          <el-input placeholder="请输入数据库ID"
                    v-model="queryInfo.databaseId"
                    clearable>
          </el-input>
          </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入数据库名称"
                    v-model="queryInfo.databaseName"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryDatabase">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="2">
          <el-button class="add" type="primary" @click="addDialogVisible=true">新增</el-button>
        </el-col>
        <!-- <el-col :span="2">
          <el-button class="add" type="danger" @click="removeDatabaseById()">删除</el-button>
        </el-col> -->
      </el-row>
      <!-- 表格 -->
      <el-table border :data="databaseList" @selection-change="handleSelectionChange" ref="databaseTable">
         <!-- <el-table-column type="selection" width="55"></el-table-column> -->
        <el-table-column label="ID" prop="data_base_id" min-width="50px"></el-table-column>
        <el-table-column label="数据库名称" prop="data_base_name" min-width="130px" show-overflow-tooltip>
          <template v-slot="slotProps">
            <el-link type="primary" @click="showEditDialog(slotProps.row)">{{ slotProps.row.data_base_name }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="数据库类型" prop="data_base_type" min-width="100px" show-overflow-tooltip></el-table-column>
        <el-table-column label="连接地址" prop="ip_address" min-width="100px" show-overflow-tooltip></el-table-column>
        <el-table-column label="端口" prop="data_base_port" min-width="100px" show-overflow-tooltip></el-table-column>
        <el-table-column label="服务名称" prop="db_name" min-width="100px" show-overflow-tooltip></el-table-column>
        <el-table-column label="备注" prop="remark" show-overflow-tooltip min-width="100px"></el-table-column>
        <el-table-column label="创建人" prop="creater" min-width="100px"></el-table-column>
        <el-table-column label="最终修改时间" prop="update_time" min-width="130px"></el-table-column>
        <el-table-column label="操作" fixed="right" min-width="120px">
          <template v-slot="slotProps">
            <el-button type="primary" v-if="slotProps.row.creater==user_name" icon="el-icon-edit" size="mini" @click="showEditDialog(slotProps.row)"></el-button>
          <el-button type="danger" v-if="slotProps.row.creater==user_name || role_id==0" icon="el-icon-delete" size="mini" @click="removeDatabaseById(slotProps.row.data_base_id)"></el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 编辑数据库对话框 -->
    <el-dialog :visible.sync="editDialogVisible"
                :title="title"
                width="50%"
                @close="editDialogClosed"
                :close-on-click-modal="false">
      <el-form :model="editForm"
                label-width="100px"
                :rules="editFormRules"
                ref="editFormRef">
        <el-row>
          <el-col :span="12">
            <el-form-item label="数据库名称" prop="data_base_name">
              <el-input v-model="editForm.data_base_name"></el-input>
            </el-form-item>
         </el-col>
         <el-col :span="12">
            <el-form-item label="数据库类型" prop="password">
              <el-select v-model="editForm.data_base_type">
                <el-option value="MySQL">MySQL</el-option>
                <el-option value="Oracle">Oracle</el-option>
                <el-option value="SQLServer">SQLServer</el-option>
                <el-option value="Redis">Redis</el-option>
              </el-select>
            </el-form-item>
        </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="连接地址" prop="ip_address">
              <el-input v-model="editForm.ip_address"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="端口号">
              <el-input v-model="editForm.data_base_port"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="登录账号" prop="db_account">
              <el-input v-model="editForm.db_account"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务名称" prop="db_name">
              <el-input v-model="editForm.db_name"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否修改密码">
              <el-switch
                v-model="isModify"
                active-color="#13ce66">
              </el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="登录密码" prop="db_pwd" v-if="isModify">
              <el-input v-model="editForm.db_pwd"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
        <el-form-item label="备注">
          <el-input type="textarea" v-model="editForm.remark"></el-input>
        </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="editForm.creater==user_name">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="editTestDatabase">测试连接</el-button>
        <el-button type="primary" @click="editDatabase">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 新增数据库对话框 -->
    <el-dialog :visible.sync="addDialogVisible"
                title="新增数据库"
                width="50%"
                @close="addDialogClosed"
                :close-on-click-modal="false">
      <el-form :model="addForm"
                label-width="100px"
                :rules="addFormRules"
                ref="addFormRef">
        <el-row>
          <el-col :span="12">
            <el-form-item label="数据库名称" prop="data_base_name">
              <el-input v-model="addForm.data_base_name"></el-input>
            </el-form-item>
         </el-col>
         <el-col :span="12">
            <el-form-item label="数据库类型" prop="data_base_type">
              <el-select v-model="addForm.data_base_type">
                <el-option value="MySQL">MySQL</el-option>
                <el-option value="Oracle">Oracle</el-option>
                <el-option value="SQLServer">SQLServer</el-option>
                <el-option value="Redis">Redis</el-option>
              </el-select>
            </el-form-item>
        </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="连接地址" prop="ip_address">
              <el-input v-model="addForm.ip_address"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="端口号">
              <el-input v-model="addForm.data_base_port"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="登录账号" prop="db_account">
              <el-input v-model="addForm.db_account"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="登录密码" prop="db_pwd">
              <el-input v-model="addForm.db_pwd"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
        <el-form-item label="服务名称" prop="db_name">
          <el-input v-model="addForm.db_name"></el-input>
        </el-form-item>
        </el-row>
        <el-row>
        <el-form-item label="备注">
          <el-input type="textarea" v-model="addForm.remark"></el-input>
        </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addTestDatabase">测试连接</el-button>
        <el-button type="primary" @click="addDatabase">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 环境选择对话框 -->
    <el-dialog :visible.sync="enviromentDialogVisible"
                width="30%"
                @close="enviromentDialogClosed"
                :close-on-click-modal="false">
      <el-form label-width="130px"
               :model="caseRunForm"
               ref="caseRunFormRef"
               :rules="caseRunFormRules">
        <el-form-item label="请选择运行环境" prop="env_type">
          <el-select v-model="env_type">
            <el-option label="测试环境" value="1"></el-option>
            <el-option label="预发环境" value="2"></el-option>
            <el-option label="生产环境" value="3"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="enviromentDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="connectDatabase">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'data_base_info',
      user_name: window.localStorage.getItem('user_name'),
      role_id: window.localStorage.getItem('role_id'),
      queryInfo: {
        databaseId: null,
        databaseName: '',
        creater: window.localStorage.getItem('user_name'),
        pagenum: 1,
        pagesize: 5
      },
      isAdd: true,
      env_type: '1',
      databaseList: [],
      addDialogVisible: false,
      addForm: {
        data_base_name: '',
        data_base_type: '',
        ip_address: '',
        data_base_port: '',
        db_account: '',
        db_pwd: '',
        db_name: '',
        remark: ''
      },
      total: 0,
      addFormRules: {
        data_base_name: [
          { required: true, message: '请输入数据库名称', trigger: 'blur' }
        ],
        data_base_type: [
          { required: true, message: '请选择数据库类型', trigger: 'blur' }
        ],
        ip_address: [
          { required: true, message: '请输入连接地址', trigger: 'blur' }
        ],
        db_name: [
          { required: true, message: '请输入服务名称', trigger: 'blur' }
        ]
      },
      editDialogVisible: false,
      editForm: {
      },
      editFormRules: {
        data_base_name: [
          { required: true, message: '请输入数据库名称', trigger: 'blur' }
        ],
        data_base_type: [
          { required: true, message: '请选择数据库类型', trigger: 'blur' }
        ],
        ip_address: [
          { required: true, message: '请输入连接地址', trigger: 'blur' }
        ],
        db_name: [
          { required: true, message: '请输入服务名称', trigger: 'blur' }
        ]
      },
      isModify: false,
      type: '',
      title: '编辑数据库',
      enviromentDialogVisible: false,
      deleteList: []
    }
  },
  created() {
    this.getDatabaseList()
  },
  methods: {
    queryDatabase() {
      this.queryInfo.pagenum = 1
      this.getDatabaseList()
    },
    async getDatabaseList() {
      console.log('this.user_name=========>', this.user_name)
      console.log('this.queryInfo=========>', this.queryInfo)
      const { data: res } = await this.$http.get(this.model + '/db_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取数据库列表失败')
      console.log('数据库列表===》', res.data)
      this.databaseList = res.data.databases
      this.total = res.data.total
    },
    addDialogClosed() {
      this.$refs.addFormRef.resetFields()
      this.addForm = {
        data_base_name: '',
        data_base_type: '',
        ip_address: '',
        data_base_port: '',
        db_account: '',
        db_pwd: '',
        db_name: '',
        remark: ''
      }
    },
    addDatabase() {
      this.$refs.addFormRef.validate(async valid => {
        if (valid) {
          const { data: res } = await this.$http.post(this.model + '/db_add/', this.addForm)
          if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
          this.$message.success('新增数据库成功')
          this.addDialogVisible = false
          this.getDatabaseList()
        } else {
          return false
        }
      })
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getDatabaseList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getDatabaseList()
    },
    addTestDatabase() {
      this.enviromentDialogVisible = true
      this.isAdd = true
    },
    async connectDatabase() {
      if (this.isAdd) {
        this.$refs.addFormRef.validate(async valid => {
          if (valid) {
            this.$set(this.addForm, 'env_type', this.env_type)
            const { data: res } = await this.$http.post(this.model + '/db_connect/', this.addForm)
            if (res.meta.status !== 200) return this.$message.error('接口错误')
            this.$message.success(res.meta.msg)
            console.log('测试连接===》', res)
          } else {
            return false
          }
        })
      } else {
        this.$refs.editFormRef.validate(async valid => {
          if (valid) {
            this.$set(this.editForm, 'env_type', this.env_type)
            if (!this.isModify) return this.$message.error('请开启修改密码，填入密码信息')
            const { data: res } = await this.$http.post(this.model + '/db_connect/', this.editForm)
            if (res.meta.status !== 200) return this.$message.error('接口错误')
            this.$message.success(res.meta.msg)
            console.log('测试连接===》', res)
          } else {
            return false
          }
        })
      }
      this.enviromentDialogVisible = false
    },
    enviromentDialogClosed() {
      this.$refs.caseRunFormRef.resetFields()
    },
    showEditDialog(database) {
      const objString = JSON.stringify(database)
      this.editForm = JSON.parse(objString)
      if (this.editForm.creater === this.user_name) {
        console.log('this.editForm.creater========>', this.editForm.creater)
        this.title = '编辑数据库'
      } else {
        console.log('this.editForm.creater========>', this.editForm.creater)
        this.title = '查看数据库'
      }
      this.editForm.db_pwd = ''
      this.editDialogVisible = true
    },
    editDialogClosed() {
      this.$refs.editFormRef.resetFields()
      this.isModify = false
    },
    editDatabase() {
      this.$refs.editFormRef.validate(async valid => {
        if (valid) {
          const { data: res } = await this.$http.put(this.model + '/' + this.editForm.data_base_id + '/db_update/', this.editForm)
          if (res.meta.status !== 200) return this.$message.error('编辑数据库失败')
          this.$message.success('编辑数据库成功')
          this.editDialogVisible = false
          this.getDatabaseList()
        } else {
          return false
        }
      })
    },
    editTestDatabase() {
      this.enviromentDialogVisible = true
      this.isAdd = false
    },
    removeDatabaseById(id) {
      this.$confirm('此操作将永久删除该数据库, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log('id====>', id)
        this.$http.delete(this.model + '/' + id + '/db_delete/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error(result.data.meta.msg)
          this.$message.success('删除数据库成功')
          this.getDatabaseList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleSelectionChange(value) {
      this.deleteList = []
      value.forEach(element => {
        this.deleteList.push(element.data_base_id)
      })
    }
  }
}
</script>
