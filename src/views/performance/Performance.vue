<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>性能测试</el-breadcrumb-item>
      <el-breadcrumb-item>性能配置</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="5">
          <el-input placeholder="请输入配置ID"
                    v-model="queryInfo.search_locust_task_id"
                    clearable>
          </el-input>
          </el-col>
          <el-col :span="5">
          <el-input placeholder="请输入配置名称"
                    v-model="queryInfo.search_locust_task_name"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryPerformance">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">
          <el-button class="add" type="primary" @click="showDialog(true, 0, 'add')">添加配置</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="performanceList" border>
          <el-table-column label="ID" prop="locust_task_id" min-width="50px"></el-table-column>
          <el-table-column label="配置名称" prop="locust_task_name" min-width="150px" show-overflow-tooltip></el-table-column>
          <el-table-column label="用户数" prop="number_of_user" min-width="100px"></el-table-column>
          <el-table-column label="每秒启动用户" prop="user_spawned_second" min-width="100px"></el-table-column>
          <el-table-column label="压测时间" prop="run_time" min-width="100px"></el-table-column>
          <el-table-column label="任务状态" min-width="100px">
            <template v-slot="scopeProps">
              <el-tag v-if="scopeProps.row.locust_task_status==='B'" type="success">未运行</el-tag>
              <el-tag v-else-if="scopeProps.row.locust_task_status==='R'" type="warning">运行中</el-tag>
              <el-tag v-else-if="scopeProps.row.locust_task_status==='S'">已完成</el-tag>
              <el-tag v-else-if="scopeProps.row.locust_task_status==='F'" type="danger">运行失败</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="remark" min-width="120px" show-overflow-tooltip></el-table-column>
          <el-table-column label="创建人" prop="creater" min-width="100px"></el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="150px"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="260px">
            <template v-slot="slotProps">
            <el-tooltip class="item" effect="dark" content="查看" placement="top">
              <el-button type="warning" icon="el-icon-view" size="mini" @click="showDialog(false,slotProps.row.locust_task_id, 'check')"></el-button>
            </el-tooltip>
            <el-button type="primary" v-if="slotProps.row.creater==user_name" icon="el-icon-edit" size="mini" @click="showDialog(false,slotProps.row.locust_task_id, 'update')"></el-button>
            <el-button type="danger" v-if="slotProps.row.creater==user_name" icon="el-icon-delete" size="mini" @click="removePerformanceById(slotProps.row.locust_task_id)"></el-button>
            <el-tooltip class="item" effect="dark" content="性能测试报告" placement="top" :enterable="false">
              <el-button type="info" icon="el-icon-document-checked" size="mini" @click="showReport(slotProps.row.locust_task_id)"></el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="运行性能测试" placement="top" :enterable="false">
                <el-button type="success" icon="el-icon-refresh" size="mini" @click="showEnviromentDialog(slotProps.row.locust_task_id)"></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 新增编辑对话框 -->
    <el-dialog :visible.sync="dialogVisible"
                :title="title"
                width="55%"
                @close="DialogClosed"
                :close-on-click-modal="false">
      <el-form :model="performanceForm"
                label-width="100px"
                :rules="performanceFormRules"
                ref="performanceFormRef">
        <el-row>
          <el-col :span="24">
            <el-form-item label="配置名称" prop="locust_task_name">
              <el-input v-model="performanceForm.locust_task_name">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
         <!-- 案例组成 -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="案例组成">
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-button type="primary"
                         @click="addCase">添加组成</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 案例组成表格 -->
        <el-row>
          <el-form-item>
            <el-table :data="caseInfoList">
              <el-table-column label="接口案例" prop="case">
                <template v-slot="scopeProps">
                  <el-select v-model="scopeProps.row.case"
                              filterable>
                    <el-option v-for="item in caseList"
                               :key="item.id"
                               :label="item.label"
                               :value="item.id"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="案例权重" prop="rate">
                <template v-slot="scopeProps">
                  <el-input v-model="scopeProps.row.rate"></el-input>
                </template>
              </el-table-column>
              <el-table-column min-width="50px">
                <template v-slot="slotProps">
                  <el-button type="danger"
                             size="mini"
                             @click="removeCurrentCase(slotProps.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="模拟用户数" prop="number_of_user">
              <el-input v-model="performanceForm.number_of_user">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="启动用户" prop="user_spawned_second">
              <el-input v-model="performanceForm.user_spawned_second">
                <template slot="append">人/秒</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="压测时间" prop="run_time">
              <el-input v-model="performanceForm.run_time">
                <template slot="append">秒</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="performanceForm.remark"></el-input>
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="type!='check'">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitPerformance">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 环境选择对话框 -->
    <el-dialog :visible.sync="enviromentDialogVisible"
                width="30%"
                @close="enviromentDialogClosed"
                :close-on-click-modal="false">
      <el-form label-width="130px"
               :model="enviromentForm"
               ref="enviromentFormRef"
               :rules="enviromentFormRules">
        <el-form-item label="请选择运行环境" prop="env_type">
          <el-select v-model="enviromentForm.env_type">
            <el-option label="测试环境" value="1"></el-option>
            <el-option label="预发环境" value="2"></el-option>
            <el-option label="生产环境" value="3"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="enviromentDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="runPerformance">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'performance',
      user_name: window.localStorage.getItem('user_name'),
      queryInfo: {
        search_locust_task_id: null,
        search_locust_task_name: '',
        creater: window.localStorage.getItem('user_name'),
        pagenum: 1,
        pagesize: 5
      },
      performanceList: [],
      total: 0,
      dialogVisible: false,
      title: '',
      performanceForm: {
        locust_task_type: '1'
      },
      caseInfoList: [
        {
          case: null,
          rate: null
        }
      ],
      caseList: [],
      performanceFormRules: {
        device_name: [
          { required: true, message: '请输入配置名称', trigger: 'blur' }
        ],
        platform_version: [
          { required: true, message: '请选择安卓版本', trigger: 'blur' }
        ],
        connect_way: [
          { required: true, message: '请输入配置地址', trigger: 'blur' }
        ],
        device_udid: [
          { required: true, message: '请输入配置编码', trigger: 'blur' }
        ]
      },
      isAdd: true,
      enviromentForm: {
        env_type: '',
        locust_task_id: ''
      },
      enviromentDialogVisible: false,
      enviromentFormRules: {
        env_type: [
          { required: true, message: '请选择运行环境', trigger: 'blur' }
        ]
      },
      type: ''
    }
  },
  created() {
    this.getPerformanceList()
    this.getCaseList()
  },
  methods: {
    queryPerformance() {
      this.queryInfo.pagenum = 1
      this.getPerformanceList()
    },
    async getPerformanceList() {
      const { data: res } = await this.$http.get(this.model + '/task_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取配置列表失败')
      this.total = res.data.total
      this.performanceList = res.data.performances
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getPerformanceList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getPerformanceList()
    },
    DialogClosed() {
      this.$refs.performanceFormRef.resetFields()
      this.caseInfoList = []
    },
    async showDialog(isAdd, id, type) {
      this.type = type
      if (this.type === 'add') {
        this.title = '新增配置'
      } else if (this.type === 'update') {
        this.title = '编辑配置'
      } else if (this.type === 'check') {
        this.title = '查看配置'
      }
      this.isAdd = isAdd
      if (!isAdd) {
        const { data: res } = await this.$http.get('user_info/get_base_info/', { params: { type: 'Performance', id: id } })
        if (res.meta.status !== 200) return this.$message.error('获取性能编辑信息失败')
        this.performanceForm = res.data
        this.caseInfoList = res.data.formation
      }
      this.dialogVisible = true
    },
    submitPerformance() {
      this.$refs.performanceFormRef.validate(async valid => {
        if (!valid) return false
        var caseStr = ''
        this.caseInfoList.forEach(item => {
          caseStr = caseStr + item.case + ',' + item.rate + '|'
        })
        caseStr = caseStr.substr(0, caseStr.length - 1)
        this.performanceForm.case_id = caseStr
        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/task_add/', this.performanceForm)
          if (res.meta.status !== 200) return this.$message.error('新增配置失败')
          this.$message.success('新增配置成功')
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.performanceForm.locust_task_id + '/task_update/', this.performanceForm)
          if (res.meta.status !== 200) return this.$message.error('编辑配置失败')
          this.$message.success('编辑配置成功')
        }
        this.getPerformanceList()
        this.dialogVisible = false
      })
    },
    removePerformanceById(id) {
      this.$confirm('此操作将永久删除该配置, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete(this.model + '/' + id + '/task_delete/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error('删除配置失败')
          this.$message.success('删除配置成功')
          this.getPerformanceList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    addCase() {
      this.caseInfoList.push(
        {
          case: null,
          rate: null
        }
      )
    },
    async getCaseList() {
      const { data: res } = await this.$http.get('user_info/info/', { params: { type: 'cases' } })
      if (res.meta.status !== 200) return this.$message.error('获取案例信息失败')
      this.caseList = res.data
    },
    removeCurrentCase(index) {
      this.caseInfoList.splice(index, 1)
    },
    async showReport(id) {
      window.open('http://127.0.0.1:8000/api/performance/task_report/?id=' + id)
    },
    showEnviromentDialog(id) {
      this.enviromentForm.locust_task_id = id
      this.enviromentDialogVisible = true
    },
    enviromentDialogClosed() {
      this.enviromentForm = {
        env_type: '',
        locust_task_id: ''
      }
    },
    async runPerformance() {
      const { data: res } = await this.$http.post(this.model + '/' + this.enviromentForm.locust_task_id + '/task_run/', this.enviromentForm)
      if (res.meta.status !== 200) return this.$message.error('运行性能测试失败')
      this.$message.success(res.meta.msg)
      this.enviromentDialogVisible = false
    }
  }
}
</script>
