<template>
  <div>
    <!-- 面包屑导航区域 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>权限管理</el-breadcrumb-item>
      <el-breadcrumb-item>角色列表</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 卡片视图 -->
    <el-card>
      <el-row :gutter="20">
          <el-col :span="7">
          <el-input placeholder="请输入角色名称"
                    v-model="queryInfo.roleName"
                    clearable>
            <el-button slot="append" icon="el-icon-search" @click="queryRoles"></el-button>
          </el-input>
        </el-col>

        <el-col :span="6">
          <el-button type="primary" @click="addRoleDialogVisible=true">添加角色</el-button>
        </el-col>
      </el-row>

      <!-- 角色列表区域 -->
      <el-table :data="rolelist" border stripe>
        <!-- 展开列 -->
        <el-table-column type="expand">
          <template slot-scope="scope">
            <el-row :class="['bdbottom', i1 === 0 ? 'bdtop' : '', 'vcenter']" v-for="(item1, i1) in scope.row.children" :key="item1.id">
              <!-- 渲染一级权限 -->
              <el-col :span="5">
                <!-- <el-tag closable @close="removeRightById(scope.row, item1.id)">{{item1.authName}}</el-tag> -->
                <el-tag>{{item1.authName}}</el-tag>
                <i class="el-icon-caret-right"></i>
              </el-col>
              <!-- 渲染二级和三级权限 -->
              <el-col :span="19">
                <!-- 通过 for 循环 嵌套渲染二级权限 -->
                <el-row :class="[i2 === 0 ? '' : 'bdtop', 'vcenter']" v-for="(item2, i2) in item1.children" :key="item2.id">
                  <el-col :span="6">
                    <!-- <el-tag type="success" closable @close="removeRightById(scope.row, item2.id)">{{item2.authName}}</el-tag> -->
                    <el-tag type="success">{{item2.authName}}</el-tag>
                    <i class="el-icon-caret-right"></i>
                  </el-col>
                  <el-col :span="18">
                    <!-- <el-tag type="warning" v-for="item3 in item2.children" :key="item3.id" closable @close="removeRightById(scope.row, item3.id)">{{item3.authName}}</el-tag> -->
                    <el-tag type="warning" v-for="item3 in item2.children" :key="item3.id">{{item3.authName}}</el-tag>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </template>
        </el-table-column>
        <!-- 索引列 -->
        <el-table-column prop="id" min-width="50px" label="ID"></el-table-column>
        <el-table-column label="角色名称" prop="roleName" min-width="150px"></el-table-column>
        <el-table-column label="角色描述" prop="roleDesc" min-width="250px"></el-table-column>
        <el-table-column label="操作" min-width="200px">
          <template slot-scope="scopeProps" v-if="scopeProps.row.id!==0">
            <el-button size="mini" type="primary" icon="el-icon-edit" @click="editRoleDialogShow(scopeProps.row)">编辑</el-button>
            <el-button size="mini" type="danger" icon="el-icon-delete" @click="deleteRole(scopeProps.row.id)">删除</el-button>
            <el-button size="mini" type="warning" icon="el-icon-setting" @click="showSetRightDialog(scopeProps.row)">分配权限</el-button>
          </template>
        </el-table-column>
      </el-table>

       <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :page-sizes="[5, 10, 20, 30, 40]"
          :current-page.sync="queryInfo.pagesize"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>

    <!-- 分配权限的对话框 -->
    <el-dialog title="分配权限"
               :visible.sync="setRightDialogVisible"
               width="50%"
               @close="setRightDialogClosed"
               :close-on-click-modal="false">
      <!-- 树形控件 -->
      <el-tree :data="rightslist" :props="treeProps" show-checkbox node-key="id" default-expand-all :default-checked-keys="defKeys" ref="treeRef"></el-tree>
      <span slot="footer" class="dialog-footer">
        <el-button @click="setRightDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="allotRights">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 编辑角色 -->
    <el-dialog title="编辑角色"
               :visible.sync="editRoleDialogVisible"
               width="50%"
               @close="editRoleDialogClosed"
               :close-on-click-modal="false">
      <el-form :model="editRoleForm"
               label-width="80px"
               ref="editRoleRef"
               :rules="editFormRules">
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="editRoleForm.roleName"></el-input>
        </el-form-item>
        <el-form-item label="角色描述">
          <el-input v-model="editRoleForm.roleDesc"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editRoleDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="eidtRole">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 新增角色 -->
    <el-dialog title="新增角色"
               :visible.sync="addRoleDialogVisible"
               width="50%"
               @close="addRoleDialogClosed"
               :close-on-click-modal="false">
      <el-form :model="addRoleForm"
               label-width="80px"
               ref="addFormRef"
               :rules="addFormRules">
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="addRoleForm.roleName"></el-input>
        </el-form-item>
        <el-form-item label="角色描述">
          <el-input v-model="addRoleForm.roleDesc"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addRoleDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="addRole">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'role_info',
      // 所有角色列表数据
      rolelist: [],
      queryInfo: {
        roleName: '',
        pagenum: 1,
        pagesize: 5
      },
      // 控制分配权限对话框的显示与隐藏
      setRightDialogVisible: false,
      // 所有权限的数据
      rightslist: [],
      // 树形控件的属性绑定对象
      treeProps: {
        label: 'authName',
        children: 'children'
      },
      // 默认选中的节点Id值数组
      defKeys: [],
      // 当前即将分配权限的角色id
      roleId: '',
      total: 0,
      editRoleDialogVisible: false,
      editRoleForm: {},
      addRoleDialogVisible: false,
      addRoleForm: {},
      addFormRules: {
        roleName: [
          { required: true, message: '请输入角色名称', trigger: 'blur' }
        ]
      },
      eidtFormRules: {
        roleName: [
          { required: true, message: '请输入角色名称', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getRolesList()
  },
  methods: {
    queryRoles() {
      this.queryInfo.pagenum = 1
      this.getRolesList()
    },
    // 获取所有角色的列表
    async getRolesList() {
      const { data: res } = await this.$http.get(this.model + '/role_list/', {
        params: this.queryInfo
      })

      if (res.meta.status !== 200) {
        return this.$message.error('获取角色列表失败！')
      }

      this.rolelist = res.data.roles
      this.total = res.data.total

      console.log(this.rolelist)
    },
    // 根据Id删除对应的权限
    async removeRightById(role, rightId) {
      // 弹框提示用户是否要删除
      const confirmResult = await this.$confirm(
        '此操作将永久删除该文件, 是否继续?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).catch(err => err)

      if (confirmResult !== 'confirm') {
        return this.$message.info('取消了删除！')
      }

      const { data: res } = await this.$http.delete(
        this.model + `roles/${role.id}/rights/${rightId}`
      )

      if (res.meta.status !== 200) {
        return this.$message.error('删除权限失败！')
      }

      // this.getRolesList()
      role.children = res.data
    },
    // 展示分配权限的对话框
    async showSetRightDialog(role) {
      this.roleId = role.id
      // 获取所有权限的数据
      const { data: res } = await this.$http.get('/user_info/menu')

      if (res.meta.status !== 200) {
        return this.$message.error('获取权限数据失败！')
      }

      // 把获取到的权限数据保存到 data 中
      this.rightslist = res.data
      console.log(this.rightslist)

      // 递归获取三级节点的Id
      this.getLeafKeys(role, this.defKeys)

      this.setRightDialogVisible = true
    },
    // 通过递归的形式，获取角色下所有三级权限的id，并保存到 defKeys 数组中
    getLeafKeys(node, arr) {
      // 如果当前 node 节点不包含 children 属性，则是三级节点
      if (!node.children || node.children.length === 0) {
        return arr.push(node.id)
      }

      node.children.forEach(item => this.getLeafKeys(item, arr))
    },
    // 监听分配权限对话框的关闭事件
    setRightDialogClosed() {
      this.defKeys = []
    },
    // 点击为角色分配权限
    async allotRights() {
      const keys = [
        ...this.$refs.treeRef.getCheckedKeys(),
        ...this.$refs.treeRef.getHalfCheckedKeys()
      ]
      const idStr = keys.join(',')

      const { data: res } = await this.$http.post(
        this.model + `/${this.roleId}/role_rights/`,
        { id: this.roleId, rids: idStr }
      )

      if (res.meta.status !== 200) {
        return this.$message.error('分配权限失败！')
      }

      this.$message.success('分配权限成功！')
      this.getRolesList()
      this.setRightDialogVisible = false
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getRolesList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getRolesList()
    },
    editRoleDialogShow(role) {
      const objString = JSON.stringify(role)
      this.editRoleForm = JSON.parse(objString)
      this.editRoleDialogVisible = true
    },
    eidtRole() {
      this.$refs.eidtFormRef.validate(async valid => {
        if (!valid) return false
        const { data: res } = await this.$http.put(this.model + '/' + this.editRoleForm.id + '/role_update/', this.editRoleForm)
        if (res.meta.status !== 200) return this.$message.error('编辑角色失败！')
        this.$message.success('编辑角色成功！')
        this.editRoleDialogVisible = false
        this.getRolesList()
      })
    },
    deleteRole(id) {
      this.$confirm('此操作将永久删除该角色, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete(this.model + '/' + id + '/delete_role/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error('删除角色失败')
          this.$message.success('删除角色成功')
          this.getRolesList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    addRole() {
      this.$refs.addFormRef.validate(async valid => {
        if (!valid) return false
        const { data: res } = await this.$http.post(this.model + '/role_add/', this.addRoleForm)
        if (res.meta.status !== 200) return this.$message.error('新增角色失败！')
        this.$message.success('新增角色成功！')
        this.addRoleDialogVisible = false
        this.getRolesList()
      })
    },
    editRoleDialogClosed() {
      this.$refs.editRoleRef.resetFields()
    },
    addRoleDialogClosed() {
      this.addRoleForm = {}
      this.$refs.addFormRef.resetFields()
    }
  }
}
</script>

<style lang="less" scoped>
.el-tag {
  margin: 7px;
}

.bdtop {
  border-top: 1px solid #eee;
}

.bdbottom {
  border-bottom: 1px solid #eee;
}

.vcenter {
  display: flex;
  align-items: center;
}
</style>
