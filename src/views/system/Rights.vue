<template>
  <div>
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>权限管理</el-breadcrumb-item>
      <el-breadcrumb-item>权限列表</el-breadcrumb-item>
    </el-breadcrumb>
    <!-- 卡片 -->
    <el-card>
      <el-table :data="rightList" border>
        <el-table-column label="序号" type="index"></el-table-column>
        <el-table-column label="名称" prop="authName"></el-table-column>
        <el-table-column label="路径" prop="path"></el-table-column>
        <el-table-column label="等级">
          <template v-slot="slotProps">
            <el-tag v-if="slotProps.row.level === '1'">一级</el-tag>
            <el-tag type="success" v-else-if="slotProps.row.level === '2'">二级</el-tag>
            <el-tag type="warning" v-else-if="slotProps.row.level === '3'">三级</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :page-sizes="[5, 10, 20, 30, 40]"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'right_info',
      rightList: [],
      queryInfo: {
        type: 'list',
        pagenum: 1,
        pagesize: 5
      },
      total: 0
    }
  },
  created() {
    this.getRightList()
  },
  methods: {
    async getRightList() {
      const { data: res } = await this.$http.get(`${this.model}/right_list/`, { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取权限列表失败')
      this.rightList = res.data.rights
      this.total = res.data.total
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getRightList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getRightList()
    }
  }
}
</script>
