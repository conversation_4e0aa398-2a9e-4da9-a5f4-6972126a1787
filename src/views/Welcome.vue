<template>
  <div>
    <el-row>
      <div class="title">数据看板</div>
    </el-row>
    <el-row>
      <div class="time">最新数据更新时间：{{ update_time }}</div>
    </el-row>
    <div class="data-margin">
      <el-row :gutter="80">
        <el-col :span="6">
          <el-card class="bottom-card">
            <div class="text item"><span class="maru"></span>测试环境用例数</div>
            <div class="text item"><div class="center-data">{{ data.test_core_scene }}</div></div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="bottom-card">
            <div class="text item"><span class="maru"></span>生产环境用例数</div>
            <div class="text item"><div class="center-data">{{ data.pro_core_scene }}</div></div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="bottom-card">
            <div class="text item"><span class="maru"></span>WEB自动化用例数</div>
            <div class="text item"><div class="center-data">118</div></div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="bottom-card">
            <div class="text item"><span class="maru"></span>新零售接口覆盖数</div>
            <div class="text item"><div class="center-data">{{ data.interface_coverage_num }}</div></div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="80">
        <el-col :span="6">
          <el-card class="top-card">
            <div class="text item"><span class="maru"></span>场景数</div>
            <div class="text item"><div class="center-data">{{ data.scene_count }}</div></div>
            <div class="text item">今日新增：{{ data.today_scene }}</div>
<!--            <div class="text item">昨日数据：{{ data.yesterday_scene }}</div>-->
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="top-card">
            <div class="text item"><span class="maru"></span>接口数</div>
            <div class="text item"><div class="center-data">{{ data.interface_count }}</div></div>
            <div class="text item">今日新增：{{ data.today_interface }}</div>
<!--            <div class="text item">昨日数据：{{ data.yesterday_interface }}</div>-->
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="top-card">
            <div class="text item"><span class="maru"></span>案例数</div>
            <div class="text item"><div class="center-data">{{ data.case_count }}</div></div>
            <div class="text item">今日新增：{{ data.today_case }}</div>
<!--            <div class="text item">昨日数据：{{ data.yesterday_case }}</div>-->
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="top-card">
            <div class="text item"><span class="maru"></span>任务数</div>
            <div class="text item"><div class="center-data">{{ data.api_task_count }}</div></div>
            <div class="text item">今日新增：{{ data.today_api_task }}</div>
<!--            <div class="text item">昨日数据：{{ data.yesterday_api_task }}</div>-->
          </el-card>
        </el-col>
      </el-row>
<!--      <el-row :gutter="80">-->
<!--        <el-col :span="6">-->
<!--          <el-card class="bottom-card">-->
<!--            <div class="text item"><span class="maru"></span>UI块</div>-->
<!--            <div class="text item"><div class="center-data">{{ data.chunk_count }}</div></div>-->
<!--            <div class="text item">昨日数据：{{ data.yesterday_chunk }}</div>-->
<!--          </el-card>-->
<!--        </el-col>-->
<!--        <el-col :span="6">-->
<!--          <el-card class="bottom-card">-->
<!--            <div class="text item"><span class="maru"></span>元素数</div>-->
<!--            <div class="text item"><div class="center-data">{{ data.element_count }}</div></div>-->
<!--            <div class="text item">昨日数据：{{ data.yesterday_element }}</div>-->
<!--          </el-card>-->
<!--        </el-col>-->
<!--        <el-col :span="6">-->
<!--          <el-card class="bottom-card">-->
<!--            <div class="text item"><span class="maru"></span>UI案例数</div>-->
<!--            <div class="text item"><div class="center-data">{{ data.webcase_count }}</div></div>-->
<!--            <div class="text item">昨日数据：{{ data.yesterday_webcase }}</div>-->
<!--          </el-card>-->
<!--        </el-col>-->
<!--        <el-col :span="6">-->
<!--          <el-card class="bottom-card">-->
<!--            <div class="text item"><span class="maru"></span>UI任务数</div>-->
<!--            <div class="text item"><div class="center-data">{{ data.ui_task_count }}</div></div>-->
<!--            <div class="text item">昨日数据：{{ data.yesterday_ui_task }}</div>-->
<!--          </el-card>-->
<!--        </el-col>-->
<!--      </el-row>-->
      <el-row>
        <el-col :span="12">
          <div id="yearSceneCharts" style="width:110%;height:400px;margin-top:30px"></div>
        </el-col>
        <el-col :span="12">
          <div id="quarterSceneCharts" style="width:110%;height:400px;margin-top:30px"></div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <div id="testDailyMonitoringStatisticsCharts" style="width:110%;height:400px;margin-top:30px"></div>
        </el-col>
        <el-col :span="12">
          <div id="proDailyMonitoringStatisticsCharts" style="width:110%;height:400px;margin-top:30px"></div>
        </el-col>
      </el-row>
      <el-row>
        <div class="title">接口访问流量TOP20</div>
      </el-row>
      <el-row>
        <div class="time">统计时间范围：{{ this.barQueryInfo.start_date }}至{{ this.barQueryInfo.end_date }}</div>
      </el-row>
      <el-row>
        <el-col>
          <div id="trafficCharts" style="width:100%;height:400px;margin-top:30px"></div>
        </el-col>
      </el-row>
<!--      <div style="background:#f9f9f9;padding:10px 20px;margin-top:10px"><div id="charts" style="width:100%;height:400px;margin-top:30px;"></div></div>-->
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'

export default {
  data() {
    return {
      data: {},
      update_time: '',
      barQueryInfo: {
        start_date: '',
        end_date: '',
        interface_id: '',
        interface_name: '',
        interface_address: '',
        service: '',
        top_num: '20',
        pagenum: 1,
        pagesize: 20
      },
      monitoringQuery: { // 查询栏
        environment: '',
        start_date: '',
        end_date: '',
        is_pending: '',
        pagenum: 1,
        pagesize: 40
      }
    }
  },
  created() {
    let starttime = new Date()
    let endtime = new Date()
    starttime.setMonth(starttime.getMonth() - 1)
    endtime.setDate(endtime.getDate() - 1)
    starttime = new Date(starttime).toLocaleDateString()
    endtime = new Date(endtime).toLocaleDateString()
    this.monitoringQuery.start_date = this.barQueryInfo.start_date = starttime.replace(/\//g, '-')
    this.monitoringQuery.end_date = this.barQueryInfo.end_date = endtime.replace(/\//g, '-')
    this.getQuarterSceneData()
    this.getBarData()
    this.getYearSceneData()
    this.getIndex()
    this.getDailyMonitoringStatisticsCharts('1')
    this.getDailyMonitoringStatisticsCharts('3')
  },
  methods: {
    async getIndex() {
      const { data: res } = await this.$http.get('/user_info/index')
      if (res.meta.status !== 200) return this.$message.error('获取首页数据失败')
      this.data = res.data
      var time = new Date()
      this.update_time = time.toLocaleString()
      // this.drawLine()
    },
    // drawLine() {
    //   var myChart = this.$echarts.init(document.getElementById('charts'))
    //   var option = {
    //     title: {
    //       text: '最近10次发版成功率',
    //       x: 'center'
    //     },
    //     tooltip: {},
    //     legend: {
    //       data: ['测试环境', '预发环境'],
    //       x: 'right'
    //     },
    //     xAxis: {
    //       data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']
    //     },
    //     yAxis: {
    //       name: '成功率（%）'
    //     },
    //     series: [
    //       {
    //         name: '测试环境',
    //         type: 'line',
    //         data: this.data.test_rate
    //       },
    //       {
    //         name: '预发环境',
    //         type: 'line',
    //         data: this.data.uat_rate
    //       }
    //     ]
    //   }
    //
    //   // 使用刚指定的配置项和数据显示图表。
    //   myChart.setOption(option)
    // },
    async getQuarterSceneData() {
      var { data: res } = await this.$http.get('/user_info/user_data', { params: { type: 'scene', time: 'quarter' } })
      if (res.meta.status !== 200) return this.$message.error('获取季度场景排行数据失败')
      var sceneChart = this.$echarts.init(document.getElementById('quarterSceneCharts'))
      var option = {
        title: {
          text: '本季度场景排行榜'
        },
        tooltip: {},
        legend: {
          data: ['场景数']
        },
        xAxis: {
          data: res.data.user_data
        },
        yAxis: {},
        series: [{
          name: '场景数',
          type: 'bar',
          data: res.data.count_data
        }],
        color: ['#c23531']
      }
      sceneChart.setOption(option)
    },
    async getYearSceneData() {
      var { data: res } = await this.$http.get('/user_info/user_data', { params: { type: 'scene', time: 'year' } })
      if (res.meta.status !== 200) return this.$message.error('获取年度场景数据失败')
      var caseChart = this.$echarts.init(document.getElementById('yearSceneCharts'))
      var option = {
        title: {
          text: '本年度场景排行榜'
        },
        tooltip: {},
        legend: {
          data: ['场景数']
        },
        xAxis: {
          data: res.data.user_data
        },
        yAxis: {},
        series: [{
          name: '场景数',
          type: 'bar',
          data: res.data.count_data
        }]
      }
      caseChart.setOption(option)
    },
    async getBarData() {
      const { data: res } = await this.$http.post('/interface_info/interface_traffic_summary/', this.barQueryInfo)
      if (res.meta.status !== 200) return this.$message.error('获取流量排行数据失败')
      this.total = res.data.total
      const trafficChart = this.$echarts.init(document.getElementById('trafficCharts'))
      const xData = res.data.data.map(function(item) {
        return item.interface_name
      })
      const yData = res.data.data.map(function(item) {
        return item.total_access_count
      })
      const seriesData = res.data.data.map(item => ({
        value: item.total_access_count,
        interface_address: item.interface_address,
        interface_way: item.interface_way,
        service: item.service,
        total_access_count: item.total_access_count
      }))
      const option = {
        tooltip: {
          show: true,
          trigger: 'item',
          enterable: true,
          formatter: function (params) {
            const data = params.data
            return `
              <span class='dot'></span>接口名称: ${params.name}<br>
              <span class='dot'></span>接口地址: ${data.interface_address}<br>
              <span class='dot'></span>请求方式: ${data.interface_way}<br>
              <span class='dot'></span>服务: ${data.service}<br>
              <span class='dot'></span>访问量: ${data.total_access_count}
            `
          },
          backgroundColor: 'rgba(0,0,0,0.75)',
          borderColor: 'rgba(0,0,0,0.75)',
          textStyle: {
            color: '#fff',
            fontsize: '14',
            width: 10,
            height: 10,
            overflow: 'break'
          }
        },
        xAxis: {
          data: xData,
          // x轴文字配置
          axisLabel: {
            interval: 0,
            width: 50,
            overflow: 'truncate'
          }
        },
        yAxis: {},
        series: [{
          name: '接口详情',
          type: 'bar',
          data: seriesData
        }],
        grid: {
          top: '1%',
          bottom: '18%',
          left: '10%',
          right: '1%'
        },
        dataZoom: [{
          // 启用水平缩放
          type: 'slider',
          start: 0,
          end: 100
        }]
      }
      trafficChart.setOption(option)
      // 监听浏览器窗口大小变化,当窗口大小改变时,调用trafficChart.resize()方法来调整图标大小
      window.addEventListener('resize', () => {
        trafficChart.resize() // myChart是上面定义的
      })
    },
    async getDailyMonitoringStatisticsCharts(environment) {
      this.monitoringQuery.environment = environment
      let testDailyMonitoringStatisticsCharts
      let proDailyMonitoringStatisticsCharts
      const { data: res } = await this.$http.post('/task_info/get_daily_monitoring_list/', this.monitoringQuery)
      if (environment === '1') {
        testDailyMonitoringStatisticsCharts = this.$echarts.init(document.getElementById('testDailyMonitoringStatisticsCharts'))
      }
      if (environment === '3') {
        proDailyMonitoringStatisticsCharts = this.$echarts.init(document.getElementById('proDailyMonitoringStatisticsCharts'))
      }
      // 构建完成的日期序列
      const startDate = dayjs(this.monitoringQuery.start_date)
      const endDate = dayjs(this.monitoringQuery.end_date)
      const dateRange = []
      for (let date = startDate; date.isBefore(endDate) || date.isSame(endDate); date = date.add(1, 'day')) {
        dateRange.push(date.format('YYYY-MM-DD'))
      }
      // 创建一个空对象用于存储日期对应的数据
      const dateMap = {}
      // 将数据填充到日期映射中
      res.data.data.forEach(item => {
        // 以日期为键，存储多个字段
        dateMap[item.date] = {
          value: item.test_error + item.test_fail, // 报错用例总和
          total: item.test_all, // 用例总数
          fail: item.test_fail, // 失败数
          error: item.test_error, // 错误数
          pass: item.test_pass // 通过数
        }
      })
      // 填充图表数据，如果日期缺失则填充 null
      // 填充图表数据，确保保留完整对象
      const seriesData = dateRange.map(date => {
        if (dateMap[date] !== undefined) {
          return {
            value: dateMap[date].value, // Y 轴显示 total_fail
            total: dateMap[date].total,
            fail: dateMap[date].fail,
            error: dateMap[date].error,
            pass: dateMap[date].pass
          }
        } else {
          return null
        }
      })
      const option = {
        title: {
          text: environment === '1' ? '测试环境近一月监控失败用例数' : environment === '3' ? '生产环境近一月监控失败用例数' : '近一月监控失败用例数' // 默认值
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          enterable: true,
          formatter: function(params) {
            const data = params[0].data // 获取当前数据点的数据
            if (!data) return params[0].name + '<br/>无数据' // 如果数据为 null
            return `${params[0].name}<br/>
<!--              <span class='dot'></span>用例总数: ${data.total}<br>-->
<!--              <span class='dot'></span>通过数: ${data.pass}<br>-->
              <span class='dot'></span>失败数: ${data.fail}<br>
              <span class='dot'></span>错误数: ${data.error}
            `
          }
        },
        xAxis: {
          name: '日期',
          type: 'category',
          nameLocation: 'middle', // 标题位置
          nameTextStyle: {
            padding: 30, // 与轴线的距离
            fontWeight: 'bold',
            fontSize: 14
          },
          data: dateRange
        },
        yAxis: {
          name: '失败用例数',
          nameLocation: 'middle',
          nameTextStyle: {
            padding: 50, // 与轴线的距离
            fontWeight: 'bold',
            fontSize: 14
          },
          type: 'value'
        },
        series: [{
          name: '失败用例数',
          type: 'line',
          data: seriesData,
          connectNulls: false,
          smooth: true, // 使折线平滑
          lineStyle: {
            color: '#5470C6' // 线条颜色
          },
          itemStyle: {
            color: '#5470C6' // 数据点颜色
          },
          areaStyle: {
            color: 'rgba(84, 112, 198, 0.3)' // 区域填充色
          }
        }]
      }
      if (environment === '1') {
        // 设置配置项并渲染图表
        testDailyMonitoringStatisticsCharts.setOption(option)
        // 窗口大小变化时，调整图表大小
        window.addEventListener('resize', () => {
          testDailyMonitoringStatisticsCharts.resize()
        })
      }
      if (environment === '3') {
        // 设置配置项并渲染图表
        proDailyMonitoringStatisticsCharts.setOption(option)
        // 窗口大小变化时，调整图表大小
        window.addEventListener('resize', () => {
          proDailyMonitoringStatisticsCharts.resize()
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.title {
  width: 100%;
  height:20px;
  line-height:20px;
  font-size: 20px;
  margin-top: 20px;
  margin-bottom: 10px;
  text-align: center;
  font-weight: 700;
}
.time{
    width: 100%;
    height: 14px;
    line-height: 14px;
    font-size: 14px;
    text-align: center;
    color: rgb(74, 74, 74);
    margin-top: 10px;
}
.data-margin {
  width: 90%;
  margin: auto;
  margin-top: 20px;
}
.el-card {
  color: #fff;
  border-radius: 20px;
  margin-bottom: 25px;
}
.top-card {
  background: #4F88FF;
}
.top-card .center-data {
   background: #2663E2;
}
.center-data {
  width: 80%;
  margin: 10px auto 10px auto;
  font-size: 20px;
  height: 40px;
  line-height: 40px;
  border-radius: 20px;
  font-family: PingFangTC-Semibold,PingFangTC;
  font-weight: 600;
}
.maru {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  background: #FFFFFF;
  margin-right: 10px;
}
.item {
  text-align: center;
}
.bottom-card {
  background: #F6B03E;
}
.bottom-card .center-data {
  background: #E0590B;
}
/deep/ .dot {
  width: 10px; height: 10px; background: #a1cc95; display: inline-block; border-radius: 100px; margin: 0 6px 0 0;
}
</style>
