<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>售后问题</el-breadcrumb-item>
      <el-breadcrumb-item>问题列表</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="10">
          <el-col :span="4">
          <el-input placeholder="提交人姓名或工号"
                    v-model="queryInfo.send_from"
                    clearable
                    @keyup.enter.native="queryList"
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-input placeholder="所属商户"
                    v-model="queryInfo.belong_mer_code"
                    clearable
                    @keyup.enter.native="queryList"
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-select placeholder="业务归属"
                     v-model="queryInfo.project_type"
                     clearable
                     multiple>
            <el-option v-for="item in productList"
                             :key="item.product_id"
                             :label="item.product_name"
                             :value="item.product_id">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select placeholder="处理状态"
                     v-model="queryInfo.status"
                     clearable
                     multiple>
            <el-option value="0" label="待受理"></el-option>
            <el-option value="1" label="已处理"></el-option>
            <el-option value="2" label="解决中"></el-option>
            <el-option value="3" label="已关闭"></el-option>
            <el-option value="4" label="无需处理"></el-option>
          </el-select>
        </el-col>
        <el-col :span="7">
           <el-date-picker
              v-model="queryDate"
              :clearable="true"
              type="daterange"
              align="right"
              style="width: 500px"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd"
              disabledDate="false">
            </el-date-picker>
          </el-col>
      </el-row>
      <el-row :gutter="10" style="margin-top: 10px;">
        <el-col :span="4">
          <el-input placeholder="处理人姓名或工号"
                    v-model="queryInfo.comp_user"
                    clearable
                    @keyup.enter.native="queryList"
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-input placeholder="问题描述"
                    v-model="queryInfo.msg_content"
                    clearable
                    @keyup.enter.native="queryList"
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-select placeholder="问题类型"
                     v-model="queryInfo.question_type"
                     clearable
                     multiple>
            <el-option value="0" label="咨询类"></el-option>
            <el-option value="1" label="支持类"></el-option>
            <el-option value="2" label="BUG"></el-option>
            <el-option value="3" label="需求类"></el-option>
            <el-option value="4" label="操作类"></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select placeholder="是否AI回答"
                     v-model="queryInfo.is_auto"
                     clearable>
            <el-option value="0" label="否"></el-option>
            <el-option value="1" label="是"></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select placeholder="是否推送消息"
                     v-model="queryInfo.is_send"
                     clearable>
            <el-option value="0" label="否"></el-option>
            <el-option value="1" label="是"></el-option>
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="queryList">查 询</el-button>
          <el-button type="primary" @click="queryReset">重 置</el-button>
          <el-button type="primary" @click="queryExport">导 出</el-button>
          <template v-if="!robotStatusLoading">
            <el-tag v-if="robotStatus === 2" type="success" style="margin-left:10px; cursor: pointer;" @click="getRobotStatus">消息推送正常</el-tag>
            <el-tag v-if="robotStatus === 1" type="danger" style="margin-left:10px; cursor: pointer;" @click="getRobotStatus">消息推送中断</el-tag>
            <el-tag v-if="robotStatus !== 1 && robotStatus !== 2" type="warning" style="margin-left:10px; cursor: pointer;" @click="getRobotStatus">消息推送异常</el-tag>
          </template>
          <template v-if="robotStatusLoading">
            <el-tag style="margin-left:10px; cursor: pointer;" >
              <i class="el-icon-loading"></i>
              更新中
            </el-tag>
          </template>
<!--          <el-button v-if="this.role_id !=='19'" type="primary" @click="updateRota">更新值班表</el-button>-->
<!--          <el-button type="primary" @click="AIConfig">回复配置</el-button>-->
<!--          <el-button type="primary" @click="queryList" style="padding: 12px 11px;">查 询</el-button>-->
<!--          <el-button type="primary" @click="queryReset" style="padding: 12px 11px;">重 置</el-button>-->
<!--          <el-button v-if="user_name==='liudawei'" type="primary" @click="AIConfig" style="padding: 12px 11px;">回复配置</el-button>-->
<!--          <el-switch-->
<!--            style="margin-left: 12px;"-->
<!--            v-model="isAutoRefresh"-->
<!--            active-text="自动刷新"-->
<!--            @change="toggleAutoRefresh"-->
<!--          />-->
        </el-col>
      </el-row>
      <el-row :gutter="10" style="margin-top: 10px;" v-if="role_id === '0'" type="flex">
        <el-col :span="4">
          <el-select placeholder="是否需要更新到知识库"
                     v-model="queryInfo.is_knowledge"
                     clearable>
            <el-option value="0" label="否"></el-option>
            <el-option value="1" label="是"></el-option>
            <el-option value="2" label="已更新"></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select placeholder="是否有训练价值"
                     v-model="queryInfo.is_valuable"
                     clearable>
            <el-option value="0" label="否"></el-option>
            <el-option value="1" label="是"></el-option>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select placeholder="是否复盘"
                     v-model="queryInfo.is_review"
                     clearable>
            <el-option value="0" label="否"></el-option>
            <el-option value="1" label="是"></el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row>
        <el-table
          :data="questionList"
          stripe
          border
          :header-cell-style="{
            backgroundColor: '#e6f7ff',  // 推荐使用Element默认背景色
            color: '#606266',           // 保持默认文字颜色
            fontSize: '12px',           // 适当加大字号
            fontWeight: 'bold'          // 加粗字体
          }">
          <el-table-column label="序号" prop="seq" min-width="60px" fixed="left" align="center"></el-table-column>
          <el-table-column label="问题时间" prop="msg_time" min-width="145px" fixed="left" align="center"></el-table-column>
          <el-table-column min-width="200px" fixed="left">
            <template #header>
              <span>
                问题描述
                <el-tooltip content="点击单元格可查看问题描述详情" placement="top">
                  <i class="el-icon-info" style="margin-left: 5px; color: #909399; cursor: pointer;"></i>
                </el-tooltip>
              </span>
            </template>
            <template #default="scope">
              <el-popover
                placement="top"
                width="800"
                trigger="click"
                popper-class="dark-popover"
              >
                <div v-html="getToolTipContent(scope.row.msg_content)"
                     style="white-space: pre-line; font-size: 12px"></div>
                <div slot="reference"
                     class="cell-content"
                     style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden; min-width: 180px; cursor: pointer;">
                  {{ getTextContent(scope.row.msg_content) }}
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="所属商户" prop="belong_mer_code" min-width="105px" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.isEditingMercode">
                <el-input
                  class="edit-input"
                  v-model="scope.row.belong_mer_code"
                  size="mini"
                  style="width: 60px; padding-left: 0px"
                >
                </el-input>
                <i
                  class="el-icon-check"
                  style="cursor: pointer"
                  @click="saveEdit(scope.row, 'belong_mer_code')"
                ></i>
                <i
                  style="cursor: pointer"
                  class="el-icon-close"
                  @click="cancelEdit(scope.row, 'belong_mer_code')"
                ></i>
              </div>
              <div v-else>
                {{ scope.row.belong_mer_code }}
                <i v-if="role_id !=='19' && scope.row.status !==3 && scope.row.status !==4 && scope.row.ai_answer_status !== 1"
                  style="cursor: pointer"
                  class="el-icon-edit-outline"
                  @click="startEdit(scope.row, 'belong_mer_code')"
                ></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="ERP类型" prop="mer_type" min-width="90px" align="center">
            <template v-slot="slotProps">
              <span v-if="slotProps.row.mer_type==='0'">H1</span>
              <span v-if="slotProps.row.mer_type==='1'">H2</span>
              <span v-if="slotProps.row.mer_type==='2'">非海典ERP</span>
            </template>
          </el-table-column>
          <el-table-column label="处理人" min-width="140px" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.isEditingCompUser">
                <el-tooltip class="item" effect="dark" content="请输入处理人姓名或工号" placement="top">
                  <el-select
                    v-model="scope.row.comp_user"
                    filterable
                    remote
                    :remote-method="handleSearch"
                    :loading="loading"
                    size="mini"
                    style="width: 85px;"
                    class="edit-input"
                  >
                    <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-tooltip>
                <i
                  class="el-icon-check"
                  style="cursor: pointer"
                  @click="saveEdit(scope.row, 'comp_user')"
                ></i>
                <i
                  style="cursor: pointer"
                  class="el-icon-close"
                  @click="cancelEdit(scope.row, 'comp_user')"
                ></i>
              </div>
              <div v-else>
                <div v-if="scope.row.comp_user">
                  {{ scope.row.comp_user_name }} ({{ scope.row.comp_user }})
                  <i v-if="role_id !=='19' && scope.row.status !==3 && scope.row.status !==4 && scope.row.ai_answer_status !== 1"
                  style="cursor: pointer"
                  class="el-icon-edit-outline"
                  @click="startEdit(scope.row, 'comp_user')"
                ></i>
                </div>
                <div v-else>
                  <i v-if="role_id !=='19' && scope.row.status !==3 && scope.row.status !==4 && scope.row.ai_answer_status !== 1"
                    style="cursor: pointer"
                    class="el-icon-edit-outline"
                    @click="startEdit(scope.row, 'comp_user')"
                  ></i>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="图片" min-width="90px" align="center">
            <template slot-scope="scope">
              <template v-if="hasImage(scope.row.msg_content)">
                <el-link @click="previewImages(scope.row.msg_content)" style="font-size: 12px;">查看图片<i class="el-icon-view el-icon--right"></i> </el-link>
              </template>
              <template v-else>
                <span>无</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="问题回复" min-width="150px">
            <template #header>
              <span>
                问题回复
                <el-tooltip content="点击单元格可查看问题回复详情" placement="top">
                  <i class="el-icon-info" style="margin-left: 5px; color: #909399; cursor: pointer;"></i>
                </el-tooltip>
              </span>
            </template>
            <template #default="scope">
              <el-popover
                placement="top"
                width="800"
                trigger="click"
                popper-class="dark-popover"
              >
                <div v-html="formatTooltipContent(scope.row.answers.question_answer_manual)"
                     style="white-space: pre-line; font-size: 12px"></div>
                <div slot="reference"
                     class="cell-content"
                     style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden; min-width: 180px; cursor: pointer;">
                  {{ scope.row.answers.question_answer_manual }}
                </div>
              </el-popover>
            </template>
          </el-table-column>
<!--          <el-table-column label="问题回复" min-width="150px">-->
<!--            <template #default="scope">-->
<!--                &lt;!&ndash; 调用 getTextContent 方法获取并显示文本 &ndash;&gt;-->
<!--              <el-tooltip placement="top">-->
<!--                <div style="white-space: nowrap;text-overflow: ellipsis; width: 180px; overflow: hidden">{{ scope.row.answers.question_answer_manual }}</div>-->
<!--                <div slot="content" v-html="formatTooltipContent(scope.row.answers.question_answer_manual)" style="white-space: pre-line; max-width: 800px;">-->
<!--                </div>-->
<!--              </el-tooltip>-->
<!--            </template>-->
<!--          </el-table-column>-->
          <el-table-column label="处理状态" prop="status" min-width="125px" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.isEditingStatus">
                <el-select
                  v-model.number="scope.row.status"
                  size="mini"
                  style="width: 80px;"
                  class="edit-input"
                >
                  <el-option :value="0" label="待受理"></el-option>
                  <el-option :value="1" label="已处理"></el-option>
                  <el-option :value="2" label="解决中"></el-option>
                  <el-option :value="3" label="已关闭"></el-option>
                  <el-option :value="4" label="无需处理"></el-option>
                </el-select>
                <i
                  class="el-icon-check"
                  style="cursor: pointer"
                  @click="saveEdit(scope.row, 'status')"
                ></i>
                <i
                  style="cursor: pointer"
                  class="el-icon-close"
                  @click="cancelEdit(scope.row, 'status')"
                ></i>
              </div>
              <div v-else>
                <span v-if="scope.row.status == 0" style="color: #FF9C99; font-weight: bold; font-size: 13px">待受理</span>
                <span v-if="scope.row.status == 1" style="color: #98D7B6; font-weight: bold; font-size: 13px">已处理</span>
                <span v-if="scope.row.status == 2" style="color: #FFBA84; font-weight: bold; font-size: 13px">解决中</span>
                <span v-else-if="scope.row.status == 3" style="color: #969b96; font-weight: bold; font-size: 13px">已关闭</span>
                <span v-else-if="scope.row.status == 4" style="color: #969b96; font-weight: bold; font-size: 13px">无需处理</span>
<!--                <i v-if="role_id !=='19'"-->
<!--                  style="cursor: pointer"-->
<!--                  class="el-icon-edit-outline"-->
<!--                  @click="startEdit(scope.row, 'status')"-->
<!--                ></i>-->
              </div>
            </template>
          </el-table-column>
          <el-table-column label="问题类型" prop="question_type" min-width="125px" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.isEditingType">
                <el-select
                  v-model.number="scope.row.question_type"
                  size="mini"
                  style="width: 80px;"
                  class="edit-input"
                >
                  <el-option :value="0" label="咨询类"></el-option>
                  <el-option :value="1" label="支持类"></el-option>
                  <el-option :value="2" label="BUG"></el-option>
                  <el-option :value="3" label="需求类"></el-option>
                  <el-option :value="4" label="操作类"></el-option>
                </el-select>
                <i
                  class="el-icon-check"
                  style="cursor: pointer"
                  @click="saveEdit(scope.row, 'question_type')"
                ></i>
                <i
                  style="cursor: pointer"
                  class="el-icon-close"
                  @click="cancelEdit(scope.row, 'question_type')"
                ></i>
              </div>
              <div v-else>
                <span v-if="scope.row.question_type == 0">咨询类</span>
                <span v-if="scope.row.question_type == 1">支持类</span>
                <span v-else-if="scope.row.question_type == 2" style="color: #f35d58; font-weight: bold; font-size: 13px">
                  <a :href="scope.row.bug_url.startsWith('http') ? scope.row.bug_url : 'https://' + scope.row.bug_url" target="_blank" style="color: inherit;">BUG</a>
                </span>
                <span v-else-if="scope.row.question_type == 3">需求类</span>
                <span v-else-if="scope.row.question_type == 4">操作类</span>
                <i v-if="role_id !=='19' && scope.row.status !==3 && scope.row.status !==4 && scope.row.ai_answer_status !== 1"
                  style="cursor: pointer"
                  class="el-icon-edit-outline"
                  @click="startEdit(scope.row, 'question_type')"
                ></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="业务归属" prop="project_type" min-width="130px" align="center">
             <template slot-scope="scope">
              <div v-if="scope.row.isEditingProject">
                <el-select
                  v-model.number="scope.row.project_type"
                  size="mini"
                  style="width: 85px;"
                  class="edit-input"
                >
                  <el-option v-for="item in productList"
                             :key="item.product_id"
                             :label="item.product_name"
                             :value="item.product_id">
                  </el-option>
                </el-select>
                <i
                  class="el-icon-check"
                  style="cursor: pointer"
                  @click="saveEdit(scope.row, 'project_type')"
                ></i>
                <i
                  style="cursor: pointer"
                  class="el-icon-close"
                  @click="cancelEdit(scope.row, 'project_type')"
                ></i>
              </div>
              <div v-else>
                <span>{{ productMap[scope.row.project_type] || '' }}</span>
                <i v-if="role_id !=='19' && scope.row.status !==3 && scope.row.status !==4 && scope.row.ai_answer_status !== 1"
                  style="cursor: pointer"
                  class="el-icon-edit-outline"
                  @click="startEdit(scope.row, 'project_type')"
                ></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="售后群名称" prop="room_name" min-width="170px" align="center"></el-table-column>
          <el-table-column label="提交人" min-width="100px" align="center">
            <template slot-scope="scope">
          <!-- 显示 send_from 和 send_from_user -->
            <div>{{ scope.row.send_from_name }} ({{ scope.row.send_from }})</div>
            </template>
          </el-table-column>
          <el-table-column label="所属大区" prop="belong_area" min-width="100px" align="center"></el-table-column>
          <el-table-column label="处理时长(分钟)" prop="processing_time" min-width="105px" align="center"></el-table-column>
          <el-table-column v-if="role_id ==='0'" label="是否需要更新到知识库" prop="is_knowledge" min-width="150px" align="center">
            <template v-slot="slotProps">
              <span v-if="slotProps.row.is_knowledge===0">否</span>
              <span v-if="slotProps.row.is_knowledge===1">是</span>
              <span v-if="slotProps.row.is_knowledge===2">已更新</span>
            </template>
          </el-table-column>
          <el-table-column v-if="role_id ==='0'" label="是否复盘" prop="is_review" min-width="70px" align="center">
            <template v-slot="slotProps">
              <span v-if="slotProps.row.is_review===0">否</span>
              <span v-if="slotProps.row.is_review===1">是</span>
            </template>
          </el-table-column>
          <el-table-column v-if="role_id ==='0'" label="是否有训练价值" prop="is_valuable" min-width="105px" align="center">
            <template v-slot="slotProps">
              <span v-if="slotProps.row.is_valuable===0">否</span>
              <span v-if="slotProps.row.is_valuable===1">是</span>
            </template>
          </el-table-column>
          <el-table-column label="AI回答" prop="answers.is_auto" min-width="70px" fixed="right" align="center">
            <template v-slot="slotProps">
              <span v-if="slotProps.row.answers.is_auto===0" style="color: #FFBA84; font-weight: bold; font-size: 13px">否</span>
              <span v-if="slotProps.row.answers.is_auto===1">是</span>
            </template>
          </el-table-column>
          <el-table-column label="消息已推送" prop="is_send" min-width="90px" fixed="right" align="center">
            <template v-slot="slotProps">
              <span v-if="slotProps.row.is_send===false" style="color: #FFBA84; font-weight: bold; font-size: 13px">否</span>
              <span v-if="slotProps.row.is_send===true">是</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" v-if="role_id !=='19'" fixed="right" min-width="210px" align="center">
            <template v-slot="slotProps">
              <div class="custom-button-group" v-if="role_id !=='19' && slotProps.row.ai_answer_status !== 1">
                <el-badge :is-dot="slotProps.row.have_append" class="item">
                  <el-tooltip class="item" effect="dark" content="编辑答复" placement="top" :enterable="false">
                    <el-button circle type="primary" icon="el-icon-edit-outline" size="mini" @click="editAnswser(slotProps.row.msg_id, slotProps.$index)"></el-button>
                  </el-tooltip>
                </el-badge>
                <el-tooltip class="item" effect="dark" content="推送消息" placement="top" :enterable="false">
                  <el-popconfirm
                    title="确定要推送消息吗？"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-warning"
                    @confirm="pushMessage(slotProps.row.msg_id, slotProps.row.room_id)"
                  >
                    <el-button circle :disabled="slotProps.row.answers.question_answer_manual==='' || slotProps.row.status ===3 || slotProps.row.status ===4" type="success" icon="el-icon-s-promotion" size="mini" slot="reference"></el-button>
                  </el-popconfirm>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="删除" placement="top" :enterable="false">
                  <el-button circle v-if= "((role_id !=='19' && role_id !=='0') && staff_no === slotProps.row.comp_user) || role_id ==='0'" :disabled="slotProps.row.status ===3" icon="el-icon-delete" type="warning" size="mini" @click="deleteDialogVisible=true, answerForm.id=slotProps.row.id"></el-button>
<!--                  <el-popconfirm-->
<!--                    title="确定要删除此问题吗？"-->
<!--                    confirm-button-text="确定"-->
<!--                    cancel-button-text="取消"-->
<!--                    icon="el-icon-warning"-->
<!--                    @confirm="deleteQuestion(slotProps.row.id)"-->
<!--                  >-->
<!--                    <el-button circle v-if="role_id ==='0'" :disabled="slotProps.row.status ===3" icon="el-icon-delete" type="warning" size="mini" slot="reference"></el-button>-->
<!--                  </el-popconfirm>-->
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="无需处理" placement="top" :enterable="false">
                  <el-button circle v-if="role_id ==='0'" :disabled="slotProps.row.status ===1 || slotProps.row.status ===3 || slotProps.row.status ===4" icon="el-icon-s-check" type="info" size="mini" @click="undealQuestionVisible=true, answerForm.id=slotProps.row.id"></el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="已处理" placement="top" :enterable="false">
                  <el-button circle v-if="role_id ==='0'" :disabled="slotProps.row.status ===1 || slotProps.row.status ===3 || slotProps.row.status ===4" type="primary" icon="el-icon-thumb" size="mini" @click="handleQuestion(slotProps.row.msg_id, slotProps.$index)"></el-button>
                </el-tooltip>
              </div>
              <div  v-if="role_id !=='19' && slotProps.row.ai_answer_status === 1">
                <el-button type="primary" :loading="true" size="mini">AI回答中,请稍后操作</el-button>
              </div>
            </template>
          </el-table-column>
<!--          <el-table-column label="操作" fixed="right" min-width="210px" align="center">-->
<!--            <template>-->
<!--              -->
<!--            </template>-->
<!--          </el-table-column>-->
          <el-table-column v-if="role_id ==='19'" label="操作" fixed="right" min-width="200px" align="center">
            <template v-slot="slotProps">
              <div class="custom-button-group">
                <el-badge :is-dot="slotProps.row.have_append" class="item">
                  <el-tooltip class="item" effect="dark" content="编辑答复" placement="top" :enterable="false">
                    <el-button circle type="primary" icon="el-icon-edit-outline" size="mini" @click="editAnswser(slotProps.row.msg_id, slotProps.$index)"></el-button>
                  </el-tooltip>
                </el-badge>
                <el-tooltip class="item" effect="dark" content="推送消息" placement="top" :enterable="false">
                  <el-popconfirm
                    title="确定要推送消息吗？"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-warning"
                    @confirm="pushMessage(slotProps.row.msg_id, slotProps.row.room_id)"
                  >
                    <el-button circle :disabled="slotProps.row.answers.question_answer_manual==='' || slotProps.row.status ===3 || slotProps.row.status ===4" type="success" icon="el-icon-s-promotion" size="mini" slot="reference"></el-button>
                  </el-popconfirm>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="删除" placement="top" :enterable="false">
                  <el-button circle v-if= "staff_no === slotProps.row.comp_user" icon="el-icon-delete" type="warning" size="mini" @click="deleteDialogVisible=true, answerForm.id=slotProps.row.id"></el-button>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="关闭问题" placement="top" :enterable="false">
                  <el-popconfirm
                    title="确定要关闭此问题吗？"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-warning"
                    @confirm="closeQuestion(slotProps.row.id)"
                  >
                    <el-button :disabled="slotProps.row.status !==1" circle icon="el-icon-finished" type="info" size="mini" slot="reference"></el-button>
                  </el-popconfirm>
                </el-tooltip>
              </div>
              <div v-if="slotProps.row.ai_answer_status === 1">
                <el-button type="primary" :loading="true" size="mini">AI回答中,请稍后操作</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <!-- 列表分页 -->
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
      <!--图片预览弹框-->
      <el-dialog
        :visible.sync="dialogVisible"
        width="100%"
        class="transparent-dialog">
        <div style="position: relative; height: 620px;">
          <!-- 图片展示区域 -->
          <div v-if="previewImagesList.length > 0" style="height: 100%; display: flex; align-items: center; justify-content: center;">
            <!-- 页码提示 -->
            <div style="position: absolute; top: 0px; left: 50%; transform: translateX(-50%); color: rgb(255,255,255); font-weight: bold; font-size: 24px;">
              {{ currentImageIndex + 1 }} / {{ previewImagesList.length }}
            </div>
            <img
              :src="currentImageSrc"
              style="max-width: 90%; max-height: 550px; display: block;"
            >
            <!-- 分页控制器 -->
            <div>
              <el-button
                circle
                size="mini"
                :disabled="currentImageIndex === 0"
                @click="previousImage"
                style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%);"
              >
                <i class="el-icon-arrow-left" style="font-size: 30px; font-weight: bold;"></i>
              </el-button>
              <el-button
                circle
                size="mini"
                :disabled="currentImageIndex === previewImagesList.length - 1"
                @click="nextImage"
                style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-weight: bold;"
              >
                <i class="el-icon-arrow-right" style="font-size: 30px; font-weight: bold;"></i>
              </el-button>
            </div>
          </div>
          <div v-else style="text-align: center; padding: 50px;">
            暂无可用图片
          </div>
        </div>
      </el-dialog>
      <!--编辑答复弹框-->
      <el-dialog
        :visible.sync="editDialogVisible"
        @close="closeEditAnswser"
        width="80%">
          <template slot="title">
            <span style="text-align: left">编辑问题</span>
            <div style="text-align: right; width: 90%;">
              <el-button type="warning" round size="mini" :disabled="isFirst" @click="goToPrevQuestion">上一条</el-button>
              <span class="page-info">
                {{ currentIndex + 1 }} / {{ questionMsgIds.length }}
              </span>
              <el-button type="warning" round size="mini" :disabled="isLast" @click="goToNextQuestion">下一条</el-button>
            </div>
          </template>
        <el-divider content-position="center" style="margin-top: -30px">基本信息</el-divider>
        <el-form :model="answerForm" :rules="answerFormRules" ref="answerFormRef" key="answerFormRef" style="margin-left: 30px">
          <el-row>
            <el-col :span="5">
              <el-form-item label="问题时间:" class="bold-label">
                <span>{{ answerForm.msg_time }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="提交人:" class="bold-label">
                <span>{{ answerForm.send_from_name }} ({{ answerForm.send_from }})</span>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="所属商户:" class="bold-label">
                <el-input
                    class="edit-input"
                    v-model="answerForm.belong_mer_code"
                    size="mini"
                    style="width: 80px; padding-left: 0px"
                  ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="ERP类型:" class="bold-label">
                <el-select
                  v-model="answerForm.mer_type"
                  size="mini"
                  style="width: 80px;"
                  class="edit-input"
                >
                  <el-option value="0" label="H1"></el-option>
                  <el-option value="1" label="H2"></el-option>
                  <el-option value="2" label="非海典ERP"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="群名称:" class="bold-label">
                <span>{{ answerForm.room_name }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="5">
              <el-form-item label="业务归属:" class="bold-label">
                <el-select
                  v-model.number="answerForm.project_type"
                  size="mini"
                  style="width: 90px;"
                  class="edit-input"
                  @change="onProjectTypeChange"
                >
                  <el-option v-for="item in productList"
                             :key="item.product_id"
                             :label="item.product_name"
                             :value="item.product_id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="问题类型:" class="bold-label">
                <el-select
                  v-model.number="answerForm.question_type"
                  @visible-change="handleQuestionTypeChange"
                  size="mini"
                  style="width: 80px;"
                  class="edit-input"
                >
                  <el-option :value="0" label="咨询类"></el-option>
                  <el-option :value="1" label="支持类"></el-option>
                  <el-option :value="2" label="BUG"></el-option>
                  <el-option :value="3" label="需求类"></el-option>
                  <el-option :value="4" label="操作类"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="处理人:" class="bold-label">
                <el-tooltip class="item" effect="dark" content="请输入处理人姓名或工号" placement="top">
                  <el-select
                    v-model="answerForm.comp_user"
                    @change="handleUserChange"
                    filterable
                    remote
                    :remote-method="handleSearch"
                    :loading="loading"
                    size="mini"
                    style="width: 110px;"
                    class="edit-input"
                  >
                    <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-tooltip>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="处理状态:" class="bold-label">
                <span v-if="answerForm.status === 0" style="color: #FF9C99; font-weight: bold; font-size: 13px">待受理</span>
                <span v-if="answerForm.status === 1" style="color: #98D7B6;">已处理</span>
                <span v-if="answerForm.status === 2" style="color: #FFBA84; font-weight: bold; font-size: 13px">解决中</span>
                <span v-else-if="answerForm.status === 3" style="color: #5e625f; font-weight: bold; font-size: 13px">已关闭</span>
                <span v-else-if="answerForm.status === 4" style="color: #5e625f; font-weight: bold; font-size: 13px">无需处理</span>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="图片:" class="bold-label">
                <span v-if="hasImage(answerForm.msg_content)">
                  <el-link @click="previewImages(answerForm.msg_content)" style="font-weight: normal">查看<i class="el-icon-view el-icon--right"></i> </el-link>
                </span>
                <span v-else>无</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="role_id ==='0'">
            <el-col :span="5">
              <el-form-item label="是否复盘:" class="bold-label">
                <el-select
                  v-model.number="answerForm.is_review"
                  @visible-change="handleReviewOrValuableChange"
                  size="mini"
                  style="width: 90px;"
                  class="edit-input"
                >
                  <el-option :value=0 label="否"></el-option>
                  <el-option :value=1 label="是"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="是否有训练价值:" class="bold-label">
                <el-select
                  v-model.number="answerForm.is_valuable"
                  @visible-change="handleReviewOrValuableChange"
                  size="mini"
                  style="width: 75px;"
                  class="edit-input"
                >
                  <el-option :value=0 label="否"></el-option>
                  <el-option :value=1 label="是"></el-option>
                </el-select>
                <el-tooltip placement="top">
                  <template #content>
                    <div>
                      <p>【是否有训练价值】解释：</p>
                      <p>后期识别产品线时，会变更识别方式，不再使用prompt，而是使用AI进行文本训练，故已有的数据对训练是否有价值非常重要，例如：</p>
                      <p>1、接口还是被禁用了，需要麻烦帮忙看下（无价值，无法根据文本定位到产品线）</p>
                      <p>2、百家惠，请问这个数据获取需要多久，麻烦看下还没有完成（无价值，无法根据文本定位到产品线）</p>
                      <p>3、子商品库存一直在变动，但是第三方商品界面的库存没有任何变动记录，没有锁库存，麻烦看下吧（有价值，可以定位为商品云）</p>
                    </div>
                  </template>
                 <i class="el-icon-info" style="margin-left: 5px; color: #909399; cursor: pointer;"></i>
               </el-tooltip>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="是否需要更新到知识库:" class="bold-label">
                <!-- AI回答时显示静态文本 -->
                <template v-if="answerForm.answers.is_auto">
                  <span v-if="answerForm.is_knowledge === 0">否</span>
                  <span v-if="answerForm.is_knowledge === 1">是</span>
                  <!-- 已更新时显示查看按钮 -->
                  <template v-else-if="answerForm.is_knowledge === 2">
                    <el-popover
                      placement="top"
                      width="800"
                      trigger="click"
                      popper-class="dark-popover"
                    >
                      <div>
                        <p>问题描述：{{ answerForm.knowledge.question_title }}</p>
                        <p>问题答案：{{ answerForm.knowledge.question_answer }}</p>
                      </div>
                      <el-link slot="reference" style="font-weight: normal">
                        已更新<i class="el-icon-view el-icon--right"></i>
                      </el-link>
                    </el-popover>
                  </template>
                </template>
                <!-- 可编辑状态 -->
                <template v-else-if="answerForm.status === 1">
                  <el-select
                    v-if="answerForm.is_knowledge === 0 || answerForm.is_knowledge === 1"
                    :value="answerForm.is_knowledge"
                    @change="handleKnowledgeChange"
                    size="mini"
                    style="width: 75px;"
                    class="edit-input"
                  >
                    <el-option :value="0" label="否"></el-option>
                    <el-option :value="1" label="是"></el-option>
                  </el-select>
                  <i
                    v-if="answerForm.is_knowledge === 1"
                    class="el-icon-edit-outline"
                    style="cursor: pointer; margin-left: 5px;"
                    @click="editKnowledgeForm"
                  ></i>
                  <template v-if="answerForm.is_knowledge === 2">
                    <el-popover
                      placement="top"
                      width="800"
                      trigger="click"
                      popper-class="dark-popover"
                    >
                      <div>
                        <p>问题描述：{{ answerForm.knowledge.question_title }}</p>
                        <p>问题答案：{{ answerForm.knowledge.question_answer }}</p>
                      </div>
                      <el-link slot="reference" style="font-weight: normal">
                        已更新<i class="el-icon-view el-icon--right"></i>
                      </el-link>
                    </el-popover>
                  </template>
                </template>
                <!-- 其他情况显示静态文本 -->
                <template v-else>
                  <span v-if="answerForm.is_knowledge === 0">否</span>
                  <span v-if="answerForm.is_knowledge === 1">是</span>
                  <!-- 已更新时显示查看按钮 -->
                  <template v-else-if="answerForm.is_knowledge === 2">
                    <el-popover
                      placement="top"
                      width="800"
                      trigger="click"
                      popper-class="dark-popover"
                    >
                      <div>
                        <p>问题描述：{{ answerForm.knowledge.question_title }}</p>
                        <p>问题答案：{{ answerForm.knowledge.question_answer }}</p>
                      </div>
                      <el-link slot="reference" style="font-weight: normal">
                        已更新<i class="el-icon-view el-icon--right"></i>
                      </el-link>
                    </el-popover>
                  </template>
                </template>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="answerForm.question_type ===2">
            <el-form-item label="BUG链接:" class="bold-label"  prop="bug_url">
                <el-input
                    class="edit-input"
                    v-model="answerForm.bug_url"
                    size="mini"
                    style="width: 1000px; padding-left: 0px"
                  ></el-input>
              </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="问题描述:" class="bold-label">
                <span>{{ getTextContent(answerForm.msg_content) }}</span>
              </el-form-item>
            <el-timeline style="margin-right: 20px" :reverse="false">
              <el-timeline-item
                type="primary"
                v-for="(question, index) in answerForm.childrens"
                :key="index"
                :timestamp="question.create_time">
                <template slot="dot">
                   <el-tag size="medium" v-if="question.type===1" class="custom-dot">
                    合并
                  </el-tag>
                  <el-tag size="medium" type="info" v-if="question.type===2" class="custom-dot">
                    引用
                  </el-tag>
                </template>
                <template>
                  <span v-if="isTextContent(question.msg_content)" style="white-space: pre-wrap;">
                    <span v-if="question.type===1 || question.type===2" :class="{'text-collapsed': !Boolean(question.isCollapsed)}">
                      {{ getTextContent(question.msg_content) }}
                    </span>
                    <el-button
                      v-if="question && shouldShowToggle(question)"
                      type="text"
                      size="mini"
                      @click="$set(question, 'isCollapsed', !question.isCollapsed)"
                      class="toggle-btn"
                    >
                      {{ !question.isCollapsed ? '展开' : '收起' }}
                    </el-button>
                  </span>
                </template>
<!--                &lt;!&ndash; 判断 media_type 为 text 时显示 media_content &ndash;&gt;-->
<!--                <span v-if="isTextContent(question.msg_content)" style="white-space: pre-wrap;">-->
<!--                  <span v-if="question.type===1 || question.type===2">-->
<!--                    {{ getTextContent(question.msg_content) }}-->
<!--                  </span>-->
<!--                </span>-->
                <span v-if="hasImage(question.msg_content)">
                  <el-button size="mini" type="text" round plain @click="previewImages(question.msg_content)">查看图片</el-button>
                </span>
                <span v-if="question.type===1 && (answerForm.status===0 || answerForm.status===2)">
                  <el-popconfirm
                    title="确定要解绑此消息吗？"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-warning"
                    @confirm="unbindMsg(question.sub_msg_id, answerForm.msg_id)"
                  >
                    <el-button size="mini" type="text" round plain slot="reference">解绑</el-button>
                  </el-popconfirm>
                </span>
              </el-timeline-item>
            </el-timeline>
          </el-row>
          <el-row>
            <el-form-item prop="answers.question_answer_manual" class="bold-label">
              <template slot="label">
                回复内容:
              </template>
              <el-popover
                placement="top"
                width="800"
                trigger="click"
                popper-class="dark-popover"
                v-if="answerForm.answers && answerForm.answers.question_answer && answerForm.answers.is_auto===0"
              >
              <div v-html="formatTooltipContent(answerForm.answers.question_answer)"
              style="white-space: pre-line; font-size: 12px"></div>
              <el-button slot="reference" size="mini" type="text" style="margin-left: 10px;">查看AI原始回复</el-button>
            </el-popover>
              <el-input
                type="textarea"
                :rows="5"
                maxlength="3000"
                show-word-limit
                v-model="answerForm.answers.question_answer_manual"
                @paste.native="handlePastePic"
                ref="replyInput"
                placeholder="请输入回复内容或粘贴图片(支持jpg/png格式，单张图片不超过5MB,限制3张图片)"></el-input>
            </el-form-item>
          </el-row>
          <el-row style="text-align: right">
            <span style="float:left; margin-right: auto;">
              <el-tooltip class="item" effect="dark" content="告知售后问题正在排查中，以免售后焦虑，觉得问题无人处理" placement="top" :enterable="false">
                <el-button
                :disabled="answerForm.status===1 ||answerForm.status===3 || answerForm.status===4 || isRestassured===true"
                type="warning" @click="Restassured">安心告知</el-button>
              </el-tooltip>
            </span>
            <span>
              <el-button
                  v-if= "(role_id !=='0' && staff_no === answerForm.comp_user) || role_id ==='0'"
                  :disabled="answerForm.status ===3"
                  type="danger"
                  @click="deleteDialogVisible=true, answerForm.id">
                删 除
              </el-button>
              <el-button @click="closeEditAnswser">取 消</el-button>
              <el-button
                :disabled="answerForm.status===1 ||answerForm.status===3 || answerForm.status===4
                ||!answerForm.answers.question_answer_manual.trim()
                ||originalAnswerForm.answers.question_answer_manual === answerForm.answers.question_answer_manual
                ||originalAnswerForm.comp_user === answerForm.comp_user"
                type="primary" @click="transferAnswer">转 交</el-button>
              <el-button :disabled="answerForm.status===3 || answerForm.status===4" type="primary" @click="submitAnswer(false)">保 存</el-button>
              <el-tooltip class="item" effect="dark" content="回复内容为空时不允许推送" placement="top" :enterable="false">
                <el-button type="primary" :disabled="!answerForm.answers.question_answer_manual.trim() || answerForm.status===3 || answerForm.status===4 || isPushing===true" @click="submitAnswer(true)">保存并推送</el-button>
              </el-tooltip>
            </span>
          </el-row>
          <el-row style="margin-top: 20px">
            <!-- 图片上传部分 -->
            <el-form-item label="上传图片:">
              <el-upload
                ref="uploader"
                list-type="picture-card"
                :action="uploadPath"
                :multiple="true"
                :limit="3"
                :file-list="fileList"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :on-success="handleUploadSuccess"
                :on-exceed="handleExceed"
                :before-upload="beforeUpload"
                :on-change="handFileList"
                :auto-upload="true">
<!--                <el-button slot="trigger" type="primary">点击上传</el-button>-->
                <i class="el-icon-plus"></i>
                <div slot="tip" class="el-upload__tip">
                  支持jpg/png格式，单张图片不超过5MB
                </div>
              </el-upload>
            </el-form-item>
          </el-row>
          <el-row>
            <div style="margin-bottom: 25px; margin-left: 30px; font-weight: bold">操作日志</div>
            <el-timeline style="margin-right: 20px">
              <el-timeline-item
                type="primary"
                v-for="(log, index) in answerForm.logs"
                :key="index"
                :timestamp="log.timestamp">
                {{log.content}}
<!--                 <a-->
<!--                    v-if="log.attachment && log.attachment.length"-->
<!--                    href="javascript:void(0)"-->
<!--                    @click="handleLogPicPreview(log.attachment)"-->
<!--                  >-->
<!--                    查看图片-->
<!--                  </a>-->
                <span v-if="log.attachment && log.attachment.length">
                  <el-button size="mini" type="text" round plain @click="handleLogPicPreview(log.attachment)">查看图片</el-button>
                </span>
              </el-timeline-item>
            </el-timeline>
          </el-row>
        </el-form>
      </el-dialog>
      <!--去处理弹框-->
      <el-dialog
        :visible.sync="handleQuestionDialogVisible"
        title = "处理问题"
        width="60%">
        <el-divider content-position="center" style="margin-top: -30px">基本信息</el-divider>
        <el-form label-width="100px" :model="handleQuestionForm" :rules="handleQuestionFormRules" ref="handleQuestionFormRef" key="handleQuestionFormRef">
          <el-row>
            <el-col :span="8">
              <el-form-item label="问题时间:" class="bold-label">
                <span>{{ handleQuestionForm.msg_time }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="提交人:" class="bold-label">
                <span>{{ handleQuestionForm.send_from_name }} ({{ handleQuestionForm.send_from }})</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="群名称:" class="bold-label">
                <span>{{ handleQuestionForm.room_name }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="所属商户:" class="bold-label">
                <el-input
                    class="edit-input"
                    v-model="handleQuestionForm.belong_mer_code"
                    size="mini"
                    style="width: 80px; padding-left: 0px"
                  ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="业务归属:" class="bold-label">
                <el-select
                  v-model.number="handleQuestionForm.project_type"
                  size="mini"
                  style="width: 90px;"
                  class="edit-input"
                  @change="onProjectTypeChange"
                >
                  <el-option v-for="item in productList"
                             :key="item.product_id"
                             :label="item.product_name"
                             :value="item.product_id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="处理人:" class="bold-label">
                <el-tooltip class="item" effect="dark" content="请输入处理人姓名或工号" placement="top">
                  <el-select
                    v-model="handleQuestionForm.comp_user"
                    filterable
                    remote
                    :remote-method="handleSearch"
                    :loading="loading"
                    size="mini"
                    style="width: 110px;"
                    class="edit-input"
                  >
                    <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-tooltip>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="问题类型:" class="bold-label">
                <el-select
                  v-model.number="handleQuestionForm.question_type"
                  size="mini"
                  style="width: 80px;"
                  class="edit-input"
                >
                  <el-option :value="0" label="咨询类"></el-option>
                  <el-option :value="1" label="支持类"></el-option>
                  <el-option :value="2" label="BUG"></el-option>
                  <el-option :value="3" label="需求类"></el-option>
                  <el-option :value="4" label="操作类"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="处理状态:" class="bold-label">
                <span v-if="handleQuestionForm.status == 0" style="color: #FF9C99; font-weight: bold; font-size: 13px">待受理</span>
                <span v-if="handleQuestionForm.status == 1" style="color: #98D7B6;">已处理</span>
                <span v-if="handleQuestionForm.status == 2" style="color: #FFBA84; font-weight: bold; font-size: 13px">解决中</span>
                <span v-else-if="handleQuestionForm.status == 3" style="color: #5e625f; font-weight: bold; font-size: 13px">已关闭</span>
                <span v-else-if="handleQuestionForm.status == 4" style="color: #5e625f; font-weight: bold; font-size: 13px">无需处理</span>
              </el-form-item>
<!--              <el-form-item label="处理状态:" class="bold-label">-->
<!--                <el-select-->
<!--                  v-model.number="answerForm.status"-->
<!--                  size="mini"-->
<!--                  style="width: 90px;"-->
<!--                  class="edit-input"-->
<!--                >-->
<!--                  <el-option :value="0" label="待受理"></el-option>-->
<!--                  <el-option :value="1" label="已处理"></el-option>-->
<!--                  <el-option :value="2" label="解决中"></el-option>-->
<!--                  <el-option :value="3" label="已关闭"></el-option>-->
<!--                  <el-option :value="4" label="无需处理"></el-option>-->
<!--                </el-select>-->
<!--              </el-form-item>-->
            </el-col>
            <el-col :span="8">
              <el-form-item label="图片:" class="bold-label">
                <span v-if="hasImage(handleQuestionForm.msg_content)">
                  <el-link @click="previewImages(handleQuestionForm.msg_content)" style="font-weight: normal">查看<i class="el-icon-view el-icon--right"></i> </el-link>
                </span>
                <span v-else>无</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item label="问题描述:" class="bold-label">
                <span>{{ getTextContent(handleQuestionForm.msg_content) }}</span>
              </el-form-item>
          </el-row>
          <el-row v-if="handleQuestionForm.question_type ===2">
            <el-form-item label="BUG链接:" class="bold-label" prop="bug_url">
                <el-input
                    class="edit-input"
                    v-model="handleQuestionForm.bug_url"
                    size="mini"
                    style="width: 750px; padding-left: 0px"
                    placeholder="请输入BUG的TAPD链接"
                  ></el-input>
              </el-form-item>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="处理时间:" class="bold-label" prop="comp_time">
                <el-date-picker
                  v-model="handleQuestionForm.comp_time"
                  type="datetime"
                  placeholder="选择日期时间"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="创建时间:" class="bold-label">
                <span>{{ handleQuestionForm.create_time }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-form-item label="回复内容:" class="bold-label" prop="answers.question_answer_manual">
              <el-input
                type="textarea"
                :rows="5"
                maxlength="3000"
                show-word-limit
                v-model="handleQuestionForm.answers.question_answer_manual"
                placeholder="请输入回复内容"></el-input>
            </el-form-item>
          </el-row>
        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button @click="handleQuestionDialogVisible = false">取 消</el-button>
            <el-tooltip
              content="回复内容未变更，无法保存"
              :disabled="formHasChanged"
              placement="top"
            >
              <div class="disabled-button-wrapper">
                <el-button
                  type="primary"
                  :disabled="!formHasChanged"
                  @click="submitHandleQuestion"
                >
                  保 存
                </el-button>
              </div>
            </el-tooltip>
          </span>
      </el-dialog>
      <!--配置AI自动回复弹框-->
      <el-dialog :visible.sync="AIConfigDialogVisible"
                width="65%"
                title = "AI自动回复配置"
                @close = "AIConfigDialogVisible = false">
        <template>
          <div style="display: flex; justify-content: center; align-items: center; height: 100%;">
            <el-table
              :data="AIConfigList"
              style="width: 100%"
              border>
              <el-table-column
                prop="room_name"
                label="群名称"
                width="200">
              </el-table-column>
              <el-table-column
                prop="web_hook"
                label="群机器人地址"
                >
                <template slot-scope="scope">
                  <div v-if="scope.row.isEditingWebhook">
                    <el-input
                      class="edit-input"
                      v-model="scope.row.web_hook"
                      size="mini"
                      style="width: 400px; padding-left: 0px"
                    >
                    </el-input>
                    <i
                      class="el-icon-check"
                      style="cursor: pointer"
                      @click="saveAIconfig(scope.row, 'web_hook')"
                    ></i>
                    <i
                      style="cursor: pointer"
                      class="el-icon-close"
                      @click="cancelEdit(scope.row, 'web_hook')"
                    ></i>
                  </div>
                  <div v-else>
                    {{ scope.row.web_hook }}
                    <i
                      style="cursor: pointer"
                      class="el-icon-edit-outline"
                      @click="startEdit(scope.row, 'web_hook')"
                    ></i>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="群是否可见"
                width="100">
                  <template slot-scope="scope">
                    <el-switch
                      v-model="scope.row.is_display"
                      @change="handleGroupVisible(scope.row)"
                      active-color="#13ce66"
                    ></el-switch>
                  </template>
              </el-table-column>
              <el-table-column
                label="群消息识别"
                width="110">
                  <template slot-scope="scope">
                    <el-switch
                      v-model="scope.row.prediction_message_toggle"
                      @change="handleredictionMessage(scope.row)"
                      active-color="#13ce66"
                    ></el-switch>
                  </template>
              </el-table-column>
              <el-table-column
                label="AI自动回复"
                width="100">
                  <template slot-scope="scope">
                    <el-switch
                      v-model="scope.row.reply_toggle"
                      @change="handleAIConfigChange(scope.row)"
                      active-color="#13ce66"
                    ></el-switch>
                  </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      <span slot="footer" class="dialog-footer">
        <el-button @click="AIConfigDialogVisible = false">关 闭</el-button>
<!--        <el-button type="primary" @click="saveAIconfig(AIConfigList)">保 存</el-button>-->
      </span>
    </el-dialog>
      <!--无需处理弹框-->
      <el-dialog :visible.sync="undealQuestionVisible"
                width="60%"
                title = "编辑无需处理原因"
                @close = "undealQuestionVisible = false, undealQuestionForm = {}">
        <el-form label-width="130px"
                 :model="undealQuestionForm"
                 ref="undealQuestionFormRef"
                 :rules="undealQuestionFormRules"
                 key="undealQuestionFormRef">
          <el-form-item label="原因:" class="bold-label" prop="notes">
                <el-input
                  type="textarea"
                  :rows="5"
                  maxlength="3000"
                  show-word-limit
                  v-model="undealQuestionForm.notes"
                  placeholder="请输入无需处理原因"></el-input>
              </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="undealQuestionVisible = false">取 消</el-button>
          <el-button type="primary" @click="undealQuestion">确 定</el-button>
        </span>
      </el-dialog>
      <!--删除弹框-->
      <el-dialog :visible.sync="deleteDialogVisible"
                width="60%"
                title = "删除问题处理原因"
                @close = "deleteDialogVisible = false, deleteQuestionForm = {}">
        <el-form label-width="130px"
                 :model="deleteQuestionForm"
                 ref="deleteQuestionFormRef"
                 :rules="deleteQuestionFormRules"
                 key="deleteQuestionFormRef">
          <el-form-item label="原因:" class="bold-label" prop="notes">
                <el-input
                  type="textarea"
                  :rows="5"
                  maxlength="3000"
                  show-word-limit
                  v-model="deleteQuestionForm.notes"
                  placeholder="请输入删除原因"></el-input>
              </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="deleteQuestion">确 定</el-button>
        </span>
      </el-dialog>
      <!--维护BUG链接弹框-->
      <el-dialog :visible.sync="bugURLlDialogVisible"
                width="60%"
                title = "维护BUG链接"
                @close = "bugURLlDialogVisible = false, bugURLForm = {}">
        <el-form label-width="130px"
                 :model="bugURLForm"
                 ref="bugURLFormRef"
                 :rules="bugURLFormRules"
                 key="bugURLFormRef">
          <el-form-item label="BUG链接:" class="bold-label" prop="bug_url">
            <el-input
              v-model="bugURLForm.bug_url"
              placeholder="请输入BUG的TAPD链接"
              style="width: 100%"
            ></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="bugURLlDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="saveBugURL">确 定</el-button>
        </span>
      </el-dialog>
      <!-- 知识库维护问题弹框 -->
      <el-dialog
        title="更新知识库问题"
        :visible.sync="knowledgeDialogVisible"
        width="50%"
      >
        <el-form label-width="100px"
                 :model="knowledgeForm"
                 ref="knowledgeFormRef"
                 :rules="knowledgeFormRules"
                 key="knowledgeFormRef">
          <el-form-item label="问题描述" prop="question_title">
            <el-input
              v-model="knowledgeForm.question_title"
              type="textarea"
              :rows="3"
            ></el-input>
          </el-form-item>
          <el-form-item label="问题答案" prop="question_answer">
            <el-input
              v-model="knowledgeForm.question_answer"
              type="textarea"
              :rows="10"
            ></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="knowledgeDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSaveKnowledge">确 定</el-button>
        </span>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>

import { baseUrl } from '../../main'
import _ from 'lodash'
export default {
  data() {
    return {
      model: 'question',
      queryDate: this.getLastMonthRange(),
      questionList: [], // 售后问题列表
      productList: [
        {
          product_id: '',
          product_name: ''
        }
      ], // 业务线列表
      questionMsgIds: [], // 售后问题列表所有msg_id
      currentQuestionMsgId: '', // 当前详情页售后问题的msg_id
      user_name: window.localStorage.getItem('user_name'),
      chinese_name: window.localStorage.getItem('chinese_name'),
      staff_no: window.localStorage.getItem('staff_no'),
      role_id: window.localStorage.getItem('role_id'),
      queryInfo: { // 查询栏
        start_date: '',
        msg_content: '',
        send_from: '',
        belong_area: '',
        comp_user: '',
        project_type: '',
        status: '',
        question_type: '',
        belong_mer_code: '',
        is_auto: '',
        is_send: '',
        is_valuable: '',
        is_review: '',
        pagenum: 1,
        pagesize: 20
      },
      total: 0,
      update_date: {}, // 问题列表更新
      crop_id: '', // 企业id
      answerForm: { // 编辑答复弹框的数据
        answers: {
          question_answer_manual: ''
        }
      },
      originalData: {}, // 保存编辑前的数据，用于取消编辑时恢复
      knowledgeForm: { // 知识库维护问题弹框的数据
        msg_id: '',
        question_title: '',
        question_answer: ''
      },
      handleQuestionForm: { // 去处理弹框的数据
        answers: {
          question_answer_manual: ''
        }
      },
      bugURLForm: { bug_url: '' }, // bug链接弹框的数据
      comp_name: '', // 处理人姓名工号,格式为 姓名(工号)
      undealQuestionForm: {
        notes: ''
      }, // 无需处理编辑弹框的数据
      deleteQuestionForm: {
        notes: ''
      }, // 删除弹框的数据
      originalAnswerForm: {
        answers: {
          question_answer_manual: ''
        }
      }, // 编辑答复弹框的原始数据
      isCollapsed: true, // 追加的问题描述是否折叠展示
      isPushing: false, // 推送按钮
      isAnswerChange: '', // 问题答复是否更新
      isAutoRefresh: true, // 自动刷新列表
      isFlashing: false, // 浏览器标签页标题闪烁状态
      isRestassured: false, // 是否安心告知
      dialogVisible: false, // 控制图片预览弹框的显示
      editDialogVisible: false, // 控制编辑答复弹框的显示
      deleteDialogVisible: false, // 控制删除弹框的显示
      handleQuestionDialogVisible: false, // 控制去处理答复弹框的显示
      AIConfigDialogVisible: false, // 控制AI自动回复配置弹框的显示
      undealQuestionVisible: false, // 控制无需处理弹框的显示
      bugURLlDialogVisible: false, // bug_url维护弹框的显示
      knowledgeDialogVisible: false, //  控制知识库问题更新弹框的显示
      originalIsKnowledge: 0, //  保存是否需要更新知识库原始值
      previewImagesList: [], // 存储当前预览图片的地址
      currentImageIndex: 0,
      AIConfigList: {}, // AI自动回复配置列表
      robotStatus: 2, // 句子机器人状态
      robotStatusLoading: false, // 句子查询加载状态
      options: [], // 下拉选项
      loading: false, // 加载状态
      intervalId: null, // 用于存储定时器的 ID
      flashIntervalId: null, // 用于存储定时器的 ID
      timer: null, // 定时器实例
      fileList: [],
      currentIndex: 0,
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 31)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一年',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      undealQuestionFormRules: {
        notes: [
          { required: true, message: '请输入无需处理原因', trigger: 'blur' }
        ]
      },
      deleteQuestionFormRules: {
        notes: [
          { required: true, message: '请输入删除原因', trigger: 'blur' }
        ]
      },
      answerFormRules: {
        bug_url: [
          { required: true, message: '请输入BUG的TAPD链接', trigger: 'blur' }
        ]
      },
      bugURLFormRules: {
        bug_url: [
          { required: true, message: '请输入BUG的TAPD链接', trigger: 'blur' }
        ]
      },
      knowledgeFormRules: {
        question_title: [
          { required: true, message: '请输入问题描述', trigger: 'blur' }
        ],
        question_answer: [
          { required: true, message: '请输入问题答案', trigger: 'blur' }
        ]
      },
      handleQuestionFormRules: {
        'answers.question_answer_manual': [
          { required: true, message: '请输入回复内容', trigger: 'blur' }
        ],
        bug_url: [
          { required: true, message: '请输入BUG的TAPD链接', trigger: 'blur' }
        ],
        comp_time: [
          { required: true, message: '请选择处理时间', trigger: 'blur' },
          { validator: (rule, value, callback) => this.validateCompTime(rule, value, callback), trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
    this.default_date()
    this.getQuestionList()
    this.get_product_info()
    this.getQuestionIds()
    if (this.role_id === '0') {
      this.getRobotStatus()
    }
  },
  deactivated() {
    // 清理事件监听器
    window.removeEventListener('keydown', this.handleKeydown)
    // 组件销毁前清除列表刷新定时器，避免内存泄漏
    if (this.intervalId) {
      clearInterval(this.intervalId)
    }
    // 组件销毁前清除标签页标题闪烁定时器，避免内存泄漏
    if (this.flashIntervalId) {
      clearInterval(this.flashIntervalId)
      document.title = '海典测试平台' // 恢复为默认标题
      this.isFlashing = false
    }
  },
  activated() {
    this.queryReset()
    // 监听键盘事件
    window.addEventListener('keydown', this.handleKeydown)
    // 每隔 60 秒调用一次 myMethod
    this.intervalId = setInterval(() => {
      this.getQuestionList()
      // 判断当前用户是否为非大区用户,如果不是不去用户,则需判断是否存在待受理问题
      this.hasQuestion()
    }, 60000) // 60000 毫秒 = 60 秒
  },
  watch: {
    // 监听 dateRange 的变化，自动更新 queryInfo
    queryDate(newVal) {
      if (newVal && newVal.length === 2) {
        this.queryInfo.start_date = newVal[0]
        this.queryInfo.end_date = newVal[1]
      } else {
        this.queryInfo.start_date = ''
        this.queryInfo.end_date = ''
      }
    },
    question_type(newVal) {
      if (newVal === '2') {
        this.bugURLlDialogVisible = true
      }
    }
  },
  computed: {
    // 当前图片的 src
    currentImageSrc() {
      const currentImage = this.previewImagesList[this.currentImageIndex]
      return currentImage ? currentImage.media_content : ''
    },
    uploadPath() {
      // 动态生成上传路径
      // const baseURl = axios.defaults.baseURL
      return `${baseUrl}question/question_attachment_upload/`
    },
    // 是否是第一条
    isFirst() {
      return this.currentIndex === 0
    },
    // 是否是最后一条
    isLast() {
      return this.currentIndex === this.questionMsgIds.length - 1
    },
    formHasChanged() {
      if (!this.originalAnswerForm || !this.handleQuestionForm) return false
      // 比较两个表单对象的多个字段,只要有一个字段不同，返回 true，否则返回 false
      return this.originalAnswerForm.answers.question_answer_manual !== this.handleQuestionForm.answers.question_answer_manual
      // this.originalAnswerForm.belong_mer_code !== this.handleQuestionForm.belong_mer_code ||
      // this.originalAnswerForm.comp_user !== this.handleQuestionForm.comp_user ||
      // this.originalAnswerForm.project_type !== this.handleQuestionForm.project_type ||
      // this.originalAnswerForm.question_type !== this.handleQuestionForm.question_type ||
      // this.originalAnswerForm.bug_url !== this.handleQuestionForm.bug_url ||
      // this.originalAnswerForm.mer_type !== this.handleQuestionForm.mer_type ||
      // this.originalAnswerForm.comp_time !== this.handleQuestionForm.comp_time ||
    },
    productMap() {
      const map = {}
      this.productList.forEach(item => {
        map[item.product_id] = item.product_name
      })
      return map
    }
  },
  methods: {
    // 查询按钮点击事件
    queryList() {
      this.queryInfo.pagenum = 1
      this.getQuestionList()
      this.getQuestionIds()
    },
    // 重置按钮方法
    queryReset() {
      if (this.role_id === '0') {
        // this.queryDate = this.getLastFridayAndThisThursday()
        this.queryDate = this.getLastMonthRange()
      }
      if (this.role_id === '1' || this.role_id === '9' || this.role_id === '19') {
        this.queryDate = this.getLastMonthRange()
      }
      this.queryInfo = {
        start_date: '',
        end_date: '',
        send_from: '',
        belong_area: '',
        comp_user: '',
        project_type: '',
        status: '',
        question_type: '',
        belong_mer_code: '',
        is_auto: '',
        is_valuable: '',
        is_review: '',
        pagenum: 1,
        pagesize: 20
      }
      this.default_date()
      this.getQuestionList()
      this.getQuestionIds()
    },
    // 更新值班人按钮响应方法
    async updateRota() {
      const { data: res } = await this.$http.get(this.model + '/update_rota/')
      if (res.meta.status !== 200) return this.$message.error('值班表更新失败')
      this.$message.success('值班表更新成功')
    },
    // 设置每页条数
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getQuestionList()
    },
    // 设置页数
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getQuestionList()
    },
    // 获取售后问题列表
    async getQuestionList() {
      const { data: res } = await this.$http.post(this.model + '/question_list/', this.queryInfo)
      if (res.meta.status !== 200) return this.$message.error('获取售后问题列表失败')
      this.total = res.data.total
      // 初始化列表字段是否可编辑的状态为false
      this.questionList = res.data.questions.map(item => ({
        ...item,
        isEditingCompUser: false,
        isEditingProject: false,
        isEditingStatus: false,
        isEditingType: false,
        isEditingMercode: false
      }))
    },
    // 获取业务线
    async get_product_info() {
      const { data: res } = await this.$http.get(this.model + '/get_product_info/')
      if (res.meta.status !== 200) return this.$message.error('获取产品线失败')
      this.productList = res.data
    },
    // 获取售后问题列表的所有ID
    async getQuestionIds() {
      const getIDQueryInfo = JSON.parse(JSON.stringify(this.queryInfo))
      getIDQueryInfo.pagesize = 1000
      getIDQueryInfo.only_msg_id = '1'
      const { data: res } = await this.$http.post(this.model + '/question_list/', getIDQueryInfo)
      if (res.meta.status !== 200) return this.$message.error('获取售后问题列表失败')
      this.total = res.data.total
      // 初始化列表字段是否可编辑的状态为false
      this.questionMsgIds = res.data.questions
    },
    // 导出售后问题列表
    async queryExport() {
      try {
        // 深拷贝 queryInfo，避免修改原始对象
        const exportQueryInfo = JSON.parse(JSON.stringify(this.queryInfo))
        exportQueryInfo.pagesize = 10000
        const response = await this.$http.post(this.model + '/export_question_data/', exportQueryInfo, {
          responseType: 'blob'
        })
        // 检查响应是否为 Blob 类型
        if (response.data instanceof Blob && response.data.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          const filename = '售后问题列表.xlsx'
          await this.downloadFile(response, filename) // 假设 downloadFile 接收 Blob 对象
        } else {
          // 如果不是预期的 Excel 文件，尝试解析为 JSON
          const text = await response.data.text()
          const errorData = JSON.parse(text)
          this.$message.error(errorData.meta.msg || '导出失败')
        }
      } catch (error) {
        // 处理其他异常情况
        if (error.response) {
          // 服务端返回了错误响应
          const text = await error.response.data.text()
          const errorData = JSON.parse(text)
          this.$message.error(errorData.meta.msg || '导出失败')
        } else {
          this.$message.error('网络错误，请稍后重试')
        }
        console.error('导出接口调用失败:', error)
      }
    },
    // 下载文件
    async downloadFile(response, filename) {
      try {
        // 创建 Blob 对象
        const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        const downloadUrl = window.URL.createObjectURL(blob)
        // 创建一个隐藏的链接元素
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = filename
        document.body.appendChild(link)
        // 触发下载并移除链接元素
        link.click()
        link.remove()
        // 释放 URL 对象
        window.URL.revokeObjectURL(downloadUrl)
      } catch (error) {
        this.$message.error('导出失败')
      }
    },
    // 判断当前用户是否有待受理问题,若存在待受理问题则闪烁浏览器标签页标题
    async hasQuestion() {
      const queryData = {
        comp_user: this.staff_no
      }
      const { data: res } = await this.$http.post(this.model + '/question_list/', queryData)
      // 检查是否存在待受理状态的数据
      const hasPending = res.data.questions.some(question => question.status === 0)
      // 根据是否有待受理数据更新页面标题
      if (hasPending) {
        this.startTitleFlashing()
      } else {
        this.stopTitleFlashing()
      }
    },
    // 封装问题描述文本,若有多个描述，则拼接
    getTextContent(msgContent) {
      if (!msgContent || msgContent.length === 0) {
        return '' // 如果msgContent为空或没有数据，返回空字符串
      }
      return msgContent
        .filter(item => item.media_type === 'text')
        .map(item => `${item.media_content}`)
        .join('\n') // 可以根据需要调整拼接方式，例如空格分隔
    },
    // 封装问题描述的tooltip,若有多个描述，则换行显示
    getToolTipContent(msgContent) {
      if (!msgContent || msgContent.length === 0) {
        return '' // 如果msgContent为空或没有数据，返回空字符串
      }
      return msgContent
        .filter(item => item.media_type === 'text')
        .map(item => `${item.media_content}<br>`) // 为每一行加上<p>标签
        .join('\n') // 可以根据需要调整拼接方式，例如空格分隔
    },
    // tooltip换行显示
    formatTooltipContent(content) {
      return content && content.replace(/\n/g, '<br>')
    },
    // 判断列表是否有图片
    hasImage(contentList) {
      // 判断 contentList 是否是一个数组
      if (!Array.isArray(contentList)) {
        return false // 或者返回一个默认值
      }
      return contentList.some(content => content.media_type === 'image')
    },
    // 预览图片
    previewImages(contentList) {
      this.previewImagesList = contentList.filter(content => content.media_type === 'image')
      this.dialogVisible = true
      this.currentImageIndex = 0 // 每次打开重置为第一张
    },
    // 查看图片-上一张
    previousImage() {
      if (this.currentImageIndex > 0) {
        this.currentImageIndex--
      }
    },
    // 查看图片-下一张
    nextImage() {
      if (this.currentImageIndex < this.previewImagesList.length - 1) {
        this.currentImageIndex++
      }
    },
    // 键盘事件监听器,实现左右键切换图片
    handleKeydown(event) {
      // 检查事件目标是否为输入框（input 或 textarea）
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return // 如果是输入框或文本区域，直接返回，不处理事件
      }
      if (event.key === 'ArrowLeft') {
        this.previousImage()
        event.preventDefault() // 阻止默认行为
        event.stopPropagation() // 阻止事件冒泡
      } else if (event.key === 'ArrowRight') {
        this.nextImage()
        event.preventDefault()
        event.stopPropagation()
      }
    },
    // 开始编辑
    startEdit(row, column) {
      if (column === 'comp_user') {
        // 获取原始数据,保证取消编辑时数据能恢复
        this.originalData = {
          comp_user: row.comp_user,
          comp_user_name: row.comp_user_name
        }
        // 清空当前选择和搜索选项
        row.comp_user = ''
        this.options = []
        row.isEditingCompUser = true
        this.crop_id = row.crop_id
      } else if (column === 'project_type') {
        this.originalData = {
          project_type: row.project_type
        }
        row.isEditingProject = true
      } else if (column === 'status') {
        this.originalData = {
          status: row.status
        }
        row.isEditingStatus = true
      } else if (column === 'question_type') {
        this.originalData = {
          question_type: row.question_type
        }
        row.isEditingType = true
      } else if (column === 'belong_mer_code') {
        this.originalData = {
          belong_mer_code: row.belong_mer_code
        }
        row.isEditingMercode = true
      } else if (column === 'web_hook') {
        this.originalData = {
          web_hook: row.web_hook
        }
        row.isEditingWebhook = true
      }
    },
    // 保存编辑
    saveEdit: _.debounce(async function (row, column) {
      this.update_date.id = row.id
      if (column === 'comp_user') {
        if (row.comp_user === '' || row.comp_user == null) {
          this.$message.warning('请选择处理人')
          return
        }
        this.update_date.comp_user = row.comp_user
        this.crop_id = ''
        row.isEditingCompUser = false
      } else if (column === 'project_type') {
        if (row.project_type === '' || row.project_type == null) {
          this.$message.warning('请选择业务归属')
          return
        }
        this.update_date.project_type = row.project_type
        row.isEditingProject = false
      } else if (column === 'mer_type') {
        if (row.mer_type === '' || row.mer_type == null) {
          this.$message.warning('请选择ERP类型')
          return
        }
        this.update_date.mer_type = row.mer_type
        row.isEditingProject = false
      } else if (column === 'status') {
        if (row.status === '' || row.status == null) {
          this.$message.warning('请选择处理状态')
          return
        }
        this.update_date.status = row.status
        row.isEditingStatus = false
      } else if (column === 'question_type') {
        if (row.question_type === '' || row.question_type == null) {
          this.$message.warning('请选择问题类型')
          return
        }
        // 如果是 BUG 类型，打开弹窗
        if (row.question_type === 2) {
          this.$set(row, 'tempBugUrl', row.bug_url || '') // 临时存储 bug_url
          this.bugURLlDialogVisible = true // 打开弹窗
          this.bugURLForm.id = row.id // 保存当前行
          this.row = row
          // row.isEditingType = false
          return // 不立即保存，等待弹窗操作
        }
        // 非 BUG 类型，直接保存
        this.update_date.question_type = row.question_type
        row.isEditingType = false
      } else if (column === 'belong_mer_code') {
        if (row.belong_mer_code === '' || row.belong_mer_code == null) {
          this.$message.warning('请输入商户编码')
          return
        }
        this.update_date.belong_mer_code = row.belong_mer_code
        row.isEditingMercode = false
      }
      const { data: res } = await this.$http.post(this.model + '/question_update/', this.update_date)
      if (res.meta.status !== 200) return this.$message.error('编辑失败【' + res.meta.msg + '】')
      this.$message.success(res.meta.msg)
      await this.getQuestionList()
      this.update_date = {}
      if (column === 'comp_user') {
        // 更新表格数据
        const selectedOption = this.options.find(opt => opt.value === row.comp_user)
        if (selectedOption) {
          row.comp_user_name = selectedOption.label.split('(')[0].trim()
        }
      }
    },
    500,
    { leading: false, trailing: true }
    ),
    // 更新复盘信息接口
    async handleReviewOrValuableChange(visible) {
      if (!visible && (this.originalAnswerForm.is_review !== this.answerForm.is_review || this.originalAnswerForm.is_valuable !== this.answerForm.is_valuable)) {
        const updateDate = {}
        updateDate.msg_id = this.answerForm.msg_id
        updateDate.is_review = this.answerForm.is_review
        updateDate.is_valuable = this.answerForm.is_valuable
        const { data: res } = await this.$http.post(this.model + '/question_review_update/', updateDate)
        if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
        this.$message.success('更新成功')
        this.originalAnswerForm.is_review = this.answerForm.is_review
        this.originalAnswerForm.is_valuable = this.answerForm.is_valuable
        await this.getQuestionList()
      }
    },
    // 实时更新问题类型方法
    async handleQuestionTypeChange(visible) {
      if (!visible && this.originalAnswerForm.question_type !== this.answerForm.question_type && this.answerForm.question_type !== 2) {
        const updateDate = {}
        updateDate.id = this.answerForm.id
        updateDate.question_type = this.answerForm.question_type
        const { data: res } = await this.$http.post(this.model + '/question_update/', updateDate)
        if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
        this.$message.success('更新成功')
        this.originalAnswerForm.question_type = this.answerForm.question_type
        await this.getQuestionList()
      }
    },
    // 保存列表页维护的BUG链接
    saveBugURL() {
      this.$refs.bugURLFormRef.validate(async (valid) => {
        if (valid) {
          this.update_date.question_id = this.bugURLForm.id
          this.update_date.bug_url = this.bugURLForm.bug_url
          this.update_date.is_push = false
          this.update_date.question_type = 2
          const { data: res } = await this.$http.post(this.model + '/answer_update/', this.update_date)
          if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
          this.$message.success('维护成功')
          this.bugURLlDialogVisible = false
          this.bugURLForm = {}
          this.row.isEditingType = false
          await this.getQuestionList()
        }
      })
    },
    // 编辑是否需要更新知识库弹框
    async editKnowledgeForm() {
      const { data: res } = await this.$http.get(this.model + '/question_detail/', { params: { msg_id: this.answerForm.msg_id } })
      const objString = JSON.stringify(res.data.questions)
      const question = JSON.parse(objString)
      this.knowledge = question.knowledge
      // 打开弹框
      this.knowledgeDialogVisible = true
      this.knowledgeForm = {
        msg_id: this.answerForm.msg_id,
        question_title: this.knowledge.question_title,
        question_answer: this.knowledge.question_answer
      }
    },
    // 监听是否需要更新知识库选项更新时处理
    handleKnowledgeChange(val) {
      if (val === 1) {
        // 打开弹框
        this.knowledgeDialogVisible = true
        // 可以在这里初始化表单数据
        if (this.answerForm.knowledge && Object.keys(this.answerForm.knowledge).length > 0) {
          this.knowledgeForm = {
            msg_id: this.answerForm.msg_id,
            question_title: this.answerForm.knowledge.question_title,
            question_answer: this.answerForm.knowledge.question_answer
          }
        } else {
          this.knowledgeForm = {
            msg_id: this.answerForm.msg_id,
            question_title: this.getTextContent(this.answerForm.msg_content),
            question_answer: this.answerForm.answers.question_answer_manual
          }
        }
      } else {
        this.answerForm.is_knowledge = 0
      }
    },
    async handleSaveKnowledge(val) {
      this.$refs.knowledgeFormRef.validate(async (valid) => {
        if (valid) {
          this.knowledgeForm.type = '1'
          const { data: res } = await this.$http.post(this.model + '/knowledge_update/', this.knowledgeForm)
          if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
          this.originalAnswerForm.is_knowledge = this.answerForm.is_knowledge = 1
          this.$message.success('编辑成功')
          this.knowledgeDialogVisible = false
          this.knowledgeForm = {}
        }
      })
    },
    // 取消编辑
    cancelEdit(row, column) {
      if (column === 'comp_user') {
        row.isEditingCompUser = false
        row.comp_user = this.originalData.comp_user
        row.comp_user_name = this.originalData.comp_user_name
        this.crop_id = ''
      } else if (column === 'project_type') {
        row.project_type = this.originalData.project_type
        row.isEditingProject = false
      } else if (column === 'status') {
        row.status = this.originalData.status
        row.isEditingStatus = false
      } else if (column === 'question_type') {
        row.question_type = this.originalData.question_type
        row.isEditingType = false
      } else if (column === 'belong_mer_code') {
        row.belong_mer_code = this.originalData.belong_mer_code
        row.isEditingMercode = false
      } else if (column === 'web_hook') {
        row.web_hook = this.originalData.web_hook
        row.isEditingWebhook = false
      }
    },
    // 列表编辑处理人,通过接口获取处理人信息
    async handleSearch(query) {
      if (!this.crop_id && this.answerForm) {
        this.crop_id = this.answerForm.crop_id
      }
      if (!query) {
        this.options = [] // 清空选项
        return
      }
      this.loading = true
      try {
        // 调用接口
        const { data } = await this.$http.get(this.model + '/get_emp_info/', {
          params: { empname: query, crop_id: this.crop_id }
        })

        // 格式化接口数据到 options
        this.options = data.data.map(item => ({
          value: item.emp_account,
          label: item.emp_name + '(' + item.emp_account + ')'
        }))
      } catch (err) {
        console.log('无搜索结果')
      } finally {
        this.loading = false
      }
    },
    handleUserChange(selectedValue) {
      const selectedOption = this.options.find(opt => opt.value === selectedValue)
      this.comp_name = selectedOption?.label || ''
    },
    // 编辑问题答复
    async editAnswser(msgId, rowNum = null) {
      this.currentQuestionMsgId = msgId
      this.currentIndex = this.questionMsgIds.indexOf(msgId)
      const { data: res } = await this.$http.get(this.model + '/question_detail/', { params: { msg_id: msgId } })
      if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
      const objString = JSON.stringify(res.data.questions)
      this.answerForm = JSON.parse(objString)
      this.answerForm.file_url = []
      this.originalAnswerForm = JSON.parse(objString)
      this.originalIsKnowledge = this.answerForm.is_knowledge
      if (this.answerForm.attachment) {
        this.fileList = (this.answerForm.attachment || []).map(item => {
          return {
            url: item,
            name: item.split('/').pop(),
            status: 'success' // 设置文件状态为成功
          }
        })
        this.answerForm.file_url = this.answerForm.attachment
      }
      this.editDialogVisible = true
      // 调用更新已读状态接口
      if (this.answerForm.have_append && rowNum !== null) {
        const { data: res } = await this.$http.post(this.model + '/read_status_update/', { msg_id: msgId })
        if (res.meta.status === 200) {
          // 调用成功将列表中对应行数的have_append字段设置为false
          this.questionList[rowNum].have_append = false
        }
      }
      await this.handleSearch(this.answerForm.comp_user)
      return true
    },
    // 上一条
    goToPrevQuestion() {
      if (!this.isFirst) {
        this.$loading({
          lock: true,
          text: '加载中,请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        this.currentIndex--
        const prevMsgId = this.questionMsgIds[this.currentIndex]
        if (this.editAnswser(prevMsgId)) {
          this.$loading().close()
        }
      }
    },
    // 下一条
    goToNextQuestion() {
      if (!this.isLast) {
        this.$loading({
          lock: true,
          text: '加载中,请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        this.currentIndex++
        const nextMsgId = this.questionMsgIds[this.currentIndex]
        if (this.editAnswser(nextMsgId)) {
          this.$loading().close()
        }
      }
    },
    // 关闭编辑答复弹框的回调
    closeEditAnswser() {
      this.editDialogVisible = false
      this.answerForm = {
        answers: {
          question_answer_manual: ''
        }
      }
      this.originalAnswerForm = {
        answers: {
          question_answer_manual: ''
        }
      }
      this.fileList = []
      this.isRestassured = false // 关闭编辑页，将安心告知按钮置为可点击
    },
    // 安心告知
    Restassured: _.debounce(async function () {
      const id = this.answerForm.id
      this.$loading({
        lock: true,
        text: '消息推送中,请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const { data: res } = await this.$http.get(this.model + '/robot_send_msg/', { params: { msg_id: this.answerForm.msg_id } })
      if (res.meta.status !== 200) {
        this.$loading().close()
        return this.$message.error('安心告知消息推送失败')
      }
      this.isRestassured = true
      this.$loading().close()
      this.$message.success('安心告知消息推送成功')
    },
    500,
    { leading: false, trailing: true }
    ),
    // 提交问题答复
    submitAnswer: _.debounce(async function (isPush) {
      this.$refs.answerFormRef.validate(async valid => {
        if (!valid) return false
        this.update_date.is_push = isPush
        // this.update_date.file_url = this.answerForm.attachment
        let isUpdate = 0
        if (this.originalAnswerForm.belong_mer_code !== this.answerForm.belong_mer_code) {
          this.update_date.belong_mer_code = this.answerForm.belong_mer_code
          isUpdate = 1
        }
        if (this.originalAnswerForm.comp_user !== this.answerForm.comp_user) {
          this.update_date.comp_user = this.answerForm.comp_user
          isUpdate = 1
        }
        if (this.originalAnswerForm.project_type !== this.answerForm.project_type) {
          this.update_date.project_type = this.answerForm.project_type
          isUpdate = 1
        }
        if (this.originalAnswerForm.question_type !== this.answerForm.question_type) {
          this.update_date.question_type = this.answerForm.question_type
          isUpdate = 1
        }
        if (this.originalAnswerForm.bug_url !== this.answerForm.bug_url) {
          this.update_date.bug_url = this.answerForm.bug_url
          isUpdate = 1
        }
        if (this.originalAnswerForm.mer_type !== this.answerForm.mer_type) {
          this.update_date.mer_type = this.answerForm.mer_type
          isUpdate = 1
        }
        // if (this.originalAnswerForm.status !== this.answerForm.status) {
        //   this.update_date.status = this.answerForm.status
        //   isUpdate = 1
        // }
        if (this.originalAnswerForm.answers.question_answer_manual !== this.answerForm.answers.question_answer_manual) {
          this.update_date.question_answer_manual = this.answerForm.answers.question_answer_manual
          this.update_date.answer_id = this.answerForm.answers.id
          isUpdate = 1
        }
        if (JSON.stringify(this.originalAnswerForm.attachment) !== JSON.stringify(this.answerForm.file_url)) {
          this.update_date.file_url = this.answerForm.file_url
          isUpdate = 1
        }
        if ((this.originalAnswerForm.is_knowledge !== this.answerForm.is_knowledge) && !this.answerForm.is_knowledge) {
          const { data: res } = await this.$http.post(this.model + '/knowledge_update/', { msg_id: this.answerForm.msg_id, type: '0' })
          if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
          this.originalAnswerForm.is_knowledge = this.answerForm.is_knowledge = 0
          if (isUpdate !== 1) {
            this.$message.success('编辑成功')
          }
        }
        if (isUpdate === 1 || (isPush && !this.isPushing)) {
          if (isPush) {
            this.$loading({
              lock: true,
              text: '消息推送中,请稍后',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            })
          }
          this.isPushing = true
          this.update_date.question_id = this.answerForm.id
          const { data: res } = await this.$http.post(this.model + '/answer_update/', this.update_date)
          if (res.meta.status !== 200) return this.$message.error('操作失败')
          this.$message.success('操作成功')
          if (isPush) {
            this.$loading().close()
          }
          this.isPushing = false
        }
        this.answerForm = {
          answers: {
            question_answer_manual: ''
          }
        }
        this.update_date = {}
        this.editDialogVisible = false
        await this.getQuestionList()
        this.fileList = []
        return true
      })
    },
    500,
    { leading: false, trailing: true }
    ),
    // 判断 msg_content 是否包含 text 类型
    isTextContent(msgContent) {
      if (!Array.isArray(msgContent) || !msgContent.length) return false
      return msgContent.some(item => item.media_type === 'text')
    },
    // 去处理问题答复
    async handleQuestion(msgId, rowNum = null) {
      const { data: res } = await this.$http.get(this.model + '/question_detail/', { params: { msg_id: msgId } })
      const objString = JSON.stringify(res.data.questions)
      this.handleQuestionForm = JSON.parse(objString)
      // const objString = JSON.stringify(row)
      // this.handleQuestionForm = JSON.parse(objString)
      this.originalAnswerForm = JSON.parse(objString)
      this.handleQuestionDialogVisible = true
      await this.handleSearch(this.handleQuestionForm.comp_user)
    },
    // 调用获取值班人员接口
    async onProjectTypeChange(projectType) {
      this.loading = true
      try {
        // 调用接口，根据 project_type 获取处理人选项
        const { data } = await this.$http.get(this.model + '/get_duty_info/', {
          params: { project_type: projectType, room_id: this.answerForm.room_id }
        })
        if (data.meta.status === 200 && data.data.length > 0) {
          // 假设接口返回的是一个数组格式的处理人列表
          this.answerForm.comp_user = data.data[0].duty_account
          this.handleSearch(this.answerForm.comp_user)
        } else if (data.meta.status !== 200) {
          this.$message.error('值班人员接口查询失败')
        }
      } catch (error) {
        this.$message.error('接口请求失败')
      } finally {
        this.loading = false
      }
    },
    // 编辑弹框解绑消息
    unbindMsg: _.debounce(async function (subMsgId, msgId) {
      const pushMessage = {
        msgId: msgId,
        subMsgId: subMsgId
      }
      const { data: res } = await this.$http.get(this.model + '/unbind_msg/', { params: { parent_msg_id: msgId, sub_msg_id: subMsgId } })
      if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
      this.$message.success('解绑成功')
      await this.editAnswser(msgId)
    },
    500,
    { leading: false, trailing: true }
    ),
    // 转出问题
    transferAnswer: _.debounce(async function () {
      const transferData = {
        question_id: this.answerForm.id,
        answer_id: this.answerForm.answers.id,
        question_answer_manual: this.answerForm.answers.question_answer_manual,
        comp_user: this.answerForm.comp_user,
        file_url: this.answerForm.file_url
      }
      if (this.originalAnswerForm.belong_mer_code !== this.answerForm.belong_mer_code) {
        transferData.belong_mer_code = this.answerForm.belong_mer_code
      }
      if (this.originalAnswerForm.project_type !== this.answerForm.project_type) {
        transferData.project_type = this.answerForm.project_type
      }
      if (this.originalAnswerForm.question_type !== this.answerForm.question_type) {
        transferData.question_type = this.answerForm.question_type
      }
      if (this.originalAnswerForm.mer_type !== this.answerForm.mer_type) {
        transferData.mer_type = this.answerForm.mer_type
      }
      if (this.originalAnswerForm.answers.question_answer_manual !== this.answerForm.answers.question_answer_manual) {
        transferData.question_answer_manual = this.answerForm.answers.question_answer_manual
      }
      if (JSON.stringify(this.originalAnswerForm.attachment) !== JSON.stringify(this.answerForm.file_url)) {
        transferData.file_url = this.answerForm.file_url
      }
      this.$confirm(
        '该问题会交转给' + this.comp_name + '，并同时往售后群推送回复消息',
        '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(async () => {
          const { data: res } = await this.$http.post(this.model + '/question_transfer/', transferData)
          if (res.meta.status === 200) {
            this.$message({
              type: 'success',
              message: '问题转出成功!'
            })
            this.answerForm = {
              answers: {
                question_answer_manual: ''
              }
            }
            this.update_date = {}
            this.editDialogVisible = false
            await this.getQuestionList()
            this.fileList = []
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消问题转出'
          })
        })
    },
    500,
    { leading: false, trailing: true }
    ),
    // 提交去处理编辑结果
    submitHandleQuestion: _.debounce(async function () {
      this.$refs.handleQuestionFormRef.validate(async valid => {
        if (!valid) return false
        let isUpdate = 0
        if (this.originalAnswerForm.belong_mer_code !== this.handleQuestionForm.belong_mer_code) {
          this.update_date.belong_mer_code = this.handleQuestionForm.belong_mer_code
          isUpdate = 1
        }
        if (this.originalAnswerForm.comp_user !== this.handleQuestionForm.comp_user) {
          this.update_date.comp_user = this.handleQuestionForm.comp_user
          isUpdate = 1
        }
        if (this.originalAnswerForm.project_type !== this.handleQuestionForm.project_type) {
          this.update_date.project_type = this.handleQuestionForm.project_type
          isUpdate = 1
        }
        if (this.originalAnswerForm.question_type !== this.handleQuestionForm.question_type) {
          this.update_date.question_type = this.handleQuestionForm.question_type
          isUpdate = 1
        }
        if (this.originalAnswerForm.bug_url !== this.handleQuestionForm.bug_url) {
          this.update_date.bug_url = this.handleQuestionForm.bug_url
          isUpdate = 1
        }
        if (this.originalAnswerForm.answers.question_answer_manual !== this.handleQuestionForm.answers.question_answer_manual) {
          this.update_date.question_answer_manual = this.handleQuestionForm.answers.question_answer_manual
          this.update_date.answer_id = this.handleQuestionForm.answers.id
          isUpdate = 1
        }
        if (this.originalAnswerForm.mer_type !== this.handleQuestionForm.mer_type) {
          this.update_date.mer_type = this.handleQuestionForm.mer_type
          isUpdate = 1
        }
        this.update_date.comp_time = this.handleQuestionForm.comp_time
        if (isUpdate === 1) {
          this.update_date.question_id = this.handleQuestionForm.id
          const { data: res } = await this.$http.post(this.model + '/answer_update/', this.update_date)
          if (res.meta.status !== 200) return this.$message.error('处理失败')
          this.$message.success('处理成功')
        }
        this.handleQuestionForm = {
          answers: {
            question_answer_manual: ''
          }
        }
        this.update_date = {}
        this.handleQuestionDialogVisible = false
        await this.getQuestionList()
      })
    },
    500,
    { leading: false, trailing: true }
    ),
    // 将消息推送至群
    pushMessage: _.debounce(async function (msgId, roomId) {
      const pushMessage = {
        msgId: msgId,
        roomId: roomId
      }
      const { data: res } = await this.$http.post(this.model + '/question_push_message/', pushMessage)
      if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
      this.$message.success('消息推送成功')
      await this.getQuestionList()
      return true // 推送成功时返回 true
    },
    500,
    { leading: false, trailing: true }
    ),
    // // 保存并推送回答
    // submitAndPushAnswer: _.debounce(async function () {
    //   const msgId = this.answerForm.msg_id
    //   const roomId = this.answerForm.room_id
    //   // 等待 submitAnswer 成功后再推送消息
    //   await this.submitAnswer()
    //   await this.pushMessage(msgId, roomId)
    // },
    // 500,
    // { leading: false, trailing: true }
    // ),
    // 删除问题
    deleteQuestion: _.debounce(async function () {
      this.$refs.deleteQuestionFormRef.validate(async valid => {
        if (!valid) return false
        this.deleteQuestionForm.type = 1
        this.deleteQuestionForm.id = this.answerForm.id
        const { data: res } = await this.$http.post(this.model + '/question_update_status/', this.deleteQuestionForm)
        if (res.meta.status !== 200) return this.$message.error('问题删除失败')
        this.$message.success('问题删除成功')
        this.deleteQuestionForm = {}
        this.deleteDialogVisible = false
        await this.getQuestionList()
        await this.getQuestionIds()
        // 处理编辑弹框打开时删除后的逻辑
        if (this.editDialogVisible) {
          if (this.questionMsgIds.length === 0) {
            // 如果没有剩余问题，关闭编辑弹框
            this.editDialogVisible = false
            return
          }
          // 确定要显示的下一个问题
          if (this.currentIndex >= this.questionMsgIds.length) {
            // 如果删除的是最后一个问题，切换到前一个问题
            this.currentIndex = this.questionMsgIds.length - 1
          }
          // 更新 currentQuestionMsgId 并加载下一个问题
          this.currentQuestionMsgId = this.questionMsgIds[this.currentIndex]
          this.$loading({
            lock: true,
            text: '加载中,请稍后',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          if (this.editAnswser(this.currentQuestionMsgId)) {
            this.$loading().close()
          }
        }
      })
    },
    500,
    { leading: false, trailing: true }
    ),
    // 设置为无需处理问题
    undealQuestion: _.debounce(async function () {
      this.$refs.undealQuestionFormRef.validate(async valid => {
        if (!valid) return false
        this.undealQuestionForm.type = 4
        this.undealQuestionForm.id = this.answerForm.id
        const { data: res } = await this.$http.post(this.model + '/question_update_status/', this.undealQuestionForm)
        if (res.meta.status !== 200) return this.$message.error('设置失败')
        this.$message.success('设置成功')
        this.undealQuestionForm = {}
        this.undealQuestionVisible = false
        await this.getQuestionList()
      })
    },
    500,
    { leading: false, trailing: true }
    ),
    // 提交人关闭问题
    closeQuestion: _.debounce(async function (id) {
      const { data: res } = await this.$http.post(this.model + '/question_update_status/', { id: id, type: '3' })
      if (res.meta.status !== 200) return this.$message.error('问题关闭失败')
      this.$message.success('问题关闭成功')
      await this.getQuestionList()
    },
    500,
    { leading: false, trailing: true }
    ),
    // 获取句子机器人状态
    async getRobotStatus() {
      this.robotStatusLoading = true
      try {
        const { data: res } = await this.$http.get(this.model + '/get_robot_status/')
        if (res.meta.status !== 200) {
          this.$message.error('句子机器人状态获取失败')
          return
        }
        this.robotStatus = res.data
      } catch (error) {
        this.$message.error('请求失败')
      } finally {
        this.robotStatusLoading = false
      }
    },
    // 获取群AI配置
    async AIConfig() {
      const { data: res } = await this.$http.get(this.model + '/get_room_config/')
      if (res.meta.status !== 200) return this.$message.error('配置获取失败')
      this.AIConfigList = res.data
      this.AIConfigDialogVisible = true
      this.AIConfigList = res.data.map(item => ({
        ...item,
        isEditingWebhook: false
      }))
    },
    // 更新群AI配置
    handleAIConfigChange: _.debounce(async function (row) {
      const AIConfig = {
        id: row.id,
        reply_toggle: row.reply_toggle
      }
      const { data: res } = await this.$http.post(this.model + '/room_update/', AIConfig)
      if (res.meta.status !== 200) return this.$message.error('配置更新失败')
      this.$message.success('配置更新成功')
    },
    500,
    { leading: false, trailing: true }
    ),
    // 更新群是否可见配置
    handleGroupVisible: _.debounce(async function (row) {
      const groupDisplay = {
        id: row.id,
        is_display: row.is_display
      }
      const { data: res } = await this.$http.post(this.model + '/room_update/', groupDisplay)
      if (res.meta.status !== 200) return this.$message.error('配置更新失败')
      this.$message.success('配置更新成功')
    },
    500,
    { leading: false, trailing: true }
    ),
    // 更新群消息是否识别配置
    handleredictionMessage: _.debounce(async function (row) {
      const groupDisplay = {
        id: row.id,
        prediction_message_toggle: row.prediction_message_toggle
      }
      const { data: res } = await this.$http.post(this.model + '/room_update/', groupDisplay)
      if (res.meta.status !== 200) return this.$message.error('配置更新失败')
      this.$message.success('配置更新成功')
    },
    500,
    { leading: false, trailing: true }
    ),
    // 更新群AI配置WEBHOOK地址
    saveAIconfig: _.debounce(async function (row) {
      const AIConfig = {
        id: row.id,
        web_hook: row.web_hook
      }
      const { data: res } = await this.$http.post(this.model + '/room_update/', AIConfig)
      if (res.meta.status !== 200) return this.$message.error('群机器人地址更新失败')
      this.$message.success('群机器人地址更新成功')
      row.isEditingWebhook = false
    },
    500,
    { leading: false, trailing: true }
    ),
    // 设置查询条件默认值,当用户角色为开发或测试时,则将处理人赋默认值;若用户为大区,则将提交人赋默认值,其他条件则不做处理
    default_date() {
      if (this.role_id === '1' || this.role_id === '9' || this.role_id === '19') {
        this.queryInfo.comp_user = this.chinese_name
        this.queryDate = this.getLastMonthRange()
      } else if (this.role_id === '0') {
        // this.queryDate = this.getLastFridayAndThisThursday()
        this.queryDate = this.getLastMonthRange()
      }
      this.queryInfo.start_date = this.queryDate[0]
      this.queryInfo.end_date = this.queryDate[1]
    },
    // 获取最近一个月的时间范围
    getLastMonthRange() {
      const now = new Date()
      const lastMonth = new Date()
      lastMonth.setMonth(now.getMonth() - 1) // 向前推一个月
      // 格式化为 yyyy-MM-dd 格式
      const formatDate = (date) => {
        const year = date.getFullYear()
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        const day = date.getDate().toString().padStart(2, '0')
        return `${year}-${month}-${day}`
      }
      return [formatDate(lastMonth), formatDate(now)]
    },
    getLastFridayAndThisThursday() {
      const today = new Date()
      // 计算本周四
      const thisThursday = new Date(today)
      thisThursday.setDate(today.getDate() - today.getDay() + 4)
      // 计算本周五
      const thisFriday = new Date(today)
      thisFriday.setDate(thisThursday.getDate() + 1)
      // 计算上周五
      const lastFriday = new Date(today)
      lastFriday.setDate(thisFriday.getDate() - 7)
      // 格式化日期为 YYYY-MM-DD
      const formatDate = (date) => {
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}`
      }
      return [formatDate(lastFriday), formatDate(thisThursday)]
    },
    // 校验处理时间
    validateCompTime(rule, value, callback) {
      const createTime = this.answerForm.create_time
      // 转换时间格式，确保比较正确
      const compTime = new Date(value).getTime()
      const createTimeMs = new Date(createTime).getTime()

      if (compTime <= createTimeMs) {
        callback(new Error('处理时间必须大于创建时间'))
      } else {
        callback()
      }
    },
    // 启动浏览器标签栏闪烁
    startTitleFlashing() {
      if (this.isFlashing) return // 如果已经在闪烁则不重复启动
      this.isFlashing = true
      // 设置闪烁的标题内容
      const originalTitle = document.title
      const flashingTitle = '来问题了'
      // 每250毫秒切换一次标题，达到闪烁效果
      this.flashIntervalId = setInterval(() => {
        document.title = document.title === originalTitle ? flashingTitle : originalTitle
      }, 250) // 250ms 切换一次标题
    },
    // 停止浏览器标签栏闪烁
    stopTitleFlashing() {
      // 停止闪烁，恢复原始标题
      clearInterval(this.flashIntervalId)
      document.title = '海典测试平台' // 恢复为默认标题
      this.isFlashing = false
    },
    // 上传成功回调
    handleUploadSuccess(response, file, fileList) {
      if (response.meta.status === 200) {
        this.$set(this.answerForm, 'file_url', [
          ...(this.answerForm.file_url || []),
          response.data.fileUrl
        ])
        // 更新fileList中的URL为实际的服务器URL
        const index = this.fileList.findIndex(f => f.uid === file.uid)
        if (index !== -1) {
          this.$set(this.fileList, index, {
            ...this.fileList[index],
            url: response.data.fileUrl,
            status: 'success',
            percentage: 100 // 上传成功设置为100%
          })
        }
      }
    },
    handFileList(file, fileList) {
      this.fileList = fileList
    },
    // 删除文件回调
    handleRemove(file) {
      const index = this.fileList.indexOf(file)
      if (index > -1) {
        this.answerForm.file_url.splice(index, 1)
        this.fileList.splice(index, 1)
      }
      // 更新文件列表
      this.fileList = this.fileList.filter(f => f.uid !== file.uid)
    },
    // 文件超出限制回调
    handleExceed(files, fileList) {
      this.$message.warning('最多只允许上传3张图片')
    },
    // 预览图片
    handlePreview() {
      const previewList = this.answerForm.file_url.map(fileUrl => {
        return {
          media_content: fileUrl,
          media_type: 'image'
        }
      })
      this.previewImages(previewList)
      // window.open(file.url)
    },
    // 上传前校验：限制为 PNG 和 JPG 格式，并检查大小
    beforeUpload(file) {
      // 只允许的 MIME 类型
      const validTypes = ['image/png', 'image/jpeg']
      const isValidType = validTypes.includes(file.type)

      // 只允许的扩展名
      const fileName = file.name.toLowerCase()
      const validExtensions = ['.png', '.jpeg', '.jpg']
      const isValidExtension = validExtensions.some(ext => fileName.endsWith(ext))

      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isValidType) {
        this.$message.error('文件格式无效，只能上传 PNG 或 JPG 格式的图片!')
        return false
      }
      if (!isValidExtension) {
        this.$message.error('文件扩展名必须为 .png、.jpeg 或 .jpg!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('图片大小不能超过5MB!')
        return false
      }

      return isValidType && isValidExtension && isLt5M
    },
    // 操作日志查看图片
    handleLogPicPreview(picList) {
      const previewList = picList.map(fileUrl => {
        return {
          media_content: fileUrl,
          media_type: 'image'
        }
      })
      this.previewImages(previewList)
      // window.open(file.url)
    },
    // 处理粘贴事件
    async handlePastePic(event) {
      // 获取剪贴板数据，兼容不同浏览器的事件对象
      const items = (event.clipboardData || event.originalEvent.clipboardData).items
      // 用于存储检测到的图片文件
      const imageFiles = []
      // 遍历剪贴板中的所有项
      for (const item of items) {
        // 检查是否为图片类型（MIME类型以'image'开头）
        if (item.type.indexOf('image') !== -1) {
          // 将剪贴板项转换为文件对象
          const file = item.getAsFile()
          // 在上传前校验文件（大小、格式等），beforeUpload 返回布尔值
          const isValid = await this.beforeUpload(file)
          if (isValid) {
            // 如果文件有效，添加到图片文件数组
            imageFiles.push(file)
          }
        }
      }
      // 如果检测到有效的图片文件
      if (imageFiles.length > 0) {
        event.preventDefault() // 阻止默认粘贴行为
        // 调用上传方法处理这些图片文件
        this.uploadPastedImages(imageFiles)
      }
    },
    // 处理粘贴的图片并自动上传
    uploadPastedImages(files) {
      // 遍历所有图片文件
      files.forEach(async (file, index) => {
        // 检查当前文件列表是否已达到上限（3个）
        if (this.fileList.length >= 3) {
          // 如果超过限制，触发超出处理函数
          this.handleExceed()
          return
        }

        // 创建临时文件对象，用于在 el-upload 组件中显示
        const tempFile = {
          name: file.name || `pasted-image-${index}.png`, // 提供默认文件名
          url: URL.createObjectURL(file),
          raw: file,
          status: 'uploading',
          uid: Date.now() + index,
          percentage: 0 // 初始化进度为0
        }

        // 使用 Vue.set 添加文件到 fileList，确保响应式更新
        this.$set(this.fileList, this.fileList.length, tempFile)

        // 自动上传逻辑
        try {
          // 创建 FormData 对象用于文件上传
          const formData = new FormData()
          // 将文件添加到 FormData，键名为 'file'
          formData.append('file', file)
          // 调用上传文件方法
          const response = await this.$http.post(this.uploadPath, formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent) => {
              // 更新上传进度
              const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
              const fileIndex = this.fileList.findIndex(f => f.uid === tempFile.uid)
              if (fileIndex !== -1) {
                this.$set(this.fileList[fileIndex], 'percentage', percent)
              }
            }
          })

          if (response.data.meta.status === 200) {
            // 调用成功处理函数，更新文件状态和URL
            this.handleUploadSuccess(response.data, tempFile, this.fileList)
          }
        } catch (error) {
          console.error('图片上传失败:', error)
          const fileIndex = this.fileList.findIndex(f => f.uid === tempFile.uid)
          if (fileIndex !== -1) {
            this.$set(this.fileList[fileIndex], 'status', 'failed')
            this.$set(this.fileList[fileIndex], 'percentage', 0)
          }
          this.$message.error('图片上传失败')
        }
      })
    },
    // 判断是否需要折叠文本
    shouldShowToggle(question) {
      const content = question.msg_content
      if (content) {
        const text = this.getTextContent(content)
        // 条件1：判断文本长度是否超过300个字符
        const isLongText = text.length > 300
        // 条件2：计算行数（换行符数量 + 1）
        const lineCount = (text.match(/\n/g) || []).length + 1
        const hasManyLines = lineCount > 3
        // 满足任意条件即需要折叠
        return isLongText || hasManyLines
      }
      return false
    }
  }
}
</script>

<style>
.text-collapsed {
  display: -webkit-box;
  -webkit-line-clamp: 3; /* 限制显示3行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.toggle-btn {
  margin-left: 5px;
  padding: 0 5px;
}
/* 鼠标悬浮效果 */
.el-table__body tr.el-table__row:hover > td,
.el-table__body tr.el-table__row--striped:hover > td {
  background-color: #ecf5ff !important;
}

/* 设置弹框背景透明 */
.transparent-dialog .el-dialog {
  background-color: rgba(255, 255, 255, 0.13); /* 半透明背景 */
  //border: 2px solid rgba(0, 0, 0, 0.5); /* 添加边框颜色 */
  box-shadow: none; /* 去掉阴影 */
  margin: 0; /* 去掉外边距 */
  position: fixed; /* 设为固定定位 */
  top: 0; /* 顶部为0 */
  ////left: 50%; /* 水平居中 */
  ////transform: translateX(-50%); /* 使其水平居中 */
  //height: 100%; /* 高度填满 */
  //overflow: auto; /* 如果内容超出，允许滚动 */
}
.transparent-dialog .el-dialog__header,
.transparent-dialog .el-dialog__body,
.transparent-dialog .el-dialog__footer {
  background-color: transparent; /* 内部元素背景透明 */
  margin: 0; /* 去掉内部元素的外边距 */
  padding: 0; /* 去掉内部元素的内边距 */
}

.disabled-button-wrapper {
  display: inline-block; /* 确保包裹层不影响布局 */
  margin-left: 10px;
}

/* 使 popover 样式与 tooltip 的 dark 主题一致 */
.dark-popover {
  background-color: #303133 !important;
  color: #fff !important;
  border: none !important;
}

/* 箭头颜色 */
.dark-popover[x-placement^=top] .popper__arrow::after {
  border-top-color: #303133 !important;
}

.dark-popover[x-placement^=bottom] .popper__arrow::after {
  border-bottom-color: #303133 !important;
}

.dark-popover[x-placement^=left] .popper__arrow::after {
  border-left-color: #303133 !important;
}

.dark-popover[x-placement^=right] .popper__arrow::after {
  border-right-color: #303133 !important;
}

</style>

<style scoped>
::v-deep .el-dialog__header {
    display: flex;
}

::v-deep .el-dialog__body {
    padding-top: 0px;
}

/* 斑马纹样式 */
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #f5f7fa; /* 浅蓝色 */
}

/* 默认行颜色 */
::v-deep .el-table__body tr td {
  background: white; /* 白色 */
}

/* 鼠标悬停颜色 */
::v-deep .el-table__body tr:hover > td {
  background: #e6f7ff !important; /* 悬停时的浅蓝色 */
}

/* 自定义 dot 样式 */
.custom-dot {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}

/* 使用 ::v-deep 穿透 scoped 限制 */
.item ::v-deep .el-badge__content.is-fixed.is-dot {
  right: 12px;
  top: 3px;
}

.cell-content:hover {
  color: #409EFF; /* Element Plus 蓝色 */
}

</style>
