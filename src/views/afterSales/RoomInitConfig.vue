<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>售后问题</el-breadcrumb-item>
      <el-breadcrumb-item>群初始化配置</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row>
        <el-table
          :data="roomList"
          stripe
          border
          max-height="600"
          style="width: 99.99%;"
          :header-cell-style="{
            backgroundColor: '#e6f7ff',  // 推荐使用Element默认背景色
            color: '#606266',           // 保持默认文字颜色
            fontSize: '12px',           // 适当加大字号
            fontWeight: 'bold'          // 加粗字体
          }">
          <el-table-column prop="room_id" label="群ID" min-width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="room_name" label="群名称" min-width="150" show-overflow-tooltip></el-table-column>
          <el-table-column prop="room_channel" label="群类型" min-width="60" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="scope.row.room_channel === 0">内部群</span>
              <span v-if="scope.row.room_channel === 1">外部群</span>
            </template>
          </el-table-column>
          <el-table-column v-if="user_name === 'liudawei'" prop="managerInfo" label="群管理员" min-width="200" show-overflow-tooltip>
            <template slot-scope="scope">
              <div v-if="scope.row.isEditingManager">
                <el-select
                  v-model="scope.row.wx_manager_no"
                  filterable
                  remote
                  multiple
                  :remote-method="handleSearch"
                  :loading="loading"
                  size="mini"
                  style="width: 200px;"
                  class="edit-input"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <i
                  class="el-icon-check"
                  style="cursor: pointer"
                  @click="saveAIconfig(scope.row)"
                ></i>
                <i
                  style="cursor: pointer"
                  class="el-icon-close"
                  @click="cancelEdit(scope.row, 'manager')"
                ></i>
              </div>
              <div v-else style="display: flex; align-items: center; width: 100%;">
                <div style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden;">
                  {{ formatManager('manager', scope.row.wx_managers) }}
                </div>
                <i
                  v-if="!scope.row.isEditingManager"
                  style="cursor: pointer; flex-shrink: 0; margin-left: 8px;"
                  class="el-icon-edit-outline"
                  @click="startEdit(scope.row, 'manager')"
                ></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="群是否可见" width="85" align="center">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.is_display"
                @change="handleGroupVisible(scope.row)"
                active-color="#13ce66"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="群消息识别" width="85" align="center">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.prediction_message_toggle"
                @change="handleRedictionMessage(scope.row)"
                active-color="#13ce66"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="全产品线获取答案" width="120" align="center">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.all_business_toggle"
                @change="handleAllBusiness(scope.row)"
                active-color="#13ce66"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="AI自动回复" width="85" align="center">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.reply_toggle"
                @change="handleAIConfigChange(scope.row)"
                active-color="#13ce66"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" min-width="80px" align="center">
            <template v-slot="slotProps">
              <el-button type="primary" size="mini" @click="handleDutyDialogOpen(slotProps.row)">配置值班人员</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-dialog
        title="值班人员配置"
        :visible.sync="dutyConfigVisible"
        width="40%"
        @close = "handleDutyDialogClose"
        append-to-body
        aria-modal="true"
        role="dialog"
      >
        <template>
          <el-table :data="dutyList" :border=true style="width: 99.99%;">
            <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
            <el-table-column prop="project_type" label="业务线" width="150" align="center">
              <template slot-scope="scope">
                <el-select
                v-model.number="scope.row.project_type"
                size="mini"
                filterable
                style="width: 120px;"
                :disabled="scope.row.project_type === 99"
                @change="handleProjectTypeChange(scope.row)"
                popper-class="centered-dropdown"
                >
                  <el-option
                    v-for="item in getAvailableProductList(scope.row)"
                    :key="item.product_id"
                    :label="item.product_name"
                    :value="item.product_id"
                    class="centered-option">
                  </el-option>
              </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="duty_user_no" label="值班人员" width="250px" align="center">
              <template slot-scope="scope">
                <div>
                  <el-select
                    v-model="scope.row.duty_user_no"
                    filterable
                    remote
                    :remote-method="handleSearch"
                    :loading="loading"
                    size="mini"
                    style="width: 200px;"
                    class="edit-input"
                    :placeholder="formatManager('duty', scope.row.dutyInfo) || '请选择值班人员'"
                    @focus="handleDutySelectFocus(scope.row)"
                    @change="handleDutyUserChange(scope.row)"
                    popper-class="centered-dropdown"
                  >
                    <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      class="centered-option"
                    ></el-option>
                  </el-select>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="80px" align="center">
              <template v-slot="slotProps">
                <!-- 当业务线或值班人员为空时，直接删除，不需要二次确认 -->
                <el-button
                  type="danger"
                  size="mini"
                  @click="deleteDutyUser(slotProps.row)"
                  :disabled="slotProps.row.project_type === 99"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 10px;">
            <div style="display: flex; justify-content: flex-start; margin-bottom: 10px;">
              <i class="el-icon-circle-plus-outline" @click="addDutyUser" style="cursor: pointer; font-size: 20px;"></i>
            </div>
            <div style="display: flex; justify-content: flex-end;">
              <el-button @click="handleDutyDialogClose" style="margin-right: 10px;">取 消</el-button>
              <el-button
                type="primary"
                @click="batchSaveDutyUsers"
                :disabled="!canBatchSave"
              >保存</el-button>
            </div>
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'question',
      productList: [
        {
          product_id: '',
          product_name: ''
        }
      ], // 业务线列表
      roomList: [], // 售后群列表
      originalRoomList: [], // 用于保存原始数据
      user_name: window.localStorage.getItem('user_name'),
      chinese_name: window.localStorage.getItem('chinese_name'),
      staff_no: window.localStorage.getItem('staff_no'),
      role_id: window.localStorage.getItem('role_id'),
      comp_name: '', // 处理人姓名工号,格式为 姓名(工号)
      isEditingManager: false, // 是否正在编辑群管理员
      isEditingDutyUser: false, // 是否正在编辑值班人员
      originalManager: '', // 保存原始的群管理员值，用于取消编辑时恢复
      dutyList: [], // 值班人员列表
      room_id: '', // 售后群id
      originalDutyList: [], // 用于保存原始的值班人员列表，用于取消编辑时恢复
      loading: false, // 加载状态
      options: [], // 下拉选项
      dutyConfigVisible: false, // 值班人员配置弹窗是否显示
      dutyRowsChanged: {} // 记录哪些行发生了变化
    }
  },
  mounted() {
    this.getRoomList()
    this.get_product_info()
  },
  computed: {
    productMap() {
      const map = {}
      this.productList.forEach(item => {
        map[item.product_id] = item.product_name
      })
      return map
    },
    // 检查是否可以批量保存
    canBatchSave() {
      // 如果dutyList不存在，不能保存
      if (!this.dutyList) {
        return false
      }

      // 检查是否有任何行发生了变化或者列表为空（允许保存空列表）
      const hasChanges = Object.values(this.dutyRowsChanged).some(changed => changed === true)

      // 如果列表为空，允许保存
      if (this.dutyList.length === 0) {
        return true
      }

      // 检查是否所有行都有业务线和值班人员
      const allRowsValid = this.dutyList.every(row => {
        return (row.project_type !== undefined && row.project_type !== '') &&
               (row.duty_user_no !== undefined && row.duty_user_no !== '')
      })

      return hasChanges && allRowsValid
    }
  },
  methods: {
    // 获取业务线
    async get_product_info() {
      const { data: res } = await this.$http.get(this.model + '/get_product_info/')
      if (res.meta.status !== 200) return this.$message.error('获取产品线失败')
      this.productList = res.data
    },
    // 获取群配置列表
    async getRoomList() {
      // const managerNo = ''
      let managerNo = ''
      if (this.user_name !== 'liudawei') {
        managerNo = this.staff_no
      }
      const { data: res } = await this.$http.get(this.model + '/get_room_config/', { params: { wx_manager_no: managerNo } })
      if (res.meta.status !== 200) return this.$message.error('群列表获取失败')
      this.roomList = res.data.map(item => {
        // 处理 managerInfo
        let managerInfo = ''
        if (item.wx_managers && item.wx_managers.length > 0) {
          managerInfo = this.formatManager('manager', item.wx_managers)
        }
        return {
          ...item,
          isEditingManager: false,
          managerInfo: managerInfo // 添加处理后的 managerInfo 字段
        }
      })
    },
    // 更新群AI配置群管理员
    async saveAIconfig(row) {
      const AIConfig = {
        id: row.id,
        wx_manager_no: row.wx_manager_no
      }
      const { data: res } = await this.$http.post(this.model + '/room_update/', AIConfig)
      if (res.meta.status !== 200) return this.$message.error('群管理员更新失败')
      // 保存成功后，获取管理员信息
      if (row.wx_manager_no && row.wx_manager_no.length > 0) {
        try {
          // 为每个 wx_manager_no 单独查询
          const managerPromises = row.wx_manager_no.map(async (empAccount) => {
            try {
              const { data } = await this.$http.get(this.model + '/get_emp_info/', {
                params: { empname: empAccount } // 接口使用 empname 参数
              })
              // 假设接口返回单个对象或数组的第一个元素
              return data.data[0]
            } catch (err) {
              console.error(`获取 emp_account: ${empAccount} 信息失败`, err)
              return null // 查询失败返回 null
            }
          })
          // 等待所有查询完成
          const selectedManagers = (await Promise.all(managerPromises)).filter(item => item !== null)
          // 更新 wx_managers
          row.wx_managers = selectedManagers.map(item => ({
            wx_manager_no: item.emp_account,
            wx_manager_name: item.emp_name
          }))
        } catch (err) {
          console.error('获取管理员信息失败', err)
          this.$message.error('获取管理员信息失败')
          row.managerInfo = ''
          row.wx_managers = []
        }
      } else {
        row.managerInfo = ''
        row.wx_managers = []
      }
      this.$message.success('群管理员地址更新成功')
      row.isEditingManager = false
    },
    // 更新群AI自动回复配置
    async handleAIConfigChange(row) {
      // 如果是从关闭状态切换到开启状态，需要检查是否配置了值班人员
      if (row.reply_toggle === true) {
        // 检查是否配置了值班人员
        if (!this.hasDutyUsers(row)) {
          // 未配置值班人员，显示错误提示
          this.$message.error('请先配置群值班人员')
          // 阻止开关状态变更，将开关值重置回false
          row.reply_toggle = false
          // 不调用接口更新配置
          return
        }
      }

      // 已配置值班人员或者是从开启状态切换到关闭状态，正常调用接口更新配置
      const AIConfig = {
        id: row.id,
        reply_toggle: row.reply_toggle
      }
      const { data: res } = await this.$http.post(this.model + '/room_update/', AIConfig)
      if (res.meta.status !== 200) return this.$message.error('配置更新失败')
      this.$message.success('配置更新成功')
    },
    // 更新群是否可见配置
    async handleGroupVisible(row) {
      // 如果是从关闭状态切换到开启状态，需要检查是否配置了值班人员
      if (row.is_display === true) {
        // 检查是否配置了值班人员
        if (!this.hasDutyUsers(row)) {
          // 未配置值班人员，显示错误提示
          this.$message.error('请先配置群值班人员')
          // 阻止开关状态变更，将开关值重置回false
          row.is_display = false
          // 不调用接口更新配置
          return
        }
      }

      // 已配置值班人员或者是从开启状态切换到关闭状态，正常调用接口更新配置
      const groupDisplay = {
        id: row.id,
        is_display: row.is_display
      }
      const { data: res } = await this.$http.post(this.model + '/room_update/', groupDisplay)
      if (res.meta.status !== 200) return this.$message.error('配置更新失败')
      this.$message.success('配置更新成功')
    },
    // 更新群消息是否识别配置
    async handleRedictionMessage(row) {
      // 如果是从关闭状态切换到开启状态，需要检查是否配置了值班人员
      if (row.prediction_message_toggle === true) {
        // 检查是否配置了值班人员
        if (!this.hasDutyUsers(row)) {
          // 未配置值班人员，显示错误提示
          this.$message.error('请先配置群值班人员')
          // 阻止开关状态变更，将开关值重置回false
          row.prediction_message_toggle = false
          // 不调用接口更新配置
          return
        }
      }

      // 已配置值班人员或者是从开启状态切换到关闭状态，正常调用接口更新配置
      const groupDisplay = {
        id: row.id,
        prediction_message_toggle: row.prediction_message_toggle
      }
      const { data: res } = await this.$http.post(this.model + '/room_update/', groupDisplay)
      if (res.meta.status !== 200) return this.$message.error('配置更新失败')
      this.$message.success('配置更新成功')
    },
    // 更新全产品线获取答案配置
    async handleAllBusiness(row) {
      // 如果是从关闭状态切换到开启状态，需要检查是否配置了值班人员
      if (row.all_business_toggle === true) {
        // 检查是否配置了值班人员
        if (!this.hasDutyUsers(row)) {
          // 未配置值班人员，显示错误提示
          this.$message.error('请先配置群值班人员')
          // 阻止开关状态变更，将开关值重置回false
          row.all_business_toggle = false
          // 不调用接口更新配置
          return
        }
      }

      // 已配置值班人员或者是从开启状态切换到关闭状态，正常调用接口更新配置
      const groupDisplay = {
        id: row.id,
        all_business_toggle: row.all_business_toggle
      }
      const { data: res } = await this.$http.post(this.model + '/room_update/', groupDisplay)
      if (res.meta.status !== 200) return this.$message.error('配置更新失败')
      this.$message.success('配置更新成功')
    },
    // 打开更新值班人员弹框
    async handleDutyDialogOpen(row) {
      const { data: res } = await this.$http.get(this.model + '/get_room_duty/', { params: { room_id: row.room_id } })
      if (res.meta.status !== 200) return this.$message.error('获取值班人员列表失败')

      // 处理数据，添加dutyInfo字段
      this.dutyList = res.data.map(item => {
        // 确保duty_user和duty_user_no都有值
        const dutyInfo = {
          duty_user_no: item.duty_user_no || '',
          duty_user: item.duty_user || ''
        }

        return {
          ...item,
          dutyInfo: dutyInfo
        }
      })

      this.room_id = row.room_id

      // 为每个已有值班人员预加载选项
      this.dutyList.forEach(item => {
        if (item.dutyInfo && item.dutyInfo.duty_user_no) {
          // 预加载当前值班人员到options中
          this.handleDutySelectFocus(item)
        }
      })

      // 打开对话框时保存原始数据
      this.originalDutyList = JSON.parse(JSON.stringify(this.dutyList))

      // 重置变更记录
      this.dutyRowsChanged = {}
      this.dutyConfigVisible = true
    },
    // 关闭更新值班人员弹框
    handleDutyDialogClose() {
      // 关闭时恢复原始数据
      this.dutyList = JSON.parse(JSON.stringify(this.originalDutyList))
      this.dutyRowsChanged = {} // 重置所有变更标记，包括deleted标记
      this.dutyConfigVisible = false
      this.getRoomList()
    },
    // 处理值班人员变更
    handleDutyUserChange(row) {
      // 找到行的索引
      const index = this.dutyList.findIndex(item => item === row)
      if (index !== -1) {
        // 标记该行已变更
        this.dutyRowsChanged[index] = true

        // 更新dutyInfo对象
        const selectedOption = this.options.find(opt => opt.value === row.duty_user_no)
        if (selectedOption) {
          if (!row.dutyInfo) {
            row.dutyInfo = {}
          }
          row.dutyInfo.duty_user_no = selectedOption.value
          row.dutyInfo.duty_user = selectedOption.label.split('(')[0].trim()

          // 同时更新duty_user字段，保持一致性
          row.duty_user = selectedOption.label.split('(')[0].trim()
        }
      }
    },
    // 处理业务线变更
    handleProjectTypeChange(row) {
      // 找到行的索引
      const index = this.dutyList.findIndex(item => item === row)
      if (index !== -1) {
        // 标记该行已变更
        this.dutyRowsChanged[index] = true
      }
    },
    // 获取可用的业务线列表（排除已选择的业务线）
    getAvailableProductList(currentRow) {
      // 获取所有已被选择的业务线ID（排除当前行）
      const selectedProjectTypes = this.dutyList
        .filter(row => row !== currentRow && row.project_type) // 排除当前行和没有选择业务线的行
        .map(row => row.project_type) // 获取已选择的业务线ID
      // 返回未被选择的业务线列表
      return this.productList.filter(product => {
        // 如果是当前行已选择的业务线，保留在选项中
        if (currentRow.project_type === product.product_id) {
          return true
        }

        // 不允许选择"其他"业务线
        if (product.product_id === 99) {
          return false
        }

        // 过滤掉已被其他行选择的业务线
        return !selectedProjectTypes.includes(product.product_id)
      })
    },
    // 检查是否可以保存值班人员（保留此方法以兼容现有代码，但不再使用）
    canSaveDutyUser() {
      return false // 始终返回false，因为我们不再使用单独的保存按钮
    },
    // 添加值班人员
    addDutyUser() {
      // 检查是否已有数据
      if (this.dutyList.length >= 1) {
        // 检查是否已存在"其他"业务线
        const hasOther = this.dutyList.some(item => item.project_type === 99)
        if (!hasOther) {
          // 新增两行：一行空白行，一行"其他"业务线
          const newRow1 = {
            room_id: this.room_id,
            project_type: '',
            duty_user: '',
            duty_user_no: '',
            dutyInfo: { duty_user_no: '', duty_user: '' }
          }
          const newRow2 = {
            room_id: this.room_id,
            project_type: 99,
            duty_user: '',
            duty_user_no: '',
            dutyInfo: { duty_user_no: '', duty_user: '' }
          }
          this.dutyList.push(newRow1, newRow2)

          // 标记为已更改
          this.dutyRowsChanged[this.dutyList.length - 2] = true
          this.dutyRowsChanged[this.dutyList.length - 1] = true

          // 预先加载下拉选项
          this.handleSearch('')
          return
        }
      }
      // 如果为空,只向 dutyList 添加一个新的空对象
      const newRow = {
        room_id: this.room_id,
        project_type: '',
        dutyInfo: { duty_user_no: '', duty_user: '' }, // 值班人员编码初始为空
        duty_user_no: '', // 值班人员编码初始为空
        duty_user: '' // 值班人员名称初始为空
      }

      this.dutyList.push(newRow)

      // 标记为已更改
      this.dutyRowsChanged[this.dutyList.length - 1] = true

      // 预先加载下拉选项
      this.handleSearch('')
    },
    // 更新值班人员（不再单独调用接口，仅更新本地数据）
    saveDutyUser(row) {
      // 找到行的索引
      const index = this.dutyList.findIndex(item => item === row)
      if (index === -1) return

      // 查找选中的选项
      const selectedOption = this.options.find(opt => opt.value === row.duty_user_no)
      if (selectedOption) {
        // 更新dutyInfo对象
        if (!row.dutyInfo) {
          row.dutyInfo = {}
        }
        row.dutyInfo.duty_user_no = selectedOption.value
        row.dutyInfo.duty_user = selectedOption.label.split('(')[0].trim()

        // 同时更新duty_user字段，保持一致性
        row.duty_user = selectedOption.label.split('(')[0].trim()
      }

      // 标记该行已变更
      this.dutyRowsChanged[index] = true
    },
    // 批量保存所有值班人员配置
    async batchSaveDutyUsers() {
      // 检查是否有数据，如果dutyList不存在则初始化为空数组
      if (!this.dutyList) {
        this.dutyList = []
      }

      // 构建请求参数
      const dutyConfig = this.dutyList.map(row => {
        return {
          project_id: row.project_type,
          duty_user_no: row.duty_user_no
        }
      })

      const updateInfo = {
        room_id: this.room_id,
        duty_config: dutyConfig
      }

      try {
        const { data: res } = await this.$http.post(this.model + '/room_duty_update/', updateInfo)
        if (res.meta.status !== 200) return this.$message.error('值班人员更新失败')

        // 更新所有行的dutyInfo对象
        this.dutyList.forEach((row, index) => {
          // 清除变更标记
          this.dutyRowsChanged[index] = false
        })

        // 清除deleted标记
        this.dutyRowsChanged.deleted = false

        // 检查是否需要关闭群配置开关（当值班人员列表为空时）
        if (this.dutyList.length === 0) {
          // 调用关闭群配置开关方法
          await this.closeRoomConfigs(this.room_id)
        }

        this.$message.success('值班人员配置更新成功')

        // 关闭弹窗
        this.dutyConfigVisible = false

        // 刷新群配置列表
        await this.getRoomList()
      } catch (error) {
        console.error('保存值班人员失败', error)
        this.$message.error('保存值班人员失败')
      }
    },
    // 删除值班人员（不再单独调用接口，仅更新本地数据）
    async deleteDutyUser(row) {
      // 找到要删除的行索引
      const index = this.dutyList.findIndex(item => item === row)
      if (index === -1) return

      // 检查业务线或值班人员是否为空
      const isEmptyRow = !row.project_type || row.project_type === '' ||
                         !row.duty_user_no || row.duty_user_no === ''

      // 检查是否满足特殊条件：只有三行数据且其中一行为"其他"业务线
      const hasThreeRows = this.dutyList.length === 3
      const otherRow = hasThreeRows ? this.dutyList.find(item => item.project_type === 99) : null
      const isSpecialCase = hasThreeRows && otherRow && row.project_type !== 99

      // 直接从 dutyList 中删除该行
      this.dutyList.splice(index, 1)

      // 标记为有变更，确保删除后可以保存
      this.dutyRowsChanged.deleted = true

      // 特殊情况：如果删除后只剩两行，且其中一行是"其他"业务线，也需要删除"其他"业务线
      if (isSpecialCase) {
        const otherIndex = this.dutyList.findIndex(item => item.project_type === 99)
        if (otherIndex !== -1) {
          // 检查"其他"业务线行是否为空行
          const isOtherRowEmpty = !otherRow.duty_user_no || otherRow.duty_user_no === ''

          if (isOtherRowEmpty || !isEmptyRow) {
            // 如果是空行或者删除的不是空行，直接在前端删除
            this.dutyList.splice(otherIndex, 1)
          }
        }
      }
    },
    // 关闭群配置开关
    async closeRoomConfigs(roomId) {
      // 查找当前群在roomList中的配置信息
      const currentRoom = this.roomList.find(item => item.room_id === roomId)
      if (!currentRoom) return false

      // 检查四个开关状态
      const configsToClose = []

      if (currentRoom.is_display) {
        configsToClose.push({
          name: '群是否可见',
          key: 'is_display'
        })
      }

      if (currentRoom.prediction_message_toggle) {
        configsToClose.push({
          name: '群消息识别',
          key: 'prediction_message_toggle'
        })
      }

      if (currentRoom.reply_toggle) {
        configsToClose.push({
          name: 'AI自动回复',
          key: 'reply_toggle'
        })
      }

      if (currentRoom.all_business_toggle) {
        configsToClose.push({
          name: '全产品线获取答案',
          key: 'all_business_toggle'
        })
      }

      // 如果有需要关闭的配置
      if (configsToClose.length > 0) {
        try {
          // 关闭所有需要关闭的配置
          for (const config of configsToClose) {
            const updateConfig = {
              id: currentRoom.id,
              [config.key]: false
            }

            const { data: res } = await this.$http.post(this.model + '/room_update/', updateConfig)

            if (res.meta.status !== 200) {
              this.$message.error(`${config.name}配置关闭失败`)
              return false // 如果有任何一个配置关闭失败，返回失败
            }

            // 更新本地数据
            currentRoom[config.key] = false
            this.$message.success(`已自动关闭${config.name}配置`)
          }
          return true // 所有配置关闭成功
        } catch (error) {
          console.error('关闭群配置失败', error)
          this.$message.error('关闭群配置失败')
          return false // 出现错误，返回失败
        }
      }
      return true // 没有需要关闭的配置，视为成功
    },
    // 处理值班人员选择框获得焦点
    handleDutySelectFocus(row) {
      // 如果是已有记录（有值班人员信息），则显示当前值班人员
      if (row.dutyInfo && row.dutyInfo.duty_user_no) {
        // 确保options中包含当前值班人员
        const option = {
          value: row.dutyInfo.duty_user_no,
          label: `${row.dutyInfo.duty_user}(${row.dutyInfo.duty_user_no})`
        }

        // 检查options中是否已存在该选项
        const exists = this.options.some(opt => opt.value === option.value)
        if (!exists) {
          this.options = [option, ...this.options]
        }

        // 设置初始值（如果尚未设置）
        if (!row.duty_user_no) {
          row.duty_user_no = row.dutyInfo.duty_user_no
        }
      }

      // 无论是否是新增记录，都调用handleSearch获取默认列表
      this.handleSearch('')
    },
    // 开始编辑
    startEdit(row, type) {
      if (type === 'manager') {
        console.log('row', row)
        this.originalManager = row.wx_manager_no
        // 进入编辑模式前，确保wx_manager_no是数组格式
        if (typeof row.wx_manager_no === 'string') {
          row.wx_manager_no = row.wx_manager_no ? row.wx_manager_no.split(',') : []
        }
        row.isEditingManager = true
        // 如果有初始值，可以预加载选项
        if (row.wx_manager_no && row.wx_manager_no.length > 0) {
          this.handleSearch('') // 加载已有选项
        }
        this.options = row.wx_managers.map(item => ({
          value: item.wx_manager_no,
          label: item.wx_manager_name + '(' + item.wx_manager_no + ')'
        }))
      } else if (type === 'duty') {
        console.log('row', row)
        this.originalDuty = row.duty_user_no
        row.isEditingDutyUser = true

        // 清空选项列表，确保初始为空
        this.options = []

        // 如果是已有记录（有值班人员信息），则显示当前值班人员
        if (row.dutyInfo && row.dutyInfo.duty_user_no) {
          // 创建一个选项，使用dutyInfo中的信息
          this.options = [{
            value: row.dutyInfo.duty_user_no,
            label: `${row.dutyInfo.duty_user}(${row.dutyInfo.duty_user_no})`
          }]
        }
        // 如果是新增记录，options保持为空数组，等待用户输入搜索内容
      }
    },
    // 取消编辑
    cancelEdit(row, type) {
      if (type === 'manager') {
        row.wx_manager_no = this.originalManager
        row.isEditingManager = false
      } else if (type === 'duty') {
        row.duty_user_no = this.originalDuty
        row.isEditingDutyUser = false
      }
    },
    // 列表编辑处理人,通过接口获取处理人信息
    async handleSearch(query) {
      this.loading = true
      try {
        // 调用接口，即使查询为空也发送请求获取默认列表
        const { data } = await this.$http.get(this.model + '/get_emp_info/', {
          params: { empname: query || '' }
        })

        // 格式化接口数据到 options
        this.options = data.data.map(item => ({
          value: item.emp_account,
          label: item.emp_name + '(' + item.emp_account + ')'
        }))
      } catch (err) {
        console.log('无搜索结果')
        this.options = [] // 确保在出错时也清空选项
      } finally {
        this.loading = false
      }
    },
    // 格式化人员信息
    formatManager(type, List) {
      if (type === 'manager') {
        if (!List || List.length === 0) {
          return ''
        }
        return List.map(manager => `${manager.wx_manager_name}(${manager.wx_manager_no})`).join('、')
      }
      if (type === 'duty') {
        if (!List || List.length === 0) {
          return ''
        }
        // 如果duty_user和duty_user_no都为空，返回空字符串
        if (!List.duty_user && !List.duty_user_no) {
          return ''
        }
        return `${List.duty_user}(${List.duty_user_no})`
      }
    },
    // 检查群是否配置了值班人员
    hasDutyUsers(row) {
      // 检查duty_users字段是否存在且不为空
      return row.duty_users && row.duty_users.trim() !== ''
    }
  }
}
</script>

<style>
.bold-label .el-form-item__label {
  font-weight: bold;
}

/* 鼠标悬浮效果 */
.el-table__body tr.el-table__row:hover > td,
.el-table__body tr.el-table__row--striped:hover > td {
  background-color: #ecf5ff !important;
}
.custom-button-group .el-button {
  margin: 4px !important; /* 移除按钮之间的间距 */
}

.el-dialog__body {
  padding-top: 0;
}

/* 设置弹框背景透明 */
.transparent-dialog .el-dialog {
  background-color: rgba(255, 255, 255, 0.13); /* 半透明背景 */
  box-shadow: none; /* 去掉阴影 */
  margin: 0; /* 去掉外边距 */
  position: fixed; /* 设为固定定位 */
  top: 0; /* 顶部为0 */
}
.transparent-dialog .el-dialog__header,
.transparent-dialog .el-dialog__body,
.transparent-dialog .el-dialog__footer {
  background-color: transparent; /* 内部元素背景透明 */
  margin: 0; /* 去掉内部元素的外边距 */
  padding: 0; /* 去掉内部元素的内边距 */
}

</style>

<style scoped>
/* 斑马纹样式 */
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #f5f7fa; /* 浅蓝色 */
}

/* 默认行颜色 */
::v-deep .el-table__body tr td {
  background: white; /* 白色 */
}

/* 鼠标悬停颜色 */
::v-deep .el-table__body tr:hover > td {
  background: #e6f7ff !important; /* 悬停时的浅蓝色 */
}

</style>
