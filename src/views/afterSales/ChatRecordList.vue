<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>售后问题</el-breadcrumb-item>
      <el-breadcrumb-item>问题列表</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="10">
          <el-col :span="5">
          <el-input placeholder="提交人姓名或工号"
                    v-model="workChatInfo.send_from"
                    clearable
                    @keyup.enter.native="queryList"
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="售后群名称"
                    v-model="workChatInfo.room_name"
                    clearable
                    @keyup.enter.native="queryList"
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-select placeholder="业务归属"
                     v-model="workChatInfo.project_type"
                     clearable
                     multiple>
            <el-option v-for="item in productList"
                             :key="item.product_id"
                             :label="item.product_name"
                             :value="item.product_id">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="7">
           <el-date-picker
              v-model="queryDate"
              :clearable="true"
              type="daterange"
              align="right"
              style="width: 300px"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd"
              disabledDate="false">
            </el-date-picker>
          </el-col>
      </el-row>
      <el-row :gutter="10" style="margin-top: 10px;">
      <el-col :span="5">
          <el-input placeholder="问题描述"
                    v-model="workChatInfo.msg_content"
                    clearable
                    @keyup.enter.native="queryList"
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-select placeholder="是否问题"
                     v-model="workChatInfo.is_question"
                     clearable>
            <el-option value="False" label="否"></el-option>
            <el-option value="True" label="是"></el-option>
          </el-select>
        </el-col>
        <el-col :span="5">
          <el-select placeholder="是否人工删除"
                     v-model="workChatInfo.is_manual_delete"
                     clearable>
            <el-option value="0" label="否"></el-option>
            <el-option value="1" label="是"></el-option>
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="queryList">查 询</el-button>
          <el-button type="primary" @click="queryReset">重 置</el-button>
          <el-button type="primary" @click="identifyQuestion">识别问题</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table
          :data="workChatList"
          stripe
          border
          :header-cell-style="{
            backgroundColor: '#e6f7ff',  // 推荐使用Element默认背景色
            color: '#606266',           // 保持默认文字颜色
            fontSize: '12px',           // 适当加大字号
            fontWeight: 'bold'          // 加粗字体
          }"
          @selection-change="handleSelectionChange">
          <el-table-column type="selection"  min-width="95px" fixed="left" align="center"></el-table-column>
          <el-table-column label="seq" prop="seq"  min-width="60px" fixed="left" align="center"></el-table-column>
          <el-table-column label="问题描述" min-width="200px" fixed="left">
            <template #default="scope">
                <!-- 调用 getTextContent 方法获取并显示文本 -->
              <el-tooltip placement="top">
                <div
                  :style="{
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                    overflow: 'hidden',
                    width: `${scope.column.realWidth - 20}px`, // 动态计算宽度
                  }"
                >
                  {{ getTextContent(scope.row.msg_content) }}
                </div>
                <div slot="content" v-html="getToolTipContent(scope.row.msg_content)" style="white-space: pre-line"></div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="图片" min-width="90px" align="center">
            <template slot-scope="scope">
              <template v-if="hasImage(scope.row.msg_content)">
                <el-link @click="previewImages(scope.row.msg_content)" style="font-size: 12px;">查看图片<i class="el-icon-view el-icon--right"></i> </el-link>
              </template>
              <template v-else>
                <span>暂无图片</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="业务归属" prop="project_type" min-width="80px" align="center">
            <template slot-scope="scope">
              <span>{{ productMap[scope.row.project_type] || '' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="售后群名称" prop="room_name" min-width="180px" align="center"></el-table-column>
          <el-table-column label="提交人" min-width="140px" align="center">
            <template slot-scope="scope">
          <!-- 显示 send_from 和 send_from_user -->
            <div>{{ scope.row.send_from_name }} ({{ scope.row.send_from }})</div>
            </template>
          </el-table-column>
          <el-table-column label="消息产生时间" prop="msg_time" min-width="150px" align="center"></el-table-column>
          <el-table-column label="问题落库时间" prop="create_time" min-width="150px" align="center"></el-table-column>
          <el-table-column label="延迟时间（秒）" prop="delay_time" min-width="120px" align="center"></el-table-column>
          <el-table-column label="是否问题" prop="is_question" min-width="80px" align="center">
            <template v-slot="slotProps">
              <span v-if="!slotProps.row.is_question" style="color: #FFBA84; font-weight: bold; font-size: 13px">否</span>
              <span v-if="slotProps.row.is_question">是</span>
            </template>
          </el-table-column>
          <el-table-column label="是否人工删除" prop="is_manual_delete" min-width="100px" align="center">
            <template v-slot="slotProps">
              <span v-if="!slotProps.row.is_manual_delete">否</span>
              <el-tooltip
                v-if="slotProps.row.is_manual_delete"
                placement="top">
                <template #content>
                  <div>
                    删除原因：{{ slotProps.row.notes }}<br>
                    删除人：{{ slotProps.row.manual_delete_user }}<br>
                    删除时间：{{ slotProps.row.manual_delete_time }}
                  </div>
                </template>
                <span style="color: #FF9C99; font-weight: bold; font-size: 13px">是</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" min-width="80px" align="center">
            <template v-slot="slotProps">
              <div :key="slotProps.row.msg_id">
                <template v-if="!slotProps.row.is_question">
                  <el-popconfirm
                    title="确定要设置为问题吗？"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-warning"
                    @confirm="setAsQuestion(slotProps.row.msg_id)"
                  >
                    <el-button circle  icon="el-icon-check" type="primary" size="small" slot="reference"></el-button>
                  </el-popconfirm>
                </template>
                <template v-else>
                  <el-popconfirm
                    title="确定要删除此问题吗？"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-warning"
                    @confirm="deleteQuestion(slotProps.row.id)"
                  >
                    <el-button circle  icon="el-icon-close" type="info" size="small" slot="reference"></el-button>
                  </el-popconfirm>
                </template>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="workChatInfo.pagenum"
          :page-size="workChatInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
      <!--图片预览弹框-->
      <el-dialog
        :visible.sync="dialogVisible"
        width="100%"
        class="transparent-dialog">
        <div style="position: relative; height: 620px;">
          <!-- 图片展示区域 -->
          <div v-if="previewImagesList.length > 0" style="height: 100%; display: flex; align-items: center; justify-content: center;">
            <!-- 页码提示 -->
            <div style="position: absolute; top: 0px; left: 50%; transform: translateX(-50%); color: rgb(255,255,255); font-weight: bold; font-size: 24px;">
              {{ currentImageIndex + 1 }} / {{ previewImagesList.length }}
            </div>
            <img
              :src="currentImageSrc"
              style="max-width: 90%; max-height: 550px; display: block;"
            >
            <!-- 分页控制器 -->
            <div>
              <el-button
                circle
                size="mini"
                :disabled="currentImageIndex === 0"
                @click="previousImage"
                style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%);"
              >
                <i class="el-icon-arrow-left" style="font-size: 30px; font-weight: bold;"></i>
              </el-button>
              <el-button
                circle
                size="mini"
                :disabled="currentImageIndex === previewImagesList.length - 1"
                @click="nextImage"
                style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-weight: bold;"
              >
                <i class="el-icon-arrow-right" style="font-size: 30px; font-weight: bold;"></i>
              </el-button>
            </div>
          </div>
          <div v-else style="text-align: center; padding: 50px;">
            暂无可用图片
          </div>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'question',
      queryDate: this.getLastMonthRange(),
      workChatList: [], // 售后问题列表
      productList: [
        {
          product_id: '',
          product_name: ''
        }
      ], // 业务线列表
      user_name: window.localStorage.getItem('user_name'),
      chinese_name: window.localStorage.getItem('chinese_name'),
      staff_no: window.localStorage.getItem('staff_no'),
      role_id: window.localStorage.getItem('role_id'),
      workChatInfo: { // 查询栏
        start_date: '',
        end_date: '',
        msg_content: '',
        send_from: '',
        room_name: '',
        project_type: '',
        is_question: '',
        is_manual_delete: '',
        pagenum: 1,
        pagesize: 20
      },
      crop_id: '',
      dialogVisible: false, // 控制图片预览弹框的显示
      previewImagesList: [], // 存储当前预览图片的地址
      currentImageIndex: 0,
      total: 0,
      options: [], // 下拉选项
      selectedRows: [], // 选中的行数据
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 31)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一年',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  created() {
    const now = new Date()
    const starttime = new Date(now.setMonth(now.getMonth() - 3)).toLocaleDateString().replace(/\//g, '-')
    const endtime = new Date().toLocaleDateString().replace(/\//g, '-')
    this.workChatInfo.start_date = starttime
    this.workChatInfo.end_date = endtime
    this.getWorkChatList()
    this.get_product_info()
  },
  activated() {
    this.queryReset()
  },
  watch: {
    // 监听 dateRange 的变化，自动更新 workChatInfo
    queryDate(newVal) {
      if (newVal && newVal.length === 2) {
        this.workChatInfo.start_date = newVal[0]
        this.workChatInfo.end_date = newVal[1]
      } else {
        this.workChatInfo.start_date = ''
        this.workChatInfo.end_date = ''
      }
    }
  },
  computed: {
    // 当前图片的 src
    currentImageSrc() {
      const currentImage = this.previewImagesList[this.currentImageIndex]
      return currentImage ? currentImage.media_content : ''
    },
    productMap() {
      const map = {}
      this.productList.forEach(item => {
        map[item.product_id] = item.product_name
      })
      return map
    }
  },
  mounted() {
    // 监听键盘事件
    window.addEventListener('keydown', this.handleKeydown)
  },
  beforeDestroy() {
    // 清理事件监听器
    window.removeEventListener('keydown', this.handleKeydown)
  },
  methods: {
    queryList() {
      this.workChatInfo.pagenum = 1
      this.getWorkChatList()
    },
    // 翻页
    handleSizeChange(newSize) {
      this.workChatInfo.pagesize = newSize
      this.getWorkChatList()
    },
    handleCurrentChange(newPage) {
      this.workChatInfo.pagenum = newPage
      this.getWorkChatList()
    },
    // 获取售后问题列表
    async getWorkChatList() {
      const { data: res } = await this.$http.post(this.model + '/work_chat_list/', this.workChatInfo)
      if (res.meta.status !== 200) return this.$message.error('获取群聊记录列表失败')
      this.total = res.data.total
      this.workChatList = res.data.data.map(item => ({
        ...item
      }))
    },
    // 获取业务线
    async get_product_info() {
      const { data: res } = await this.$http.get(this.model + '/get_product_info/')
      if (res.meta.status !== 200) return this.$message.error('获取产品线失败')
      this.productList = res.data
      console.log(this.productList)
    },
    // 封装问题描述文本,若有多个描述，则拼接
    getTextContent(msgContent) {
      if (!msgContent || msgContent.length === 0) {
        return '' // 如果msgContent为空或没有数据，返回空字符串
      }
      return msgContent
        .filter(item => item.media_type === 'text')
        .map(item => `${item.media_content}`)
        .join('\n') // 可以根据需要调整拼接方式，例如空格分隔
    },
    // 封装问题描述的tooltip,若有多个描述，则换行显示
    getToolTipContent(msgContent) {
      if (!msgContent || msgContent.length === 0) {
        return '' // 如果msgContent为空或没有数据，返回空字符串
      }
      return msgContent
        .filter(item => item.media_type === 'text')
        .map(item => `${item.media_content}<br>`) // 为每一行加上<p>标签
        .join('\n') // 可以根据需要调整拼接方式，例如空格分隔
    },
    // 获取最近一个月的时间范围
    getLastMonthRange() {
      const now = new Date()
      const lastMonth = new Date()
      lastMonth.setMonth(now.getMonth() - 1) // 向前推一个月
      // 格式化为 yyyy-MM-dd 格式
      const formatDate = (date) => {
        const y = date.getFullYear()
        const m = (date.getMonth() + 1).toString().padStart(2, '0')
        const d = date.getDate().toString().padStart(2, '0')
        return `${y}-${m}-${d}`
      }
      return [formatDate(lastMonth), formatDate(now)]
    },
    // 重置按钮方法
    queryReset() {
      this.queryDate = this.getLastMonthRange()
      this.workChatInfo = {
        start_date: '',
        end_date: '',
        send_from: '',
        belong_area: '',
        project_type: '',
        pagenum: 1,
        pagesize: 20
      }
      this.getWorkChatList()
    },
    // 判断列表是否有图片
    hasImage(contentList) {
      // 判断 contentList 是否是一个数组
      if (!Array.isArray(contentList)) {
        return false // 或者返回一个默认值
      }
      return contentList.some(content => content.media_type === 'image')
    },
    // 预览图片
    previewImages(contentList) {
      this.previewImagesList = contentList.filter(content => content.media_type === 'image')
      this.dialogVisible = true
      this.currentImageIndex = 0 // 每次打开重置为第一张
    },
    // 查看图片-上一张
    previousImage() {
      if (this.currentImageIndex > 0) {
        this.currentImageIndex--
      }
    },
    // 查看图片-下一张
    nextImage() {
      if (this.currentImageIndex < this.previewImagesList.length - 1) {
        this.currentImageIndex++
      }
    },
    // 键盘事件监听器,实现左右键切换图片
    handleKeydown(event) {
      if (event.key === 'ArrowLeft') {
        this.previousImage()
        event.preventDefault() // 阻止默认行为
        event.stopPropagation() // 阻止事件冒泡
      } else if (event.key === 'ArrowRight') {
        this.nextImage()
        event.preventDefault() // 阻止默认行为
        event.stopPropagation() // 阻止事件冒泡
      }
    },
    // 置为非问题
    async deleteQuestion(id) {
      const { data: res } = await this.$http.post(this.model + '/question_update_status/', { id: id, type: '1' })
      if (res.meta.status !== 200) return this.$message.error('问题删除失败')
      this.$message.success('问题删除成功')
      this.getWorkChatList()
    },
    // 设置为问题
    async setAsQuestion(msgId) {
      const { data: res } = await this.$http.post(this.model + '/question_identify/', { msgId: [msgId] })
      if (res.meta.status !== 200) return this.$message.error('问题设置失败')
      this.$message.success('问题设置成功')
      this.getWorkChatList()
    },
    // 处理选中行变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    // 点击识别按钮
    async identifyQuestion() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请先选择至少一行数据')
        return
      }
      // 获取选中行的 id
      const selectedIds = this.selectedRows.map((row) => row.msg_id)
      const { data: res } = await this.$http.post(this.model + '/question_identify/', { msgId: selectedIds, forceQuestion: '0', type: '0' })
      if (res.meta.status !== 200) return this.$message.error('问题重新识别失败')
      this.$message.success('问题识别中')
      this.getWorkChatList()
    }
  }
}
</script>

<style>
.edit-input .el-input__inner {
  width: 100%;
  padding: 0 7px;
}
.edit-input .el-input__suffix{
  left: 50px;
}
.bold-label .el-form-item__label {
  font-weight: bold;
}

/* 鼠标悬浮效果 */
.el-table__body tr.el-table__row:hover > td,
.el-table__body tr.el-table__row--striped:hover > td {
  background-color: #ecf5ff !important;
}
.custom-button-group .el-button {
  margin: 4px !important; /* 移除按钮之间的间距 */
}

.el-dialog__body {
  padding-top: 0;
}

/* 设置弹框背景透明 */
.transparent-dialog .el-dialog {
  background-color: rgba(255, 255, 255, 0.13); /* 半透明背景 */
  //border: 2px solid rgba(0, 0, 0, 0.5); /* 添加边框颜色 */
  box-shadow: none; /* 去掉阴影 */
  margin: 0; /* 去掉外边距 */
  position: fixed; /* 设为固定定位 */
  top: 0; /* 顶部为0 */
  ////left: 50%; /* 水平居中 */
  ////transform: translateX(-50%); /* 使其水平居中 */
  //height: 100%; /* 高度填满 */
  //overflow: auto; /* 如果内容超出，允许滚动 */
}
.transparent-dialog .el-dialog__header,
.transparent-dialog .el-dialog__body,
.transparent-dialog .el-dialog__footer {
  background-color: transparent; /* 内部元素背景透明 */
  margin: 0; /* 去掉内部元素的外边距 */
  padding: 0; /* 去掉内部元素的内边距 */
}
</style>

<style scoped>
/* 斑马纹样式 */
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #f5f7fa; /* 浅蓝色 */
}

/* 默认行颜色 */
::v-deep .el-table__body tr td {
  background: white; /* 白色 */
}

/* 鼠标悬停颜色 */
::v-deep .el-table__body tr:hover > td {
  background: #e6f7ff !important; /* 悬停时的浅蓝色 */
}

</style>
