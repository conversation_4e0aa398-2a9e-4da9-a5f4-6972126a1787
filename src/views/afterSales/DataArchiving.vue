<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>售后问题</el-breadcrumb-item>
      <el-breadcrumb-item>数据归档</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="10">
        <el-col :span="4">
          <el-input placeholder="所属商户"
                    v-model="queryInfo.belong_mer_code"
                    clearable
                    @keyup.enter.native="queryList"
                    >
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select placeholder="售后群名称"
                     v-model="queryInfo.room_id"
                     clearable
                     multiple
                     filterable>
            <el-option v-for="item in roomList"
                        :key="item.room_id"
                        :label="item.room_name"
                        :value="item.room_id">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="7">
          <el-date-picker
            v-model="queryDate"
            :clearable="true"
            type="daterange"
            align="right"
            style="width: 330px"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            value-format="yyyy-MM-dd"
            disabledDate="false">
          </el-date-picker>
        </el-col>
        <el-col :span="7">
          <el-button type="primary" @click="queryList">查 询</el-button>
          <el-button type="primary" @click="queryReset">重 置</el-button>
          <el-button type="primary" @click="queryExport">导 出</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table
          :data="questionList"
          stripe
          border
          :header-cell-style="{
            backgroundColor: '#e6f7ff',  // 推荐使用Element默认背景色
            color: '#606266',           // 保持默认文字颜色
            fontSize: '12px',           // 适当加大字号
            fontWeight: 'bold'          // 加粗字体
          }">
          <el-table-column label="问题时间" prop="msg_time" min-width="145px" fixed="left" align="center"></el-table-column>
          <el-table-column label="问题描述" min-width="200px" fixed="left">
            <template #default="scope">
                <!-- 调用 getTextContent 方法获取并显示文本 -->
              <el-tooltip placement="top">
                 <div
                  :style="{
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                    overflow: 'hidden',
                    width: `${scope.column.realWidth - 20}px`, // 动态计算宽度
                  }"
                >
                  {{ getTextContent(scope.row.msg_content) }}
                 </div>
                <div slot="content" v-html="getToolTipContent(scope.row.msg_content)" style="white-space: pre-line"></div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="所属商户" prop="belong_mer_code" min-width="105px" align="center"></el-table-column>
          <el-table-column label="ERP类型" prop="mer_type" min-width="90px" align="center">
            <template v-slot="slotProps">
              <span v-if="slotProps.row.mer_type==='0'">H1</span>
              <span v-if="slotProps.row.mer_type==='1'">H2</span>
              <span v-if="slotProps.row.mer_type==='2'">非海典ERP</span>
            </template>
          </el-table-column>
          <el-table-column label="处理人" prop="comp_user" min-width="140px" align="center">
            <template slot-scope="scope">
              {{ scope.row.comp_user_name }} ({{ scope.row.comp_user }})
            </template>
          </el-table-column>
          <el-table-column label="图片" min-width="90px" align="center">
            <template slot-scope="scope">
              <template v-if="hasImage(scope.row.msg_content)">
                <el-link @click="previewImages(scope.row.msg_content)" style="font-size: 12px;">查看图片<i class="el-icon-view el-icon--right"></i> </el-link>
              </template>
              <template v-else>
                <span>无</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="问题回复" min-width="150px">
            <template #default="scope">
                <!-- 调用 getTextContent 方法获取并显示文本 -->
              <el-tooltip placement="top">
                <div style="white-space: nowrap;text-overflow: ellipsis; width: 180px; overflow: hidden">{{ scope.row.answers.question_answer_manual }}</div>
                <div slot="content" v-html="formatTooltipContent(scope.row.answers.question_answer_manual)" style="white-space: pre-line; max-width: 800px;"></div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="问题类型" prop="question_type" min-width="125px" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.question_type == 0">咨询类</span>
              <span v-if="scope.row.question_type == 1">支持类</span>
              <span v-else-if="scope.row.question_type == 2" style="color: #f35d58; font-weight: bold; font-size: 13px">BUG</span>
              <span v-else-if="scope.row.question_type == 3">需求类</span>
              <span v-else-if="scope.row.question_type == 4">操作类</span>
            </template>
          </el-table-column>
          <el-table-column label="业务归属" prop="project_type" min-width="130px" align="center">
           <template slot-scope="scope">
            <span>{{ productMap[scope.row.project_type] || '' }}</span>
           </template>
          </el-table-column>
          <el-table-column label="售后群名称" prop="room_name" min-width="170px" align="center"></el-table-column>
          <el-table-column label="提交人" min-width="140px" align="center">
            <template slot-scope="scope">
          <!-- 显示 send_from 和 send_from_user -->
            <div>{{ scope.row.send_from_name }} ({{ scope.row.send_from }})</div>
            </template>
          </el-table-column>
          <el-table-column label="所属大区" prop="belong_area" min-width="100px" align="center"></el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <!-- 列表分页 -->
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
      <!--图片预览弹框-->
      <el-dialog
        :visible.sync="dialogVisible"
        width="100%"
        class="transparent-dialog">
        <div style="position: relative; height: 620px;">
          <!-- 图片展示区域 -->
          <div v-if="previewImagesList.length > 0" style="height: 100%; display: flex; align-items: center; justify-content: center;">
            <!-- 页码提示 -->
            <div style="position: absolute; top: 0px; left: 50%; transform: translateX(-50%); color: rgb(255,255,255); font-weight: bold; font-size: 24px;">
              {{ currentImageIndex + 1 }} / {{ previewImagesList.length }}
            </div>
            <img
              :src="currentImageSrc"
              style="max-width: 90%; max-height: 550px; display: block;"
            >
            <!-- 分页控制器 -->
            <div>
              <el-button
                circle
                size="mini"
                :disabled="currentImageIndex === 0"
                @click="previousImage"
                style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%);"
              >
                <i class="el-icon-arrow-left" style="font-size: 30px; font-weight: bold;"></i>
              </el-button>
              <el-button
                circle
                size="mini"
                :disabled="currentImageIndex === previewImagesList.length - 1"
                @click="nextImage"
                style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); font-weight: bold;"
              >
                <i class="el-icon-arrow-right" style="font-size: 30px; font-weight: bold;"></i>
              </el-button>
            </div>
          </div>
          <div v-else style="text-align: center; padding: 50px;">
            暂无可用图片
          </div>
        </div>
</el-dialog>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'question',
      queryDate: this.getLastMonthRange(),
      questionList: [], // 售后问题列表
      productList: [
        {
          product_id: '',
          product_name: ''
        }
      ], // 业务线列表
      roomList: [], // 售后群列表
      user_name: window.localStorage.getItem('user_name'),
      chinese_name: window.localStorage.getItem('chinese_name'),
      staff_no: window.localStorage.getItem('staff_no'),
      role_id: window.localStorage.getItem('role_id'),
      queryInfo: { // 查询栏
        start_date: '',
        end_date: '',
        belong_mer_code: '',
        room_id: '',
        room_visibility: 1,
        pagenum: 1,
        pagesize: 20
      },
      total: 0,
      update_date: {}, // 问题列表更新
      crop_id: '', // 企业id
      comp_name: '', // 处理人姓名工号,格式为 姓名(工号)
      dialogVisible: false, // 控制图片预览弹框的显示
      previewImagesList: [], // 存储当前预览图片的地址
      currentImageIndex: 0,
      options: [], // 下拉选项
      loading: false, // 加载状态
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 31)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一年',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  mounted() {
    this.default_date()
    this.getQuestionList()
    this.getRoomList()
    this.get_product_info()
  },
  deactivated() {
    // 清理事件监听器
    window.removeEventListener('keydown', this.handleKeydown)
    // 组件销毁前清除列表刷新定时器，避免内存泄漏
    if (this.intervalId) {
      clearInterval(this.intervalId)
    }
  },
  activated() {
    this.queryReset()
    // 监听键盘事件
    window.addEventListener('keydown', this.handleKeydown)
  },
  watch: {
    // 监听 dateRange 的变化，自动更新 queryInfo
    queryDate(newVal) {
      if (newVal && newVal.length === 2) {
        this.queryInfo.start_date = newVal[0]
        this.queryInfo.end_date = newVal[1]
      } else {
        this.queryInfo.start_date = ''
        this.queryInfo.end_date = ''
      }
    }
  },
  computed: {
    // 当前图片的 src
    currentImageSrc() {
      const currentImage = this.previewImagesList[this.currentImageIndex]
      return currentImage ? currentImage.media_content : ''
    },
    productMap() {
      const map = {}
      this.productList.forEach(item => {
        map[item.product_id] = item.product_name
      })
      return map
    }
  },
  methods: {
    // 查询按钮点击事件
    queryList() {
      this.queryInfo.pagenum = 1
      this.getQuestionList()
    },
    // 重置按钮方法
    queryReset() {
      this.queryDate = this.getLastMonthRange()
      this.queryInfo = {
        start_date: '',
        end_date: '',
        belong_mer_code: '',
        room_id: '',
        room_visibility: 1,
        pagenum: 1,
        pagesize: 20
      }
      this.default_date()
      this.getQuestionList()
    },
    // 设置每页条数
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getQuestionList()
    },
    // 设置页数
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getQuestionList()
    },
    // 获取售后问题列表
    async getQuestionList() {
      const { data: res } = await this.$http.post(this.model + '/question_list/', this.queryInfo)
      if (res.meta.status !== 200) return this.$message.error('获取售后问题列表失败')
      this.total = res.data.total
      // 初始化列表字段是否可编辑的状态为false
      this.questionList = res.data.questions.map(item => ({
        ...item,
        isEditingCompUser: false,
        isEditingProject: false,
        isEditingStatus: false,
        isEditingType: false,
        isEditingMercode: false
      }))
    },
    // 获取业务线
    async get_product_info() {
      const { data: res } = await this.$http.get(this.model + '/get_product_info/')
      if (res.meta.status !== 200) return this.$message.error('获取产品线失败')
      this.productList = res.data
      console.log(this.productList)
    },
    // 导出售后问题列表
    async queryExport() {
      try {
        const exportInfo = this.queryInfo
        exportInfo.pagesize = 10000
        const response = await this.$http.post(this.model + '/export_question_archive_data/', exportInfo, {
          responseType: 'blob'
        })
        // 检查响应是否为 Blob 类型
        if (response.data instanceof Blob && response.data.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
          const filename = '售后问题列表-数据归档.xlsx'
          await this.downloadFile(response, filename) // 假设 downloadFile 接收 Blob 对象
        } else {
          // 如果不是预期的 Excel 文件，尝试解析为 JSON
          const text = await response.data.text()
          const errorData = JSON.parse(text)
          this.$message.error(errorData.meta.msg || '导出失败')
        }
      } catch (error) {
        // 处理其他异常情况
        if (error.response) {
          // 服务端返回了错误响应
          const text = await error.response.data.text()
          const errorData = JSON.parse(text)
          this.$message.error(errorData.meta.msg || '导出失败')
        } else {
          this.$message.error('网络错误，请稍后重试')
        }
        console.error('导出接口调用失败:', error)
      }
    },
    // 下载文件
    async downloadFile(response, filename) {
      try {
        // 创建 Blob 对象
        const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        const downloadUrl = window.URL.createObjectURL(blob)
        // 创建一个隐藏的链接元素
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = filename
        document.body.appendChild(link)
        // 触发下载并移除链接元素
        link.click()
        link.remove()
        // 释放 URL 对象
        window.URL.revokeObjectURL(downloadUrl)
      } catch (error) {
        this.$message.error('导出失败')
      }
    },
    // 获取售后群列表
    async getRoomList() {
      const { data: res } = await this.$http.get(this.model + '/get_room_config/')
      if (res.meta.status !== 200) return this.$message.error('群列表获取失败')
      this.roomList = res.data
      console.log('roomList', this.roomList)
    },
    // 封装问题描述文本,若有多个描述，则拼接
    getTextContent(msgContent) {
      if (!msgContent || msgContent.length === 0) {
        return '' // 如果msgContent为空或没有数据，返回空字符串
      }
      return msgContent
        .filter(item => item.media_type === 'text')
        .map(item => `${item.media_content}`)
        .join('\n') // 可以根据需要调整拼接方式，例如空格分隔
    },
    // 封装问题描述的tooltip,若有多个描述，则换行显示
    getToolTipContent(msgContent) {
      if (!msgContent || msgContent.length === 0) {
        return '' // 如果msgContent为空或没有数据，返回空字符串
      }
      return msgContent
        .filter(item => item.media_type === 'text')
        .map(item => `${item.media_content}<br>`) // 为每一行加上<p>标签
        .join('\n') // 可以根据需要调整拼接方式，例如空格分隔
    },
    // tooltip换行显示
    formatTooltipContent(content) {
      return content && content.replace(/\n/g, '<br>')
    },
    // 判断列表是否有图片
    hasImage(contentList) {
      // 判断 contentList 是否是一个数组
      if (!Array.isArray(contentList)) {
        return false // 或者返回一个默认值
      }
      return contentList.some(content => content.media_type === 'image')
    },
    // 预览图片
    previewImages(contentList) {
      this.previewImagesList = contentList.filter(content => content.media_type === 'image')
      this.dialogVisible = true
      this.currentImageIndex = 0 // 每次打开重置为第一张
    },
    // 查看图片-上一张
    previousImage() {
      if (this.currentImageIndex > 0) {
        this.currentImageIndex--
      }
    },
    // 查看图片-下一张
    nextImage() {
      if (this.currentImageIndex < this.previewImagesList.length - 1) {
        this.currentImageIndex++
      }
    },
    // 键盘事件监听器,实现左右键切换图片
    handleKeydown(event) {
      if (event.key === 'ArrowLeft') {
        this.previousImage()
        event.preventDefault() // 阻止默认行为
        event.stopPropagation() // 阻止事件冒泡
      } else if (event.key === 'ArrowRight') {
        this.nextImage()
        event.preventDefault() // 阻止默认行为
        event.stopPropagation() // 阻止事件冒泡
      }
    },
    // 判断 msg_content 是否包含 text 类型
    isTextContent(msgContent) {
      console.log(msgContent)
      if (!Array.isArray(msgContent) || !msgContent.length) return false
      return msgContent.some(item => item.media_type === 'text')
    },
    // 设置查询条件默认值,当用户角色为开发或测试时,则将处理人赋默认值;若用户为大区,则将提交人赋默认值,其他条件则不做处理
    default_date() {
      // if (this.role_id === '1' || this.role_id === '9') {
      //   this.queryInfo.comp_user = this.chinese_name
      //   this.queryDate = this.getLastMonthRange()
      // } else if (this.role_id === '19') {
      //   this.queryInfo.send_from = this.chinese_name
      //   this.queryDate = this.getLastMonthRange()
      // } else if (this.role_id === '0') {
      //   // this.queryDate = this.getLastFridayAndThisThursday()
      //   this.queryDate = this.getLastMonthRange()
      // }
      this.queryDate = this.getLastMonthRange()
      this.queryInfo.start_date = this.queryDate[0]
      this.queryInfo.end_date = this.queryDate[1]
    },
    // 获取最近一个月的时间范围
    getLastMonthRange() {
      const now = new Date()
      const lastMonth = new Date()
      lastMonth.setMonth(now.getMonth() - 1) // 向前推一个月
      // 格式化为 yyyy-MM-dd 格式
      const formatDate = (date) => {
        const year = date.getFullYear()
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        const day = date.getDate().toString().padStart(2, '0')
        return `${year}-${month}-${day}`
      }
      return [formatDate(lastMonth), formatDate(now)]
    }
  }
}
</script>

<style>
.edit-input .el-input__inner {
  width: 100%;
  padding: 0 7px;
}
.edit-input .el-input__suffix{
  left: 50px;
}
.bold-label .el-form-item__label {
  font-weight: bold;
}

/* 鼠标悬浮效果 */
.el-table__body tr.el-table__row:hover > td,
.el-table__body tr.el-table__row--striped:hover > td {
  background-color: #ecf5ff !important;
}
.custom-button-group .el-button {
  margin: 4px !important; /* 移除按钮之间的间距 */
}

.el-dialog__body {
  padding-top: 0;
}

/* 设置弹框背景透明 */
.transparent-dialog .el-dialog {
  background-color: rgba(255, 255, 255, 0.13); /* 半透明背景 */
  //border: 2px solid rgba(0, 0, 0, 0.5); /* 添加边框颜色 */
  box-shadow: none; /* 去掉阴影 */
  margin: 0; /* 去掉外边距 */
  position: fixed; /* 设为固定定位 */
  top: 0; /* 顶部为0 */
  ////left: 50%; /* 水平居中 */
  ////transform: translateX(-50%); /* 使其水平居中 */
  //height: 100%; /* 高度填满 */
  //overflow: auto; /* 如果内容超出，允许滚动 */
}
.transparent-dialog .el-dialog__header,
.transparent-dialog .el-dialog__body,
.transparent-dialog .el-dialog__footer {
  background-color: transparent; /* 内部元素背景透明 */
  margin: 0; /* 去掉内部元素的外边距 */
  padding: 0; /* 去掉内部元素的内边距 */
}

</style>

<style scoped>
/* 斑马纹样式 */
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #f5f7fa; /* 浅蓝色 */
}

/* 默认行颜色 */
::v-deep .el-table__body tr td {
  background: white; /* 白色 */
}

/* 鼠标悬停颜色 */
::v-deep .el-table__body tr:hover > td {
  background: #e6f7ff !important; /* 悬停时的浅蓝色 */
}

</style>
