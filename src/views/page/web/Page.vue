<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>UI测试</el-breadcrumb-item>
      <el-breadcrumb-item>WEB-UI</el-breadcrumb-item>
      <el-breadcrumb-item>页面管理</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="5">
          <el-input placeholder="请输入页面ID"
                    v-model="queryInfo.search_page_id"
                    clearable>
          </el-input>
          </el-col>
          <el-col :span="5">
          <el-input placeholder="请输入页面名称"
                    v-model="queryInfo.search_page_name"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryPage">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">
          <el-button class="add" type="primary" @click="showDialog(true, {}, 'add')">添加页面</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="pageList" border>
          <el-table-column label="ID" prop="page_id" min-width="50px"></el-table-column>
          <el-table-column label="页面名称" prop="page_name" min-width="150px" show-overflow-tooltip></el-table-column>
          <el-table-column label="页面地址" prop="page_url" show-overflow-tooltip min-width="120px"></el-table-column>
          <el-table-column label="备注" prop="remark" min-width="120px" show-overflow-tooltip></el-table-column>
          <el-table-column label="创建人" prop="creater" min-width="100px"></el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="120px"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="120px">
            <template v-slot="slotProps">
            <el-tooltip class="item" effect="dark" content="查看" placement="top">
              <el-button type="warning" icon="el-icon-view" size="mini" @click="showDialog(false,slotProps.row, 'check')"></el-button>
            </el-tooltip>
            <el-button v-if="slotProps.row.creater==user_name" type="primary" icon="el-icon-edit" size="mini" @click="showDialog(false,slotProps.row, 'update')"></el-button>
            <el-button v-if="slotProps.row.creater==user_name" type="danger" icon="el-icon-delete" size="mini" @click="removePageById(slotProps.row.page_id)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 新增编辑对话框 -->
    <el-dialog :visible.sync="dialogVisible"
                :title="title"
                width="50%"
                @close="DialogClosed"
                :close-on-click-modal="false">
      <el-form :model="pageForm"
                label-width="100px"
                :rules="pageFormRules"
                ref="pageFormRef">
        <el-row>
          <el-col :span="24">
            <el-form-item label="页面名称" prop="page_name">
              <el-input v-model="pageForm.page_name">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="页面类型" prop="page_type">
              <el-select v-model="pageForm.page_type">
                <el-option label="WEB" value="WEB"></el-option>
                <el-option label="WAP" value="WAP"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="页面地址" prop="page_url">
              <el-input v-model="pageForm.page_url">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="pageForm.remark"></el-input>
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="type!='check'">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitPage">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'page_info',
      user_name: window.localStorage.getItem('user_name'),
      queryInfo: {
        search_page_id: null,
        search_page_name: '',
        creater: window.localStorage.getItem('user_name'),
        pagenum: 1,
        pagesize: 5
      },
      pageList: [],
      total: 0,
      dialogVisible: false,
      title: '',
      pageForm: {
        page_name: '',
        page_type: 'WEB',
        page_url: '',
        remark: ''
      },
      pageFormRules: {
        page_name: [
          { required: true, message: '请输入页面名称', trigger: 'blur' }
        ],
        page_type: [
          { required: true, message: '请选择页面类型', trigger: 'blur' }
        ],
        page_url: [
          { required: true, message: '请输入页面地址', trigger: 'blur' }
        ]
      },
      isAdd: true,
      type: ''
    }
  },
  created() {
    this.getPageList()
  },
  methods: {
    queryPage() {
      this.queryInfo.pagenum = 1
      this.getPageList()
    },
    async getPageList() {
      const { data: res } = await this.$http.get(this.model + '/page_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取参数列表失败')
      console.log('数据库列表==>', res)
      this.total = res.data.total
      this.pageList = res.data.pages
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getPageList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getPageList()
    },
    DialogClosed() {
      this.$refs.pageFormRef.resetField()
    },
    showDialog(isAdd, page, type) {
      this.type = type
      if (this.type === 'add') {
        this.title = '新增页面'
      } else if (this.type === 'update') {
        this.title = '编辑页面'
      } else if (this.type === 'check') {
        this.title = '查看页面'
      }
      this.isAdd = isAdd
      if (!isAdd) {
        const objString = JSON.stringify(page)
        this.pageForm = JSON.parse(objString)
      }
      this.dialogVisible = true
    },
    submitPage() {
      this.$refs.pageFormRef.validate(async valid => {
        if (!valid) return false
        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/page_add/', this.pageForm)
          if (res.meta.status !== 200) return this.$message.error('新增页面失败')
          this.$message.success('新增页面成功')
          this.getPageList()
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.pageForm.page_id + '/page_update/', this.pageForm)
          if (res.meta.status !== 200) return this.$message.error('编辑参数失败')
          this.$message.success('编辑页面成功')
          this.getPageList()
        }
        this.dialogVisible = false
      })
    },
    removePageById(id) {
      this.$confirm('此操作将永久删除该页面, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete(this.model + '/' + id + '/page_delete/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error('删除页面失败')
          this.$message.success('删除页面成功')
          this.getPageList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>
