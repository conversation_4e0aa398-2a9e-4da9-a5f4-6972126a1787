<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>UI测试</el-breadcrumb-item>
      <el-breadcrumb-item>WEB-UI</el-breadcrumb-item>
      <el-breadcrumb-item>WEB元素</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="5">
          <el-input placeholder="请输入元素ID"
                    v-model="queryInfo.search_element_id"
                    clearable>
          </el-input>
          </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入元素名称"
                    v-model="queryInfo.search_element_name"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryWebElment">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">
          <el-button class="add" type="primary" @click="showDialog(true, {}, 'add')">添加元素</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="webElementList" border>
          <el-table-column label="ID" prop="element_id" min-width="50px"></el-table-column>
          <el-table-column label="元素名称" prop="element_name" min-width="120px" show-overflow-tooltip></el-table-column>
          <el-table-column label="定位方式" prop="find_element_type" show-overflow-tooltip min-width="100px"></el-table-column>
          <el-table-column label="定位值" prop="element_value" show-overflow-tooltip min-width="100px"></el-table-column>
          <el-table-column label="操作方式" prop="operate_element_type" min-width="100px"></el-table-column>
          <el-table-column label="操作值" prop="send_value" min-width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column label="备注" prop="remark" min-width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column label="创建人" prop="creater" min-width="100px"></el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="120px"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="170px">
            <template v-slot="slotProps">
            <el-tooltip class="item" effect="dark" content="查看" placement="top">
              <el-button type="warning" icon="el-icon-view" size="mini" @click="showDialog(false,slotProps.row, 'check')"></el-button>
            </el-tooltip>
            <el-button type="primary" v-if="slotProps.row.creater==user_name" icon="el-icon-edit" size="mini" @click="showDialog(false,slotProps.row, 'update')"></el-button>
            <el-button type="danger" v-if="slotProps.row.creater==user_name" icon="el-icon-delete" size="mini" @click="removeWebElementById(slotProps.row.element_id)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 新增编辑对话框 -->
    <el-dialog :visible.sync="dialogVisible"
                :title="title"
                width="50%"
                @close="DialogClosed"
                :close-on-click-modal="false">
      <el-form :model="webElementForm"
                label-width="100px"
                :rules="webElementFormRules"
                ref="webElementFormRef">
        <el-row>
          <el-col :span="24">
            <el-form-item label="元素名称" prop="element_name">
              <el-input v-model="webElementForm.element_name">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属页面" prop="page_id">
              <el-select v-model="webElementForm.page_id">
                <el-option v-for="item in pageList"
                           :key="item.id"
                           :label="item.label"
                           :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标签名称" prop="label_name">
              <el-input v-model="webElementForm.label_name"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="定位方式" prop="find_element_type">
              <el-select v-model="webElementForm.find_element_type">
                <el-option label="ID" value="ID"></el-option>
                <el-option label="CLASS" value="CLASS"></el-option>
                <el-option label="NAME" value="NAME"></el-option>
                <el-option label="XPATH" value="XPATH"></el-option>
                <el-option label="CSS_SELECTOR" value="CSS_SELECTOR"></el-option>
                <el-option label="LINK_TEXT" value="LINK_TEXT"></el-option>
                <el-option label="TAG_NAME" value="TAG_NAME"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="定位值" prop="element_value">
              <el-input v-model="webElementForm.element_value"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="元素索引" prop="element_num">
              <el-input v-model="webElementForm.element_num"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="休眠时间" prop="sleep_time">
              <el-input v-model="webElementForm.sleep_time"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
         <el-row>
          <el-col :span="12">
            <el-form-item label="操作方式" prop="operate_element_type">
              <el-select v-model="webElementForm.operate_element_type">
                <el-option label="click" value="click"></el-option>
                <el-option label="sendKeys" value="sendKeys"></el-option>
                <el-option label="select" value="select"></el-option>
                <el-option label="hover" value="hover"></el-option>
                <el-option label="scrollTop" value="scrollTop"></el-option>
                <el-option label="scrollLeft" value="scrollLeft"></el-option>
                <el-option label="uploadFile" value="uploadFile"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="webElementForm.operate_element_type!=='click'&&webElementForm.operate_element_type!=='hover'">
            <el-form-item label="操作值" prop="send_value">
              <el-input v-model="webElementForm.send_value"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="页面滑动" prop="sweep_page">
              <el-select v-model="webElementForm.sweep_page">
                <el-option label="无" value="0"></el-option>
                <el-option label="上滑" value="1"></el-option>
                <el-option label="下滑" value="2"></el-option>
                <el-option label="左滑" value="3"></el-option>
                <el-option label="右滑" value="4"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="切换窗口" prop="is_switch_window">
              <el-select v-model="webElementForm.is_switch_window">
                <el-option label="不切换" value="0"></el-option>
                <el-option label="切换其他窗口" value="1"></el-option>
                <el-option label="切换主窗口" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="webElementForm.is_switch_window==='1'">
          <el-col :span="12">
            <el-form-item label="窗口值" prop="window_value">
              <el-input v-model="webElementForm.window_value"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="webElementForm.remark"></el-input>
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="type != 'check'">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitWebElement">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'web_element',
      user_name: window.localStorage.getItem('user_name'),
      queryInfo: {
        search_element_id: null,
        search_element_name: '',
        creater: window.localStorage.getItem('user_name'),
        pagenum: 1,
        pagesize: 5
      },
      webElementList: [],
      total: 0,
      dialogVisible: false,
      title: '',
      webElementForm: {
        find_element_type: 'ID',
        operate_element_type: 'click',
        is_switch_window: '0'
      },
      pageList: [],
      webElementFormRules: {
        page_id: [
          { required: true, message: '请选择所属页面', trigger: 'blur' }
        ],
        label_name: [
          { required: true, message: '请输入标签名称', trigger: 'blur' }
        ],
        element_name: [
          { required: true, message: '请输入元素名称', trigger: 'blur' }
        ],
        find_element_type: [
          { required: true, message: '请选择定位方式', trigger: 'blur' }
        ],
        element_value: [
          { required: true, message: '请输入定位值', trigger: 'blur' }
        ],
        operate_element_type: [
          { required: true, message: '请选择操作方式', trigger: 'blur' }
        ],
        send_value: [
          { required: true, message: '请输入操作值', trigger: 'blur' }
        ]
      },
      isAdd: true
    }
  },
  created() {
    this.getWebElmentList()
    this.getPageList()
  },
  methods: {
    queryWebElment() {
      this.queryInfo.pagenum = 1
      this.getWebElmentList()
    },
    async getWebElmentList() {
      const { data: res } = await this.$http.get(this.model + '/web_element_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取元素列表失败')
      this.total = res.data.total
      this.webElementList = res.data.webElements
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getWebElmentList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getWebElmentList()
    },
    DialogClosed() {
      this.webElementForm = {
        find_element_type: 'ID',
        operate_element_type: 'click',
        is_switch_window: '0'
      }
    },
    showDialog(isAdd, element, type) {
      this.type = type
      if (this.type === 'add') {
        this.title = '新增元素'
      } else if (this.type === 'update') {
        this.title = '编辑元素'
      } else if (this.type === 'check') {
        this.title = '查看元素'
      }
      this.isAdd = isAdd
      if (!isAdd) {
        const objString = JSON.stringify(element)
        this.webElementForm = JSON.parse(objString)
      }
      this.dialogVisible = true
    },
    submitWebElement() {
      this.$refs.webElementFormRef.validate(async valid => {
        if (!valid) return false
        if (this.webElementForm.operate_element_type === 'click' || this.webElementForm.operate_element_type === 'hover') {
          this.webElementForm.send_value = ''
        }
        if (this.webElementForm.is_switch_window !== '1') {
          this.webElementForm.window_value = ''
        }
        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/web_element_add/', this.webElementForm)
          if (res.meta.status !== 200) return this.$message.error('新增页面元素失败')
          this.$message.success('新增页面元素成功')
          this.getWebElmentList()
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.webElementForm.element_id + '/web_element_update/', this.webElementForm)
          if (res.meta.status !== 200) return this.$message.error('编辑页面元素失败')
          this.$message.success('编辑页面元素成功')
          this.getWebElmentList()
        }
        this.dialogVisible = false
      })
    },
    removeWebElementById(id) {
      this.$confirm('此操作将永久删除该元素, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete(this.model + '/' + id + '/web_element_delete/').then(res => {
          if (res.data.meta.status !== 200) return this.$message.error(res.data.meta.msg)
          this.$message.success('删除元素成功')
          this.getWebElmentList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async getPageList() {
      const { data: res } = await this.$http.get('user_info/info/', { params: { type: 'pages' } })
      if (res.meta.status !== 200) return this.$message.error('获取页面数据失败')
      this.pageList = res.data
    }
  }
}
</script>
