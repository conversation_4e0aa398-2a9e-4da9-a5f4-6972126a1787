<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>UI测试</el-breadcrumb-item>
      <el-breadcrumb-item>UI块管理</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="5">
          <el-input placeholder="请输入块ID"
                    v-model="queryInfo.search_chunk_id"
                    clearable>
          </el-input>
          </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入块名称"
                    v-model="queryInfo.search_chunk_name"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryChunk">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">
          <el-button class="add" type="primary" @click="showDialog(true, 0, 'add')">添加块</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="chunkList" border>
          <el-table-column label="ID" prop="chunk_id" min-width="50px"></el-table-column>
          <el-table-column label="块名称" prop="chunk_name" min-width="150px"></el-table-column>
          <el-table-column label="块描述" prop="chunk_describe" show-overflow-tooltip min-width="100px"></el-table-column>
          <el-table-column label="元素类型" prop="element_type" min-width="100px"></el-table-column>
          <el-table-column label="创建人" prop="creater" min-width="100px"></el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="120px"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="120px">
            <template v-slot="slotProps">
            <el-tooltip class="item" effect="dark" content="查看" placement="top">
              <el-button type="warning" icon="el-icon-view" size="mini" @click="showDialog(false,slotProps.row.chunk_id, 'check')"></el-button>
            </el-tooltip>
            <el-button type="primary" v-if="slotProps.row.creater==user_name" icon="el-icon-edit" size="mini" @click="showDialog(false, slotProps.row.chunk_id, 'update')"></el-button>
            <el-button type="danger" v-if="slotProps.row.creater==user_name" icon="el-icon-delete" size="mini" @click="removeChunkById(slotProps.row.chunk_id)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 新增编辑对话框 -->
    <el-dialog :visible.sync="dialogVisible"
                :title="title"
                width="65%"
                @close="DialogClosed"
                :close-on-click-modal="false"
                class="chunk-dialog">
      <el-form :model="chunkForm"
                label-width="100px"
                :rules="chunkFormRules"
                ref="chunkFormRef">
        <el-row>
          <el-col :span="12">
            <el-form-item label="块名称" prop="chunk_name">
              <el-input v-model="chunkForm.chunk_name">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="块描述" prop="chunk_describe">
              <el-input v-model="chunkForm.chunk_describe">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="元素类型">
              <el-radio v-model="chunkForm.element_type" label="WEB">WEB</el-radio>
              <el-radio v-model="chunkForm.element_type" label="Android">Android</el-radio>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="元素操作">
            <div class="web-transfer">
              <el-transfer
                filterable
                filter-placeholder="请输入元素操作"
                v-model="chunkForm.element_pool"
                :data="elementList"
                :props="elementProps"
                :titles="['待选元素', '已选元素']">
              </el-transfer>
            </div>
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="type!=='check'">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitChunk">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'chunk_info',
      user_name: window.localStorage.getItem('user_name'),
      queryInfo: {
        search_chunk_id: null,
        search_chunk_name: '',
        creater: window.localStorage.getItem('user_name'),
        pagenum: 1,
        pagesize: 5
      },
      chunkList: [],
      elementList: [],
      elementProps: {
        key: 'id',
        label: 'label'
      },
      total: 0,
      dialogVisible: false,
      title: '',
      chunkForm: {
        chunk_name: '',
        chunk_describe: '',
        element_type: 'WEB',
        element_pool: []
      },
      chunkFormRules: {
        chunk_name: [
          { required: true, message: '请输入块名称', trigger: 'blur' }
        ]
      },
      isAdd: true,
      type: ''
    }
  },
  created() {
    this.getChunkList()
    this.getElementList()
  },
  methods: {
    queryChunk() {
      this.queryInfo.pagenum = 1
      this.getChunkList()
    },
    async getChunkList() {
      const { data: res } = await this.$http.get(this.model + '/chunk_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取块列表失败')
      this.total = res.data.total
      this.chunkList = res.data.chunks
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getChunkList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getChunkList()
    },
    DialogClosed() {
      this.chunkForm = {
        chunk_name: '',
        chunk_describe: '',
        element_type: 'WEB',
        element_pool: []
      }
    },
    async showDialog(isAdd, id, type) {
      this.type = type
      if (this.type === 'add') {
        this.title = '新增块'
      } else if (this.type === 'update') {
        this.title = '编辑块'
      } else if (this.type === 'check') {
        this.title = '查看块'
      }
      this.isAdd = isAdd
      if (!isAdd) {
        const { data: res } = await this.$http.get('/user_info/get_base_info/', { params: { id: id, type: 'Chunks' } })
        if (res.meta.status !== 200) this.$message.error('获取块编辑信息失败')
        this.chunkForm = res.data
      }
      this.dialogVisible = true
    },
    submitChunk() {
      this.$refs.chunkFormRef.validate(async valid => {
        if (!valid) return false
        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/chunk_add/', this.chunkForm)
          if (res.meta.status !== 200) return this.$message.error('新增元素块失败')
          this.$message.success('新增元素块成功')
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.chunkForm.chunk_id + '/chunk_update/', this.chunkForm)
          if (res.meta.status !== 200) return this.$message.error('编辑参数失败')
          this.$message.success('编辑参数成功')
        }
        this.getChunkList()
        this.dialogVisible = false
      })
    },
    removeChunkById(id) {
      this.$confirm('此操作将永久删除该元素块, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete(this.model + '/' + id + '/chunk_delete/').then(res => {
          if (res.data.meta.status !== 200) return this.$message.error('删除元素块失败')
          this.$message.success('删除元素块成功')
          this.getChunkList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async getElementList() {
      const { data: res } = await this.$http.get('user_info/info/', { params: { type: 'webChunks' } })
      this.elementList = res.data
    }
  }
}
</script>

<style lang="less" scoped>
.chunk-dialog .el-input {
  width: 80% !important;
}
</style>
