<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>UI测试</el-breadcrumb-item>
      <el-breadcrumb-item>安卓-UI</el-breadcrumb-item>
      <el-breadcrumb-item>设备管理</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="5">
          <el-input placeholder="请输入设备ID"
                    v-model="queryInfo.search_device_id"
                    clearable>
          </el-input>
          </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入设备名称"
                    v-model="queryInfo.search_device_name"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="querydevice">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">
          <el-button class="add" type="primary" @click="showDialog(true, {}, 'add')">添加设备</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="deviceList" border>
          <el-table-column label="ID" prop="device_id" min-width="50px"></el-table-column>
          <el-table-column label="设备名称" prop="device_name"></el-table-column>
          <el-table-column label="安卓版本" prop="platform_version"></el-table-column>
          <el-table-column label="设备编码" prop="device_udid" min-width="100px"></el-table-column>
          <el-table-column label="连接方式" prop="connect_way"></el-table-column>
          <el-table-column label="备注" prop="remark"></el-table-column>
          <el-table-column label="创建人" prop="creater"></el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="120px"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="120px">
            <template v-slot="slotProps">
            <el-tooltip class="item" effect="dark" content="查看" placement="top">
              <el-button type="warning" icon="el-icon-view" size="mini" @click="showDialog(false,slotProps.row, 'check')"></el-button>
            </el-tooltip>
            <el-button type="primary" v-if="slotProps.row.creater==user_name" icon="el-icon-edit" size="mini" @click="showDialog(false,slotProps.row, 'update')"></el-button>
            <el-button type="danger" v-if="slotProps.row.creater==user_name" icon="el-icon-delete" size="mini" @click="removePageById(slotProps.row.device_id)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 新增编辑对话框 -->
    <el-dialog :visible.sync="dialogVisible"
                :title="title"
                width="50%"
                @close="DialogClosed"
                :close-on-click-modal="false">
      <el-form :model="deviceForm"
                label-width="100px"
                :rules="deviceFormRules"
                ref="deviceFormRef">
        <el-row>
          <el-col :span="24">
            <el-form-item label="设备名称" prop="device_name">
              <el-input v-model="deviceForm.device_name">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="安卓版本" prop="platform_version">
              <el-input v-model="deviceForm.platform_version">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="连接方式" prop="connect_way">
              <el-select v-model="deviceForm.connect_way">
                <el-option label="USB数据线连接" value="USB"></el-option>
                <el-option label="WIFI无线连接" value="WIFI"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="设备编码" prop="device_udid">
              <el-input v-model="deviceForm.device_udid">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="deviceForm.remark"></el-input>
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="type!='check'">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitPage">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'device',
      user_name: window.localStorage.getItem('user_name'),
      queryInfo: {
        search_device_id: null,
        search_device_name: '',
        pagenum: 1,
        pagesize: 5,
        creater: window.localStorage.getItem('user_name')
      },
      deviceList: [],
      total: 0,
      dialogVisible: false,
      title: '',
      deviceForm: {
        device_name: '',
        platform_version: '',
        device_udid: '',
        connect_way: 'USB',
        remark: ''
      },
      deviceFormRules: {
        device_name: [
          { required: true, message: '请输入设备名称', trigger: 'blur' }
        ],
        platform_version: [
          { required: true, message: '请选择安卓版本', trigger: 'blur' }
        ],
        connect_way: [
          { required: true, message: '请输入设备地址', trigger: 'blur' }
        ],
        device_udid: [
          { required: true, message: '请输入设备编码', trigger: 'blur' }
        ]
      },
      isAdd: true,
      type: ''
    }
  },
  created() {
    this.getdeviceList()
  },
  methods: {
    querydevice() {
      this.queryInfo.pagenum = 1
      this.getdeviceList()
    },
    async getdeviceList() {
      const { data: res } = await this.$http.get(this.model + '/device_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取设备列表失败')
      this.total = res.data.total
      this.deviceList = res.data.devices
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getdeviceList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getdeviceList()
    },
    DialogClosed() {
      this.deviceForm = {
        device_name: '',
        platform_version: '',
        device_udid: '',
        connect_way: 'USB',
        remark: ''
      }
    },
    showDialog(isAdd, page, type) {
      this.type = type
      if (this.type === 'add') {
        this.title = '新增设备'
      } else if (this.type === 'update') {
        this.title = '编辑设备'
      } else if (this.type === 'check') {
        this.title = '查看设备'
      }
      this.isAdd = isAdd
      if (!isAdd) {
        const objString = JSON.stringify(page)
        this.deviceForm = JSON.parse(objString)
      }
      this.dialogVisible = true
    },
    submitPage() {
      this.$refs.deviceFormRef.validate(async valid => {
        if (!valid) return false
        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/device_add/', this.deviceForm)
          if (res.meta.status !== 200) return this.$message.error('新增设备失败')
          this.$message.success('新增设备成功')
          this.getdeviceList()
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.deviceForm.device_id + '/device_update/', this.deviceForm)
          if (res.meta.status !== 200) return this.$message.error('编辑设备失败')
          this.$message.success('编辑设备成功')
          this.getdeviceList()
        }
        this.dialogVisible = false
      })
    },
    removePageById(id) {
      this.$confirm('此操作将永久删除该设备, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete(this.model + '/' + id + '/device_delete/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error('删除设备失败')
          this.$message.success('删除设备成功')
          this.getdeviceList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>
