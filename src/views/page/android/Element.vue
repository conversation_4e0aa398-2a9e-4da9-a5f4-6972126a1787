<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>UI测试</el-breadcrumb-item>
      <el-breadcrumb-item>安卓-UI</el-breadcrumb-item>
      <el-breadcrumb-item>安卓元素</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="5">
          <el-input placeholder="请输入元素ID"
                    v-model="queryInfo.search_appElement_id"
                    clearable>
          </el-input>
          </el-col>
          <el-col :span="5">
          <el-input placeholder="请输入元素名称"
                    v-model="queryInfo.search_appElement_name"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryElement">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">
          <el-button class="add" type="primary" @click="showDialog(true, {}, 'add')">添加元素</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="elementList" border>
          <el-table-column label="ID" prop="appElement_id" min-width="50px" fixed="left"></el-table-column>
          <el-table-column label="元素名称" prop="appElement_name" min-width="150px" show-overflow-tooltip fixed="left"></el-table-column>
          <el-table-column label="定位方式" prop="find_appElement_type" show-overflow-tooltip min-width="100px"></el-table-column>
          <el-table-column label="定位值" prop="appElement_value" min-width="120px" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作方式" prop="operate_appElement_type" show-overflow-tooltip min-width="100px"></el-table-column>
          <el-table-column label="操作值" prop="send_value" min-width="120px" show-overflow-tooltip></el-table-column>
          <el-table-column label="存在操作" prop="appElement_exists" show-overflow-tooltip min-width="100px"></el-table-column>
          <el-table-column label="不存在操作" prop="appElement_not_exists" show-overflow-tooltip min-width="100px"></el-table-column>
          <el-table-column label="休眠时间" prop="sleep_time" min-width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column label="页面滑动" prop="sweep_page" show-overflow-tooltip min-width="100px">
            <template v-slot="slotProps">
              <span v-if="slotProps.row.sweep_page==='0'">无</span>
              <span v-else-if="slotProps.row.sweep_page==='1'">上滑</span>
              <span v-else-if="slotProps.row.sweep_page==='2'">下滑</span>
              <span v-else-if="slotProps.row.sweep_page==='3'">左滑</span>
              <span v-else-if="slotProps.row.sweep_page==='4'">右滑</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="remark" show-overflow-tooltip min-width="100px"></el-table-column>
          <el-table-column label="创建人" prop="creater" min-width="100px"></el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="150px"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="200px">
            <template v-slot="slotProps">
            <el-tooltip class="item" effect="dark" content="查看" placement="top">
              <el-button type="warning" icon="el-icon-view" size="mini" @click="showDialog(false,slotProps.row, 'check')"></el-button>
            </el-tooltip>
            <el-button type="primary" v-if="slotProps.row.creater==user_name" icon="el-icon-edit" size="mini" @click="showDialog(false,slotProps.row, 'update')"></el-button>
            <el-button type="danger" v-if="slotProps.row.creater==user_name" icon="el-icon-delete" size="mini" @click="removePageById(slotProps.row.appElement_id)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 新增编辑对话框 -->
    <el-dialog :visible.sync="dialogVisible"
                :title="title"
                width="50%"
                @close="DialogClosed"
                :close-on-click-modal="false">
      <el-form :model="elementForm"
                label-width="100px"
                :rules="elementFormRules"
                ref="elementFormRef">
        <el-row>
          <el-col :span="12">
            <el-form-item label="元素名称" prop="appElement_name">
              <el-input v-model="elementForm.appElement_name">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="定位方式" prop="find_appElement_type">
              <el-select v-model="elementForm.find_appElement_type">
                <el-option label="ID" value="ID"></el-option>
                <el-option label="NAME" value="NAME"></el-option>
                <el-option label="CLASS" value="CLASS"></el-option>
                <el-option label="XPATH" value="XPATH"></el-option>
                <el-option label="AccessibilityId" value="AccessibilityId"></el-option>
                <el-option label="AndroidUiautomator" value="AndroidUiautomator"></el-option>
                <el-option label="Point（坐标定位并点击）" value="Point"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="定位值" prop="appElement_value">
              <el-input v-model="elementForm.appElement_value">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="操作方式" prop="operate_appElement_type">
              <el-select v-model="elementForm.operate_appElement_type">
                <el-option label="click" value="click"></el-option>
                <el-option label="sendKeys" value="sendKeys"></el-option>
                <el-option label="isExists" value="isExists"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="elementForm.operate_appElement_type==='sendKeys'">
            <el-form-item label="操作值" prop="send_value">
              <el-input v-model="elementForm.send_value">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="elementForm.operate_appElement_type==='isExists'">
          <el-col :span="12">
            <el-form-item label="存在操作" prop="appElement_exists">
              <el-select v-model="elementForm.appElement_exists">
                <el-option v-for="item in elementOptionList"
                           :key="item.id"
                           :label="item.label"
                           :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="不存在操作" prop="appElement_not_exists">
              <el-select v-model="elementForm.appElement_not_exists">
                <el-option v-for="item in elementOptionList"
                           :key="item.id"
                           :label="item.label"
                           :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="休眠时间" prop="sleep_time">
              <el-input v-model="elementForm.sleep_time">
                <template slot="append">秒</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="页面滑动" prop="sweep_page">
              <el-select v-model="elementForm.sweep_page">
                <el-option label="无" value="0"></el-option>
                <el-option label="上滑" value="1"></el-option>
                <el-option label="下滑" value="2"></el-option>
                <el-option label="左滑" value="3"></el-option>
                <el-option label="右滑" value="4"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="elementForm.remark"></el-input>
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="type!='check'">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitPage">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'app_element',
      user_name: window.localStorage.getItem('user_name'),
      queryInfo: {
        search_appElement_id: null,
        search_appElement_name: '',
        creater: window.localStorage.getItem('user_name'),
        pagenum: 1,
        pagesize: 5
      },
      elementList: [],
      total: 0,
      dialogVisible: false,
      title: '',
      elementForm: {
      },
      elementOptionList: [],
      elementFormRules: {
        device_name: [
          { required: true, message: '请输入元素名称', trigger: 'blur' }
        ],
        platform_version: [
          { required: true, message: '请选择安卓版本', trigger: 'blur' }
        ],
        connect_way: [
          { required: true, message: '请输入元素地址', trigger: 'blur' }
        ],
        device_udid: [
          { required: true, message: '请输入元素编码', trigger: 'blur' }
        ]
      },
      isAdd: true,
      type: ''
    }
  },
  created() {
    this.getElementList()
    this.getElementOptionList()
  },
  methods: {
    queryElement() {
      this.queryInfo.pagenum = 1
      this.getElementList()
    },
    async getElementList() {
      const { data: res } = await this.$http.get(this.model + '/element_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取元素列表失败')
      this.total = res.data.total
      this.elementList = res.data.appElements
    },
    async getElementOptionList() {
      const { data: res } = await this.$http.get('/user_info/info/', { params: { type: 'appElements' } })
      if (res.meta.status !== 200) return this.$message.error('获取元素下拉信息失败')
      this.elementOptionList = res.data
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getElementList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getElementList()
    },
    DialogClosed() {
      this.$refs.elementFormRef.resetField()
    },
    showDialog(isAdd, page, type) {
      this.type = type
      if (this.type === 'add') {
        this.title = '新增元素'
      } else if (this.type === 'update') {
        this.title = '编辑元素'
      } else if (this.type === 'check') {
        this.title = '查看元素'
      }
      this.isAdd = isAdd
      if (!isAdd) {
        const objString = JSON.stringify(page)
        this.elementForm = JSON.parse(objString)
      }
      this.dialogVisible = true
    },
    submitPage() {
      this.$refs.elementFormRef.validate(async valid => {
        if (!valid) return false
        if (this.elementForm.operate_appElement_type === 'click') {
          this.elementForm.send_value = ''
          this.elementForm.appElement_exists = ''
          this.elementForm.appElement_not_exists = ''
        } else if (this.elementForm.operate_appElement_type === 'sendKeys') {
          this.elementForm.appElement_exists = ''
          this.elementForm.appElement_not_exists = ''
        } else if (this.elementForm.operate_appElement_type === 'isExists') {
          this.elementForm.send_value = ''
        }
        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/element_add/', this.elementForm)
          if (res.meta.status !== 200) return this.$message.error('新增元素失败')
          this.$message.success('新增元素成功')
          this.getElementList()
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.elementForm.appElement_id + '/element_update/', this.elementForm)
          if (res.meta.status !== 200) return this.$message.error('编辑元素失败')
          this.$message.success('编辑元素成功')
          this.getElementList()
        }
        this.dialogVisible = false
      })
    },
    removePageById(id) {
      this.$confirm('此操作将永久删除该元素, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete(this.model + '/' + id + '/element_delete/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error('删除元素失败')
          this.$message.success('删除元素成功')
          this.getElementList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>
