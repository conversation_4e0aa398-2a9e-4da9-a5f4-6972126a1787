<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>UI测试</el-breadcrumb-item>
      <el-breadcrumb-item>安卓-UI</el-breadcrumb-item>
      <el-breadcrumb-item>安卓案例</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="5">
          <el-input placeholder="请输入案例ID"
                    v-model="queryInfo.search_androidCase_id"
                    clearable>
          </el-input>
          </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入案例名称"
                    v-model="queryInfo.search_androidCase_name"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryCase">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">
          <el-button class="add" type="primary" @click="showDialog(true, 0, 'add')">添加案例</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="caseList" border>
          <el-table-column label="ID" prop="androidCase_id" min-width="50px"></el-table-column>
          <el-table-column label="案例名称" prop="androidCase_name" min-width="150px" show-overflow-tooltip></el-table-column>
          <el-table-column label="案例描述" prop="androidCase_describe" show-overflow-tooltip min-width="120px"></el-table-column>
          <el-table-column label="案例等级" prop="androidCase_level" min-width="100px"></el-table-column>
          <el-table-column label="待测真机" prop="test_device" min-width="120px"></el-table-column>
          <el-table-column label="待测应用" prop="test_application" min-width="120px"></el-table-column>
          <el-table-column label="是否断言" prop="is_assert" min-width="100px">
            <template v-slot="scopeProps">
              <span v-if="scopeProps.row.is_assert">是</span>
              <span v-else>否</span>
            </template>
          </el-table-column>
          <el-table-column label="创建人" prop="creater" min-width="100px"></el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="150px"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="260px">
            <template v-slot="slotProps">
            <el-tooltip class="item" effect="dark" content="查看" placement="top">
              <el-button type="warning" icon="el-icon-view" size="mini" @click="showDialog(false,slotProps.row.androidCase_id, 'check')"></el-button>
            </el-tooltip>
            <el-button type="primary" v-if="slotProps.row.creater==user_name" icon="el-icon-edit" size="mini" @click="showDialog(false, slotProps.row.androidCase_id, 'update')"></el-button>
            <el-button type="danger" v-if="slotProps.row.creater==user_name" icon="el-icon-delete" size="mini" @click="removeCaseById(slotProps.row.androidCase_id)"></el-button>
            <!-- <el-tooltip class="item" effect="dark" content="导出案例脚本" placement="top" :enterable="false">
              <el-button type="info" icon="el-icon-download" size="mini" @click="showEnviromentDialog(slotProps.row.androidCase_id)"></el-button>
            </el-tooltip> -->
            <el-tooltip class="item" effect="dark" content="调试案例" placement="top" :enterable="false">
              <el-button type="success" icon="el-icon-caret-right" size="mini" @click="showSceneRunDialog(slotProps.row.androidCase_id)"></el-button>
            </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 新增编辑对话框 -->
    <el-dialog :visible.sync="dialogVisible"
                :title="title"
                width="50%"
                @close="DialogClosed"
                :close-on-click-modal="false">
      <el-form :model="caseForm"
                label-width="100px"
                :rules="caseFormRules"
                ref="caseFormRef">
        <el-row>
          <el-col :span="24">
            <el-form-item label="案例名称" prop="androidCase_name">
              <el-input v-model="caseForm.androidCase_name">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="案例描述" prop="androidCase_describe">
              <el-input v-model="caseForm.androidCase_describe">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="案例等级" prop="androidCase_level">
              <el-select v-model="caseForm.androidCase_level">
                <el-option label="低" value="低"></el-option>
                <el-option label="普通" value="普通"></el-option>
                <el-option label="高" value="高"></el-option>
                <el-option label="紧急" value="紧急"></el-option>
                <el-option label="立刻" value="立刻"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="待测设备" prop="test_device">
              <el-select v-model="caseForm.test_device">
                <el-option v-for="item in deviceList"
                           :key="item.id"
                           :label="item.label"
                           :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="待测应用" prop="test_application">
              <el-select v-model="caseForm.test_application">
                <el-option v-for="item in applicationList"
                           :key="item.id"
                           :label="item.label"
                           :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 案例组成 -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="案例组成" prop="page_url">
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-button type="primary"
                         @click="addFormation">添加组成</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 案例组成表格 -->
        <el-row>
          <el-form-item>
            <el-table :data="caseForm.formation">
              <el-table-column label="组成类型" prop="formation_type">
                <template v-slot="scopeProps">
                  <el-select v-model="scopeProps.row.formation_type">
                    <el-option label="元素操作" value="0"></el-option>
                    <el-option label="UI块" value="1"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="组成内容" prop="web_case_name">
                <template v-slot="scopeProps">
                  <el-select v-if="scopeProps.row.formation_type=='1'"
                             v-model="scopeProps.row.chunk_id">
                    <el-option v-for="item in chunkList"
                               :key="item.id"
                               :label="item.label"
                               :value="item.id"></el-option>
                  </el-select>
                  <el-select v-else-if="scopeProps.row.formation_type=='0'"
                             v-model="scopeProps.row.element_id">
                    <el-option v-for="item in elementkList"
                               :key="item.id"
                               :label="item.label"
                               :value="item.id"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column min-width="50px">
                <template v-slot="slotProps">
                  <el-button type="danger"
                             size="mini"
                             @click="removeCurrentFormation(slotProps.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-row>
        <!-- 断言 -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否断言" prop="is_assert">
              <el-switch
                v-model="caseForm.is_assert"
                active-color="#13ce66">
              </el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="caseForm.is_assert">
            <el-form-item>
              <el-button type="primary"
                         @click="addAssert">添加断言</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 断言组成表格 -->
        <el-row v-if="caseForm.is_assert">
          <el-form-item>
            <el-table :data="caseForm.asserts">
              <el-table-column label="断言数据" min-width="50px">网页代码</el-table-column>
              <el-table-column label="断言类型">
                <template v-slot="scopeProps">
                  <el-select v-model="scopeProps.row.assert_operator">
                   <el-option label="包含" value="=="></el-option>
                   <el-option label="不包含" value="!="></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="期望值">
                <template v-slot="scopeProps">
                  <el-input v-model="scopeProps.row.assert_result"></el-input>
                </template>
              </el-table-column>
              <el-table-column min-width="50px">
                <template v-slot="slotProps">
                  <el-button type="danger"
                             size="mini"
                             @click="removeCurrentAssert(slotProps.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitCase">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 环境选择对话框 -->
    <el-dialog :visible.sync="enviromentDialogVisible"
                width="30%"
                @close="enviromentDialogClosed"
                :close-on-click-modal="false">
      <el-form label-width="130px"
               :model="enviromentForm"
               ref="enviromentFormRef"
               :rules="enviromentFormRules">
        <el-form-item label="请选择运行环境" prop="env_type">
          <el-select v-model="enviromentForm.env_type">
            <el-option label="测试环境" value="1"></el-option>
            <el-option label="预发环境" value="2"></el-option>
            <el-option label="生产环境" value="3"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="type!='check'">
        <el-button @click="enviromentDialogVisible = false">取 消</el-button>
        <el-button v-if="operate" type="primary" @click="downloadAndroidCase">确 定</el-button>
        <el-button v-else type="primary" @click="runAndroidCase">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'app_case',
      user_name: window.localStorage.getItem('user_name'),
      queryInfo: {
        search_androidCase_id: null,
        search_androidCase_name: '',
        creater: window.localStorage.getItem('user_name'),
        pagenum: 1,
        pagesize: 5
      },
      caseList: [],
      total: 0,
      dialogVisible: false,
      title: '',
      chunkList: [],
      elementkList: [],
      deviceList: [],
      applicationList: [],
      caseForm: {
        androidCase_name: '',
        androidCase_describe: '',
        androidCase_level: '低',
        test_device: null,
        test_application: null,
        formation: [
          {
            formation_type: '0',
            chunk_id: '',
            element_id: ''
          }
        ],
        is_assert: false,
        asserts: [
          {
            assert_type: '1',
            assert_operator: '==',
            assert_result: '',
            assert_database: '',
            assert_sql: ''
          }
        ]
      },
      caseFormRules: {
        androidCase_name: [
          { required: true, message: '请输入案例名称', trigger: 'blur' }
        ],
        test_device: [
          { required: true, message: '请选择待测设备', trigger: 'blur' }
        ],
        test_application: [
          { required: true, message: '请选择待测应用', trigger: 'blur' }
        ]
      },
      isAdd: true,
      enviromentForm: {
        env_type: '',
        case_type: 'Android',
        androidCase_id: ''
      },
      enviromentDialogVisible: false,
      enviromentFormRules: {
        env_type: [
          { required: true, message: '请选择运行环境', trigger: 'blur' }
        ]
      },
      operate: true
    }
  },
  created() {
    this.getCaseList()
    this.getChunkList()
    this.getElementList()
    this.getDeviceList()
    this.getApplicationList()
  },
  methods: {
    queryCase() {
      this.queryInfo.pagenum = 1
      this.getCaseList()
    },
    async getCaseList() {
      const { data: res } = await this.$http.get(this.model + '/case_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取页面案例列表失败')
      this.total = res.data.total
      this.caseList = res.data.appCases
    },
    async getDeviceList() {
      const { data: res } = await this.$http.get('user_info/info/', { params: { type: 'devices' } })
      if (res.meta.status !== 200) return this.$message.error('获取设备信息失败')
      this.deviceList = res.data
    },
    async getApplicationList() {
      const { data: res } = await this.$http.get('user_info/info/', { params: { type: 'applications' } })
      if (res.meta.status !== 200) return this.$message.error('获取应用信息失败')
      this.applicationList = res.data
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getCaseList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getCaseList()
    },
    DialogClosed() {
      this.caseForm = {
        androidCase_name: '',
        androidCase_describe: '',
        androidCase_level: '低',
        test_device: null,
        test_application: null,
        formation: [
          {
            formation_type: '0',
            chunk_id: '',
            element_id: ''
          }
        ],
        is_assert: false,
        asserts: [
          {
            assert_type: '1',
            assert_operator: '==',
            assert_result: '',
            assert_database: '',
            assert_sql: ''
          }
        ]
      }
    },
    async showDialog(isAdd, id, type) {
      this.type = type
      if (this.type === 'add') {
        this.title = '新增案例'
      } else if (this.type === 'update') {
        this.title = '编辑案例'
      } else if (this.type === 'check') {
        this.title = '查看案例'
      }
      this.title = isAdd ? '新增案例' : '编辑案例'
      this.isAdd = isAdd
      if (!isAdd) {
        const { data: res } = await this.$http.get('user_info/get_base_info/', { params: { id: id, type: 'Android' } })
        if (res.meta.status !== 200) return this.$message.error('获取案例编辑信息失败')
        this.caseForm = res.data
      }
      this.dialogVisible = true
    },
    submitCase() {
      this.$refs.caseFormRef.validate(async valid => {
        if (!valid) return false
        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/case_add/', this.caseForm)
          if (res.meta.status !== 200) return this.$message.error('新增案例失败')
          this.$message.success('新增案例成功')
          this.getCaseList()
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.caseForm.androidCase_id + '/case_update/', this.caseForm)
          if (res.meta.status !== 200) return this.$message.error('编辑参数失败')
          this.$message.success('编辑案例成功')
          this.getCaseList()
        }
        this.dialogVisible = false
      })
    },
    removeCaseById(id) {
      this.$confirm('此操作将永久删除该案例, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete(this.model + '/' + id + '/case_delete/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error('删除案例失败')
          this.$message.success('删除案例成功')
          this.getCaseList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    addFormation() {
      this.caseForm.formation.push(
        {
          formation_type: '0',
          chunk_id: '',
          element_id: ''
        }
      )
    },
    removeCurrentFormation(index) {
      this.caseForm.formation.splice(index, 1)
    },
    async getChunkList() {
      const { data: res } = await this.$http.get('user_info/info/', { params: { type: 'appChunks' } })
      if (res.meta.status !== 200) return this.$message.error('获取元素块数据失败')
      this.chunkList = res.data
    },
    async getElementList() {
      const { data: res } = await this.$http.get('user_info/info/', { params: { type: 'appElements' } })
      if (res.meta.status !== 200) return this.$message.error('获取页面元素数据失败')
      this.elementkList = res.data
    },
    addAssert() {
      this.caseForm.asserts.push(
        {
          assert_type: '1',
          assert_operator: '==',
          assert_result: '',
          assert_database: '',
          assert_sql: ''
        }
      )
    },
    removeCurrentAssert(index) {
      this.caseForm.asserts.splice(index, 1)
    },
    showEnviromentDialog(id) {
      this.operate = true
      this.enviromentForm.androidCase_id = id
      this.enviromentDialogVisible = true
    },
    enviromentDialogClosed() {
      this.enviromentForm = {
        env_type: '',
        case_type: 'Android',
        androidCase_id: ''
      }
    },
    showSceneRunDialog(id) {
      this.$confirm('请确认已连接待测设备及开启Appium服务！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.operate = false
        this.enviromentForm.androidCase_id = id
        this.enviromentDialogVisible = true
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消调试'
        })
      })
    },
    async downloadAndroidCase() {
      const { data: res } = await this.$http.post('web_case/' + this.enviromentForm.androidCase_id + '/case_export/', this.enviromentForm)
      if (res.meta.status !== 200) this.$message.error(res.meta.msg)
      this.$http({
        method: 'get',
        url: this.model + '/download/',
        responseType: 'blob',
        params: { name: res.data }
      }).then(file => {
        const blob = new Blob([file.data])
        const aEle = document.createElement('a')
        aEle.download = 'script.py'
        aEle.href = URL.createObjectURL(blob)
        aEle.click()
        URL.revokeObjectURL(aEle.href)
      }).catch(error => {
        this.$message.error('下载安卓脚本失败')
        console.log(error)
      })
      this.enviromentDialogVisible = false
    },
    async runAndroidCase() {
      this.enviromentDialogVisible = false
      const { data: res } = await this.$http.post(this.model + '/' + this.enviromentForm.androidCase_id + '/app_case_run/', this.enviromentForm)
      if (res.meta.status !== 200) return this.$message.error(res.meta.msg)
    }
  }
}
</script>
