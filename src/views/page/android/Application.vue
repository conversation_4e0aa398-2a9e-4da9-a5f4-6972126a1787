<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>UI测试</el-breadcrumb-item>
      <el-breadcrumb-item>WEB-UI</el-breadcrumb-item>
      <el-breadcrumb-item>应用管理</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="5">
          <el-input placeholder="请输入应用ID"
                    v-model="queryInfo.search_application_id"
                    clearable>
          </el-input>
          </el-col>
          <el-col :span="5">
          <el-input placeholder="请输入应用名称"
                    v-model="queryInfo.search_application_name"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input placeholder="请输入创建人"
                    v-model="queryInfo.creater"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryApplication">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">
          <el-button class="add" type="primary" @click="showDialog(true, {}, 'add')">添加应用</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="applicationList" border>
          <el-table-column label="ID" prop="application_id" min-width="50px"></el-table-column>
          <el-table-column label="应用名称" prop="application_name"></el-table-column>
          <el-table-column label="应用包名" prop="application_packageName"></el-table-column>
          <el-table-column label="启动页" prop="application_activityName" min-width="100px" show-overflow-tooltip></el-table-column>
          <el-table-column label="备注" prop="remark"></el-table-column>
          <el-table-column label="创建人" prop="creater"></el-table-column>
          <el-table-column label="最终修改时间" prop="update_time" min-width="120px"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="120px">
            <template v-slot="slotProps">
            <el-tooltip class="item" effect="dark" content="查看" placement="top">
              <el-button type="warning" icon="el-icon-view" size="mini" @click="showDialog(false,slotProps.row, 'check')"></el-button>
            </el-tooltip>
            <el-button type="primary" icon="el-icon-edit" v-if="slotProps.row.creater==user_name" size="mini" @click="showDialog(false,slotProps.row, 'update')"></el-button>
            <el-button type="danger" icon="el-icon-delete" v-if="slotProps.row.creater==user_name" size="mini" @click="removePageById(slotProps.row.application_id)"></el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
    <!-- 新增编辑对话框 -->
    <el-dialog :visible.sync="dialogVisible"
                :title="title"
                width="50%"
                @close="DialogClosed"
                :close-on-click-modal="false">
      <el-form :model="applicationForm"
                label-width="100px"
                :rules="applicationFormRules"
                ref="applicationFormRef">
        <el-row>
          <el-col :span="24">
            <el-form-item label="应用名称" prop="application_name">
              <el-input v-model="applicationForm.application_name">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="应用包名" prop="application_packageName">
              <el-input v-model="applicationForm.application_packageName">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="启动页" prop="application_activityName">
              <el-input v-model="applicationForm.application_activityName">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="applicationForm.remark"></el-input>
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer" v-if="type != 'check'">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitApplication">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'application',
      user_name: window.localStorage.getItem('user_name'),
      queryInfo: {
        search_application_id: null,
        search_application_name: '',
        creater: window.localStorage.getItem('user_name'),
        pagenum: 1,
        pagesize: 5
      },
      applicationList: [],
      total: 0,
      dialogVisible: false,
      title: '',
      applicationForm: {
        device_name: '',
        platform_version: '',
        device_udid: '',
        connect_way: 'USB',
        remark: ''
      },
      applicationFormRules: {
        application_name: [
          { required: true, message: '请输入应用名称', trigger: 'blur' }
        ],
        application_packageName: [
          { required: true, message: '请输入应用包名', trigger: 'blur' }
        ],
        application_activityName: [
          { required: true, message: '请输入启动页', trigger: 'blur' }
        ]
      },
      isAdd: true,
      type: ''
    }
  },
  created() {
    this.getApplicationList()
  },
  methods: {
    queryApplication() {
      this.queryInfo.pagenum = 1
      this.getApplicationList()
    },
    async getApplicationList() {
      const { data: res } = await this.$http.get(this.model + '/application_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取应用列表失败')
      this.total = res.data.total
      this.applicationList = res.data.applications
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getApplicationList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getApplicationList()
    },
    DialogClosed() {
      this.$refs.applicationFormRef.resetFields()
    },
    showDialog(isAdd, page, type) {
      this.type = type
      if (this.type === 'add') {
        this.title = '新增应用'
      } else if (this.type === 'update') {
        this.title = '编辑应用'
      } else if (this.type === 'check') {
        this.title = '查看应用'
      }
      this.isAdd = isAdd
      if (!isAdd) {
        const objString = JSON.stringify(page)
        this.applicationForm = JSON.parse(objString)
      }
      this.dialogVisible = true
    },
    submitApplication() {
      this.$refs.applicationFormRef.validate(async valid => {
        if (!valid) return false
        if (this.isAdd) {
          const { data: res } = await this.$http.post(this.model + '/application_add/', this.applicationForm)
          if (res.meta.status !== 200) return this.$message.error('新增应用失败')
          this.$message.success('新增应用成功')
          this.getApplicationList()
        } else {
          const { data: res } = await this.$http.put(this.model + '/' + this.applicationForm.application_id + '/application_update/', this.applicationForm)
          if (res.meta.status !== 200) return this.$message.error('编辑应用失败')
          this.$message.success('编辑应用成功')
          this.getApplicationList()
        }
        this.dialogVisible = false
      })
    },
    removePageById(id) {
      this.$confirm('此操作将永久删除该应用, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete(this.model + '/' + id + '/application_delete/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error('删除应用失败')
          this.$message.success('删除应用成功')
          this.getApplicationList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>
