<template>
  <el-container>
    <el-header>
      <div @click="goToWelcome" style="cursor: pointer">
        <img src="../assets/hydee.png"
             alt="">
        <span>海典测试平台</span>
      </div>
      <div>
        <span style="font-size: 14px;padding-right: 10px;">当前用户：{{user_name}}</span>
        <el-button @click="logout">退出登录</el-button>
      </div>
    </el-header>
    <el-container>
      <el-aside :width="isCollapse? '64px':'200px'">
        <div class="toggle-button" @click="toggleCollapse">|||</div>
        <el-menu background-color="#313541"
                 text-color="#fff"
                 active-text-color="#409EFF"
                 :unique-opened="true"
                 :collapse="isCollapse"
                 :collapse-transition="false"
                 :router="true"
                 :default-active="activePath">
          <el-submenu v-for="item in menulist"
                      :key="item.id"
                      :index="item.id+''">
            <template slot="title">
              <i :class="iconsObj[item.id]"></i>
              <span>{{item.authName}}</span>
            </template>
            <template v-for="subItem in item.children">
              <template v-if="subItem.children.length===0">
                <el-menu-item :key="subItem.id"
                              :index="'/'+subItem.path"
                              @click="saveNavState(subItem)">
                  <template slot="title">
                    <!-- 图标 -->
                    <i class="el-icon-menu"></i>
                    <!-- 文本 -->
                    <span>{{subItem.authName}}</span>
                  </template>
                </el-menu-item>
              </template>
              <template v-else>
                <el-submenu :index="subItem.id+''" :key="subItem.id">
                  <template slot="title">
                    <!-- 图标 -->
                    <i class="el-icon-menu"></i>
                    <!-- 文本 -->
                    <span>{{subItem.authName}}</span>
                  </template>
                  <el-menu-item v-for="grandItem in subItem.children"
                          :key="grandItem.id"
                          :index="'/'+grandItem.path"
                          @click="saveNavState(grandItem)">{{grandItem.authName}}</el-menu-item>
                </el-submenu>
              </template>
            </template>
          </el-submenu>
        </el-menu>
      </el-aside>
      <el-main>
        <div class="tab-nav" style="background:pink">
          <div class="btn-l btn"><i class="el-icon-arrow-left"></i></div>
          <div class="tab-nav-box">
          <el-tabs v-model="activePath"
                   closable
                   @tab-remove="removeTab"
                   @tab-click="clickTab"
                   >
            <el-tab-pane :key="item.id"
                          v-for="item in tabList"
                          :label="item.authName"
                          :name="'/'+item.path" />
          </el-tabs>
         </div>
          <div style="float: right;">
            <div class="btn-r btn"><i class="el-icon-arrow-right"></i></div>
            <el-tooltip class="item" effect="dark" content="关闭所有" placement="bottom">
              <div class="btn-close btn"><i class="el-icon-circle-close" @click="removeAllTab"></i></div>
            </el-tooltip>
          </div>
        </div>
        <keep-alive>
          <router-view></router-view>
        </keep-alive>
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
export default {
  data () {
    return {
      menulist: [],
      iconsObj: {
        1: 'iconfont icon-set',
        2: 'iconfont icon-data',
        3: 'iconfont icon-cecurity-protection',
        4: 'iconfont icon-phone',
        5: 'iconfont icon-topsales',
        6: 'iconfont icon-bussiness-man',
        59: 'el-icon-service',
        65: 'el-icon-picture-outline-round',
        71: 'el-icon-cherry'
      },
      isCollapse: false,
      activePath: '',
      tabList: [],
      user_name: ''
    }
  },
  created () {
    this.getMenus()
    this.activePath = window.sessionStorage.getItem('activePath')
    this.getTabList()
    this.user_name = window.localStorage.getItem('user_name')
  },
  watch: {
    $route: 'getActivePath'
  },
  methods: {
    logout() {
      window.sessionStorage.clear()
      this.$router.push('/login')
    },
    toggleCollapse() {
      this.isCollapse = !this.isCollapse
    },
    saveNavState(value) {
      window.sessionStorage.setItem('activePath', '/' + value.path)
      this.activePath = '/' + value.path
      var tab = this.tabList.filter(tab => tab.id === value.id)
      if (tab.length === 0) {
        this.tabList.push(value)
      }
      window.sessionStorage.setItem('tabList', JSON.stringify(this.tabList))
    },
    getMenus() {
      this.$http.get('/user_info/menu').then(res => {
        console.log('menu===>', res)
        if (res.data.meta.status === 200) {
          console.log(res.data.data)
          this.menulist = res.data.data
          // this.menulist = res.data.data
          // this.activePath = window.sessionStorage.getItem('activePath')
        } else if (res.data.meta.status === 'HD1006') {
          window.sessionStorage.clear()
          this.$router.push('/login')
        } else if (res.data.meta.status === 'HD1003') {
          return this.$message.error('登录失效,请重新登录')
        }
      }).catch(error => {
        console.log('菜单栏错误====>', error)
      })
    },
    getActivePath() {
      this.activePath = window.sessionStorage.getItem('activePath')
    },
    removeTab(targetName) {
      if (targetName === this.activePath) {
        this.tabList.forEach((item, index) => {
          if ('/' + item.path === targetName) {
            const nextTab = this.tabList[index + 1] || this.tabList[index - 1]
            if (nextTab) {
              window.sessionStorage.setItem('activePath', '/' + nextTab.path)
              this.activePath = '/' + nextTab.path
              this.$router.push(this.activePath)
            }
          }
        })
      }
      this.tabList = this.tabList.filter(tab => '/' + tab.path !== targetName)
      window.sessionStorage.setItem('tabList', JSON.stringify(this.tabList))
    },
    clickTab(tab) {
      this.$router.push(this.activePath)
    },
    getTabList() {
      var tabStr = window.sessionStorage.getItem('tabList')
      if (tabStr) {
        this.tabList = JSON.parse(tabStr)
      }
    },
    removeAllTab() {
      this.tabList = []
      window.sessionStorage.setItem('tabList', JSON.stringify(this.tabList))
      this.goToWelcome()
    },
    goToWelcome() {
      this.$router.push('/welcome')
    }
  }
}
</script>

<style lang="less" scoped>
.el-container {
  min-height: 100%;
}
.el-header {
  background-color: #373d41;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 0;
  div {
    display: flex;
    align-items: center;
    span {
      font-size: 20px;
      color: #fff;
      margin-left: 15px;
    }
  }
}
.el-aside {
  background-color: #313541;
  .iconfont {
    margin-right: 10px;
  }
  .toggle-button {
    background-color: #4a5064;
    font-size: 10px;
    line-height: 24px;
    color: #fff;
    text-align: center;
    letter-spacing: 0.2em;
    cursor: pointer;
  }
  .el-menu {
    border-right: none;
  }
}
.el-main {
  background-color: #eaedf1;
  //height: 100%;
}

</style>
