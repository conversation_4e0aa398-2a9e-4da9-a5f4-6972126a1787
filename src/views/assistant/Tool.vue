<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>测试助手</el-breadcrumb-item>
      <el-breadcrumb-item>工具列表</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row>
        <el-table :data="toolList" border>
          <el-table-column label="序号" type="index" min-width="50px"></el-table-column>
          <el-table-column label="方法名称" prop="tool_name" min-width="100px"></el-table-column>
          <el-table-column label="调用方式" prop="tool_use" min-width="100px" show-overflow-tooltip ></el-table-column>
          <el-table-column label="参数说明" prop="tool_explanation" min-width="300px" show-overflow-tooltip></el-table-column>
        </el-table>
      </el-row>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      queryInfo: {},
      toolList: [
        {
          tool_name: '获取当前年月日',
          tool_use: 'get_current_ymd()',
          tool_explanation: '无参数'
        },
        {
          tool_name: '获取当前年月',
          tool_use: 'get_current_ym()',
          tool_explanation: '无参数'
        },
        {
          tool_name: '获取当前年月日(带参数)',
          tool_use: 'get_current_ymd_new(days=0)',
          tool_explanation: 'days:整数型，当前日期向后推移天数，负数则向前'
        },
        {
          tool_name: '获取年月日时分秒',
          tool_use: 'get_current_ymdhms(seconds=0, minutes=0, hours=0, days=0)',
          tool_explanation: 'seconds:整数型，当前时间向后推移秒数，负数则向前，minutes:整数型，当前时间向后推移分钟数，负数则向前，hours:整数型，当前时间向后推移小时数，负数则向前，days:整数型，当前时间向后推移天数，负数则向前'
        },
        {
          tool_name: '获取时段年月日时分秒',
          tool_use: 'get_interval_ymdhms([[seconds=0, minutes=0, hours=0],[seconds=0, minutes=0, hours=0]])',
          tool_explanation: '参数：数组型，内部仍为数组型，分别为开始时间向后推移时分秒，结束时间向后推移时分秒'
        },
        {
          tool_name: '获取当前时间并转化为时间戳',
          tool_use: 'get_current_timestamp()',
          tool_explanation: '无参数'
        },
        {
          tool_name: '获取指定级别的时间戳',
          tool_use: 'get_appoint_timestamp(num)',
          tool_explanation: 'num：整数型，代表时间戳位数，秒级时间戳，10位，毫秒级时间戳，13位，微秒级时间戳，16位'
        },
        {
          tool_name: '获取指定长度的随机整数',
          tool_use: 'get_random_num(num)',
          tool_explanation: 'num：整数型，为字符串长度，如：get_random_num(10)，随机生成10位整数'
        },
        {
          tool_name: '获取指定区间内的整数',
          tool_use: 'get_num_with_size(max,min)',
          tool_explanation: 'max：整数型，为整数上限，min：整数型，为整数下限，默认为0，可不填，如：get_num_with_size(100,10),生成小于100，大于10的整数'
        },
        {
          tool_name: '获取指定长度的一级汉字（常用汉字）',
          tool_use: 'get_ch_string(length)',
          tool_explanation: 'length：整数型，汉字长度，如：get_ch_string(10)，随机生成10个汉字'
        },
        {
          tool_name: '获取指定长度的大写英文字符串',
          tool_use: 'get_upper_en_string(length)',
          tool_explanation: 'length：整数型，字符串长度，如：get_upper_en_string(10)，随机生成10个大写英文字符'
        },
        {
          tool_name: '获取指定长度的小写英文字符串',
          tool_use: 'get_lower_en_string(length)',
          tool_explanation: 'length：整数型，字符串长度，如：get_lower_en_string(10)，随机生成10个小写英文字符'
        },
        {
          tool_name: '生成随机手机号码',
          tool_use: 'getPhoneNumber()',
          tool_explanation: '无参数'
        },
        {
          tool_name: '生成随机身份证',
          tool_use: 'getRandomIdNum()',
          tool_explanation: '无参数'
        },
        {
          tool_name: '参数引用',
          tool_use: '@{paraname}@',
          tool_explanation: 'paraname：参数管理中的参数名称。引用类型为字符串，外部需添加双引号，如："@{paraname}@"，引用参数管理中参数名称为orderid的字段'
        },
        {
          tool_name: '关联案例响应值引用',
          tool_use: '#{response}#',
          tool_explanation: 'response: 关联案例返回值中的字段名称。引用类型为字符串，外部需添加双引号，如："{response}#"，引用返回值中字段名称为orderid的字段'
        },
        {
          tool_name: '请求入参值加密',
          tool_use: 'get_encrypt(params)',
          tool_explanation: '调用加密方法自动进行加密，直接在案例入参中使用，支持@@、$$参数引用，如：get_encrypt(191@{memberPhone}@)'
        }
      ],
      total: 12
    }
  }
}
</script>
