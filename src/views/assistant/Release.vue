<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>测试助手</el-breadcrumb-item>
      <el-breadcrumb-item>发版列表</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="7">
          <el-input placeholder="请输入任务名称"
                    v-model="queryInfo.task_name"
                    clearable
                    >
          </el-input>
          </el-col>
          <el-col :span="7">
          <el-input placeholder="请输入工单名称"
                    v-model="queryInfo.code_name"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-input placeholder="请输入工单ID"
                    v-model="queryInfo.hops_id"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-input placeholder="请输入操作人"
                    v-model="queryInfo.username"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryRelease">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="releaseList" border>
          <el-table-column label="记录ID" prop="record_id" min-width="50px"></el-table-column>
          <el-table-column label="工单ID" prop="hops_id" min-width="50px"></el-table-column>
          <el-table-column label="工单名称" prop="code_name" min-width="180px"></el-table-column>
          <el-table-column label="任务名称" prop="task_name" min-width="270px"></el-table-column>
          <el-table-column label="运行环境" prop="environment" min-width="50px">
            <template v-slot="scopeProps">
              <span v-if="scopeProps.row.environment==='1'">测试环境</span>
              <span v-else-if="scopeProps.row.environment==='2'">预发环境</span>
              <span v-else-if="scopeProps.row.environment==='3'">生产环境</span>
            </template>
          </el-table-column>
          <el-table-column label="运行状态" prop="running_status" min-width="50px"></el-table-column>
          <el-table-column label="调度时间" prop="create_time" min-width="120px"></el-table-column>
          <el-table-column label="操作人" prop="username" min-width="50px"></el-table-column>
          <el-table-column label="操作" fixed="right" min-width="50px">
            <template v-slot="slotProps">
              <el-tooltip class="item" effect="dark" content="查看测试报告详情" placement="top" :enterable="false">
                <el-button type="warning" icon="el-icon-document-checked" size="mini" @click="showReport(slotProps.row.task_record_id,slotProps.row.old_task_record_id)"></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
<!--          <el-table-column label="运行状态" prop="status">-->
<!--            <template v-slot="scopeProps">-->
<!--              <span v-if="scopeProps.row.status==='C'">未运行</span>-->
<!--              <span v-else-if="scopeProps.row.status==='R'">运行中</span>-->
<!--              <span v-else-if="scopeProps.row.status==='S'">已完成</span>-->
<!--            </template>-->
<!--          </el-table-column>-->
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'tools_help',
      queryInfo: {
        task_name: '',
        search_package: '',
        pagenum: 1,
        pagesize: 5
      },
      releaseList: [],
      total: 0
    }
  },
  created() {
    this.getReleaseList()
  },
  methods: {
    queryRelease() {
      this.queryInfo.pagenum = 1
      this.getReleaseList()
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getReleaseList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getReleaseList()
    },
    async getReleaseList() {
      const { data: res } = await this.$http.get(this.model + '/new_release_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取日志列表失败')
      this.total = res.data.total
      this.releaseList = res.data.release_list
    },
    async showReport(id, oldId) {
      window.open(this.$http.defaults.baseURL + 'task_info/task_report/?task_id=' + id + '&old_task_id=' + oldId)
    }
  }
}
</script>
