<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>测试助手</el-breadcrumb-item>
      <el-breadcrumb-item>优惠计算器</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-form label-width="100px"
               style="width:65%"
               :model="calculateForm">
      <el-row>
        <el-col :span="12">
          <el-form-item label="数据库">
            <el-select v-model="calculateForm.data_base_id">
              <el-option v-for="item in databaseList"
                           :label="item.label"
                           :value="item.id"
                           :key="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="商户编码">
            <el-input v-model="calculateForm.mer_code"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <template v-for="(item, index) in calculateForm.data">
      <el-row :key="index">
        <el-col :span="6">
          <el-form-item label="商品原价">
            <el-input v-model="item.shop_price"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="购买数量">
            <el-input v-model="item.shop_num"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="商品ID">
            <el-input v-model="item.shop_id"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <el-button type="primary" @click="addActivity" v-if="index==0">添加</el-button>
            <el-button type="danger" @click="deleteCurrentActivity(index)" v-else>删除</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      </template>
      <el-row>
        <el-form-item>
            <el-button type="primary" @click="activityCalculate">计算</el-button>
          </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="计算结果">
          <el-input type="textarea"
                    readonly
                    :rows="8"
                    v-model="calculateDate"></el-input>
        </el-form-item>
      </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'tools_help',
      calculateDate: '',
      calculateForm: {
        data_base_id: null,
        mer_code: '',
        data: [
          {
            shop_price: '',
            shop_num: '',
            shop_id: ''
          }
        ]
      },
      databaseList: []
    }
  },
  created() {
    this.getDatabaseList()
  },
  methods: {
    async getDatabaseList() {
      const { data: res } = await this.$http.get('user_info/info/', { params: { type: 'databases' } })
      if (res.meta.status !== 200) return this.$message.error('获取数据库信息失败')
      this.databaseList = res.data
    },
    addActivity() {
      this.calculateForm.data.push(
        {
          shop_price: '',
          shop_num: '',
          shop_id: ''
        }
      )
    },
    deleteCurrentActivity(index) {
      this.calculateForm.data.splice(index, 1)
    },
    async activityCalculate() {
      const { data: res } = await this.$http.post(this.model + '/money_calculate/', this.calculateForm)
      if (res.meta.status !== 200) return this.$message.error('获取优惠计算信息失败')
      this.calculateDate = res.data
    }
  }
}
</script>
