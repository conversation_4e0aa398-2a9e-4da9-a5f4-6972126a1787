<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>测试助手</el-breadcrumb-item>
      <el-breadcrumb-item>请求头转换</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-form label-width="100px">
      <el-row>
        <el-form-item label="原始请求头">
          <el-input type="textarea"
                    :rows="14"
                    :placeholder="placehoderStr"
                    @blur="transform"
                    v-model="originalDate"></el-input>
        </el-form-item>
      </el-row>
      <el-row>
      </el-row>
      <el-row>
        <el-form-item label="转换后请求头">
          <el-input type="textarea"
                    readonly
                    :rows="8"
                    v-model="conversionDate"></el-input>
        </el-form-item>
      </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      originalDate: '',
      conversionDate: '',
      placehoderStr: `B端（商户平台）请求头获取方式（有效时限8小时）：
  1.打开F12，切换至Network窗口
  2.发送任意请求
  3.将接口请求头Request Header切换至source格式，复制Host至Accept-Language部分内容
  4.粘贴至该窗口，点击转换
  5.将转换后请求头更新至对应参数管理，如【b_header】
C端（微商城）请求头获取方式（长期有效，该请求头内容决定订单用户）
  1.打开F12，切换至Network窗口
  2.发送任意请求
  3.将接口请求头Request Header切换至source格式，复制Host至Accept-Language部分内容
  4.更改请求头信息：设置【devFlag:1】，【去除user-key】，【增加userId，openId，unionId】（该信息在对应数据库表中获取）
  5.粘贴至该窗口，点击转换
  6.将转换后请求头更新至对应参数管理，如【cc_header】`
    }
  },
  methods: {
    transform() {
      var header = {}
      for (var i = 0; i < this.originalDate.split('\n').length; i++) {
        var key = this.originalDate.split('\n')[i].split(': ')[0]
        var value = this.originalDate.split('\n')[i].split(': ')[1]
        header[key] = value
      }
      this.conversionDate = JSON.stringify(header)
    }
  }
}
</script>
