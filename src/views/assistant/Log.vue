<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/home' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item>测试助手</el-breadcrumb-item>
      <el-breadcrumb-item>日志列表</el-breadcrumb-item>
    </el-breadcrumb>
    <el-card>
      <el-row :gutter="20">
        <el-col :span="7">
          <el-select v-model="queryInfo.search_log_type"
                     placeholder="请选择日志类型"
                     clearable>
            <el-option label="操作日志" value="1"></el-option>
            <el-option label="执行日志" value="2"></el-option>
          </el-select>
          </el-col>
          <el-col :span="7">
          <el-input placeholder="请输入操作人"
                    v-model="queryInfo.search_person"
                    clearable
                    >
          </el-input>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="queryLog">查 询</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-table :data="logList" border>
          <el-table-column label="ID" prop="log_id" min-width="50px"></el-table-column>
          <el-table-column label="日志类型" prop="log_type">
            <template v-slot="scopeProps">
              <span v-if="scopeProps.row.log_type==='1'">操作日志</span>
              <span v-else-if="scopeProps.row.log_type==='2'">执行日志</span>
            </template>
          </el-table-column>
          <el-table-column label="执行ID" prop="running_log_id"></el-table-column>
          <el-table-column label="日志内容" prop="log_content" show-overflow-tooltip min-width="350px"></el-table-column>
          <el-table-column label="操作人" prop="person"></el-table-column>
          <el-table-column label="日志产生时间" prop="temp_time" min-width="120px" fixed="right"></el-table-column>
        </el-table>
      </el-row>
      <el-row>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryInfo.pagenum"
          :page-size="queryInfo.pagesize"
          :page-sizes="[5, 10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </el-row>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      model: 'tools_help',
      queryInfo: {
        search_log_type: '',
        search_person: '',
        pagenum: 1,
        pagesize: 5
      },
      logList: [],
      total: 0
    }
  },
  created() {
    this.getLogList()
  },
  methods: {
    queryLog() {
      this.queryInfo.pagenum = 1
      this.getLogList()
    },
    async getLogList() {
      const { data: res } = await this.$http.get(this.model + '/log_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取日志列表失败')
      this.total = res.data.total
      this.logList = res.data.logs
    },
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getLogList()
    },
    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getLogList()
    }
  }
}
</script>
