import Vue from 'vue'
import VueRouter from 'vue-router'
import Login from '../views/Login.vue'
import Home from '../views/Home.vue'
import Welcome from '../views/Welcome.vue'
import Users from '../views/system/Users.vue'
import Roles from '../views/system/Roles.vue'
import Rights from '../views/system/Rights.vue'
import Database from '../views/setting/Database.vue'
import Params from '../views/setting/Param.vue'
import Tasks from '../views/setting/task/Task.vue'
import Report from '../views/setting/task/Report.vue'
import Email from '../views/setting/Email.vue'
import Project from '../views/interface/Project.vue'
import Interface from '../views/interface/Interface.vue'
import InterfaceCompare from '../views/interface/InterfaceCompare.vue'
import InterfaceTrafficStatistics from '../views/interface/InterfaceTrafficStatistics.vue'
import ApiCase from '../views/interface/ApiCase.vue'
import Business from '../views/interface/Business.vue'
import Scene from '../views/interface/Scene.vue'
import SceneTask from '../views/interface/SceneTask.vue'
import Chunk from '../views/page/Chunk.vue'
import Page from '../views/page/web/Page.vue'
import Element from '../views/page/web/Element.vue'
import WebCase from '../views/page/web/Case.vue'
import Device from '../views/page/android/Device.vue'
import Application from '../views/page/android/Application.vue'
import AndroidElement from '../views/page/android/Element.vue'
import AndroidCase from '../views/page/android/Case.vue'
import Performance from '../views/performance/Performance.vue'
import Log from '../views/assistant/Log.vue'
import Release from '../views/assistant/Release.vue'
import Tool from '../views/assistant/Tool.vue'
import TransHeader from '../views/assistant/TransHeader.vue'
import Calculation from '../views/assistant/Calculation.vue'
import Question from '../views/afterSales/QuestionList.vue'
import ChatRecordList from '../views/afterSales/ChatRecordList.vue'
import QuestionStatistics from '../views/afterSales/GetQuestionStatistics.vue'
import DataArchiving from '../views/afterSales/DataArchiving.vue'
import RoomInitConfig from '../views/afterSales/RoomInitConfig.vue'
import DailyMonitoring from '../views/dailyMonitoring/DailyMonitoringList.vue'
import DailyMonitoringDetail from '../views/dailyMonitoring/DailyMonitoringDetailList.vue'
import TestCaseManagement from '../views/testcase/TestCaseManagement.vue'
import SmokeCaseList from '../views/testcase/SmokeCaseList.vue'
import AIGenerateTestcases from '../views/testcase/AIGenerateTestcases.vue'
import AITestCase from '../views/testcase/AITestCase.vue'
import CoreCaseList from '../views/testcase/CoreCaseList.vue'
import TestCases from '../views/testcase/TestCases.vue'
import RequirementManagement from '../views/testcase/RequirementManagement.vue'
Vue.use(VueRouter)

const routes = [
  { path: '/', redirect: '/login' },
  { path: '/login', component: Login },
  {
    path: '/home',
    component: Home,
    redirect: '/welcome',
    children: [
      { path: '/welcome', component: Welcome },
      { path: '/users', component: Users },
      { path: '/roles', component: Roles },
      { path: '/rights', component: Rights },
      { path: '/databases', component: Database },
      { path: '/parameters', component: Params },
      { path: '/tasks', component: Tasks },
      { path: '/report/:taskId?', component: Report },
      { path: '/emails', component: Email },
      { path: '/projects', component: Project },
      { path: '/interfaces', component: Interface },
      { path: '/interfaceCompare', component: InterfaceCompare },
      { path: '/testCaseManagement', component: TestCaseManagement },
      { path: '/smokeCaseList', component: SmokeCaseList },
      { path: '/AI-test-case', component: AITestCase },
      { path: '/coreCaseList', component: CoreCaseList },
      { path: '/interfaceTrafficStatistics', component: InterfaceTrafficStatistics },
      { path: '/daily-monitoring-list', component: DailyMonitoring },
      { path: '/daily-monitoring-detail-list', component: DailyMonitoringDetail },
      { path: '/api-cases', component: ApiCase, name: 'apiCase' },
      { path: '/businesses', component: Business },
      { path: '/scenes', component: Scene },
      { path: '/scene-task', component: SceneTask },
      { path: '/chunks', component: Chunk },
      { path: '/pages', component: Page },
      { path: '/web-elements', component: Element },
      { path: '/web-cases', component: WebCase },
      { path: '/devices', component: Device },
      { path: '/applications', component: Application },
      { path: '/app-elements', component: AndroidElement },
      { path: '/app-cases', component: AndroidCase },
      { path: '/locusts', component: Performance },
      { path: '/logs', component: Log },
      { path: '/releases', component: Release },
      { path: '/utils', component: Tool },
      { path: '/headers', component: TransHeader },
      { path: '/money-calculate', component: Calculation },
      { path: '/question-list', component: Question },
      { path: '/chatRecord-list', component: ChatRecordList },
      { path: '/question-statistics', component: QuestionStatistics, meta: { requiresAuth: true } },
      { path: '/data-archiving', component: DataArchiving, meta: { requiresAuth: true } },
      { path: '/room-init-config', component: RoomInitConfig },
      { path: '/ai-generate-testcases', component: AIGenerateTestcases },
      { path: '/requirement-management', component: RequirementManagement },
      { path: '/testcases', component: TestCases }
    ]
  }
]

const router = new VueRouter({
  routes
})

router.beforeEach((to, from, next) => {
  const isAuthenticated = localStorage.getItem('token') // 假设用token判断登录状态
  if (to.matched.some(route => route.meta.requiresAuth)) {
    if (!isAuthenticated) {
      // 保存完整路径（包含查询参数）
      sessionStorage.setItem('redirect', to.fullPath)
      next(`/login?url=${to.fullPath}`)
    } else {
      next()
    }
  } else {
    next()
  }
})

export default router
