<template>
  <div id="app">
    <router-view></router-view>
    <quickAssistant v-if="showQuickAssistant" @open="mAssitantToggle"/>
    <m-assitant ref="mAssitant"/>
  </div>
</template>

<script>
import quickAssistant from './components/quickAssitant.vue'
import mAssitant from './components/index.vue'

export default {
  name: 'app',
  components: {
    quickAssistant,
    mAssitant
  },
  data() {
    return {
      showQuickAssistant: true,
      excludedRoutes: ['/login'] // 需要隐藏页脚的路由路径
    }
  },
  created() {
    // 初始化时检查当前路由
    this.updateFooterVisibility(this.$route)
  },
  watch: {
    // 监听路由变化
    '$route'(to) {
      this.updateFooterVisibility(to)
    }
  },
  methods: {
    updateFooterVisibility(route) {
      // 判断当前路径是否在排除列表中
      this.showQuickAssistant = !this.excludedRoutes.includes(route.path)
    },
    mAssitantToggle() {
      this.$refs.mAssitant.onCsToggle(true)
    }
  }
}
</script>
