""" Current project exception classes.
"""
from django.http import Http404

from rest_framework import exceptions
from rest_framework.exceptions import PermissionDenied, ErrorDetail
from rest_framework.response import Response
from rest_framework.views import set_rollback

from scaffold.exceptions.exceptions import AppError

import core.exceptions


class AppErrors(core.exceptions.AppErrors):
    """ 本应用的具体错误列表 """
    ERROR_DB_IS_EXISTS = AppError("HD3001", "数据库名称已存在，请重新命名")
    ERROR_DB_IS_QUOTE = AppError("HD3002", "数据库被引用，请先解除引用后再试")
    ERROR_PARAMETER_IS_EXISTS = AppError("HD3003", "参数名称已存在，请重新命名")
    ERROR_PARAMETER_IS_QUOTE = AppError("HD3005", "删除失败：参数已被引用，请解除引用后再试")
    ERROR_TASK_IS_RUNNING = AppError("HD3006", "当前任务正在执行中，请稍后再试")
    ERROR_EMAIL_IS_QUOTE = AppError("HD3007", "邮件模板已在任务中被引用，请先解除引用后再试")
    ERROR_REPORT_IS_NULL = AppError("HD3008", "当前任务正在运行中，请稍后查看")


# pylint: disable=unused-argument
def exception_handler(exc, context):
    """
    Returns the response that should be used for any given exception.

    By default we handle the REST framework `APIException`, and also
    Django's built-in `Http404` and `PermissionDenied` exceptions.

    Any unhandled exceptions may return `None`, which will cause a 500 error
    to be raised.
    """
    if isinstance(exc, Http404):
        exc = exceptions.NotFound()
    elif isinstance(exc, PermissionDenied):
        exc = exceptions.PermissionDenied()

    if isinstance(exc, exceptions.APIException):
        headers = {}
        if getattr(exc, 'auth_header', None):
            headers['WWW-Authenticate'] = exc.auth_header
        if getattr(exc, 'wait', None):
            headers['Retry-After'] = '%d' % exc.wait

        if isinstance(exc.detail, ErrorDetail):
            if exc.detail.code == 'not_authenticated':
                exc.status_code = 401
                data = dict(
                    ok=False,
                    msg=str(exc.detail),
                    errcode=10006
                )
            else:
                data = dict(
                    ok=False,
                    msg=exc.detail
                )
        elif isinstance(exc.detail, (list, dict)):
            data = dict(
                ok=False,
                msg=str(exc.detail),
                data=exc.detail
            )
        else:
            data = dict(
                ok=False,
                msg=exc.detail
            )

        set_rollback()
        return Response(data, status=exc.status_code, headers=headers)

    return None
