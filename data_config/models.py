import json

import pytz
from django.db import models
from django_celery_beat.models import PeriodicTask, CrontabSchedule
from scaffold.models.abstract.meta import ActiveModel

from core.models import DateModel


# 数据源登记表
class DataBaseInfo(DateModel, ActiveModel):
    data_base_id = models.AutoField(primary_key=True)
    data_base_name = models.CharField(max_length=256)
    data_base_type = models.CharField(max_length=16)
    ip_address = models.CharField(max_length=128)
    data_base_port = models.CharField(max_length=50, null=True)
    db_account = models.CharField(max_length=128)
    db_pwd = models.CharField(max_length=256)
    db_name = models.Char<PERSON>ield(max_length=256)
    remark = models.TextField(null=True, blank=True)

    def __str__(self):
        return str(self.data_base_id) + "-" + self.data_base_name

    class Meta(object):
        # 定义表名
        db_table = "data_base_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = '数据源登记表'


# 参数数据表
class ParameterInfo(DateModel, ActiveModel):
    parameter_id = models.AutoField(primary_key=True)
    parameter_type = models.CharField(max_length=1, default='1')
    database_id = models.CharField(max_length=64, null=True)
    parameter_name = models.CharField(max_length=256)
    produce_parameter = models.TextField()
    uat_parameter = models.TextField()  # 预发环境
    test_parameter = models.TextField()
    remark = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.parameter_name

    class Meta(object):
        # 定义表名
        db_table = "parameter_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = '参数数据表'


# 任务信息数据表
class TaskInfo(DateModel, ActiveModel):
    task_id = models.AutoField(primary_key=True)
    task_name = models.CharField(max_length=256)
    environment = models.CharField(max_length=1)
    case_pool = models.TextField()
    task_status = models.CharField(max_length=1, verbose_name='C: 未运行，R：运行中, S: 已完成')
    run_time = models.CharField(max_length=4)
    is_send_email = models.BooleanField(default=False)
    email_model_id = models.CharField(max_length=128, null=True)
    recent_record_id = models.CharField(max_length=128, null=True)
    task_type = models.CharField(max_length=16)
    api_type = models.CharField(max_length=16)
    remark = models.TextField(null=True, blank=True)
    is_open = models.BooleanField(default=False, verbose_name='是否开启任务')
    is_monitoring = models.BooleanField(default=False, verbose_name='是否开启监控')
    cron = models.CharField(max_length=30, verbose_name='时间corn表达式', blank=True, null=True)
    is_wx_push = models.BooleanField(default=False, verbose_name='是否开启企业微信推送')
    wx_address = models.CharField(max_length=512, null=True, blank=True, verbose_name='推送企业微信消息地址')

    def save(self, *args, **kwargs):
        cron_arr = self.cron.split(' ')
        cron_dict = dict(minute=cron_arr[0], hour=cron_arr[1], day_of_month=cron_arr[2],
                         month_of_year=cron_arr[3], day_of_week=cron_arr[4])
        if self.task_id is None:
            super().save(*args, **kwargs)
            self.add_scene_task(cron_dict)
        else:
            old_name = TaskInfo.objects.filter(task_id=self.task_id)[0].task_name
            super().save(*args, **kwargs)
            self.update_scene_task(old_name, cron_dict)

    def add_scene_task(self, task_cron):
        # 获取crontab,没有就创建
        schedule = CrontabSchedule.objects.create(**task_cron, timezone=pytz.timezone('Asia/Shanghai'))

        PeriodicTask.objects.create(
            crontab=schedule,
            name=self.task_name + '_' + str(self.task_id),
            task='data_config.tasks.task_job',
            enabled=self.is_open,
            args=json.dumps([self.task_id])
        )

    def update_scene_task(self, old_name, task_cron):
        period_task = PeriodicTask.objects.filter(name=old_name + '_' + str(self.task_id)).first()
        if period_task is None:
            self.add_scene_task(task_cron)
        else:
            period_task.name = self.task_name + '_' + str(self.task_id)
            schedule = CrontabSchedule.objects.create(**task_cron, timezone=pytz.timezone('Asia/Shanghai'))
            period_task.crontab = schedule
            period_task.enabled = self.is_open
            period_task.save()

    def delete_scene_task(self, task_id, scene_name):
        period_task = PeriodicTask.objects.filter(name=scene_name + '_' + str(task_id)).first()
        if period_task:
            period_task.enabled = False
            period_task.save()

    def __str__(self):
        return self.task_name

    class Meta(object):
        # 定义表名
        db_table = "task_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = '任务信息表'


# 任务执行记录表
class TaskRunningRecord(models.Model):
    task_record_id = models.AutoField(primary_key=True)
    task_id = models.CharField(max_length=128)  # 任务ID
    task_name = models.CharField(max_length=256)  # 任务名称
    environment = models.CharField(max_length=1)  # 运行环境
    task_type = models.CharField(max_length=1, verbose_name='1: api，2：scene, 3: web案例 4:安卓案例')  # 任务类型
    test_all = models.IntegerField(default=0)  # 任务中总的场景或案例数
    test_pass = models.IntegerField(default=0)  # 任务中测试通过的场景或案例数
    test_fail = models.IntegerField(default=0)  # 任务中测试失败的场景或案例数
    test_error = models.IntegerField(default=0)  # 任务中测试报错的场景或案例数
    total_time = models.CharField(max_length=128)  # 任务运行时长
    batch_number = models.CharField(max_length=128, null=True)  # 任务执行状态
    creater = models.CharField(max_length=128)  # 任务执行人
    report_content = models.TextField(default='')  # 执行日志
    channel = models.CharField(max_length=128)  # 调用来源，1-定时任务调用，2-手动调用，3-hops调用
    old_record_id = models.IntegerField(null=True, blank=True)  # 记录重试的原task_record_id
    time_stamp = models.DateTimeField()  # 时间戳

    class Meta(object):
        # 定义表名
        db_table = "task_running_record"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-time_stamp']
        indexes = [models.Index(fields=['task_id']), ]
        # 表别名
        verbose_name = '任务信息表'


# 邮件模板
class EmailModelInfo(DateModel, ActiveModel):
    email_model_id = models.AutoField(primary_key=True)
    email_model_name = models.CharField(max_length=256)
    email_subject = models.TextField()
    email_TO = models.TextField()
    email_CC = models.TextField(null=True)
    email_content = models.TextField(blank=True, null=True)

    class Meta(object):
        # 定义表名
        db_table = "email_model_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = '邮件模板信息表'


# 场景执行记录表
class TaskMonitoringFailRecord(models.Model):
    monitoring_record_id = models.AutoField(primary_key=True)
    scene_id = models.CharField(max_length=128)  # 场景ID
    scene_name = models.CharField(max_length=256)  # 场景名称
    run_result = models.CharField(max_length=128)  # 场景运行结果 成功/失败/错误
    parent_id = models.CharField(max_length=256, default='')  # 上级ID，存储task_running_fail_record的主键ID
    business_id = models.IntegerField(verbose_name='业务ID', default=0)  # 归属ID，默认为空，不为空时表示归属的任务执行记录ID
    fail_reason = models.CharField(max_length=256)  # 失败原因
    repair_plan = models.CharField(max_length=256)  # 优化方案
    tapd_link = models.CharField(max_length=1024)  # tapd链接
    is_repair = models.CharField(max_length=1)  # 是否优化，0否,1是,2无需调整
    environment = models.CharField(max_length=1)  # 运行环境, 1测试,2预发,3生产
    fail_type = models.CharField(max_length=2)  # 错误类型，存储fail_type_info的主键ID
    creater = models.CharField(max_length=128)  # 场景创建人
    update_time = models.DateTimeField()  # 修改时间


    class Meta(object):
        # 定义表名
        db_table = "task_monitoring_fail_record"
        # 表别名
        verbose_name = '任务监控失败用例记录表'
