<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>海典自动化测试平台测试报告</title>
    <style>
    body,div,p {
        margin: 0;
        padding: 0
    }
    .data-container {
        margin: 10px 0 0 20px;
    }
        .num{
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 5px;
        }
        .label {
            text-align: left;
            color: #333;
        }
        .value{
            height: 20px;
            padding: 2px 15px;
            border-radius: 5px;
            text-align: center;
        }
        .bg-black{
            background-color:gainsboro;
            color:black;
        }
        .bg-green{
            background-color:#97cc64;
            color: #fff;
        }
        .bg-red{
            background-color:#fd5a3e;
            color: #fff;
        }
        .bg-yellow{
            background-color:#ffaa3b;
            color: #fff;
        }

    </style>
</head>
<body class="" style='font-family:"Microsoft YaHei"'>
<div style="font-size:medium;margin-top: 20px;">各位同事，大家好，以下为海典测试平台接口自动化测试执行情况，请查收：</div>
<div class="data-container">
    <div class="num">测试任务：{{ content }}</div>
    <div class="num">调度时间：{{ create_time }}</div>
    <div class="num">运行时长：{{ total_time }} 分钟</div>
    <div class="num">
        <div class="label">用例总数：</div>
        <div class="value bg-black">{{ test_all }}</div>
    </div>
    <div class="num">
        <div class="label">成功个数：</div>
        <div class="value bg-green">{{ test_pass }}</div>
    </div>
    <div class="num">
        <div class="label ">失败个数：</div>
        <div class="value bg-red">{{ test_fail }}</div>
    </div>
    <div class="num">
        <div class="label">故障个数：</div>
        <div class="value bg-yellow">{{ test_error }}</div>
    </div>
    <div style="margin-top: 20px;">
    <a style="width:120px;height:32px;line-height:32px;background-color: #39f;color:#fff;text-align:center;display:inline-block;border-radius:4px;margin-bottom: 20px;text-decoration: none;"
       href={{ url }}>☞查看测试报告</a>
</div>
<div style="margin-top: 100px;">
            <div>Best Regards</div>
            <hr size="2" width="100%" align="left"/>
            <div>云研发测试部</div>
            <div>上海海典软件股份有限公司</div>
            <div>Shanghai Hydee Software Corp., Ltd.</div>
            <div>电话 (Tel)：021-50111526-8012（上海）/0731-89917858（长沙）</div>
            <div>地址：湖南省长沙市岳麓区麓天路26号五矿.麓谷科技园二期A2栋七楼(邮编：410006)</div>
</div>
</div>
</body>
</html>