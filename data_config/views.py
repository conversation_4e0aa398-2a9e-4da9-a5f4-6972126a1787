from django.utils import timezone
from django.db.models import Count

import traceback
from collections import defaultdict

from django.core.paginator import Paginator
from django.db import transaction
from django.http import HttpResponse
from django.shortcuts import render

from base.models import UserInfo
from core.commonUtils.aesCryptUtil import PrpCrypt
from core.commonUtils.commonUtil import CommonUtil
from core.commonUtils.dbUtil import DBUtils
from core.commonUtils.getModelInfoUtil import GetModelInfo
from core.commonUtils.jsonUtil import *
from core.commonUtils.logger import Logger
from core.utils import ResponseUtils, UserUtils, DateUtils
from interface_test.models import *
from tools_help.models import ReleaseRecordInfo
from .exceptions import AppErrors
from core.servers.caseRun import TestCase
from ui_test.models import *
from scaffold.exceptions.exceptions import AppError


from rest_framework import viewsets
from rest_framework.decorators import action
from data_config.models import *
from . import serializers as s
import data_config.tasks as t
from urllib.parse import quote
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl import Workbook

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()

from django.conf import settings

# 读取配置文件，服务接口IP
platform_domain = settings.PLATFORM_DOMAIN


class DataBaseInfoViewSet(viewsets.ModelViewSet):
    queryset = DataBaseInfo.objects.all()
    serializer_class = s.DataBaseInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    # 数据库查询或列表接口
    @action(methods=['GET'], detail=False)
    def db_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            database_id = request.GET.get('databaseId', "")
            database_name = request.GET.get('databaseName', "")
            creater = request.GET.get('creater', "")
            pagenum = request.GET["pagenum"]
            pagesize = request.GET["pagesize"]
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询数据库，查询条件数据库ID=%s，数据库名称=%s" % (username, database_id, database_name)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = DataBaseInfo.objects.filter(is_active=True)
        if database_id:
            obj = obj.filter(data_base_id=database_id)
        if database_name:
            obj = obj.filter(data_base_name__contains=database_name)
        if creater:
            obj = obj.filter(creater__contains=creater)
        obj = obj.order_by('-update_time')
        paginator = Paginator(obj, pagesize)
        data = paginator.page(pagenum)
        data_list = list(data.object_list.values())
        response_json = {'pagesize': pagesize, 'pagenum': pagenum, 'databaseId': database_id,
                         'databaseName': database_name, 'total': paginator.count, 'databases': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def db_add(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            data_base_name = temp['data_base_name']
            name_same = DataBaseInfo.objects.filter(data_base_name=data_base_name, is_active=True)
            if name_same:
                return ResponseUtils.return_fail(AppErrors.ERROR_DB_IS_EXISTS)
            data_base_type = temp['data_base_type']
            ip_address = temp['ip_address']
            data_base_port = temp['data_base_port']
            db_account = temp['db_account']
            db_pwd = temp['db_pwd']
            db_pwd = str(PrpCrypt('HP9lYhuDeeJjEnAo').encrypt(db_pwd), encoding='utf-8')
            db_name = temp['db_name']
            remark = temp.get('remark')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        data_base_info = DataBaseInfo(data_base_name=data_base_name,
                                      data_base_type=data_base_type, ip_address=ip_address,
                                      data_base_port=data_base_port, db_account=db_account,
                                      db_pwd=db_pwd, db_name=db_name, remark=remark, creater=username,
                                      update_person=username, create_time=create_time,
                                      update_time=create_time)
        data_base_info.save()
        content = "用户 %s 新增数据库成功，数据库为：%s" % (username, data_base_info.__dict__)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("数据源新增成功")

    @action(methods=['POST'], detail=False)
    def db_connect(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            data_base_type = temp['data_base_type']
            ip_address = temp['ip_address']
            data_base_port = temp['data_base_port']
            db_account = temp['db_account']
            db_pwd = temp['db_pwd']
            db_name = temp['db_name']
            env_type = temp['env_type']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        ip_address = TestCase(env_type, username).parameterization(ip_address)
        data_base_port = TestCase(env_type, username).parameterization(data_base_port)
        db_name = TestCase(env_type, username).parameterization(db_name)
        db_account = TestCase(env_type, username).parameterization(db_account)
        db_pwd = TestCase(env_type, username).parameterization(db_pwd)
        db_info = "类型=%s,IP=%s,端口=%s,账户名=%s,密码=%s,数据库=%s" % \
                  (data_base_type, ip_address, data_base_port, db_account, db_pwd, db_name)
        if data_base_port:
            db_util = DBUtils(data_base_type, ip_address + ":" + data_base_port, db_account, db_pwd, db_name)
        else:
            db_util = DBUtils(data_base_type, ip_address, db_account, db_pwd, db_name)
        result = db_util.testConnect()
        content = "用户 %s 正在测试连接数据库，连接信息为：%s，连接结果为：%s" % (username, db_info, result)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success(result)

    @action(methods=['PUT'], detail=True)
    def db_update(self, request, pk):
        database_id = pk
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            data_base_name = temp['data_base_name']
            name_exit = DataBaseInfo.objects.filter(data_base_name=data_base_name, is_active=True).exclude(
                data_base_id=database_id)
            if name_exit:
                return ResponseUtils.return_fail(AppErrors.ERROR_DB_IS_EXISTS)
            data_base_type = temp['data_base_type']
            ip_address = temp['ip_address']
            data_base_port = temp['data_base_port']
            db_account = temp['db_account']
            db_name = temp['db_name']
            remark = temp.get('remark')
            db_pwd = temp['db_pwd']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        db_info = DataBaseInfo.objects.get(data_base_id=database_id).__dict__
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        if db_pwd:
            db_pwd = str(PrpCrypt('HP9lYhuDeeJjEnAo').encrypt(db_pwd), encoding='utf-8')
            DataBaseInfo.objects.filter(data_base_id=database_id).update(data_base_name=data_base_name,
                                                                         data_base_type=data_base_type,
                                                                         ip_address=ip_address,
                                                                         data_base_port=data_base_port,
                                                                         db_account=db_account, db_pwd=db_pwd,
                                                                         db_name=db_name, remark=remark,
                                                                         update_person=username,
                                                                         update_time=update_time)
        else:
            DataBaseInfo.objects.filter(data_base_id=database_id).update(data_base_name=data_base_name,
                                                                         data_base_type=data_base_type,
                                                                         ip_address=ip_address,
                                                                         data_base_port=data_base_port,
                                                                         db_account=db_account,
                                                                         db_name=db_name, remark=remark,
                                                                         update_person=username,
                                                                         update_time=update_time)
        db = DataBaseInfo.objects.get(data_base_id=database_id).__dict__
        content = "用户 %s 修改数据库信息成功，修改前信息：%s，修改后信息：%s" % (username, db_info, db)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("数据源编辑成功")

    @action(methods=['DELETE'], detail=True)
    def db_delete(self, request, pk):
        database_id = pk
        username = UserUtils.get_login_user(request).username
        db_info = DataBaseInfo.objects.get(data_base_id=database_id).__dict__
        # 判断数据库是否被引用
        case = CaseInfo.objects.filter(init_database=database_id)
        param = ParameterInfo.objects.filter(database_id=database_id)
        if case or param:
            return ResponseUtils.return_fail(AppErrors.ERROR_DB_IS_QUOTE)
        else:
            DataBaseInfo.objects.filter(data_base_id=database_id).update(is_active=False, update_person=username,
                                                                         update_time=DateUtils.get_current_time())
            content = "用户 %s 删除了数据库：%s" % (username, db_info)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("数据源删除成功")


class ParameterInfoViewSet(viewsets.ModelViewSet):
    queryset = ParameterInfo.objects.all()
    serializer_class = s.ParameterInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def parameter_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            parameter_id = request.GET.get('parameterId', '')
            parameter_name = request.GET.get('parameterName', '')
            creater = request.GET.get('creater', '')
            pagenum = request.GET.get("pagenum", 1)
            pagesize = request.GET.get("pagesize", 10)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询参数信息，查询条件：参数编号=%s，参数名称=%s" \
                  % (username, parameter_id, parameter_name)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = ParameterInfo.objects.filter(is_active=True)
        if parameter_id:
            obj = obj.filter(parameter_id=parameter_id)
        if parameter_name:
            obj = obj.filter(parameter_name__icontains=parameter_name)
        if creater:
            obj = obj.filter(creater__icontains=creater)
        obj = obj.order_by('-update_time')
        paginator = Paginator(obj, pagesize)
        data = paginator.page(pagenum)
        data_list = list(data.object_list.values())
        response_json = {'pagenum': pagenum, 'pagesize': pagesize, 'total': paginator.count,
                         'parameterId': parameter_id, 'parameterName': parameter_name, 'parameters': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def parameter_add(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            parameter_name = temp['parameter_name']
            name_same = ParameterInfo.objects.filter(parameter_name=parameter_name, is_active=True)
            if name_same:
                return ResponseUtils.return_fail(AppErrors.ERROR_PARAMETER_IS_EXISTS)
            test_parameter = temp['test_parameter']
            uat_parameter = temp['uat_parameter']
            produce_parameter = temp['produce_parameter']
            remark = temp.get('remark')
            parameter_type = temp['parameter_type']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        if parameter_type == '3' or parameter_type == '4':
            judgeParameter(test_parameter)
            judgeParameter(uat_parameter)
            judgeParameter(produce_parameter)
            database_id = temp['database_id']
        else:
            database_id = None
        create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        parameter = ParameterInfo(parameter_type=parameter_type, database_id=database_id,
                                  parameter_name=parameter_name, produce_parameter=produce_parameter,
                                  uat_parameter=uat_parameter, test_parameter=test_parameter, remark=remark,
                                  creater=username, update_person=username,
                                  create_time=create_time, update_time=create_time)
        parameter.save()
        content = "用户 %s 新增参数成功，参数信息：%s" % (username, parameter.__dict__)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("参数新增成功")

    @action(methods=['PUT'], detail=True)
    def parameter_update(self, request, pk):
        parameter_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            parameter_name = temp['parameter_name']
            name_exit = ParameterInfo.objects.filter(parameter_name=parameter_name, is_active=True).exclude(
                parameter_id=parameter_id)
            if name_exit:
                return ResponseUtils.return_fail(AppErrors.ERROR_PARAMETER_IS_EXISTS)
            test_parameter = temp['test_parameter']
            uat_parameter = temp['uat_parameter']
            produce_parameter = temp['produce_parameter']
            remark = temp.get('remark')
            parameter_type = temp['parameter_type']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

        param_before = ParameterInfo.objects.get(parameter_id=parameter_id).__dict__
        if parameter_type == '3' or parameter_type == '4':
            # database_id = temp['database_msg'].split("-")[0]
            database_id = temp['database_id']
            judgeParameter(test_parameter)
            judgeParameter(uat_parameter)
            judgeParameter(produce_parameter)
        else:
            database_id = None
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        ParameterInfo.objects.filter(parameter_id=parameter_id).update(parameter_type=parameter_type,
                                                                       database_id=database_id,
                                                                       parameter_name=parameter_name,
                                                                       produce_parameter=produce_parameter,
                                                                       uat_parameter=uat_parameter,
                                                                       test_parameter=test_parameter,
                                                                       remark=remark,
                                                                       update_person=username,
                                                                       update_time=update_time)
        param_info = ParameterInfo.objects.get(parameter_id=parameter_id).__dict__
        content = "用户 %s 修改参数信息成功，修改前信息：%s，修改后信息：%s" % (username, param_before, param_info)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("参数编辑成功")

    @action(methods=['DELETE'], detail=True)
    def parameter_delete(self, request, pk):
        parameter_id = pk
        username = UserUtils.get_login_user(request).username
        param_info = ParameterInfo.objects.get(parameter_id=parameter_id)
        param_name = "@{" + param_info.parameter_name + "}@"
        can_delete = True
        # 判断参数是否在项目中被引用
        project_list = ProjectInfo.objects.all()
        for project in project_list:
            if param_name in project.__dict__:
                can_delete = False
        # 判断参数是否在接口中被引用
        interface_list = InterfaceInfo.objects.all()
        for interface in interface_list:
            if param_name in interface.__dict__:
                can_delete = False
        # 判断参数是否在案例中被引用：
        case_list = CaseInfo.objects.all()
        for case in case_list:
            if param_name in case.__dict__:
                can_delete = False
        if can_delete:
            ParameterInfo.objects.filter(parameter_id=parameter_id).update(is_active=False, update_person=username,
                                                                           update_time=DateUtils.get_current_time())
            content = "用户 %s 删除了参数：%s" % (username, param_info.__dict__)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("删除成功")
        else:
            return ResponseUtils.return_fail(AppErrors.ERROR_PARAMETER_IS_QUOTE)


class TaskInfoViewSet(viewsets.ModelViewSet):
    queryset = TaskInfo.objects.all()
    serializer_class = s.TaskInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def task_list(self, request):
        # 从请求头中取操作人的用户ID
        userinfo = UserUtils.get_login_user(request)
        if userinfo:
            username = userinfo.username
        else:
            username = request.GET.get('username')
        try:
            task_id = request.GET.get('search_task_id', "")
            task_name = request.GET.get('search_task_name', "")
            # 1-测试;2-预发;3-生产
            environment = request.GET.get('search_environment', "")
            task_type = request.GET.get('search_task_type', "")
            api_type = request.GET.get('search_api_type', "")
            creater = request.GET.get('creater', "")
            page = request.GET.get('pagenum', "1")
            limit = request.GET.get('pagesize', "100")
            # 渠道 1-场景任务;2-hops;3-场景管理
            channel = request.GET.get('channel', "1")
            # 场景ID
            scene_id = request.GET.get('scene_id', "")
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询任务信息，查询条件：任务ID=%s，任务名称=%s，运行环境=%s，任务类型=%s" \
                  % (username, task_id, task_name, environment, task_type)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        if channel == "1":
            # 场景任务调用
            task_list = GetModelInfo().getTaskList(task_id, task_name, environment, task_type, creater, api_type)
            paginator = Paginator(task_list, limit)
            data = paginator.page(page)
            response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count, 'search_task_id': task_id,
                             'search_task_name': task_name, 'search_environment': environment,
                             'search_task_type': task_type, 'tasks': data.object_list}
        elif channel == "2":
            # hops调用
            task_all_list = GetModelInfo().getTaskList(task_id, task_name, environment, task_type, creater, api_type)
            task_list = [{"task_id": task["task_id"], "task_name": task["task_name"]} for task in task_all_list]
            response_json = {'tasks': task_list}
        else:
            # 场景管理调用
            # 根据场景id,拼接要查询的数据,XXXX|、|XXXX|、|XXXX(因为任务表中存储的场景数据格式为XXXX|XXXX|XXXX)
            task_id_list = []
            query_scene1 = scene_id + "|"
            query_scene2 = "|" + scene_id + "|"
            query_scene3 = "|" + scene_id
            taskinfo_obj = TaskInfo.objects.filter(is_active=True, api_type='SCENE')
            # case_pool like ('XXXX%')
            result1 = taskinfo_obj.filter(case_pool__startswith=query_scene1)
            if result1:
                task_id_list.extend([item.task_id for item in result1])
            # case_pool like ('%XXXX%')
            result2 = taskinfo_obj.filter(case_pool__contains=query_scene2)
            if result2:
                task_id_list.extend([item.task_id for item in result2])
            # case_pool like ('%XXXX')
            result3 = taskinfo_obj.filter(case_pool__endswith=query_scene3)
            if result3:
                task_id_list.extend([item.task_id for item in result3])
            # case_pool = ('XXXX')   当任务只引用了一个场景时,数据库存储的数据为XXXX
            result4 = taskinfo_obj.filter(case_pool=scene_id)
            if result4:
                task_id_list.extend([item.task_id for item in result4])
            # 获取未关联的任务列表
            task_all_obj = taskinfo_obj.exclude(task_id__in=task_id_list).values('task_id', 'task_name')
            task_list = [{"task_id": task["task_id"], "task_name": task["task_name"]} for task in task_all_obj]
            # 获取已关联的任务列表
            task_select_obj = taskinfo_obj.filter(task_id__in=task_id_list).values('task_id', 'task_name')
            task_selected_list = [{"task_id": task["task_id"], "task_name": task["task_name"]} for task in
                                  task_select_obj]

            response_json = {'tasks': task_list, 'selected_tasks': task_selected_list}

        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def add_scene_to_more_task(self, request):
        """
            从场景管理将场景添加至多个任务
        :param request:
          task_id   list  必填  任务id
          scene_id    必填   场景id
        :return:
        """
        userinfo = UserUtils.get_login_user(request)
        if userinfo:
            username = userinfo.username
        else:
            username = 'api'
        # 提取参数
        temp = json.loads(request.body)
        try:
            task_id_list = list(temp['task_id'])
            scene_id = temp['scene_id']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        taskinfo_obj = TaskInfo.objects.filter(is_active=True, api_type='SCENE')
        for task_id in task_id_list:
            result = self.check_scene_in_task(scene_id, task_id)
            if not result:
                taskinfo = taskinfo_obj.filter(task_id=int(task_id)).values('case_pool')
                case_pool = taskinfo.first()['case_pool']
                if case_pool:
                    new_scene = scene_id + "|"
                    new_case_pool = new_scene + case_pool
                else:
                    new_case_pool = scene_id
                # 更新任务关联场景
                TaskInfo.objects.filter(task_id=task_id).update(case_pool=new_case_pool)
                # 新增的场景,直接将该场景is_related=1
                SceneInfo.objects.filter(scene_id=scene_id).update(is_related=1)
                content = "用户 %s 修改任务信息成功，修改前信息：%s，修改后信息：%s" % (username, case_pool, new_case_pool)
                # 日志记录
                CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("场景添加至任务成功")

    @action(methods=['POST'], detail=False)
    def task_add(self, request):
        """
            添加任务
        :param request:
          task_name     必填  任务名称
          environment  必填   环境类型
          task_type    必填   任务类型
          api_type     必填   接口类型
          cron         必填   任务执行表达式
          is_open      必填   是否开启定时执行
          is_monitoring      必填   是否开启监控
          case_pool    必填   案例数据
          is_send_email 必填   是否发送邮件
          remark        非必填  备注
        :return:
        """
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            task_name = temp['task_name']
            environment = temp['environment']
            run_time = temp['run_time']
            remark = temp.get('remark')
            task_type = temp['task_type']
            api_type = temp['api_type']
            case_pool = "|".join(temp['case_pool'])
            is_send_email = temp['is_send_email']
            cron = temp['cron']
            is_open = temp['is_open']
            is_monitoring = temp['is_monitoring']
            is_wx_push = temp['is_wx_push']
            wx_address = temp['wx_address']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        if is_send_email:
            email_model_id = temp['email_model']
        else:
            email_model_id = ''
        task = TaskInfo(task_name=task_name, environment=environment, run_time=run_time, api_type=api_type,
                        is_send_email=is_send_email, task_status="C", task_type=task_type,
                        case_pool=case_pool, email_model_id=email_model_id, creater=username, remark=remark,
                        update_person=username, create_time=create_time, update_time=create_time, cron=cron,
                        is_open=is_open, is_monitoring=is_monitoring, is_wx_push=is_wx_push, wx_address=wx_address)
        task.save()
        scene_id_list = temp['case_pool']
        if scene_id_list:
            for scene_id in scene_id_list:
                try:
                    # 若不存在则更新该案例is_related字段为0
                    SceneInfo.objects.filter(scene_id=scene_id).update(is_related=1)
                    logger.info("场景%s更新is_related字段为1成功" % scene_id)
                except:
                    logger.error("场景%s更新is_related字段失败" % scene_id)
        else:
            logger.info("该任务中无任何场景数据")
        content = "用户 %s 新增任务成功，任务信息：%s" % (username, task.__dict__)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("任务新增成功")

    @action(methods=['PUT'], detail=True)
    def task_update(self, request, pk):
        task_id = pk
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            task_name = temp['task_name']
            environment = temp['environment']
            run_time = temp['run_time']
            remark = temp.get('remark')
            case_pool = "|".join(temp['case_pool'])
            is_send_email = temp['is_send_email']
            cron = temp['cron']
            is_open = temp['is_open']
            is_monitoring = temp['is_monitoring']
            is_wx_push = temp['is_wx_push']
            wx_address = temp['wx_address']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        task_info_before = TaskInfo.objects.get(task_id=task_id).__dict__
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        if is_send_email:
            send_email = True
            email_model_id = temp['email_model']
        else:
            send_email = False
            email_model_id = ''
        task_info_obj = TaskInfo.objects.filter(task_id=task_id).first()
        # 获取修改任务前的场景池
        old_case_pool = task_info_obj.case_pool
        # 处理任务信息
        task_info_obj.task_name = task_name
        task_info_obj.environment = environment
        task_info_obj.run_time = run_time
        task_info_obj.task_status = "C"
        task_info_obj.is_send_email = send_email
        task_info_obj.update_person = username
        task_info_obj.email_model_id = email_model_id
        task_info_obj.remark = remark
        task_info_obj.update_time = update_time
        task_info_obj.case_pool = case_pool
        task_info_obj.cron = cron
        task_info_obj.is_open = is_open
        task_info_obj.is_monitoring = is_monitoring
        task_info_obj.is_wx_push = is_wx_push
        task_info_obj.wx_address = wx_address
        task_info_obj.save()
        task_info = TaskInfo.objects.get(task_id=task_id).__dict__
        # 将老场景格式化为列表
        old_scene_id_list = old_case_pool.split("|") if old_case_pool else []
        # 将新场景池格式化为列表
        scene_id_list = case_pool.split("|")
        if scene_id_list:
            # 新增的场景
            added_scene_list = [item for item in scene_id_list if item not in old_scene_id_list]
            # 删除的场景
            removed_scene_list = [item for item in old_scene_id_list if item not in scene_id_list]
            # 处理删除场景
            for removed_scene_id in removed_scene_list if removed_scene_list else []:
                try:
                    result = self.check_scene_in_task(removed_scene_id)
                    if not result:
                        SceneInfo.objects.filter(scene_id=removed_scene_id).update(is_related=0)
                except:
                    logger.error("场景%s更新is_related字段失败" % removed_scene_id)
            # 处理新增场景
            for add_scene_id in added_scene_list if added_scene_list else []:
                try:
                    # 新增的场景,直接复制将该场景is_related=1
                    SceneInfo.objects.filter(scene_id=add_scene_id).update(is_related=1)
                    logger.info("场景%s更新is_related字段为1成功" % add_scene_id)
                except:
                    logger.error("场景%s更新is_related字段失败" % add_scene_id)
        content = "用户 %s 修改任务信息成功，修改前信息：%s，修改后信息：%s" % (username, task_info_before, task_info)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("任务编辑成功")

    @action(methods=['DELETE'], detail=True)
    def task_delete(self, request, pk):
        task_id = pk
        username = UserUtils.get_login_user(request).username
        task_info = TaskInfo.objects.get(task_id=task_id).__dict__
        task_info_obj = TaskInfo.objects.filter(task_id=task_id)
        case_pool = TaskInfo.objects.get(task_id=task_id).case_pool
        scene_id_list = case_pool.split("|")
        with transaction.atomic():
            # 创建事务保存点
            save_id = transaction.savepoint()
            try:
                task_info_obj.update(is_active=False, update_person=username, update_time=DateUtils.get_current_time())
                # 处理任务
                task_info_obj[0].delete_scene_task(task_id, task_info.get('task_name'))
                if scene_id_list and not all(item == '' for item in scene_id_list):
                    for scene_id in scene_id_list:
                        result = self.check_scene_in_task(scene_id)
                        # 场景未关联任务，则更新场景关联状态
                        if not result:
                            SceneInfo.objects.filter(scene_id=scene_id).update(is_related=0)
                else:
                    logger.info("该任务中无任何场景数据")
                content = "用户 %s 删除了任务：%s" % (username, task_info)
                # 日志记录
                CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            except Exception as e:
                logger.error(e)
                transaction.savepoint_rollback(save_id)
                return ResponseUtils.return_fail(AppErrors.ERROR_DELETE_FAIL)
                # 提交订单成功，显式的提交一次事务
            transaction.savepoint_commit(save_id)
        return ResponseUtils.return_success("任务删除成功")

    # 场景任务中关联场景删除接口
    @action(methods=['GET'], detail=False)
    def delete_task_scene(self, request):
        """
        场景任务中关联场景删除接口
        :param request:
        task_id     必填  任务id
        scene_id  必填   场景id
        :return:
        """
        # 从请求头中取操作人的用户ID
        # username = UserUtils.get_login_user(request).username
        userinfo = UserUtils.get_login_user(request)
        if userinfo:
            username = userinfo.username
        else:
            username = 'api'
        try:
            task_id = request.GET['task_id']
            scene_id = request.GET['scene_id']
            task_info_obj = TaskInfo.objects.filter(task_id=task_id, is_active=True).first()
            # 分割字符串为列表
            scene_list = task_info_obj.case_pool.split("|")
            # 移除包含 scene_id 的部分
            filtered_scene = [scene for scene in scene_list if scene != scene_id]
            # 如果结果是空列表，则返回空字符串
            if filtered_scene:
                # 重新组合字符串
                new_case_pool = "|".join(filtered_scene)
            else:
                new_case_pool = ""
            TaskInfo.objects.filter(task_id=task_id, is_active=True).update(case_pool=new_case_pool)
            result = self.check_scene_in_task(scene_id)
            if not result:
                SceneInfo.objects.filter(scene_id=scene_id).update(is_related=0)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 删除从任务%s中删除了场景：%s" % (username, task_id, scene_id)
        # 以系统日志类型记录到数据库
        CommonUtil().recordSystemLog(username, content)
        return ResponseUtils.return_success("场景从任务中删除成功")

    # 检查场景是否被其他任务引用
    def check_scene_in_task(self, scene_id, task_id=None):
        # 根据场景id,拼接要查询的数据,XXXX|、|XXXX|、|XXXX(因为任务表中存储的场景数据格式为XXXX|XXXX|XXXX)
        query_scene1 = scene_id + "|"
        query_scene2 = "|" + scene_id + "|"
        query_scene3 = "|" + scene_id
        if task_id:
            taskinfo = TaskInfo.objects.filter(task_id=int(task_id), is_active=True, api_type='SCENE').values(
                'case_pool')
        else:
            taskinfo = TaskInfo.objects.filter(is_active=True, api_type='SCENE').values('case_pool')
        # case_pool like ('XXXX%')
        result1 = taskinfo.filter(case_pool__startswith=query_scene1)
        # case_pool like ('%XXXX%')
        result2 = taskinfo.filter(case_pool__contains=query_scene2)
        # case_pool like ('%XXXX')
        result3 = taskinfo.filter(case_pool__endswith=query_scene3)
        # case_pool = ('XXXX')   当任务只引用了一个场景时,数据库存储的数据为XXXX
        result4 = taskinfo.filter(case_pool=scene_id)
        # 若以上四种查询方式任一种查到了数据,则证明该场景被其他有效任务引用
        if result1 or result2 or result3 or result4:
            logger.info("场景%s被任务关联" % scene_id)
            return True
        else:
            # 若以上四种查询方式均未查到数据,则证明该场景未被其他有效任务引用
            logger.info("场景%s未被任务关联" % scene_id)
            return False

    @action(methods=['GET'], detail=False)
    def task_run(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            task_id = request.GET['task_id']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        META = request.META
        response = self.run_task(task_id, username, META)
        return response
        # task_info = TaskInfo.objects.get(task_id=task_id)
        # if task_info.task_status == 'R':
        #     return ResponseUtils.return_fail(AppErrors.ERROR_TASK_IS_RUNNING)
        # else:
        #     ipaddress = ""
        #     TaskInfo.objects.filter(task_id=task_id).update(task_status='R')
        #     if task_info.task_type == 'UI':
        #         if 'HTTP_X_FORWARDED_FOR' in request.META:
        #             ipaddress = request.META['HTTP_X_FORWARDED_FOR']
        #         else:
        #             ipaddress = request.META['REMOTE_ADDR']
        #     t.task_job.apply_async(args=[task_id, ipaddress])
        #     #t.task_job.delay(task_id, ipaddress)
        #     time.sleep(1)  # 让前端有个缓冲时间再展示弹窗提示
        #     content = "用户 %s 运行了任务：%s" % (username, task_info.task_name)
        #     # 日志记录
        #     CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        #     return ResponseUtils.return_success("已调度任务在后台异步执行，别忘了稍后回来查看报告哦")

    @action(methods=['GET'], detail=False)
    def manual_retry_task(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            task_id = request.GET['task_id']
            record_id = request.GET['task_report_id']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        META = request.META
        response = self.run_task(task_id, username, META, record_id)
        return response

    def run_task(self, task_id, username, META, record_id='', batch_number='', code_name='', channel='2'):
        task_info = TaskInfo.objects.get(task_id=task_id)
        if task_info.task_status == 'R':
            return ResponseUtils.return_fail(AppErrors.ERROR_TASK_IS_RUNNING)
        else:
            ipaddress = ""
            TaskInfo.objects.filter(task_id=task_id).update(task_status='R')
            if task_info.task_type == 'UI':
                if 'HTTP_X_FORWARDED_FOR' in META:
                    ipaddress = META['HTTP_X_FORWARDED_FOR']
                else:
                    ipaddress = META['REMOTE_ADDR']
            t.task_job.apply_async(args=[task_id, record_id, ipaddress, batch_number, code_name, channel])
            # record_id = t.task_job.apply_async(args=[task_id, ipaddress])
            # t.task_job.delay(task_id, ipaddress)
            time.sleep(1)  # 让前端有个缓冲时间再展示弹窗提示
            content = "用户 %s 运行了任务：%s" % (username, task_info.task_name)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("已调度任务在后台异步执行，别忘了稍后回来查看报告哦")
            # return ResponseUtils.return_success(msg="已调度任务在后台异步执行，别忘了稍后回来查看报告哦", data={'record_id': record_id})

    @action(methods=['POST'], detail=False)
    def hops_run_task(self, request):
        """
        hops调度测试平台任务运行方法
        :param request:
          hops_id     必填  hops单号
          code_name  必填   hops项目名称
          environment    必填   环境类型
          task_id     必填   测试平台任务id
          username    必填   操作人
        :return:
        """
        temp = json.loads(request.body)
        try:
            hops_id = temp['hops_id']
            code_name = temp['code_name']
            environment = temp['environment']
            task_id = temp['task_id']
            username = temp['username']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        META = request.META
        response = self.run_task(task_id, username, META, batch_number=hops_id, code_name=code_name, channel='3')
        if response.status_code == 200:
            # hops调度成功后,将调度信息记录至release_recode
            create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            release_reocde = ReleaseRecordInfo(hops_id=hops_id,
                                               environment=environment,
                                               code_name=code_name, task_id=task_id, username=username,
                                               create_time=create_time)
            release_reocde.save()
        return ResponseUtils.return_success("%s任务调度成功" % (task_id))

    @action(methods=['GET'], detail=False)
    def task_report(self, request):
        """
          任务报表
        :param request:  task_id ： 必填 任务执行记录的id
        :return:
        """
        # testResult = []
        task_report_id = request.GET['task_id']
        old_task_report_id = request.GET.get('old_task_id', "")
        task_fail_report(task_report_id)
        report_content_obj = TaskRunningRecord.objects.filter(task_record_id=task_report_id).values(
            'task_id', 'task_name', 'task_type', 'environment', 'test_all', 'test_pass', 'test_fail', 'test_error',
            'time_stamp', 'total_time').first()
        test_total = ''
        test_pass = ''
        total_time = ''
        createTime = ''
        json_dict = {}
        scene_record = SceneRunningRecord.objects.filter(parent_id=task_report_id).values(
            'scene_id', 'scene_name', 'business_id', 'total_time', 'run_result', 'parent_id')
        if old_task_report_id:
            old_report_content_obj = TaskRunningRecord.objects.filter(task_record_id=old_task_report_id).values(
                'test_all', 'test_pass', 'total_time', 'time_stamp').first()
            createTime = old_report_content_obj['time_stamp']
            total_time = float(old_report_content_obj['total_time']) + float(report_content_obj['total_time'])
            test_total = old_report_content_obj['test_all']
            test_pass = old_report_content_obj['test_pass'] + report_content_obj['test_pass']
            old_scene_record = SceneRunningRecord.objects.filter(parent_id=old_task_report_id,
                                                                 run_result='pass').values(
                'scene_id', 'scene_name', 'business_id', 'total_time', 'run_result', 'parent_id')
            scene_record = scene_record.union(old_scene_record)
        else:
            test_total = report_content_obj['test_all']
            test_pass = report_content_obj['test_pass']
            # 解决任务执行中，运行时间为空时，类型转化问题
            if report_content_obj['total_time']:
                total_time = float(report_content_obj['total_time'])
            else:
                total_time = 0
            createTime = report_content_obj['time_stamp']
        if scene_record is not None:
            # 根据场景id获取所关联的业务线id，并去重
            business_ids = set(scene['business_id'] for scene in scene_record if scene['scene_id'])
            # 根据业务线id获取名称
            business_info_data = BusinessInfo.objects.filter(business_id__in=business_ids, is_active=True).values(
                'business_id', 'business_name')
            # 获取所有的场景id,并去重
            scene_ids = set(scene['scene_id'] for scene in scene_record if scene['scene_id'])
            # 根据场景id查询出对应的创建人
            scene_info_data = SceneInfo.objects.filter(scene_id__in=scene_ids, is_active=True).values('scene_id',
                                                                                                      'creater')
            # 将业务线数据写入字典进行缓存
            business_info_mapping = {info['business_id']: info['business_name'] for info in business_info_data}
            # 将创建人数据写入字典进行缓存
            scene_info_mapping = {info['scene_id']: info['creater'] for info in scene_info_data}
            # 列表推导式
            record_list = [
                {
                    'parentId': scene['parent_id'],
                    'sceneId': scene['scene_id'],
                    'sceneName': scene['scene_name'],
                    'businessLine': business_info_mapping.get(scene['business_id']),
                    'spendTime': scene['total_time'],
                    'bugRank': "无",
                    'creater': scene_info_mapping.get(int(scene['scene_id'])),
                    'status': "成功" if scene['run_result'] == "pass" else "失败" if scene[
                                                                                         'run_result'] == "fail" else "错误"
                }
                for scene in scene_record if scene['scene_id']
            ]
            json_dict = json.dumps({"testResult": record_list}, ensure_ascii=False)

        # 环境类型枚举转义成中文
        if report_content_obj['environment'] == "1":
            environment = "测试环境"
        elif report_content_obj['environment'] == "2":
            environment = "预发环境"
        elif report_content_obj['environment'] == "3":
            environment = "生产环境"
        else:
            environment = "错误"

        # 任务类型枚举转义成中文
        if report_content_obj['task_type'] == "1":
            taskType = "API-Case"
        elif report_content_obj['task_type'] == "2":
            taskType = "API-Scene"
        elif report_content_obj['task_type'] == "3":
            taskType = "WEB-UI"
        elif report_content_obj['task_type'] == "4":
            taskType = "Android-UI"
        else:
            taskType = "错误"

        if report_content_obj:
            try:
                # 判断是否能将运行时长转换为浮点数
                float_value = float(total_time)
                # 进行浮点数的转换和计算，转化为分钟
                total_time = round(float_value / 60, 2)
            except ValueError:
                # 处理字符串无法转换为浮点数的情况
                total_time = None
            return render(request, 'task_report.html', {'resultData': json_dict,
                                                        'taskId': report_content_obj['task_id'],
                                                        'taskName': report_content_obj['task_name'],
                                                        'runEnvironment': environment,
                                                        'taskType': taskType,
                                                        'testAll': test_total,
                                                        'testPass': test_pass,
                                                        'testFail': report_content_obj['test_fail'],
                                                        'testError': report_content_obj['test_error'],
                                                        'createTime': createTime,
                                                        'totalTime': total_time,
                                                        'platform_domain': platform_domain
                                                        })
        return HttpResponse("暂无测试报告!")

    # 获取日常监控列表
    @action(methods=['post'], detail=False)
    def get_daily_monitoring_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            environment = temp.get('environment', '')
            is_pending = temp.get('is_pending', '')
            start_date = temp.get('start_date', '')
            end_date = temp.get('end_date', '')
            page = temp.get('pagenum')
            limit = temp.get('pagesize')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询日常监控列表，查询条件：环境=%s，日期=%s-%s" % (username, environment, start_date, end_date)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        records = TaskRunningRecord.objects.filter(channel='1', environment=environment)
        if start_date and end_date:
            # 将字符串日期转换为 datetime 对象
            start_date = timezone.datetime.strptime(start_date, '%Y-%m-%d')
            end_date = timezone.datetime.strptime(end_date, '%Y-%m-%d').replace(hour=23, minute=59, second=59,
                                                                                microsecond=999999)
            records = records.filter(time_stamp__range=[start_date, end_date])

        # 预加载所有相关 TaskInfo 的 is_monitoring 状态
        task_ids = list({record.task_id for record in records})  # 去重
        task_infos = TaskInfo.objects.filter(task_id__in=task_ids).values('task_id', 'is_monitoring')
        monitoring_status = {str(info['task_id']): info['is_monitoring'] for info in task_infos}

        # 用于存储最终结果的字典
        result = {}
        daily_summary = defaultdict(lambda: {
            'test_all': 0,
            'test_fail': 0,
            'test_error': 0,
            'pending_case_total': 0,
            'record_ids': [],
            'creaters': set()
        })
        for record in records:
            # 获取日期（yyyy-mm-dd）
            date = record.time_stamp.strftime('%Y-%m-%d')
            task_id = record.task_id
            # 跳过未开启监控的任务
            if not monitoring_status.get(task_id, False):
                continue  # 不满足 is_monitoring=1 的任务直接跳过

            # 初始化日期和任务记录
            date_dict = result.setdefault(date, {})
            # 仅在原记录时累加 test_all
            if record.old_record_id is None:
                daily_summary[date]['test_all'] += record.test_all

            # 判断是否需要更新记录：如果任务ID不存在，或当前记录是重试任务
            if task_id not in date_dict or record.old_record_id is not None:
                date_dict[task_id] = record
                # 初始化待处理用例数
                pending_case_total = 0
                # 初始化 chinese_names 为空列表
                chinese_names = []
                # 如果是重试任务，更新用例总数和待处理数
                if record.old_record_id is not None:
                    pending_case_total = TaskMonitoringFailRecord.objects.filter(
                        parent_id=record.task_record_id,
                        is_repair='0'
                    ).count()
                    # 只有在有未处理用例时查询负责人
                    if pending_case_total > 0:
                        creaters = (TaskMonitoringFailRecord.objects.
                                    filter(parent_id=record.task_record_id, is_repair='0').values('creater'))
                        # 关联 UserInfo 表，获取 chinese_name
                        chinese_names = UserInfo.objects.filter(
                            username__in=creaters  # 过滤出 creater 对应的 UserInfo 记录
                        ).values_list('chinese_name', flat=True).distinct()  # 去重并提取 chinese_name

                    case_pool = TaskInfo.objects.filter(task_id=task_id).values_list('case_pool', flat=True).first()
                    if case_pool:
                        scene_total = len(case_pool.split("|"))
                        date_dict[task_id].test_all = scene_total

                # daily_summary[date]['test_all'] += record.test_all
                daily_summary[date]['test_fail'] += record.test_fail
                daily_summary[date]['test_error'] += record.test_error
                daily_summary[date]['pending_case_total'] += pending_case_total
                daily_summary[date]['record_ids'].append(str(record.task_record_id))
                daily_summary[date]['creaters'].update(chinese_names)

        # 直接生成最终结果
        daily_summary_list = [
            {
                'date': date,
                'test_all': stats['test_all'],
                'test_pass': stats['test_all'] - stats['test_fail'] - stats['test_error'],  # 动态计算通过数
                'test_fail': stats['test_fail'],
                'test_error': stats['test_error'],
                'pending_case_total': stats['pending_case_total'],
                'failure_rate': f"{round((stats['test_error'] + stats['test_fail']) / stats['test_all'] * 100, 2)}%"
                if stats['test_all'] > 0 else "0%",
                'record_ids': ','.join(stats['record_ids']),
                'creaters': ','.join(stats['creaters'])
            }
            for date, stats in daily_summary.items()
            # 根据is_pending(是否存在待处理用例)决定过滤条件,为true则只展示待处理用例数大于0的数据,为否则展示待处理用例数等于0的数据
            if is_pending == '' or (is_pending == '1' and stats['pending_case_total'] > 0) or (
                        is_pending == '0' and stats['pending_case_total'] == 0)
        ]
        # 按日期排序
        daily_summary_list.sort(key=lambda x: x['date'], reverse=True)
        paginator = Paginator(daily_summary_list, limit)
        data = paginator.page(page)
        data_list = list(data.object_list)
        response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count, 'data': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    # 获得日常监控详细运行情况列表
    @action(methods=['post'], detail=False)
    def get_daily_monitoring_detail_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            environment = temp.get('environment', '')  # 运行环境(必填)
            date = temp.get('date', '')  # 监控日期(必填)
            is_repair = temp.get('is_repair', '')  # 是否为待处理用例:0-未处理,1-已处理
            business_id = temp.get('businessId', '')  # 业务线
            scene_id = temp.get('scene_id', '')  # 业务线
            scene_name = temp.get('scene_name', '')  # 业务线
            creater = temp.get('creater', '')  # 用例负责人
            fail_type = temp.get('fail_type', '')  # 错误类型
            page = temp.get('pagenum')
            limit = temp.get('pagesize')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询日常监控详细运行情况列表" % (username)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        records = TaskRunningRecord.objects.filter(channel='1', environment=environment, time_stamp__date=date)
        task_record_dict = {}
        for record in records:
            task_id = record.task_id
            old_record_id = record.old_record_id
            # 判断是否需要更新记录：如果任务ID不存在，或当前记录是重试任务
            if task_id not in task_record_dict or old_record_id is not None:
                task_record_dict[task_id] = {
                    "task_name": record.task_name,
                    "task_record_id": record.task_record_id
                }
        # 初始化一个空的 QuerySet
        monitoring_record_list = TaskMonitoringFailRecord.objects.none()
        # 用于存储每个场景的累计报错次数
        scene_error_count = defaultdict(int)
        for task_id, record_info in task_record_dict.items():
            task_record_id = record_info["task_record_id"]
            monitoring_record = TaskMonitoringFailRecord.objects.filter(parent_id=task_record_id)
            if business_id:
                monitoring_record = monitoring_record.filter(business_id=business_id)
            if scene_id:
                monitoring_record = monitoring_record.filter(scene_id=scene_id)
            if scene_name:
                monitoring_record = monitoring_record.filter(scene_name__contains=scene_name)
            if is_repair:
                monitoring_record = monitoring_record.filter(is_repair=is_repair)
            if fail_type:
                monitoring_record = monitoring_record.filter(fail_type=fail_type)
            if creater:
                monitoring_record = monitoring_record.filter(creater__contains=creater)
            monitoring_record_list = monitoring_record_list | monitoring_record
        monitoring_record_list = monitoring_record_list.order_by('-monitoring_record_id')

        # 使用聚合查询统计每个 scene_id 的错误次数
        scene_errors = TaskMonitoringFailRecord.objects.filter(environment=environment).values('scene_id').annotate(total=Count('scene_id'))
        scene_error_count = {item['scene_id']: item['total'] for item in scene_errors}

        paginator = Paginator(monitoring_record_list, limit)
        data = paginator.page(page)
        data_list = list(data.object_list.values())
        for item in data_list:
            businessInfo = BusinessInfo.objects.get(business_id=item["business_id"])
            item["businessName"] = businessInfo.business_name
            item["task_name"] = TaskRunningRecord.objects.get(task_record_id=item["parent_id"]).task_name
            # 添加场景累计报错次数
            scene_id = item["scene_id"]
            item["scene_error_count"] = scene_error_count.get(scene_id, 0)
        response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count,
                         'data': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    # 维护日常监控用例失败原因
    @action(methods=['POST'], detail=False)
    def update_daily_monitoring_fail_reason(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        caseform = temp.get('caseHandleForm', '')
        try:
            monitoring_record_id = temp.get('record_ids', '')
            fail_type = caseform.get('fail_type')
            is_repair = caseform.get('is_repair')
            repair_plan = caseform.get('repair_plan')
            tapd_link = caseform.get('tapd_link', None)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        TaskMonitoringFailRecord.objects.filter(
            monitoring_record_id__in=monitoring_record_id).update(
            fail_type=fail_type,
            is_repair=is_repair,
            repair_plan=repair_plan,
            tapd_link=tapd_link,
            update_time=update_time)
        content = "用户 %s 维护失败用例成功,监控记录ID: %s" % (
            username, monitoring_record_id)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("处理成功")

    @action(methods=['POST'], detail=False)
    def batch_update_daily_monitoring_result(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            record_ids = temp.get('record_ids', '')  # 用例负责人
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        TaskMonitoringFailRecord.objects.filter(
            parent_id__in=record_ids, is_repair=0).update(
            is_repair=1, update_time=update_time, repair_plan='手动批量处理'
        )
        content = "用户 %s 批量处理失败用例,task_record_id: %s" % (
            username, record_ids)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("处理成功")


    @action(methods=['GET'], detail=False)
    def task_report_detail(self, request):
        """
          获取场景详情日志
        :param request:
          scene_id ： 必填 场景id
          parent_id： 必填  报告id
        :return:
        """
        try:
            scene_id = request.GET['scene_id']
            parent_id = request.GET['parent_id']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

        if scene_id == '' or parent_id == '':
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, '缺少参数值')
        scene_running_record = SceneRunningRecord.objects.filter(parent_id=parent_id,
                                                                 scene_id__exact=scene_id).first()
        if not scene_running_record:
            return ResponseUtils.return_fail(AppError("HD1000", "暂无日志记录!"))

        return ResponseUtils.return_success("获取成功", eval(scene_running_record.running_log))

    @action(methods=['GET'], detail=False)
    def task_report_list(self, request):
        """
         任务报表列表
       :param request:  task_id ： 必填 任务id
       :return:
       """
        try:
            task_id = request.GET['task_id']
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
            if page is None or page == '':
                page = 1
            if limit is None or limit == '':
                limit = 5
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        report_list = TaskRunningRecord.objects.values("task_record_id", "task_id", "task_name", "environment",
                                                       "task_type", "test_all", "test_pass", "test_fail", "test_error",
                                                       "total_time", "batch_number", "creater", "time_stamp").filter(
            task_id=task_id).order_by('-time_stamp')
        paginator = Paginator(report_list, limit)
        data = paginator.page(page)
        data_list = data.object_list
        record_list = []
        ''' if data_list is not None and len(data_list) > 0:
            for task_record in data_list:
                if task_record.report_content:
                    report_content = json.loads(task_record.report_content)
                    trd = dict(taskReportId=task_record.task_record_id, taskId=report_content["taskId"], taskName=report_content["taskName"],
                               runEnvironment=report_content["runEnvironment"], taskType=report_content["taskType"],
                               caseNum=report_content["testAll"], caseSuccess=report_content["testPass"],
                               caseFail=report_content["testFail"], caseError=report_content["testError"], createTime=report_content["createTime"],
                               totalTime=report_content["totalTime"])
                    record_list.append(trd)
                else:
                    return ResponseUtils.return_fail(AppErrors.ERROR_REPORT_IS_NULL)
        '''
        if data_list is not None and len(data_list) > 0:
            for task_record in data_list:
                trd = dict(taskReportId=task_record["task_record_id"], taskId=task_record["task_id"],
                           taskName=task_record["task_name"],
                           runEnvironment=task_record["environment"], taskType=task_record["task_type"],
                           caseNum=task_record["test_all"], caseSuccess=task_record["test_pass"],
                           caseFail=task_record["test_fail"], caseError=task_record["test_error"],
                           createTime=task_record["time_stamp"],
                           totalTime=task_record["total_time"])
                record_list.append(trd)
        response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count,
                         'task_reports': record_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['GET'], detail=False)
    def interface_compare_report(self, request):
        """
         接口差异比对报告
       :param request:  date ： 必填 比对日期 YYYY-MM-DD
       :return:
       """
        try:
            report_date = request.GET['date']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

        interface_compare_result_obj = InterfaceCompareResult.objects.filter(date=report_date)
        # 统计总数
        total = interface_compare_result_obj.count()
        # 统计变更
        change = interface_compare_result_obj.filter(compare_result=1).count()
        # 统计未覆盖
        not_covered = interface_compare_result_obj.filter(compare_result=2).count()
        # 统计新增
        add = interface_compare_result_obj.filter(compare_result=3).count()

        # 使用 in_bulk 方法来一次性获取，避免在for循环中再查表
        interface_ids = [obj.interface_id for obj in interface_compare_result_obj]
        interface_infos = InterfaceInfo.objects.in_bulk(interface_ids)

        # 预先加载project_id到project_name的映射
        project_id_to_name = {
            project.project_id: project.project_name
            for project in ProjectInfo.objects.all()
        }

        # 获取接口详细数据
        datalist = []
        for interface_compare_result in interface_compare_result_obj:
            interface_id = interface_compare_result.interface_id
            if interface_id in interface_infos:
                interface_info = interface_infos[interface_id]
                # 将 update_time 转换为 ISO 格式的字符串
                update_time_value = interface_info.update_time.isoformat()

                # 直接从字典中获取project_name，避免了数据库查询
                project_name = project_id_to_name.get(int(interface_info.project_id), "None")

                # 指定需要的字段
                selected_fields = {
                    'interface_id': interface_info.interface_id,
                    'project_name': project_name,
                    'interface_name': interface_info.interface_name,
                    'interface_way': interface_info.interface_way,
                    'interface_address': interface_info.interface_address,
                    'body_demo': interface_info.body_demo,
                    'param_demo': interface_info.param_demo,
                    'compare_result': interface_compare_result.compare_result,
                    'update_time': update_time_value,
                    'creater': interface_info.creater
                }
                datalist.append(selected_fields)
        json_dict = json.dumps({"testResult": datalist}, ensure_ascii=False)
        # response_json = {'total': total, "add": add, "change": change, "datalist": datalist}
        if interface_compare_result_obj:
            return render(request, 'interface_compare_report.html', {'resultData': json_dict,
                                                                     'taskName': "待处理接口清单",
                                                                     'taskType': "API差异比对",
                                                                     'all': total,
                                                                     'add': add,
                                                                     'change': change,
                                                                     'notCovered': not_covered,
                                                                     'createTime': report_date
                                                                     })
        return HttpResponse("暂无API差异比对报告!")


class EmailModelInfoViewSet(viewsets.ModelViewSet):
    queryset = EmailModelInfo.objects.all()
    serializer_class = s.EmailModelInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def email_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            email_model_id = request.GET.get('search_email_model_id', '')
            email_model_name = request.GET.get('search_email_model_name', '')
            creater = request.GET.get('creater', '')
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询邮件模板信息，查询条件：邮件模板编号=%s，邮件模板名称=%s" \
                  % (username, email_model_id, email_model_name)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        obj = EmailModelInfo.objects.filter(is_active=True)
        if email_model_id:
            obj = obj.filter(email_model_id=email_model_id)
        if email_model_name:
            obj = obj.filter(email_model_name__contains=email_model_name)
        if creater:
            obj = obj.filter(creater__contains=creater)
        obj = obj.order_by('-update_time')
        paginator = Paginator(obj, limit)
        data = paginator.page(page)
        data_list = list(data.object_list.values())
        response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count,
                         'search_email_model_id': email_model_id, 'search_email_model_name': email_model_name,
                         'emails': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def email_add(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            email_model_name = temp['email_model_name']
            email_subject = temp['email_subject']
            email_TO = temp['email_TO']
            email_CC = temp.get('email_CC')
            email_content = temp.get('email_content')
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        emaile = EmailModelInfo(email_model_name=email_model_name, email_subject=email_subject,
                                email_TO=email_TO, email_CC=email_CC, email_content=email_content, creater=username,
                                update_person=username, create_time=create_time, update_time=create_time)
        emaile.save()
        content = "用户 %s 新增邮件模板成功，邮件模板信息：%s" % (username, emaile.__dict__)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("邮件模板新增成功")

    @action(methods=['PUT'], detail=True)
    def email_update(self, request, pk):
        email_id = pk
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            email_model_name = temp['email_model_name']
            email_subject = temp['email_subject']
            email_TO = temp['email_TO']
            email_CC = temp['email_CC']
            email_content = temp['email_content']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        email_model_before = EmailModelInfo.objects.get(email_model_id=email_id).__dict__
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        EmailModelInfo.objects.filter(email_model_id=email_id).update(email_model_name=email_model_name,
                                                                      email_subject=email_subject,
                                                                      email_TO=email_TO, email_CC=email_CC,
                                                                      email_content=email_content,
                                                                      update_person=username,
                                                                      update_time=update_time)
        email_model_info = EmailModelInfo.objects.get(email_model_id=email_id).__dict__
        content = "用户 %s 修改邮件模板信息成功，修改前信息：%s，修改后信息：%s" % (
            username, email_model_before, email_model_info)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("邮件模板编辑成功")

    @action(methods=['DELETE'], detail=True)
    def email_delete(self, request, pk):
        email_id = pk
        username = UserUtils.get_login_user(request).username
        email_model = EmailModelInfo.objects.get(email_model_id=email_id).__dict__
        # 判断邮件模板是否被引用
        taskInfo = TaskInfo.objects.filter(email_model_id=email_id)
        email_list = []
        if taskInfo:
            for x in taskInfo:
                email_list.append(str(x.task_id) + "-" + x.task_name)
            return ResponseUtils.return_fail(AppErrors.ERROR_EMAIL_IS_QUOTE)
        else:
            EmailModelInfo.objects.filter(email_model_id=email_id).update(is_active=False, update_person=username,
                                                                          update_time=DateUtils.get_current_time())
            content = "用户 %s 删除了邮件模板：%s" % (username, email_model)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("邮件模板删除成功")


def task_fail_report(id):
    """
      任务报表
    :param request:  task_id ： 必填 任务执行记录的id
    :return:
    """
    # testResult = []
    task_report_id = id
    json_dict = {}
    scene_record = SceneRunningRecord.objects.exclude(run_result='pass').filter(parent_id=task_report_id, ).values(
        'scene_id', 'scene_name', 'business_id', 'total_time', 'run_result')
    if scene_record is not None:
        # 根据场景id获取所关联的业务线id，并去重
        business_ids = set(scene['business_id'] for scene in scene_record if scene['scene_id'])
        # 根据业务线id获取名称
        business_info_data = BusinessInfo.objects.filter(business_id__in=business_ids, is_active=True).values(
            'business_id', 'business_name')
        # 获取所有的场景id,并去重
        scene_ids = set(scene['scene_id'] for scene in scene_record if scene['scene_id'])
        # 根据场景id查询出对应的创建人
        scene_info_data = SceneInfo.objects.filter(scene_id__in=scene_ids, is_active=True).values('scene_id',
                                                                                                  'creater')
        creater_data = set(info['creater'] for info in scene_info_data)
        user_info_data = UserInfo.objects.filter(username__in=creater_data).values('username', 'chinese_name')
        # 将业务线数据写入字典进行缓存
        business_info_mapping = {info['business_id']: info['business_name'] for info in business_info_data}
        # 将创建人数据写入字典进行缓存
        scene_info_mapping = {info['scene_id']: info['creater'] for info in scene_info_data}
        user_info_mapping = {info['username']: info['chinese_name'] for info in user_info_data}
        # 列表推导式
        record_list = [
            {
                'sceneId': scene['scene_id'],
                'sceneName': scene['scene_name'],
                'businessLine': business_info_mapping.get(scene['business_id']),
                'spendTime': scene['total_time'],
                'bugRank': "无",
                'creater': user_info_mapping.get(scene_info_mapping.get(int(scene['scene_id']))),
                'status': "成功" if scene['run_result'] == "pass" else "失败" if scene['run_result'] == "fail" else "错误"
            }
            for scene in scene_record if scene['scene_id']
        ]
        return record_list


def export_fail_result(request):
    id = request.GET['task_id']
    records = task_fail_report(id)
    # 创建一个新的工作簿和工作表
    workbook = Workbook()
    sheet = workbook.active
    sheet.title = "测试报告-执行失败用例"
    # 写入表头
    headers = ["场景ID", "业务线", "负责人"]
    sheet.append(headers)
    # 设置表头样式（居中文本、加粗字体、添加边框、底色灰色、字体调大）
    bold_font = Font(bold=True, size=15)  # 字体调大
    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    thin_border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'),
                         bottom=Side(style='thin'))
    grey_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')  # 灰色底色

    for cell in sheet[1]:
        cell.font = bold_font
        cell.alignment = center_alignment
        cell.border = thin_border
        cell.fill = grey_fill  # 底色灰色
    # 设置列宽
    sheet.column_dimensions['A'].width = 100
    sheet.column_dimensions['B'].width = 20
    sheet.column_dimensions['C'].width = 20
    for record in records:
        sceneId = record["sceneId"]
        name = record["sceneName"]
        combined_data = f"{sceneId}\n{name}"
        row = [combined_data, record["businessLine"], record["creater"]]
        sheet.append(row)
    # 设置单元格样式，使换行符生效
    for row in sheet.iter_rows(min_row=2, max_col=sheet.max_column, max_row=sheet.max_row):
        for cell in row:
            cell.alignment = Alignment(wrap_text=True, horizontal='center', vertical='center')
            cell.font = Font(size=10)
            # cell.border = thin_border

    # 创建一个HTTP响应对象
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    filename = '测试报告-执行失败用例.xlsx'
    response['Content-Disposition'] = 'attachment; filename="%s"' % quote(filename)
    # 将工作簿保存到响应对象中
    workbook.save(response)

    return response
