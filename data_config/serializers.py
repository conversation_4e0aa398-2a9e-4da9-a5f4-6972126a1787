import re

from django.db.models import Max
from rest_framework import serializers
from scaffold.restframework.utils import auto_declare_serializers

from . import models as m


class DataBaseInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.DataBaseInfo
        fields = '__all__'


class ParameterInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.ParameterInfo
        fields = '__all__'


class TaskInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.TaskInfo
        fields = '__all__'


class EmailModelInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.EmailModelInfo
        fields = '__all__'


# # Auto declare serializer classes from models.
auto_declare_serializers(m, locals())
