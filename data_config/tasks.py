import time
import json
from django_celery_beat.models import PeriodicTask, CrontabSchedule

from core.servers.interfaceCompare import interface_compare
from core.servers.taskExecute import TaskExecute
from data_config.models import TaskRunningRecord
from data_config.models import TaskInfo
from hydee_auto_server.celery import app
from django.db import connections
from core.commonUtils.commonUtil import CommonUtil
from core.commonUtils.apiTestUtil import APITest
from tools_help.views import *
from interface_test.models import *
from datetime import date

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()

# 解决 OperationalError: (2006, 'MySQL server has gone away')问题
# 如果客户端使用一个连接查询多次数据库，如果连续查询则没有问题，如果查询几次后停顿超过wait_timeout后再次查询就会出现数据库连接丢失
def close_old_connections():
    for conn in connections.all():
        conn.close_if_unusable_or_obsolete()


@app.task(bind=True)
def task_job(self, task_id, record_id="", ipaddress="", batch_number="", code_name="", channel="1", email=""):
    logger.info(f'开始任务执行，task_id={task_id}')
    logger.info(f'开始任务执行，record_id={record_id}')
    logger.info(f'开始任务执行，batch_number={batch_number}')
    logger.info(f'开始任务执行，code_name={code_name}')
    close_old_connections()
    task_record_id = TaskExecute(task_id, ipaddress, batch_number, code_name, email, record_id, channel).task_execute()
    is_need_monitoring = (TaskRunningRecord.objects.filter(
        task_record_id=task_record_id, channel='1').exists()
                          and TaskInfo.objects.filter(task_id=task_id).values_list('is_monitoring', flat=True).first())
    if is_need_monitoring:
        # 将运行结果存入监控表
        GetModelInfo().saveFailScene(task_record_id)
    close_old_connections()
    logger.info(f'结束任务执行，task_id={task_id}')
    print(f'结束任务执行，task_id={task_id}')

@app.task(bind=True)
def mock_refund(self, businessReturnUrl, data):
    time.sleep(3)
    downTime = CommonUtil().get_current_ymdhms()
    data['downTime'] = downTime
    test = APITest("POST", businessReturnUrl, json.dumps(data))
    test.doAutoAPITest()

@app.task(bind=True)
def mock_pay(self, businessReturnUrl, data):
    time.sleep(3)
    downTime = CommonUtil().get_current_ymdhms()
    data['downTime'] = downTime
    test = APITest("POST", businessReturnUrl, json.dumps(data))
    test.doAutoAPITest()

@app.task(bind=True)
def interface_compare_job(self):
    #调用接口比对方法
    interface_compare(self)
    # 调用企微推送消息
    TaskExecute(None).pushMessage('api')


@app.task(bind=True)
def swagger_skywakling_list(self):
    # 将SkyWalking中的服务信息落表
    ToolsHelpViewSet.service_config(self, None)
    # 将经过business-gateway的所有接口数据落表
    ToolsHelpViewSet.visit_interface(self, None)
    # 将swagger所有数据落标
    ToolsHelpViewSet.swagger_interface(self, None)

@app.task(bind=True)
def interface_statistics_job(self):
    # 统计SkyWalking所有服务的访问频次
    ToolsHelpViewSet.visit_interface_statistics(self, None)
