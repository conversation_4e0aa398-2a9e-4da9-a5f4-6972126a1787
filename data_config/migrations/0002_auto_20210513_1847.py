# Generated by Django 3.0.6 on 2021-05-13 18:47

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('data_config', '0001_initial'),
    ]

    operations = [
        migrations.DeleteModel(
            name='AndroidCaseInfo',
        ),
        migrations.DeleteModel(
            name='AndroidElementInfo',
        ),
        migrations.DeleteModel(
            name='AppCaseRunningRecord',
        ),
        migrations.DeleteModel(
            name='ApplicationInfo',
        ),
        migrations.DeleteModel(
            name='AssertInfo',
        ),
        migrations.DeleteModel(
            name='AssertResultInfo',
        ),
        migrations.DeleteModel(
            name='CaseFormationInfo',
        ),
        migrations.DeleteModel(
            name='CaseInfo',
        ),
        migrations.DeleteModel(
            name='CaseRunningRecord',
        ),
        migrations.DeleteModel(
            name='CaseRunPreRecord',
        ),
        migrations.DeleteModel(
            name='ChunkInfo',
        ),
        migrations.DeleteModel(
            name='DeviceInfo',
        ),
        migrations.DeleteModel(
            name='ElementInfo',
        ),
        migrations.DeleteModel(
            name='InterfaceInfo',
        ),
        migrations.DeleteModel(
            name='LocustReport',
        ),
        migrations.DeleteModel(
            name='LocustTaskRecord',
        ),
        migrations.DeleteModel(
            name='LogRecord',
        ),
        migrations.DeleteModel(
            name='PageInfo',
        ),
        migrations.DeleteModel(
            name='ProjectInfo',
        ),
        migrations.DeleteModel(
            name='PublishingTaskRunningRecode',
        ),
        migrations.DeleteModel(
            name='SceneInfo',
        ),
        migrations.DeleteModel(
            name='SceneRelation',
        ),
        migrations.DeleteModel(
            name='SceneRunningRecord',
        ),
        migrations.DeleteModel(
            name='WebCaseInfo',
        ),
        migrations.DeleteModel(
            name='WebCaseRunningRecord',
        ),
    ]
