# Generated by Django 3.0.6 on 2021-05-18 17:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('data_config', '0003_auto_20210514_1022'),
    ]

    operations = [
        migrations.AddField(
            model_name='taskinfo',
            name='cron',
            field=models.Char<PERSON>ield(blank=True, max_length=30, null=True, verbose_name='时间corn表达式'),
        ),
        migrations.AddField(
            model_name='taskinfo',
            name='is_active',
            field=models.BooleanField(default=False, verbose_name='是否开启任务'),
        ),
        migrations.AlterField(
            model_name='taskinfo',
            name='remark',
            field=models.TextField(blank=True, null=True),
        ),
    ]
