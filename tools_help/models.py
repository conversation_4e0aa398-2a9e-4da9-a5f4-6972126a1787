from django.db import models


# 发版任务执行记录表
class PublishingTaskRunningRecode(models.Model):
    record_id = models.AutoField(primary_key=True)
    batch_number = models.CharField(max_length=128)  # 发版批次号
    environment = models.CharField(max_length=8)  # 运行环境
    package = models.TextField()  # 发版更新的包名
    report_content = models.TextField()  # 任务执行报告
    begin_time = models.DateTimeField()  # 发版完成时间
    end_time = models.DateTimeField(null=True)  # 任务执行完成时间
    status = models.CharField(max_length=8)  # 任务执行状态

    class Meta(object):
        # 定义表名
        db_table = "publishing_task_running_record"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-begin_time']
        # 表别名
        verbose_name = '发版任务执行记录表'


# hops调度任务记录表
class ReleaseRecordInfo(models.Model):
    record_id = models.AutoField(primary_key=True)
    hops_id = models.CharField(max_length=128)  # hops单号
    environment = models.CharField(max_length=8)  # 运行环境
    code_name = models.TextField()  # 项目名
    task_id = models.CharField(max_length=128)  # 任务id
    create_time = models.DateTimeField()  # 任务开始时间
    username = models.CharField(max_length=50)  # 操作人

    class Meta(object):
        # 定义表名
        db_table = "release_record"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-create_time']
        # 表别名
        verbose_name = 'hops调度任务记录表'


class ServiceConfig(models.Model):
    id = models.AutoField(primary_key=True)
    service = models.CharField(max_length=128)
    skywalking_service = models.CharField(max_length=128)
    swagger_domain = models.CharField(max_length=128)
    swagger_service = models.CharField(max_length=128)
    swagger_api_version = models.CharField(max_length=128)
    enable = models.BooleanField(default=False, verbose_name='是否开启流量统计')
    compare_enable = models.BooleanField(default=False, verbose_name='是否开启接口比对')

    class Meta(object):
        db_table = "service_config"
        verbose_name = '服务配置表'


class WorkChatData(models.Model):
    id = models.AutoField(primary_key=True)
    crop_id = models.CharField(max_length=100, verbose_name='企业ID')
    seq = models.IntegerField(default=0, verbose_name='会话自增ID')
    msg_id = models.CharField(max_length=255, verbose_name='消息ID')
    channel = models.CharField(max_length=50, verbose_name='渠道类型，通过msgID后缀转译，内部/外部/引用')
    is_room = models.BooleanField(default=True, verbose_name='是否群聊')
    msg_time = models.CharField(max_length=30, verbose_name='发送时间，发送时间（毫秒数）')
    msg_date = models.DateField(verbose_name='发送日期', auto_now_add=False, auto_now=False)
    belong_mer_code = models.CharField(null=True, blank=True, max_length=50, verbose_name='商户编码')
    mer_type = models.CharField(max_length=10, verbose_name='商户类型，0:H1, 1：H2, 2:非海典ERP')
    room_id = models.CharField(max_length=255, null=True, blank=True, verbose_name='群ID')
    send_from = models.CharField(max_length=255, verbose_name='发送者')
    to_list = models.TextField(verbose_name='接收者')
    msg_type = models.CharField(max_length=50, verbose_name='消息类型:text本文，image图片，mixed混合（图文）')
    msg_content = models.TextField(verbose_name='引用消息内容')
    reference_content = models.TextField(verbose_name='消息内容')
    belong_area = models.CharField(null=True, blank=True, max_length=100, verbose_name='所属大区')
    project_type = models.SmallIntegerField(null=True, blank=True, default=0,
                                            verbose_name='1:海川、2:四季蝉、3:微商城、4:组织云、5:O2O、6:B2C 7:商品云、8:药事云、9:支付云、10:接口平台、11:数仓、12:ERP、99:其他')
    direction = models.SmallIntegerField(default=0, verbose_name='消息投递方向 0：内部 1:外部')
    province = models.CharField(null=True, max_length=50, verbose_name='省份信息')
    is_question = models.BooleanField(default=False, verbose_name='是否问题')
    is_prediction = models.BooleanField(default=False, verbose_name='是否进行了预测问题分析 0：否 1：是')
    have_append = models.BooleanField(default=False, verbose_name='是否有新追加问题 0：否 1：是')
    is_reference = models.BooleanField(default=False, verbose_name='是否是引用消息 0：否 1：是')
    ai_answer_status = models.SmallIntegerField(default=0, verbose_name='AI回答状态 0:未回签 1:回答中 2:已回答')
    is_recall = models.SmallIntegerField(default=0, verbose_name='消息是否被撤回 0：否 1:是')
    is_send = models.BooleanField(default=False, verbose_name='是否已发送 0：否 1：是')
    is_manual_delete = models.BooleanField(default=False, verbose_name='是否手动设置为非问题 0：否 1：是')
    duty_user = models.CharField(null=True, blank=True, max_length=50, verbose_name='值班人员')
    comp_user = models.CharField(null=True, blank=True, max_length=50, verbose_name='处理人，默认值班人员')
    comp_time = models.DateTimeField(null=True, verbose_name='处理完成时间')
    qaid = models.IntegerField(null=True, blank=True, verbose_name='问答ID，外键，问答表ID')
    status = models.SmallIntegerField(default=0, verbose_name='0:待受理，1:已处理，2:解决中')
    question_type = models.SmallIntegerField(default=0,
                                             verbose_name='问题类型，0:咨询类，1:支持类，2:bug类，3:需求类，4:操作类')
    is_review = models.SmallIntegerField(default=0, verbose_name='是否复盘，0:否，1:是')
    is_valuable = models.SmallIntegerField(default=1, verbose_name='是否有价值，0:否，1:是')
    is_knowledge = models.SmallIntegerField(default=0, verbose_name='更新知识库，0:否，1:是, 2:已更新')
    is_valid = models.BooleanField(default=True, verbose_name='是否有效')
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='末次更新时间')

    class Meta(object):
        db_table = "work_chat_data"
        verbose_name = '会话记录表'


class WorkChatDataDetail(models.Model):
    id = models.AutoField(primary_key=True)
    msg_id = models.CharField(max_length=255, verbose_name='消息ID')
    tapd_url = models.CharField(max_length=500, verbose_name='TAPD地址')
    is_valid = models.BooleanField(default=True, verbose_name='是否有效 0否 1是')
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='末次更新时间')

    class Meta(object):
        db_table = "work_chat_data_detail"
        verbose_name = '会话记录表明细信息表'


class WorkChatItem(models.Model):
    id = models.AutoField(primary_key=True)
    msg_id = models.CharField(max_length=255, verbose_name='消息ID')
    media_type = models.CharField(max_length=50, default='0',
                                  verbose_name='媒体类型,0:text 文本 1:image图片，2:voice语音，3:video视频，4:emtion表情')
    media_content = models.TextField(verbose_name='媒体内容')
    is_valid = models.BooleanField(default=True, verbose_name='是否有效')
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='末次更新时间')

    class Meta(object):
        db_table = "work_chat_item"
        verbose_name = '会话内容表'

class WorkChatProduct(models.Model):
    id = models.AutoField(primary_key=True)
    product_id = models.IntegerField(verbose_name='产品线编码')
    product_name = models.CharField(max_length=255, verbose_name='产品线名称')
    prompt = models.CharField(max_length=500, verbose_name='提示词')
    create_time =  models.DateTimeField(verbose_name='创建时间')
    class Meta(object):
        db_table = "work_chat_product"
        verbose_name = '群产品线关系表'


class WorkDutyUser(models.Model):
    id = models.AutoField(primary_key=True)
    room_id = models.CharField(max_length=255, null=True, blank=True, verbose_name='群ID')
    crop_id = models.CharField(max_length=100, verbose_name='企业ID')
    project_type = models.SmallIntegerField(default=0,
                                            verbose_name='1:海川、2:四季蝉、3:微商城、4:组织云、5:O2O、6:B2C 7:商品云、8:药事云、9:支付云、10:接口平台、11:数仓、12:ERP、99:其他')
    duty_user = models.CharField(max_length=255, verbose_name='值班人员姓名')
    duty_user_no = models.CharField(max_length=100, verbose_name='员工编码')
    begin_time = models.DateTimeField(verbose_name='值班起始时间')
    end_time = models.DateTimeField(verbose_name='值班结束时间')
    is_valid = models.BooleanField(default=True, verbose_name='是否有效')
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='末次更新时间')

    class Meta(object):
        db_table = "work_duty_user"
        verbose_name = '群值班人员'


# class WorkDutyUserDict(models.Model):
#     id = models.AutoField(primary_key=True)
#     crop_id = models.CharField(max_length=100, verbose_name='企业ID')
#     project_type = models.SmallIntegerField(default=0,
#                                             verbose_name='1:海川、2:四季蝉、3:微商城、4:组织云、5:O2O、6:B2C 7:商品云、8:药事云、9:支付云、10:接口平台、11:数仓、12:ERP、99:其他')
#     project_name = models.CharField(max_length=100, verbose_name='项目名')
#     duty_user = models.CharField(max_length=255, verbose_name='值班人员姓名')
#     duty_user_no = models.CharField(max_length=100, verbose_name='员工编码')
#     is_valid = models.BooleanField(default=True, verbose_name='是否有效')
#     create_time = models.DateTimeField(verbose_name='创建时间')
#     update_time = models.DateTimeField(auto_now=True, verbose_name='末次更新时间')
#
#     class Meta(object):
#         db_table = "work_duty_user_dict"
#         verbose_name = '群值班默认人员'


class WorkQuestionAnswer(models.Model):
    id = models.AutoField(primary_key=True)
    msg_id = models.CharField(max_length=255, verbose_name='消息ID')
    question_title = models.TextField(verbose_name='问题标题')
    question_content = models.TextField(verbose_name='问题对应的描述')
    question_answer = models.TextField(verbose_name='问题对应的回答，存放AI的回答')
    question_answer_manual = models.TextField(verbose_name='手动回复内容')
    is_auto = models.SmallIntegerField(default=False, verbose_name='是否自动生成 0否，1是')
    answer_source = models.SmallIntegerField(default=0, verbose_name='回答来源 0:百晓生  1：其它Ai   2:人工')
    model_code = models.CharField(max_length=50, verbose_name='是否使用模型生成答案，如果有使用列出模型编码')
    is_valid = models.BooleanField(default=True, verbose_name='是否有效')
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='末次更新时间')

    class Meta(object):
        db_table = "work_question_answer"
        verbose_name = '问答表'


class WorkQuestionSummary(models.Model):
    id = models.AutoField(primary_key=True)
    project_type = models.SmallIntegerField(default=0, verbose_name='1海川，2商城，3四季蝉，4OMS，5其他')
    question_title = models.TextField(verbose_name='问题摘要')
    question_summary = models.TextField(verbose_name='问题总结')
    is_valid = models.BooleanField(default=True, verbose_name='是否有效')
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='末次更新时间')

    class Meta(object):
        db_table = "work_question_summary"
        verbose_name = '问答摘要表'


class WorkChatConfig(models.Model):
    id = models.AutoField(primary_key=True)
    room_id = models.CharField(max_length=255, null=True, blank=True, verbose_name='群ID')
    wx_room_id = models.CharField(max_length=30, verbose_name='微信群ID')
    room_name = models.CharField(max_length=200, verbose_name='群名')
    reply_toggle = models.BooleanField(default=False, verbose_name='是否开启自动回复')
    room_notice = models.TextField(verbose_name='群公告')
    is_display = models.BooleanField(default=True, verbose_name='群是否可见  0:不可见 1:可见')
    all_business_toggle =  models.BooleanField(default=False, verbose_name='是否开启全业务处理 0:否 1:是')
    wx_manager_no = models.CharField(max_length=300, verbose_name='群管理员的工号，用户ID')
    push_message = models.BooleanField(default=False, verbose_name='是否只推送消息 0：否 1:是')
    prediction_message_toggle = models.BooleanField(default=True, verbose_name='是否开启群消息识别 0:不开启  1:开启')
    web_hook = models.TextField(verbose_name='群机器人')
    room_channel = models.SmallIntegerField(default=0, verbose_name='群类型  0:内部 1:外部')
    is_valid = models.BooleanField(default=True, verbose_name='是否有效')
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='末次更新时间')

    class Meta(object):
        db_table = "work_chat_config"
        verbose_name = '问题处理配置表'


class WorkEmpInfo(models.Model):
    id = models.AutoField(primary_key=True)
    crop_id = models.CharField(max_length=100, verbose_name='企业ID')
    emp_name = models.CharField(max_length=50, verbose_name='员工姓名')
    emp_no = models.CharField(max_length=50, verbose_name='员工工号编码')
    emp_account = models.CharField(max_length=50, verbose_name='员工账号编码')
    im_contact_id = models.CharField(max_length=30, verbose_name='句子联系人ID')
    emp_position = models.CharField(max_length=255, verbose_name='员工职位信息')
    dept_info = models.CharField(max_length=1000, verbose_name='员工所属部门信息')
    sex = models.CharField(max_length=10, verbose_name='员工性别')
    phone = models.CharField(max_length=20, verbose_name='手机号')
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='末次更新时间')

    class Meta(object):
        db_table = "work_emp_info"
        verbose_name = '员工信息表'


class WorkAnswerLog(models.Model):
    id = models.AutoField(primary_key=True)
    msg_id = models.CharField(max_length=100, verbose_name='消息ID')
    attachments = models.TextField(verbose_name='附件')
    log_type = models.SmallIntegerField(verbose_name='0:编辑答案内容 1：编辑问题内容 2：转交问题 3：创建问题 4：状态变更记录 5：更新知识库')
    log_content = models.TextField(verbose_name='日志内容')
    push_flag = models.BooleanField(default=False, verbose_name='是否已发送 0：否 1：是')
    user_name = models.CharField(max_length=50, verbose_name='姓名')
    user_account = models.CharField(max_length=50, verbose_name='工号')
    is_valid = models.BooleanField(default=True, verbose_name='是否有效')
    create_time = models.DateTimeField(auto_now=True, verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='末次更新时间')

    class Meta(object):
        db_table = "work_answer_log"
        verbose_name = '日志记录表'


class WorkMerInfo(models.Model):
    id = models.AutoField(primary_key=True)
    mer_code = models.CharField(max_length=100, verbose_name='商户编码')
    mer_name = models.CharField(max_length=100, verbose_name='商户名称')
    mer_type = models.SmallIntegerField(default=1, verbose_name='0:H1, 1：H2, 3:非海典ERP')
    is_valid = models.BooleanField(default=True, verbose_name='是否有效')
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='末次更新时间')

    class Meta(object):
        db_table = "work_mer_info"
        verbose_name = '商户类型表'


class WorkAnswerAttachment(models.Model):
    id = models.AutoField(primary_key=True)
    msg_id = models.CharField(max_length=100, verbose_name='消息ID')
    attach_key = models.CharField(max_length=255, verbose_name='附件名称，即文件名称或图片名称等')
    attach_file = models.TextField(verbose_name='附件内容，图片为OSS-url')
    attach_type = models.SmallIntegerField(default=0, verbose_name='0:图片，1：文件')
    is_valid = models.BooleanField(default=True, verbose_name='是否有效')
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_time = models.DateTimeField(auto_now=True, verbose_name='末次更新时间')

    class Meta(object):
        db_table = "work_answer_attachment"
        verbose_name = '问答附件表'


class WorkQuestionRelation(models.Model):
    id = models.AutoField(primary_key=True)
    question_id = models.IntegerField(verbose_name='问答表  work_question_answer  ID')
    parent_msg_id = models.CharField(max_length=255, verbose_name='主消息 msg ID')
    sub_msg_id = models.CharField(max_length=255, verbose_name='子消息 msg ID')
    root_msg_id = models.CharField(max_length=255, verbose_name='根节点消息 ID')
    type = models.SmallIntegerField(verbose_name='1:合并，2：追加')
    mark = models.TextField(verbose_name='备注')
    create_time = models.DateTimeField(verbose_name='创建时间')

    class Meta(object):
        db_table = "work_question_relation"
        verbose_name = '消息合并/追加内容表关系表'

class WorkVectorKnowledge(models.Model):
    id = models.AutoField(primary_key=True)
    msg_id = models.CharField(max_length=100, verbose_name='消息ID')
    question_title = models.TextField(verbose_name='问题标题')
    question_answer = models.TextField(verbose_name='问题对应的回答，存放AI的回答')
    is_valid = models.BooleanField(default=True, verbose_name='是否有效，0否 1是')
    create_user = models.CharField(max_length=50, verbose_name='创建人')
    update_user = models.CharField(max_length=50, verbose_name='更新人')
    create_time =  models.DateTimeField(verbose_name='创建时间')
    update_time =  models.DateTimeField(verbose_name='末次更新时间')
    class Meta(object):
        db_table = "work_vector_knowledge"
        verbose_name = '知识库'
