import re

from django.db.models import Max
from rest_framework import serializers
from scaffold.restframework.utils import auto_declare_serializers

from . import models as m


class PublishingTaskRunningRecodeSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.PublishingTaskRunningRecode
        fields = '__all__'

class WorkChatDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.WorkChatData
        fields = '__all__'

# # Auto declare serializer classes from models.
auto_declare_serializers(m, locals())

