import json
import re
import traceback
from datetime import datetime, timed<PERSON><PERSON>
import os

from django.core.paginator import Paginator
from rest_framework import viewsets
from rest_framework.decorators import action
from django.db import transaction

import data_config.tasks as t
from core.commonUtils.commonUtil import CommonUtil
from core.commonUtils.getModelInfoUtil import GetModelInfo
from core.servers.calculateMoney import CalculateLog
from core.utils import UserUtils, ResponseUtils
from interface_test.models import *
from tools_help.servers import skywalking
from tools_help.servers import swagger
from . import serializers as s
from .exceptions import AppErrors
from .models import *
from django.db.models import Q
from django.forms.models import model_to_dict
from functools import reduce
from operator import and_

import requests
from django.conf import settings

from collections import defaultdict
from django.utils import timezone

from django.http import JsonResponse

from django.http import HttpResponse
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from io import BytesIO
from django.core.cache import cache
from tools_help.models import WorkVectorKnowledge

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()

question_msg_url = settings.QUESTION_MSG_URL
question_check_url = settings.QUESTION_CHECK_URL
rota_url = settings.ROTA_URL
notice_url = settings.NOTICE_URL
upload_url = settings.UPLOAD_URL

from concurrent.futures import ThreadPoolExecutor
# 全局线程池
async_executor = ThreadPoolExecutor(max_workers=5)

class ToolsHelpViewSet(viewsets.ModelViewSet):
    queryset = PublishingTaskRunningRecode.objects.all()
    serializer_class = s.PublishingTaskRunningRecodeSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def release_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
            batch_number = request.GET['search_batch_number']
            package = request.GET['search_package']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询发版记录信息，查询条件：批次号=%s，包名=%s" \
                  % (username, batch_number, package)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        if batch_number and package:
            release_list = PublishingTaskRunningRecode.objects.filter(batch_number=batch_number).filter(
                package__icontains=package).order_by('-begin_time')
        elif batch_number:
            release_list = PublishingTaskRunningRecode.objects.filter(batch_number=batch_number).order_by('-begin_time')
        elif package:
            release_list = PublishingTaskRunningRecode.objects.filter(package__icontains=package).order_by(
                '-begin_time')
        else:
            release_list = PublishingTaskRunningRecode.objects.all().order_by('-begin_time')
        paginator = Paginator(release_list, limit)
        data = paginator.page(page)
        data_list = []
        if data is not None and len(data.object_list) != 0:
            data_list = list(data.object_list.values())
        response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count,
                         'search_batch_number': batch_number,
                         'search_package': package, 'releases': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['GET'], detail=False)
    def new_release_list(self, request):
        # 从请求头中取操作人的用户ID
        # username = UserUtils.get_login_user(request).username
        userinfo = UserUtils.get_login_user(request)
        if userinfo:
            username = userinfo.username
        else:
            username = request.GET.get('username')
        try:
            page = request.GET.get('pagenum', "1")
            limit = request.GET.get('pagesize', "100")
            hops_id = request.GET.get('hops_id', "")
            code_name = request.GET.get('code_name', "")
            task_name = request.GET.get('task_name', "")
            username = request.GET.get('username', "")
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询发版记录信息，查询条件：hops单号=%s，项目名=%s,任务名称=%s，操作人=%s" \
                  % (username, hops_id, code_name, task_name, username)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        release_list = GetModelInfo().getReleaseList(hops_id, code_name, task_name, username)
        paginator = Paginator(release_list, limit)
        data = paginator.page(page)
        response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count, 'release_list': data.object_list}
        return ResponseUtils.return_success("查询成功", response_json)

    # 金额计算工具
    @action(methods=['POST'], detail=False)
    def money_calculate(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        if request.method == 'POST':
            # 提取参数
            data_json = json.loads(request.body)
            try:
                data_base_id = data_json['data_base_id']
                mer_code = data_json['mer_code']
                data = data_json['data']
            except Exception as e:
                error_info = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{error_info}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        shop_list = []
        # 这重循环先通过商品ID查询获取活动规则信息，存入列表
        for shopInfo in data:
            activies = GetModelInfo().getActivityParam(data_base_id, mer_code, shopInfo['shop_id'])
            shop_dict = {'shop_num': shopInfo['shop_num'], 'shop_price': shopInfo['shop_price'],
                         'shop_id': shopInfo['shop_id'], 'activies': activies}
            shop_list.append(shop_dict)
        calculatelog = CalculateLog()
        log = calculatelog.initLog2(shop_list)
        content = "用户 %s 使用金额计算工具，得到结果: %s" % (username, log)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("计算成功", log)

    # 日志查询功能
    @action(methods=['GET'], detail=False)
    def log_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            page = request.GET['pagenum']
            limit = request.GET['pagesize']
            search_log_type = request.GET['search_log_type']
            search_person = request.GET['search_person']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        content = "用户 %s 查询日志信息，查询条件：日志类型=%s，操作人=%s" \
                  % (username, search_log_type, search_person)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        if search_log_type and search_person:
            if search_log_type == "all":
                log_list = LogRecord.objects.filter(person__icontains=search_person).order_by('-temp_time')
            else:
                log_list = LogRecord.objects.filter(log_type=search_log_type).filter(
                    person__icontains=search_person).order_by('-temp_time')
        elif search_log_type:
            if search_log_type == "all":
                log_list = LogRecord.objects.all().order_by('-temp_time')
            else:
                log_list = LogRecord.objects.filter(log_type=search_log_type).order_by('-temp_time')
        elif search_person:
            log_list = LogRecord.objects.filter(person__icontains=search_person).order_by('-temp_time')
        else:
            log_list = LogRecord.objects.all().order_by('-temp_time')
        paginator = Paginator(log_list, limit)
        data = paginator.page(page)
        data_list = list(data.object_list.values())
        response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count,
                         'search_log_type': search_log_type,
                         'search_person': search_person, 'logs': data_list}
        return ResponseUtils.return_success("查询成功", response_json)

    @action(methods=['POST'], detail=False)
    def unifiedPay(self, request):
        print("==========================进入了预支付接口========================")
        temp = json.loads(request.body)
        try:
            platformBusinessNo = temp['platformBusinessNo']
            businessPayOrderCode = temp['businessPayOrderCode']
            businessOrderCode = temp['businessOrderCode']
            businessReturnUrl = temp['businessReturnUrl']
            payAmount = temp['orderAmount']
            payCode = temp['payCode']
            canCombineOrderPay = temp['canCombineOrderPay']
            encryptStr = temp['encryptStr']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        # thirdTradeNo = '4200001530202205179441197516'
        platformPayOrderCode = CommonUtil().get_appoint_timestamp(16)
        customerServiceAmount = 0.01
        data = {'resultCodeDesc': '支付成功', 'resultCode': 'SUCCESS', 'platformBusinessNo': platformBusinessNo,
                'businessPayOrderCode': businessPayOrderCode, 'businessOrderCode': businessOrderCode,
                'businessReturnUrl': businessReturnUrl,
                'payAmount': payAmount, 'platformPayOrderCode': platformPayOrderCode, 'thirdUserAccount': payCode,
                'payChannelCode': 'WEIXIN',
                'canCombineOrderPay': canCombineOrderPay, 'customerServiceAmount': customerServiceAmount,
                'encryptStr': encryptStr,
                'payCode': payCode, 'payStatus': 2, 'status': 2}
        t.mock_pay.delay(businessReturnUrl, data)
        response = {'success': True, 'bizSuccess': True, 'msg': '操作成功', 'code': '00000', 'traceId': None,
                    'currentTimeMillis': CommonUtil().get_appoint_timestamp(13), 'exception': None,
                    'data': {'resultCodeDesc': '下单数据已被缓存', 'resultCode': 'DATA_CACHE', 'status': 7}}
        return JsonResponse(data=response)

    @action(methods=['POST'], detail=False)
    def refund(self, request):
        print("==========================进入了预退款接口========================")
        temp = json.loads(request.body)
        print(temp)
        try:
            businessOrderCode = temp['businessOrderCode']
            totalAmount = temp['totalAmount']
            businessRefundOrderCode = temp['businessRefundOrderCode']
            businessReturnUrl = temp['businessReturnUrl']
            platformBusinessNo = temp['platformBusinessNo']
            encryptStr = temp['encryptStr']
            businessPayOrderCode = temp['businessPayOrderCode']
            refundAmount = temp['refundAmount']
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        thirdTradeNo = '4200001433202205246974000375'
        data = {'payerTotal': totalAmount, 'businessRefundOrderCode': businessRefundOrderCode,
                'resultCodeDesc': '退款成功',
                'resultCode': 'SUCCESS', 'payerRefund': refundAmount, 'platformBusinessNo': platformBusinessNo,
                'refundStatus': 5, 'businessOrderCode': businessOrderCode,
                'platformRefundOrderCode': '978708705077886976',
                'total': 10, 'businessReturnUrl': businessReturnUrl, 'userAccount': '长沙银行信用卡1234',
                'customerServiceAmount': 0,
                'payStatus': 5, 'refund': refundAmount, 'status': 5}
        t.mock_refund.delay(businessReturnUrl, data)
        response = {'success': True, 'bizSuccess': True, 'msg': '退款下单成功', 'code': '00000', 'traceId': None,
                    'currentTimeMillis': CommonUtil().get_appoint_timestamp(13), 'exception': None,
                    'data': {'platformOrderCode': '978700475664695296', 'businessOrderCode': businessOrderCode,
                             'businessRefundOrderCode': businessRefundOrderCode,
                             'platformRefundOrderCode': '978708705077886976',
                             'businessReturnUrl': businessReturnUrl, 'resultCodeName': '退款下单成功',
                             'resultCodeDesc': '退款下单成功',
                             'resultCode': 'REFUND_AN_ORDER_SUCCESS', 'refundStatus': 30, 'payStatus': 4, 'status': 30}}
        return JsonResponse(data=response)

    @action(methods=['GET'], detail=False)
    def service_config(self, request):
        """ 将SkyWalking中的服务信息落表 """
        # 获取所有服务列表
        services = skywalking.search_all_service(False)
        pattern = r'(?P<product>.*)_(?P<service>.*).jar'
        # 将查询的服务信息新增到服务配置表中
        for service in services:
            match = re.search(pattern, service.name)
            skywalking_service = match.group('service') if match else service.name
            service_configs = ServiceConfig.objects.filter(skywalking_service=skywalking_service)
            if len(service_configs) == 0:
                domain_suffix = 'hcloud.hydee.cn' if 'h3' in match.group('product') else 'ydjia.cn'
                swagger_domain = swagger.DOMAIN_TEMPLATE % (skywalking_service, domain_suffix)
                ServiceConfig(skywalking_service=skywalking_service, swagger_domain=swagger_domain).save()
        return JsonResponse(data={})

    @action(methods=['GET'], detail=False)
    def visit_interface(self, request):
        """ 将经过business-gateway的所有接口数据落表 """

        # 获取当前时间
        now = datetime.now()
        begin = (now - timedelta(hours=2)).strftime("%Y-%m-%d %H")
        end = (now - timedelta(hours=1)).strftime("%Y-%m-%d %H")

        logger.info('获取skywalking中经过businesses-gateway的接口集合，[%s]~[%s]', begin, end)

        # 1、获取经过business-gateway的所有请求接口 TODO 生产的service与测试的不一致
        pro_interfaces = skywalking.search_service_all_interface('pro', True, skywalking.PRO_BUSINESSES_GATEWAY, begin, end)
        grey_interfaces = skywalking.search_service_all_interface('grey', True, skywalking.GREY_BUSINESSES_GATEWAY, begin, end)
        dev_interfaces = skywalking.search_service_all_interface('dev', False, skywalking.DEV_BUSINESSES_GATEWAY, begin, end)
        test_interfaces = skywalking.search_service_all_interface('test', False, skywalking.TEST_BUSINESSES_GATEWAY, begin, end)
        no_pro_interfaces = test_interfaces + dev_interfaces
        pro_interfaces += grey_interfaces
        logger.info('生产环境获取数据[%s]条，非生产获取数据[%s]条', len(pro_interfaces), len(no_pro_interfaces))

        if len(pro_interfaces) + len(no_pro_interfaces) == 0:
            return JsonResponse(data={})

        # 2、过滤重复的请求URL
        for interface in no_pro_interfaces:
            pro_interfaces.append(interface)
        interface_dict = skywalking.deduplication_interface(pro_interfaces)

        # 3、保存新增的访问接口
        # 去掉已存在的接口
        for service, interfaces in interface_dict.items():
            interface_visits = InterfaceVisit.objects.filter(service=service)
            for interface_visit in interface_visits:
                interfaces.discard(interface_visit.interface_address)
            # 新增不存在的接口
        for service, interfaces in interface_dict.items():
            if len(interfaces) > 0:
                interface_visits = list()
                for interface in interfaces:
                    interface_visits.append(
                        InterfaceVisit(service=service, source='businesses-gateway',
                                       interface_address=interface, create_time=now))
                InterfaceVisit.objects.bulk_create(interface_visits)

        return JsonResponse(data={})

    @action(methods=['GET'], detail=False)
    def visit_interface_statistics(self, request):
        """ 统计SkyWalking所有服务的访问频次 """

        # 获取当前时间
        now = datetime.now()
        begin = (now - timedelta(hours=2)).strftime("%Y-%m-%d %H")
        end = (now - timedelta(hours=1)).strftime("%Y-%m-%d %H")
        logger.info('统计SkyWalking所有服务的访问频次，[%s]~[%s]', begin, end)

        # 获取所有服务列表
        services = skywalking.search_all_service(True)
        for service in services:
            # 跳过网关服务
            service_name = service.name
            if "gateway" in service_name:
                continue

            try:
                skywalking.save_interface_statistics(service_name, begin, end)
            except Exception as e:
                logger.error('生产环境获取服务[%s]数据失败！异常[%s]', service_name, e)

        return JsonResponse(data={})

    @action(methods=['GET'], detail=False)
    def swagger_interface(self, request):
        # 1、查询所有swagger服务
        service_configs = ServiceConfig.objects.filter(enable=True)
        # 2、轮训获取各个服务的swagger接口
        for service_config in service_configs:
            try:
                swagger.save_swagger_interface_by_service(service_config)
            except Exception as e:
                logger.error('获取并解析服务的swagger数据失败！服务配置[%s]，异常[%s]', service_config, e)
        return JsonResponse(data={})

class QuestionViewSet(viewsets.ModelViewSet):
    queryset = WorkChatData.objects.all()
    serializer_class = s.WorkChatDataSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    # 1:海川、2:四季蝉、3:微商城、4:组织云、5:O2O、6:B2C、 7:商品云、8:药事云、9:支付云、10:接口平台、11:数仓、12:ERP、99:其他
    # PROJECT_TYPE_MAP = {
    #     1: "海川",
    #     2: "四季蝉",
    #     3: "微商城",
    #     4: "组织云",
    #     5: "O2O",
    #     6: "B2C",
    #     7: "商品云",
    #     8: "药事云",
    #     9: "支付云",
    #     10: "接口平台",
    #     11: "数仓",
    #     12: "ERP",
    #     13: "医保云",
    #     14: "随心看",
    #     15: "药店通",
    #     16: "H1",
    #     17: "H2",
    #     99: "其他"
    # }

    # 定义项目类型归类映射字典
    PROJECT_TYPE_CLASSIFY_MAP = {
        1: "海川",
        2: "四季蝉",
        3: "微商城",
        4: "组织",
        5: "OMS",
        6: "OMS",
        7: "商品",
        11: "数仓",
        14: "随心看"
    }

    # 定义问题类型映射字典
    QUESTION_TYPE_MAP = {
        0: "咨询类",
        1: "支持类",
        2: "BUG",
        3: "需求类",
        4: "操作类"
    }

    # 定义问题类型映射字典
    QUESTION_STATUS_MAP = {
        0: "待受理",
        1: "已处理",
        2: "解决中",
        3: "已关闭",
        4: "无需处理"
    }

    # 定义ERP类型映射字典
    ERP_TYPE_MAP = {
        0: "H1",
        1: "H2",
        2: "非海典ERP"
    }

    # 定义要显示的字段及其标签
    FIELDS_TO_DISPLAY = {
        'comp_user': "处理人",
        'project_type': "业务归属",
        'status': "处理状态",
        'question_type': "问题类型",
        'belong_mer_code': "所属商户",
        "question_answer_manual": "回复内容",
        "manual_setting": "手动设置",
        "comp_time":"处理时间",
        "mer_type":"ERP类型",
        "file_url": "上传图片",
        "notes": "备注",
        "bug_url": "BUG链接",
        "is_knowledge": "更新知识库"
    }

    # # 字段名到映射字典的关联
    # FIELD_MAP = {
    #     'question_type': QUESTION_TYPE_MAP,
    #     'status': QUESTION_STATUS_MAP,
    #     'project_type': PROJECT_TYPE_MAP,
    #     'mer_type':ERP_TYPE_MAP
    # }
    # 动态获取项目类型映射（替代原硬编码的字典）
    @classmethod
    def get_project_type_map(cls):
        cache_key = 'workchat_product_map'
        result = cache.get(cache_key)

        if not result:
            products = WorkChatProduct.objects.all().values_list('product_id', 'product_name')
            result = dict(products)
            cache.set(cache_key, result, timeout=3600)  # 缓存1小时

        return result

    @property
    def FIELD_MAP(self):
        return {
            'question_type': self.QUESTION_TYPE_MAP,
            'status': self.QUESTION_STATUS_MAP,
            'project_type': self.get_project_type_map(),  # 动态获取
            'mer_type': self.ERP_TYPE_MAP
        }

    def batch_create_work_answer_log(self, logs):
        """批量创建日志"""
        log_objects = []
        for log in logs:
            if log['log_type'] == 4:
                # 处理状态日志的特殊逻辑
                answerlog = WorkAnswerLog.objects.filter(
                    msg_id=log['msg_id'],
                    log_type=4,
                    is_valid=True
                ).first()
                if answerlog:
                    answerlog.log_content = f"{answerlog.log_content}|{log['status']}"
                    answerlog.save()
                    continue

            # 创建新日志对象
            log_content = []
            if log['log_type'] != 4:
                for key, value in log.items():
                    if key not in ['msg_id', 'log_type', 'user_name', 'user_account', 'push_flag',
                                   'attachments'] and value is not None:
                        name = self.FIELDS_TO_DISPLAY.get(key, key)
                        if key in self.FIELD_MAP:
                            value = self.FIELD_MAP[key].get(value, value)
                        log_content.append(f"{name}: {value}")

            log_objects.append(WorkAnswerLog(
                msg_id=log['msg_id'],
                log_type=log['log_type'],
                log_content=",".join(log_content) if log.get('push_flag', False) is False else "问题推送",
                user_name=log['user_name'],
                user_account=log['user_account'],
                push_flag=log.get('push_flag', False),
                attachments=log.get('attachments')
            ))

        if log_objects:
            WorkAnswerLog.objects.bulk_create(log_objects)

    def execute_async_tasks(self, tasks):
        """执行异步任务"""
        for task in tasks:
            try:
                async_executor.submit(task)
            except Exception as e:
                logger.error(f"异步任务执行失败: {e}")

    # 附件处理
    def get_attachments(self, msg_id):
        # 附件
        attachments_query = WorkAnswerAttachment.objects.filter(
            msg_id=msg_id,
            is_valid=True
        )
        # 处理查询结果
        if not attachments_query.exists():
            return []  # 无数据时返回空列表

        attachments_data = []
        for att in attachments_query:
            try:
                data = json.loads(att.attach_file)
                attachments_data.extend(data)
            except json.JSONDecodeError:
                continue

        return attachments_data

    # 消息内容处理
    def generate_msg_content(self, msg_type, msg_id, content=None, empname=None):
        """
        根据消息类型生成 msg_content。
        增加对 empname='海典客服' 的特殊处理：截掉 '--------------' 前面的引用内容
        """

        def clean_hd_content(text):
            """海典客服专用：清理引用内容"""
            if not text:
                return text
            # 查找分隔线位置
            divider = "\r\n——————————————————————————————\r\n"
            if divider in text:
                return text.split(divider)[-1].strip()
            return text

        # 获取问题内容
        chatitems_by_msgids = defaultdict(list)
        for item in WorkChatItem.objects.filter(is_valid=True, msg_id=msg_id):
            chatitems_by_msgids[item.msg_id].append(item)

        if msg_type == 'mixed':
            try:
                result = []
                for chatitem in chatitems_by_msgids[msg_id]:
                    media_content = chatitem.media_content

                    # 特殊处理海典客服的文本消息
                    if empname == '海典客服' and chatitem.media_type == 'text':
                        media_content = clean_hd_content(media_content)

                    # 添加姓名前缀
                    if empname and chatitem.media_type == 'text':
                        media_content = f"{empname}： {media_content}"

                    result.append({
                        "media_type": chatitem.media_type,
                        "media_content": media_content
                    })
                return result

            except KeyError:
                return []
            except Exception as e:
                logger.error(f'参数异常：{e}')
                return []
        else:
            # 处理非mixed类型
            media_content = content
            if empname == '海典客服' and msg_type == 'text':
                media_content = clean_hd_content(media_content)

            if empname and msg_type == 'text':
                media_content = f"{empname}： {media_content}"

            return [{
                "media_type": msg_type,
                "media_content": media_content
            }]

    # 子消息处理，通过主消息ID递归查询
    # def get_children_msg(self, msg_id):
    #     """递归获取所有子消息，保留关系类型"""
    #
    #     def get_all_children_relations(parent_id):
    #         relations = WorkQuestionRelation.objects.filter(parent_msg_id=parent_id)
    #         if not relations:
    #             return []
    #
    #         all_relations = list(relations)
    #         for rel in relations:
    #             all_relations.extend(get_all_children_relations(rel.sub_msg_id))
    #
    #         return all_relations
    #
    #     # 获取所有关系记录
    #     all_relations = get_all_children_relations(msg_id)
    #     if not all_relations:
    #         return []
    #
    #     # 获取所有子消息的msg_id
    #     all_children_ids = [rel.sub_msg_id for rel in all_relations]
    #
    #     # 查询消息详情
    #     children_data = WorkChatData.objects.filter(
    #         msg_id__in=all_children_ids
    #     ).values('msg_id', 'msg_type', 'msg_content', 'reference_content', 'msg_time')
    #
    #     children_map = {item['msg_id']: item for item in children_data}
    #
    #     # 构建返回数据
    #     children_list = []
    #     for rel in all_relations:
    #         if rel.sub_msg_id in children_map:
    #             child = children_map[rel.sub_msg_id]
    #             msg_time = datetime.fromtimestamp(int(child['msg_time']) / 1000).replace(microsecond=0).strftime(
    #                 '%Y-%m-%d %H:%M:%S')
    #             children_list.append({
    #                 'sub_msg_id': child['msg_id'],
    #                 'type': rel.type,  # 保留关系类型
    #                 'msg_type': child['msg_type'],
    #                 'msg_content': self.generate_msg_content(
    #                     child['msg_type'],
    #                     child['msg_id'],
    #                     child['reference_content'] if child['reference_content']  else child['msg_content']
    #                 ),
    #                 'create_time': msg_time
    #             })
    #
    #     return children_list

    # 子消息处理，通过根节点ID查询
    def get_children_msg(self, msg_id):
        """获取子消息"""
        relations = WorkQuestionRelation.objects.filter(root_msg_id=msg_id)
        if not relations:
            return []

        sub_msg_ids = [rel.sub_msg_id for rel in relations]
        children_data = WorkChatData.objects.filter(
            msg_id__in=sub_msg_ids
        ).values('msg_id', 'msg_type', 'msg_content', 'send_from', 'reference_content', 'msg_time')

        children_map = {item['msg_id']: item for item in children_data}
        # 获取所有涉及的员工账号（避免N+1查询）
        emp_accounts = children_data.values_list('send_from', flat=True).distinct()
        emp_data = WorkEmpInfo.objects.filter(emp_account__in=emp_accounts).values('emp_account', 'emp_name')
        emp_map = {item['emp_account']: item for item in emp_data}

        children_list = []
        for rel in relations:
            if rel.sub_msg_id in children_map:
                child = children_map[rel.sub_msg_id]
                msg_time = datetime.fromtimestamp(int(child['msg_time']) / 1000).replace(microsecond=0).strftime('%Y-%m-%d %H:%M:%S')
                children_list.append({
                    'sub_msg_id': child['msg_id'],
                    'type': rel.type,
                    'msg_type': child['msg_type'],
                    'msg_content': self.generate_msg_content(
                        child['msg_type'],
                        child['msg_id'],
                        child['reference_content'] if child['reference_content'] else child['msg_content'],
                        emp_map[child['send_from']]['emp_name'] if child['send_from'] in emp_map else child['send_from']
                    ),
                    'create_time': msg_time
                })

        return children_list

    # 获取问题附属信息
    def get_data_detail_info(self, msg_id):
        obj = WorkChatDataDetail.objects.filter(msg_id=msg_id).first()
        if not obj:
            return {
                'bug_url': ""
            }
        else:
            return {
                'bug_url': obj.tapd_url
            }

    def get_attachments_batch(self, msg_ids):
        """批量获取附件信息"""
        attachments_query = WorkAnswerAttachment.objects.filter(
            msg_id__in=msg_ids,
            is_valid=True
        )

        attachments_map = defaultdict(list)
        for att in attachments_query:
            try:
                data = json.loads(att.attach_file)
                attachments_map[att.msg_id].extend(data)
            except json.JSONDecodeError:
                continue

        return attachments_map
    def question_log_list_batch(self, msg_ids):
        """批量获取操作日志"""
        logs_query = WorkAnswerLog.objects.filter(
            msg_id__in=msg_ids
        ).exclude(log_type=4)

        logs_map = defaultdict(list)
        for log in logs_query:
            log_entry = {
                'timestamp': log.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                'attachment': []
            }

            user = f"{log.user_name}({log.user_account or ''})"

            if log.attachments:
                try:
                    log_entry['attachment'] = json.loads(log.attachments)
                except json.JSONDecodeError:
                    pass

            if log.log_type == 0:
                content = f"{user} 【推送】问题回复" if log.push_flag else f"{user} 【编辑】问题回复：{log.log_content}"
            elif log.log_type == 1:
                content = f"{user} 【编辑】问题信息：{log.log_content}"
            elif log.log_type == 2:
                content = f"{user} 【转交】问题：{log.log_content}"
            elif log.log_type == 5:
                content = f"{user} 【设置】{log.log_content}"
            else:
                content = f"问题创建：{log.log_content}"

            log_entry['content'] = content
            logs_map[log.msg_id].append(log_entry)

        # 对每个msg_id的日志按时间排序
        for msg_id in logs_map:
            logs_map[msg_id].sort(key=lambda x: x['timestamp'], reverse=True)

        return logs_map

    def get_children_msg_batch(self, msg_ids):
        """批量获取子消息"""
        relations = WorkQuestionRelation.objects.filter(root_msg_id__in=msg_ids)
        if not relations:
            return defaultdict(list)

        # 按root_msg_id分组
        relations_map = defaultdict(list)
        for rel in relations:
            relations_map[rel.root_msg_id].append(rel.sub_msg_id)

        # 获取所有子消息
        sub_msg_ids = [rel.sub_msg_id for rel in relations]
        children_data = WorkChatData.objects.filter(
            msg_id__in=sub_msg_ids
        ).values('msg_id', 'msg_type', 'msg_content', 'send_from', 'reference_content', 'msg_time')

        children_map = {item['msg_id']: item for item in children_data}

        # 获取所有涉及的员工账号
        emp_accounts = children_data.values_list('send_from', flat=True).distinct()
        emp_data = WorkEmpInfo.objects.filter(emp_account__in=emp_accounts).values('emp_account', 'emp_name')
        emp_map = {item['emp_account']: item['emp_name'] for item in emp_data}

        # 构建结果
        result = defaultdict(list)
        for root_msg_id, sub_ids in relations_map.items():
            for sub_id in sub_ids:
                if sub_id in children_map:
                    child = children_map[sub_id]
                    msg_time = datetime.fromtimestamp(int(child['msg_time']) / 1000).replace(microsecond=0).strftime(
                        '%Y-%m-%d %H:%M:%S')
                    result[root_msg_id].append({
                        'sub_msg_id': child['msg_id'],
                        'msg_type': child['msg_type'],
                        'msg_content': self.generate_msg_content(
                            child['msg_type'],
                            child['msg_id'],
                            child['reference_content'] if child['reference_content'] else child['msg_content'],
                            emp_map.get(child['send_from'], child['send_from'])
                        ),
                        'create_time': msg_time
                    })

        return result

    def get_data_detail_info_batch(self, msg_ids):
        """批量获取详情信息"""
        details = WorkChatDataDetail.objects.filter(msg_id__in=msg_ids)
        detail_map = {d.msg_id: {'bug_url': d.tapd_url} for d in details}

        # 为没有详情的消息设置默认值
        return defaultdict(lambda: {'bug_url': ''}, detail_map)

    def get_knowledge_info_batch(self, msg_ids):
        """批量获取知识库更新信息"""
        knowledges = WorkVectorKnowledge.objects.filter(msg_id__in=msg_ids)
        knowledge_map = {k.msg_id: {'question_title': k.question_title, 'question_answer': k.question_answer} for k in knowledges}

        # 为没有详情的消息设置默认值
        return defaultdict(lambda: {'question_title': '', 'question_answer': ''}, knowledge_map)

    # 获取知识库更新信息
    def get_knowledge_info(self, msg_id):
        obj = WorkVectorKnowledge.objects.filter(msg_id=msg_id).first()
        if not obj:
            return {}
        else:
            return {
             'question_title': obj.question_title, 'question_answer': obj.question_answer
            }

    @action(methods=['POST'], detail=False)
    def question_list(self, request):
        """
        查询会话记录列表
        参数说明：
            start_date: 开始时间 (非必填)
            end_date: 结束时间 (非必填)
            send_from: 提交人 (非必填)
            belong_area: 所属大区 (非必填)
            comp_user: 处理人 (非必填)
            project_type: 业务归属 (非必填)
            status: 处理状态 (非必填)
            question_type: 问题类型 (非必填)
            belong_mer_code: 所属商户 (非必填)
            is_auto: 是否AI回答 (非必填)
            pagenum: 页码 (必填)
            pagesize: 条数 (必填)
        :return:
        """
        try:
            temp = json.loads(request.body)

            # 设置默认值并验证必填参数
            page = int(temp.get('pagenum', 1))
            limit = int(temp.get('pagesize', 10))
            if page <= 0 or limit <= 0:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, "页码和条数必须为正整数")

            # 初始化查询条件
            query_params = {
                'start_date': temp.get('start_date', ''),
                'end_date': temp.get('end_date', ''),
                'send_from': temp.get('send_from', ''),
                'belong_area': temp.get('belong_area', ''),
                'comp_user': temp.get('comp_user', ''),
                'project_type': temp.get('project_type', []),
                'status': temp.get('status', []),
                'question_type': temp.get('question_type', []),
                'belong_mer_code': temp.get('belong_mer_code', ''),
                'msg_content': temp.get('msg_content', ''),
                'room_id': temp.get('room_id', []),
                'is_auto': temp.get('is_auto', None),
                'is_send': temp.get('is_send', None),
                'room_visibility': temp.get('room_visibility', 0), # 0 仅可见群 1 全部群
                'is_review': temp.get('is_review'),  # 是否复盘 0 否 1 是
                'is_valuable': temp.get('is_valuable'),  # 是否有训练价值 0 否 1 是
                'only_msg_id': temp.get('only_msg_id', "0"),  # 是否只返回msg_id 0 否 1 是'
                'is_knowledge':  temp.get('is_knowledge'),
            }

            answer_obj = WorkQuestionAnswer.objects.filter(is_valid=True)
            emp_obj = WorkEmpInfo.objects.all()
            # 构建Q对象
            q_objects = Q(is_valid=True, is_question=True)
            # 日期范围查询
            if query_params['start_date'] and query_params['end_date']:
                q_objects &= Q(msg_date__range=(query_params['start_date'], query_params['end_date']))

            # 员工相关查询预处理
            send_from = query_params.get('send_from')
            comp_user = query_params.get('comp_user')

            if send_from or comp_user:
                # 一次性处理员工相关查询
                if send_from:
                    if send_from.isdigit():
                        q_objects &= Q(send_from=send_from)
                    else:
                        send_ids = emp_obj.filter(emp_name__icontains=send_from).values_list('emp_account', flat=True)
                        q_objects &= Q(send_from__in=send_ids)

                if comp_user:
                    if comp_user.isdigit():
                        q_objects &= Q(comp_user=comp_user)
                    else:
                        comp_ids = emp_obj.filter(emp_name__icontains=comp_user).values_list('emp_account', flat=True)
                        q_objects &= Q(comp_user__in=comp_ids)

            # 所属区域查询
            if query_params['belong_area']:
                q_objects &= Q(belong_area__icontains=query_params['belong_area'])

            # 业务线查询
            if query_params['project_type']:
                q_objects &= Q(project_type__in=query_params['project_type'])
            # 状态查询
            if query_params['status']:
                q_objects &= Q(status__in=query_params['status'])
            # 问题类型查询
            if query_params['question_type']:
                q_objects &= Q(question_type__in=query_params['question_type'])
            # 所属商户查询
            if query_params['belong_mer_code']:
                q_objects &= Q(belong_mer_code=query_params['belong_mer_code'])
            # 是否自动
            if query_params['is_auto']:
                # answerids = answer_obj.filter(is_auto=query_params['is_auto']).values_list(
                #     'msg_id', flat=True)
                answerids = answer_obj.filter(is_auto=query_params['is_auto']).values_list('msg_id', flat=True)
                q_objects &= Q(msg_id__in=answerids)
            # 是否复盘
            if query_params['is_review']:
                q_objects &= Q(is_review=query_params['is_review'])
            # 是否有训练价值
            if query_params['is_valuable']:
                q_objects &= Q(is_valuable=query_params['is_valuable'])
            # 是否需要更新知识库
            if query_params['is_knowledge']:
                q_objects &= Q(is_knowledge=query_params['is_knowledge'])
            # 是否推送
            if query_params['is_send']:
                q_objects &= Q(is_send=query_params['is_send'])
            # 问题描述
            if query_params['msg_content']:
                search_terms = query_params['msg_content'].split()  # 按空格分割成多个词
                # 使用reduce简化多个icontains查询
                content_q = reduce(and_, [Q(msg_content__icontains=term) for term in search_terms])
                q_objects &= content_q

            # 群名称
            room_query = WorkChatConfig.objects.filter(is_valid=True)
            order_by = '-create_time'  # 默认排序
            # 根据可见性条件筛选
            if query_params['room_visibility'] == 0:  # 仅可见群
                room_query = room_query.filter(is_display=True)
                order_by = '-seq'

            # 群条件
            if query_params['room_id']:
                room_query = room_query.filter(room_id__in=query_params['room_id'])

            # 获取群ID列表
            room_ids = room_query.values_list('room_id', flat=True)
            q_objects &= Q(room_id__in=room_ids)

            # 查询并分页
            workchatdata_obj = WorkChatData.objects.filter(q_objects).order_by(order_by)
            paginator = Paginator(workchatdata_obj, limit)
            data = paginator.page(page)

            # data = workchatdata_obj[(page - 1) * limit: page * limit]
            # # 获取总记录数（用于分页计算）
            # count = workchatdata_obj.count()
            # data_list = list(data.values())  # 直接转换为值列表
            if query_params['only_msg_id'] == "1":
                # 只返回msg_id字段列表
                data_list = list(data.object_list.values_list('msg_id', flat=True))
            else:
                # 返回所有字段列表
                data_list = list(data.object_list.values())
                msgids = [item['msg_id'] for item in data_list if item['msg_id']]

                # 答案数据
                answers = answer_obj.filter(msg_id__in=msgids)
                answers_by_msgids = {answer.msg_id: answer for answer in answers}

                # 员工信息
                emp_ids = set(
                    [item['duty_user'] for item in data_list if item['duty_user']] +
                    [item['comp_user'] for item in data_list if item['comp_user']] +
                    [item['send_from'] for item in data_list if item['send_from']]
                )
                emp_by_ids = {
                    emp.emp_account: emp
                    for emp in WorkEmpInfo.objects.filter(emp_account__in=emp_ids)
                }

                # 群信息
                roominfos = WorkChatConfig.objects.filter(is_valid=True, room_id__in=room_ids)
                roominfo_by_roomids = {roominfo.room_id: roominfo for roominfo in roominfos}

                # 操作日志
                logsitems_by_msgids = defaultdict(list)
                for log in WorkAnswerLog.objects.filter(msg_id__in=msgids).exclude(log_type=4):
                    logsitems_by_msgids[log.msg_id].append(log)

                # 批量获取所有附加数据
                attachments_map = self.get_attachments_batch(msgids)
                logs_map = self.question_log_list_batch(msgids)
                children_map = self.get_children_msg_batch(msgids)
                detail_map = self.get_data_detail_info_batch(msgids)
                knowledge_map = self.get_knowledge_info_batch(msgids)

                # 构建响应数据
                for item in data_list:
                    msg_id = item['msg_id']
                    # 答案信息
                    answer = answers_by_msgids.get(msg_id)
                    item['answers'] = model_to_dict(answer) if answer else {}

                    # 员工信息
                    duty_user = item['duty_user']
                    emp_obj = emp_by_ids.get(duty_user)
                    item['duty_user_name'] = emp_obj.emp_name if emp_obj else duty_user

                    comp_user = item['comp_user']
                    emp_obj = emp_by_ids.get(comp_user)
                    item['comp_user_name'] = emp_obj.emp_name if emp_obj else comp_user

                    send_from = item['send_from']
                    emp_obj = emp_by_ids.get(send_from)
                    if emp_obj:
                        item['send_from_name'] = emp_obj.emp_name
                    elif len(send_from) > 15:
                        item['send_from_name'] = send_from[-4:] # 从倒数第4位开始截取到末尾
                        item['send_from'] = ""
                    else:
                        item['send_from_name'] = send_from

                        # 消息内容
                    item['msg_content'] = self.generate_msg_content(item['msg_type'], msg_id, item['msg_content'])

                    # 群信息
                    if item['room_id'] in roominfo_by_roomids:
                        item['room_name'] = roominfo_by_roomids[item['room_id']].room_name
                    else:
                        item['room_name'] = item['room_id']

                    # 处理时长
                    # 状态为无需处理时，处理时长为0
                    if item['status'] == 4:
                        item['processing_time'] = 0
                    elif item['comp_time']:
                        item['processing_time'] = int((item['comp_time'] - item['create_time']).total_seconds() / 60)
                    else:
                        item['processing_time'] = int((timezone.now() - item['create_time']).total_seconds() / 60)

                    # 创建时间格式化
                    item['create_time'] = item['create_time'].strftime('%Y-%m-%d %H:%M:%S')

                    # 发送时间格式化
                    item['msg_time'] = datetime.fromtimestamp(int(item['msg_time']) / 1000).replace(microsecond=0).strftime(
                        '%Y-%m-%d %H:%M:%S')

                    item['attachment'] = attachments_map.get(msg_id, [])
                    item['logs'] = logs_map.get(msg_id, [])
                    item['childrens'] = children_map.get(msg_id, [])
                    item['bug_url'] = detail_map[msg_id]['bug_url']
                    item['knowledge']= knowledge_map.get(msg_id, {})

            response_data = {
                'pagenum': page,
                'pagesize': limit,
                'total': paginator.count,
                'questions': data_list
            }
            return ResponseUtils.return_success("查询成功", response_data)

        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

    # 问题详情接口
    @action(methods=['GET'],detail=False)
    def question_detail(self, request):
        try:
            msg_id = request.GET.get('msg_id', None)
            if not msg_id:
                return ResponseUtils.return_fail(AppErrors.ERROR_PARAMETER_MISSING)
            # 打印请求头信息
            logger.info(f'请求头信息：{request.headers}')
            # 获取问题详情数据
            workchatdata = WorkChatData.objects.filter(msg_id=msg_id, is_valid=True, is_question=True).first()
            if workchatdata:
                data_list = model_to_dict(workchatdata)
            else:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION)

            # 获取员工姓名
            emp_obj = WorkEmpInfo.objects.all()
            comp_user_info = emp_obj.filter(emp_account=workchatdata.comp_user).first()
            data_list['comp_user_name'] = comp_user_info.emp_name if comp_user_info else workchatdata.comp_user

            send_from_info = emp_obj.filter(emp_account=workchatdata.send_from).first()
            if send_from_info:
                data_list['send_from_name'] = send_from_info.emp_name
            elif len(workchatdata.send_from) > 15:
                data_list['send_from_name'] = workchatdata.send_from[-4:]  # 从倒数第4位开始截取到末尾
                data_list['send_from'] = ""
            else:
                data_list['send_from_name'] = workchatdata.send_from

            # 发送时间格式化
            data_list['msg_time'] = datetime.fromtimestamp(int(data_list['msg_time']) / 1000).replace(microsecond=0).strftime(
                '%Y-%m-%d %H:%M:%S')

            # 获取答案信息
            answer_data =  WorkQuestionAnswer.objects.filter(msg_id=msg_id,is_valid=True).first()
            if answer_data:
                data_list['answers'] = model_to_dict(answer_data)
            else:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION)

            # 消息内容
            data_list['msg_content'] = self.generate_msg_content(workchatdata.msg_type, msg_id, workchatdata.msg_content)

            # 增加操作日志
            logsitems = WorkAnswerLog.objects.filter(msg_id=msg_id).exclude(log_type=4)
            logs_list = self.question_log_list(logsitems)
            # 按时间降序排序日志列表
            logs_list.sort(key=lambda x: x['timestamp'], reverse=True)
            data_list['logs'] = logs_list  # 将排序后的日志列表添加到 item 字典中

            # 获取群名称
            roominfo = WorkChatConfig.objects.get(is_valid=True, room_id=workchatdata.room_id, is_display=True)
            data_list['room_name'] = roominfo.room_name

            # 增加附件
            data_list['attachment'] = self.get_attachments(msg_id)

            # 子消息
            data_list['childrens'] = self.get_children_msg(msg_id)

            info = self.get_data_detail_info(msg_id)
            # BUG链接
            data_list['bug_url'] = info['bug_url']

            # 知识库
            data_list['knowledge'] = self.get_knowledge_info(msg_id)

            response_data = {'questions': data_list}
            return ResponseUtils.return_success("查询成功", response_data)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

    # 获取操作日志
    def question_log_list(self, logsitems:list) -> list:
        # 增加操作日志
        logs_list = []  # 使用列表来存储所有的日志记录
        for logsitem in logsitems:
            log_entry = {}  # 为每个 logsitem 创建一个新的字典
            user = f"{logsitem.user_name}({logsitem.user_account or ''})"
            log_entry['timestamp'] = logsitem.create_time.strftime('%Y-%m-%d %H:%M:%S')
            log_entry['attachment'] = []
            if logsitem.attachments:
                try:
                    log_entry['attachment'] = json.loads(logsitem.attachments)
                except json.JSONDecodeError:
                    # 处理 JSON 解析失败的情况，例如记录日志或设置默认值
                    log_entry['attachment'] = []

            if logsitem.log_type == 0:
                if logsitem.push_flag:
                    log_entry['content'] = f"{user} 【推送】问题回复"
                else:
                    log_entry['content'] = f"{user} 【编辑】问题回复：{logsitem.log_content}"
            elif logsitem.log_type == 1:
                log_entry['content'] = f"{user} 【编辑】问题信息：{logsitem.log_content}"
            elif logsitem.log_type == 2:
                log_entry['content'] = f"{user} 【转交】问题：{logsitem.log_content}"
            elif logsitem.log_type == 5:
                log_entry['content'] = f"{user} 【设置】{logsitem.log_content}"
            else :
                log_entry['content'] = f"问题创建：{logsitem.log_content}"

            logs_list.append(log_entry)  # 将创建的日志记录添加到日志列表中
        return logs_list

    @action(methods=['POST'], detail=False)
    def work_chat_list(self, request):
        """
        查询会话记录列表
        参数说明：
            start_date: 开始时间 (非必填)
            end_date: 结束时间 (非必填)
            send_from: 提交人 (非必填)
            belong_area: 所属大区 (非必填)
            project_type: 业务归属 (非必填)
            belong_mer_code: 所属商户 (非必填)
            is_question: 是否问题 (非必填)
            is_manual_delete: 是否手动置为非问题(非必填)
            pagenum: 页码 (必填)
            pagesize: 条数 (必填)
        :return:
        """
        try:
            temp = json.loads(request.body)
            # 设置默认值并验证必填参数
            page = int(temp.get('pagenum', 1))
            limit = int(temp.get('pagesize', 10))
            if page <= 0 or limit <= 0:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, "页码和条数必须为正整数")

            # 初始化查询条件
            query_params = {
                'start_date': temp.get('start_date', ''),
                'end_date': temp.get('end_date', ''),
                'send_from': temp.get('send_from', ''),
                'belong_area': temp.get('belong_area', ''),
                'room_name': temp.get('room_name', ''),
                'project_type': temp.get('project_type', []),
                'belong_mer_code': temp.get('belong_mer_code', ''),
                'msg_content': temp.get('msg_content', ''),
                'is_question': temp.get('is_question', None),
                'is_manual_delete': temp.get('is_manual_delete')
            }

            emp_obj = WorkEmpInfo.objects.filter()
            # 构建Q对象
            q_objects = Q(is_valid=True)
            if query_params['start_date'] and query_params['end_date']:
                q_objects &= Q(msg_date__range=(query_params['start_date'], query_params['end_date']))

            if query_params['send_from']:
                # 判断是数字，则直接用编码查询
                if query_params['send_from'].isdigit():
                    q_objects &= Q(send_from=query_params['send_from'])
                else:
                    sendids = emp_obj.filter(emp_name__icontains=query_params['send_from']).values_list('emp_account',
                                                                                                        flat=True)
                    q_objects &= Q(send_from__in=sendids)

            if query_params['belong_area']:
                q_objects &= Q(belong_area__icontains=query_params['belong_area'])

            if query_params['room_name']:
                roominfo = WorkChatConfig.objects.filter(is_valid=True, is_display=True, room_name__contains=query_params['room_name']).values_list(
                    'room_id', flat=True)
                q_objects &= Q(room_id__in=roominfo)

            if query_params['project_type']:
                q_objects &= Q(project_type__in=query_params['project_type'])

            if query_params['belong_mer_code']:
                q_objects &= Q(belong_mer_code=query_params['belong_mer_code'])

            if query_params['is_question']:
                q_objects &= Q(is_question=query_params['is_question'])

            if query_params['msg_content']:
                search_terms = query_params['msg_content'].split()  # 按空格分割成多个词
                q_objects &= Q(msg_content__icontains=search_terms[0])  # 第一个词必须匹配

                for term in search_terms[1:]:
                    q_objects &= Q(msg_content__icontains=term)  # 后续词也必须匹配

            if query_params['is_manual_delete']:
                q_objects &= Q(is_manual_delete=query_params['is_manual_delete'])

            # 获取所有有效的 room_id
            valid_room_ids = WorkChatConfig.objects.filter(is_valid=True, is_display=True).values_list('room_id',
                                                                                                       flat=True)
            # 查询群配置=1 条件(可见)
            q_objects &= Q(room_id__in=valid_room_ids)

            # 查询并分页
            workchatdata_obj = WorkChatData.objects.filter(q_objects).order_by('-seq')
            paginator = Paginator(workchatdata_obj, limit)
            data = paginator.page(page)
            data_list = list(data.object_list.values())
            msgids = [item['msg_id'] for item in data_list if item['msg_id']]

            # 员工信息
            emp_ids = set([item['send_from'] for item in data_list if item['send_from']])
            emp_by_ids = {
                emp.emp_account: emp
                for emp in WorkEmpInfo.objects.filter(emp_account__in=emp_ids)
            }

            # 群信息
            room_ids = [item['room_id'] for item in data_list if item['room_id']]
            roominfos = WorkChatConfig.objects.filter(is_valid=True, room_id__in=room_ids)
            roominfo_by_roomids = {roominfo.room_id: roominfo for roominfo in roominfos}

            # 操作日志
            logsitems_by_msgids = defaultdict(list)
            for log in WorkAnswerLog.objects.filter(msg_id__in=msgids, log_type=1).order_by('-id'):
                logsitems_by_msgids[log.msg_id].append(log)

            for item in data_list:
                msg_id = item['msg_id']
                # 员工信息
                send_from = item['send_from']
                emp_obj = emp_by_ids.get(send_from)
                if emp_obj:
                    item['send_from_name'] = emp_obj.emp_name
                elif len(send_from) > 15:
                    item['send_from_name'] = send_from[-4:]  # 从倒数第4位开始截取到末尾
                    item['send_from'] = ""
                else:
                    item['send_from_name'] = send_from

                # 获取群名称
                if item['room_id'] in roominfo_by_roomids:
                    item['room_name'] = roominfo_by_roomids[item['room_id']].room_name
                else:
                    item['room_name'] = item['room_id']

                # 消息内容
                item['msg_content'] = self.generate_msg_content(item['msg_type'], msg_id, item['msg_content'])

                # 增加延时时间字段
                msg_time = datetime.fromtimestamp(int(item['msg_time']) / 1000).replace(microsecond=0)
                item['msg_time'] = msg_time.strftime('%Y-%m-%d %H:%M:%S')
                item['delay_time'] = int((item['create_time'] - msg_time).total_seconds())
                item['create_time'] = item['create_time'].strftime('%Y-%m-%d %H:%M:%S')

                # 增加是否人工删除字段
                item['notes'] = ''
                item['manual_delete_time'] =''
                item['manual_delete_user'] =''
                if item['is_manual_delete']:
                    for log in logsitems_by_msgids[msg_id]:
                        # 截取log_content=手动设置: 置为非问题,备注: XXXX
                        if not log.log_content:
                            continue

                        # 字符串分割处理
                        if "手动设置:" in log.log_content:
                            parts = log.log_content.split("手动设置:")
                            if len(parts) > 1:
                                action_part = parts[1].split(",")[0].strip()
                                if '置为非问题' in action_part:
                                    item['manual_delete_time'] = log.create_time.strftime('%Y-%m-%d %H:%M:%S')
                                    item['manual_delete_user'] = f'{log.user_name}({log.user_account})'
                                    # 提取备注信息
                                    if "备注:" in log.log_content:
                                        item['notes'] = log.log_content.split("备注:")[1].strip()
                                    break

            response_json = {'pagenum': page, 'pagesize': limit, 'total': paginator.count, 'data': data_list}
            return ResponseUtils.return_success("查询成功", response_json)

        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

    # 更新问题信息
    @action(methods=['POST'], detail=False)
    def question_update(self, request):
        # 获取操作人的用户ID
        userinfo = UserUtils.get_login_user(request)
        username = userinfo.chinese_name if userinfo else 'api'
        user_account = userinfo.staff_no if userinfo else 'api'

        temp = json.loads(request.body)

        try:
            question_id = temp['id']
            comp_user = temp.get('comp_user', None)
            project_type = temp.get('project_type', None)
            status = temp.get('status', None)
            question_type = temp.get('question_type', None)
            belong_mer_code = temp.get('belong_mer_code', None)
            mer_type = temp.get('mer_type', None)
            comp_user_name = None
            if not question_id:
                return ResponseUtils.return_fail(AppErrors.ERROR_PARAMETER_MISSING)
            # 检查每个字段，如果非空则添加到更新字典中
            update_data = {
                'comp_user': comp_user,
                'project_type': project_type,
                'status': status,
                'question_type': question_type,
                'belong_mer_code': belong_mer_code,
                'mer_type': int(mer_type) if mer_type is not None else None
            }
            update_data = {k: v for k, v in update_data.items() if v is not None}

            if comp_user:
                query_comp_user = WorkEmpInfo.objects.filter(emp_account=comp_user).values("emp_name").first()
                if query_comp_user:
                    comp_user_name = query_comp_user['emp_name']

            if project_type is not None:
                obj = WorkChatData.objects.filter(id=question_id).first()
                query_duty = WorkDutyUser.objects.filter(project_type=project_type, room_id=obj.room_id, is_valid=True).values("duty_user_no").first()
                if query_duty:
                    update_data['comp_user'] = query_duty['duty_user_no']
                else:
                    # 取默认值班人
                    query_duty_dict = WorkDutyUser.objects.filter(project_type='99', room_id=obj.room_id, is_valid=True).values("duty_user_no").first()
                    if query_duty_dict:
                        update_data['comp_user'] = query_duty_dict['duty_user_no']

            update_data['update_time'] = timezone.now()
            with transaction.atomic():
                # 更新数据
                WorkChatData.objects.filter(id=question_id).update(**update_data)

                workchatdata_obj = WorkChatData.objects.filter(id=question_id).first()
                msg_id = workchatdata_obj.msg_id
                logs_to_create = []
                # 收集状态日志
                logs_to_create.append({
                    'msg_id': msg_id,
                    'log_type': 1,
                    'user_name': username,
                    'user_account': user_account,
                    'comp_user':comp_user_name,
                    'project_type': project_type,
                    'question_type': question_type,
                    'status': status,
                    'belong_mer_code': belong_mer_code,
                    'mer_type': int(mer_type) if mer_type is not None else None

                })
                self.batch_create_work_answer_log(logs_to_create)
            async_tasks = []
            # 处理人变更且状态为待受理、解决中时，推送消息
            if update_data.get('comp_user') and workchatdata_obj.status in [0, 2]:
                # self.question_push_personal_message(msg_id)
                async_tasks.append(lambda: self.question_push_personal_message(msg_id))

            # 判断业务线变更，且问题状态是待受理时，调用AI回复
            if project_type is not None and workchatdata_obj.status==0:
                # self.question_to_ai_answer(msg_id)
                async_tasks.append(lambda: self.question_to_ai_answer(msg_id))

            transaction.on_commit(lambda: self.execute_async_tasks(async_tasks))

            data ={"id":question_id}
            return ResponseUtils.return_success("问题信息编辑成功",data=data)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

    # 更新问题回复
    @action(methods=['POST'], detail=False)
    def answer_update(self, request):
        # 获取操作人的用户ID
        userinfo = UserUtils.get_login_user(request)
        username = userinfo.chinese_name if userinfo else 'api'
        user_account = userinfo.staff_no if userinfo else 'api'

        temp = json.loads(request.body)
        try:
            question_id = temp.get('question_id')
            answer_id = temp.get('answer_id')
            question_answer_manual = temp.get('question_answer_manual', None)
            comp_user = temp.get('comp_user', None)
            project_type = temp.get('project_type', None)
            question_type = temp.get('question_type', None)
            belong_mer_code = temp.get('belong_mer_code', None)
            mer_type = temp.get('mer_type', None)
            comp_time = temp.get('comp_time', None)  # 仅强制已处理时，传处理时间
            file_url = temp.get('file_url') # 图片
            is_push = temp.get('is_push', False) # 是否推送群消息
            bug_url = temp.get('bug_url', None) # bug链接

            if not question_id:
                return ResponseUtils.return_fail(AppErrors.ERROR_PARAMETER_MISSING)

            # 准备日志收集列表
            logs_to_create = []
            # 准备异步任务列表
            async_tasks = []

            # 处理人姓名
            comp_user_name = None
            if comp_user:
                query_comp_user =WorkEmpInfo.objects.filter(emp_account=comp_user).values("emp_name").first()
                if query_comp_user:
                    comp_user_name = query_comp_user['emp_name']

            # 更新数据字典
            update_data = {
                'comp_user': comp_user,
                'project_type': project_type,
                'question_type': question_type,
                'belong_mer_code': belong_mer_code,
                'mer_type': int(mer_type) if mer_type is not None else None,
                'comp_time': comp_time
            }
            update_data = {k: v for k, v in update_data.items() if v is not None}

            with transaction.atomic():
                # 获取问题记录
                workchatdata_obj = WorkChatData.objects.select_for_update().get(id=question_id)
                msg_id = workchatdata_obj.msg_id
                room_id = workchatdata_obj.room_id
                create_time = workchatdata_obj.create_time

                # 处理时间校验
                if comp_time:
                    if 'T' in comp_time:
                        comp_time = datetime.strptime(comp_time, '%Y-%m-%dT%H:%M:%S')
                    else:
                        comp_time = datetime.strptime(comp_time, '%Y-%m-%d %H:%M:%S')

                    if comp_time <= create_time:
                        return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, '处理时间不能小于创建时间')

                if answer_id and question_answer_manual:
                    # 更新回复表数据
                    WorkQuestionAnswer.objects.filter(id=answer_id).update(question_answer_manual=question_answer_manual,
                                                                   is_auto=False, answer_source=2,
                                                                   update_time=timezone.now())
                    if comp_time is None:
                        # 自动更新 状态和推送状态字段
                        update_data.update({'status': 2, 'is_send': False})
                    else:
                        update_data.update({'status': 1, 'is_send': True, 'comp_time': comp_time})

                # 保存图片
                attachment_obj = WorkAnswerAttachment.objects.filter(msg_id=msg_id, is_valid=True).first()
                # 场景1: 显式传入空列表，清空附件
                if 'file_url' in temp and not file_url:
                    if attachment_obj:
                        # 删除图片
                        attachment_obj.is_valid = False
                        attachment_obj.update_time = timezone.now()
                        attachment_obj.save()
                    file_url_json = None
                # 场景2: 传入新附件列表，则更新或新建
                elif file_url and isinstance(file_url, (list, tuple)):
                    file_url_json = json.dumps(file_url)
                    if attachment_obj:
                        # 更新现有记录
                        attachment_obj.attach_file = file_url_json
                        attachment_obj.update_time = timezone.now()
                        attachment_obj.save()
                    else:
                        # 创建新记录
                        WorkAnswerAttachment.objects.create(
                            msg_id=msg_id,
                            attach_file=file_url_json,
                            attach_type=0,
                            create_time=timezone.now(),
                            update_time=timezone.now(),
                            is_valid=True
                        )
                # 场景3：仅推送的时候，兼容AI回复返回的图片，取已有的附件，作为后续日志记录
                elif is_push:
                    file_url_json = attachment_obj.attach_file if attachment_obj else None
                else:
                    file_url_json = None

                # 记录操作日志
                if question_answer_manual or file_url_json:
                    # 收集日志
                    logs_to_create.append({
                        'msg_id': msg_id,
                        'user_name': username,
                        'user_account': user_account,
                        'log_type': 0,
                        'question_answer_manual': question_answer_manual,
                        'attachments': file_url_json
                    })

                # 记录bug链接
                if bug_url and bug_url != '':
                    # 更新回复表数据
                    WorkChatDataDetail.objects.update_or_create(
                        msg_id=msg_id,
                        defaults={
                            'tapd_url': bug_url,
                            'create_time': timezone.now(),
                            'update_time': timezone.now()
                        }
                    )

                if update_data:
                    update_data['update_time'] = timezone.now()
                    # 更新问题表数据
                    WorkChatData.objects.filter(id=question_id).update(**update_data)

                    # 准备日志参数
                    log_params = {
                        'msg_id': msg_id,
                        'user_name': username,
                        'user_account': user_account,
                        'log_type': 1
                    }
                    # 定义需要排除的字段
                    EXCLUDE_LOG_FIELDS = {'status', 'is_send', 'update_time'}

                    # 构建日志专用的数据字典
                    log_update_data = {
                        k: comp_user_name if k == 'comp_user' else v
                        for k, v in update_data.items()
                        if k not in EXCLUDE_LOG_FIELDS # 排除不需要的字段
                    }

                    if bug_url is not None:
                        log_update_data['bug_url'] = bug_url

                    # 检查log_update_data是否为空
                    if log_update_data:
                        # 合并字典
                        log_params.update(log_update_data)
                        # 添加到logs_to_create
                        logs_to_create.append(log_params)

                # 获取更新后的状态
                workchatdata_obj.refresh_from_db()
                new_status = workchatdata_obj.status

                # 判断业务线变更，且问题状态是待受理时，调用AI回复
                if project_type is not None and new_status == 0:
                    async_tasks.append(lambda: self.question_to_ai_answer(msg_id))

                # 处理人变更且状态为待受理、解决中时，推送消息
                if comp_user and new_status in [0, 2]:
                    async_tasks.append(lambda: self.question_push_personal_message(msg_id))

                # 判断是否需要推送
                if is_push:
                    logger.info('消息ID:[%s]，企微群消息推送中,操作人：[%s]', msg_id, username + user_account)
                    # 异步推消息到群里
                    async_tasks.append(lambda: self._async_push_room_message(
                        msg_id, room_id, username, user_account, workchatdata_obj
                    ))
                    # 设置状态为已处理
                    new_status =1

                    # 收集推送日志
                    logs_to_create.append({
                        'msg_id': msg_id,
                        'user_name': username,
                        'user_account': user_account,
                        'log_type': 0,
                        'push_flag': True
                    })

                # 收集状态日志
                logs_to_create.append({
                    'msg_id': msg_id,
                    'log_type': 4,
                    'status': new_status,
                    'user_name': username,
                    'user_account': user_account
                })

                # 批量创建日志
                if logs_to_create:
                    self.batch_create_work_answer_log(logs_to_create)

                # 提交事务后执行异步任务
                transaction.on_commit(lambda: self.execute_async_tasks(async_tasks))

                data = {"question_id": question_id}
                return ResponseUtils.return_success("操作成功", data =data)
        except Exception as e:
            logger.error(f"操作异常: {e}")
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, str(e))

    # 问题转交
    @action(methods=['POST'], detail=False)
    def question_transfer(self, request):
        # 获取操作人的用户ID
        userinfo = UserUtils.get_login_user(request)
        username = userinfo.chinese_name if userinfo else 'api'
        user_account = userinfo.staff_no if userinfo else 'api'

        temp = json.loads(request.body)
        try:
            question_id = temp.get('question_id')
            answer_id = temp.get('answer_id')
            question_answer_manual = temp.get('question_answer_manual', None)
            comp_user = temp.get('comp_user', None)
            project_type = temp.get('project_type', None)
            question_type = temp.get('question_type', None)
            belong_mer_code = temp.get('belong_mer_code', None)
            mer_type = temp.get('mer_type', None)
            file_url = temp.get('file_url')  # 图片
            bug_url = temp.get('bug_url', None)  # bug链接

            if not question_id:
                return ResponseUtils.return_fail(AppErrors.ERROR_PARAMETER_MISSING)
            # 准备日志收集列表
            logs_to_create = []
            # 准备异步任务列表
            async_tasks = []
            # 处理人姓名
            comp_user_name = None
            if comp_user:
                query_comp_user = WorkEmpInfo.objects.filter(emp_account=comp_user).values("emp_name").first()
                if query_comp_user:
                    comp_user_name = query_comp_user['emp_name']

            # 如果非空则添加到更新字典中
            update_data = {
                'comp_user': comp_user,
                'project_type': project_type,
                'question_type': question_type,
                'belong_mer_code': belong_mer_code,
                'mer_type': int(mer_type) if mer_type is not None else None
            }
            update_data = {k: v for k, v in update_data.items() if v is not None}

            with transaction.atomic():
                workchatdata_obj = WorkChatData.objects.select_for_update().get(id=question_id)
                msg_id = workchatdata_obj.msg_id
                room_id = workchatdata_obj.room_id
                if answer_id and question_answer_manual:
                    # 更新回复表数据
                    WorkQuestionAnswer.objects.filter(id=answer_id).update(
                        question_answer_manual=question_answer_manual,
                        is_auto=False, answer_source=2,
                        update_time=timezone.now())

                # 保存图片
                # file_url_json = None
                # if file_url and isinstance(file_url, (list, tuple)):
                #     file_url_json = json.dumps(file_url)
                #     attachment_obj = WorkAnswerAttachment.objects.filter(msg_id=msg_id, is_valid=True).first()
                #     if attachment_obj:
                #         # 如果记录存在，则更新 attach_file 和 update_time
                #         attachment_obj.attach_file = file_url_json
                #         attachment_obj.update_time = timezone.now()
                #         attachment_obj.save()
                #     else:
                #         attachments = WorkAnswerAttachment(
                #             msg_id=msg_id,
                #             attach_file=file_url_json,  # 直接存储 JSON 数据
                #             attach_type=0,
                #             create_time=timezone.now(),
                #             update_time=timezone.now(),
                #             is_valid=True
                #         )
                #         attachments.save()
                # 保存图片
                attachment_obj = WorkAnswerAttachment.objects.filter(msg_id=msg_id, is_valid=True).first()
                # 场景1: 显式传入空列表，清空附件
                if 'file_url' in temp and not file_url:
                    if attachment_obj:
                        # 删除图片
                        attachment_obj.is_valid = False
                        attachment_obj.update_time = timezone.now()
                        attachment_obj.save()
                    file_url_json = None
                # 场景2: 传入新附件列表，则更新或新建
                elif file_url and isinstance(file_url, (list, tuple)):
                    file_url_json = json.dumps(file_url)
                    if attachment_obj:
                        # 更新现有记录
                        attachment_obj.attach_file = file_url_json
                        attachment_obj.update_time = timezone.now()
                        attachment_obj.save()
                    else:
                        # 创建新记录
                        WorkAnswerAttachment.objects.create(
                            msg_id=msg_id,
                            attach_file=file_url_json,
                            attach_type=0,
                            create_time=timezone.now(),
                            update_time=timezone.now(),
                            is_valid=True
                        )
                else:
                    file_url_json = None

                # 更新消息记录表
                if update_data:
                    update_data['update_time'] = timezone.now()
                    # 更新问题表数据
                    WorkChatData.objects.filter(id=question_id).update(**update_data)

                # 处理日志
                #初始化基础字典
                log_params = {
                    'msg_id': msg_id,
                    'user_name': username,
                    'user_account': user_account,
                    'log_type': 2,
                }
                # 定义需要排除的字段
                EXCLUDE_LOG_FIELDS = {'status', 'update_time'}

                # 构建日志专用的数据字典
                log_update_data = {
                    k: comp_user_name if k == 'comp_user' else v
                    for k, v in update_data.items()
                    if k not in EXCLUDE_LOG_FIELDS  # 排除不需要的字段
                }

                if bug_url is not None:
                    log_update_data['bug_url'] = bug_url

                if question_answer_manual is not None:
                    log_update_data['question_answer_manual'] = question_answer_manual
                if file_url_json is not None:
                    log_update_data['attachments'] = file_url_json

                # 检查log_update_data是否为空
                if log_update_data:
                    # 合并字典
                    log_params.update(log_update_data)
                    # 添加到logs_to_create
                    logs_to_create.append(log_params)

                # 批量创建日志
                if logs_to_create:
                    self.batch_create_work_answer_log(logs_to_create)
                # 获取更新后的状态
                workchatdata_obj.refresh_from_db()
                new_status = workchatdata_obj.status

                # 处理人变更且状态为待受理、解决中时，推送个人消息
                if comp_user and new_status in [0, 2]:
                    async_tasks.append(lambda: self.question_push_personal_message(msg_id))

                # 群消息
                async_tasks.append(lambda:  self._async_push_room_message( msg_id, room_id, username, user_account))

                # 提交事务后执行异步任务
                transaction.on_commit(lambda: self.execute_async_tasks(async_tasks))

            data = {"question_id": question_id}
            logger.info('消息ID:[%s]，转交给[%s]成功', msg_id, comp_user_name + comp_user)
            return ResponseUtils.return_success("问题转交成功", data=data)

        except Exception as e:
            logger.error(f"An error occurred: {e}")
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, str(e))

    # 更新问题状态
    @action(methods=['POST'], detail=False)
    def question_update_status(self, request):
        # 获取操作人的用户ID
        userinfo = UserUtils.get_login_user(request)
        username = userinfo.chinese_name if userinfo else 'api'
        user_account = userinfo.staff_no if userinfo else 'api'
        try:
            temp = json.loads(request.body)
            question_id = temp.get('id')
            type = int(temp.get('type', 1)) # 1 置为非问题  2 删除问题 3 关闭问题 4 无需处理
            notes = temp.get('notes', '')

            if not question_id or not isinstance(type, int) or type not in [1, 2, 3, 4]:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION)

            workchatdata_obj = WorkChatData.objects.get(is_valid=True, id=question_id)
            msg_id = workchatdata_obj.msg_id
            workchatdata_obj.update_time = timezone.now()
            logs_to_create = []
            # 初始化基础字典
            log_params = {
                'msg_id': msg_id,
                'user_name': username,
                'user_account': user_account,
                'log_type': 1,
            }

            # 置为非问题
            if type == 1:
                workchatdata_obj.is_question = False
                workchatdata_obj.is_manual_delete = True
                workchatdata_obj.save()
                WorkQuestionAnswer.objects.filter(msg_id=msg_id).delete()
                log_params['manual_setting'] = '置为非问题'
                log_params['notes'] = notes
            # 删除问题
            elif type == 2:
                workchatdata_obj.is_valid = False
                workchatdata_obj.save()
                WorkQuestionAnswer.objects.filter(msg_id=msg_id).update(is_valid=False, update_time=timezone.now())
                log_params['manual_setting'] = '删除问题'
            # 关闭问题，置为已关闭状态
            elif type == 3:
                workchatdata_obj.status = 3
                workchatdata_obj.save()
                log_params['manual_setting'] = '关闭问题'
            else:
                workchatdata_obj.status = 4
                workchatdata_obj.comp_user = user_account
                workchatdata_obj.save()
                if notes:
                    # 将备注写到问答表的人工回复字段
                    WorkQuestionAnswer.objects.filter(msg_id=msg_id).update(question_answer_manual=notes,
                                                                       is_auto=False, answer_source=2,
                                                                       update_time=timezone.now())
                log_params['manual_setting'] = f'问题无需处理"，备注({notes})"'

            # 记录日志
            logs_to_create.append(log_params)
            self.batch_create_work_answer_log(logs_to_create)

        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        return ResponseUtils.return_success("操作成功", data={"id": question_id})

    # 推送消息
    @action(methods=['POST'], detail=False)
    def question_push_message(self, request):
        # 获取操作人的用户ID
        userinfo = UserUtils.get_login_user(request)
        username = userinfo.chinese_name if userinfo else 'api'
        user_account = userinfo.staff_no if userinfo else 'api'
        temp = json.loads(request.body)
        try:
            msgId = temp.get('msgId')
            roomId = temp.get('roomId')
            if not msgId or not roomId:
                return ResponseUtils.return_fail(AppErrors.ERROR_PARAMETER_MISSING)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        data = {
            "msgId": msgId,
            "roomId": roomId
        }

        response_json =self.question_push_room_message(msgId, roomId)
        if response_json.get("code") == '10000':
            with transaction.atomic():
                update_time = timezone.now()
                workchatdata_obj = WorkChatData.objects.filter(msg_id=msgId, is_valid=True).first()
                # 更新发送状态
                workchatdata_obj.status = 1
                workchatdata_obj.is_send = True
                workchatdata_obj.comp_time = update_time
                workchatdata_obj.update_time = update_time
                workchatdata_obj.comp_user = user_account
                workchatdata_obj.save()
                # 记录日志
                logs_to_create = []
                # 收集日志
                logs_to_create.append({
                    'msg_id':  workchatdata_obj.msg_id,
                    'user_name': username,
                    'user_account': user_account,
                    'log_type': 0,
                    'push_flag': True
                })

                # 更新附件表状态
                attachment_obj = WorkAnswerAttachment.objects.filter(msg_id=msgId, is_valid=True).first()
                if attachment_obj:
                    # 如果记录存在，则更新状态 和 update_time
                    attachment_obj.is_valid = False
                    attachment_obj.update_time = update_time
                    attachment_obj.save()
                # 收集日志
                logs_to_create.append({
                    'msg_id': msgId,
                    'user_name': username,
                    'user_account': user_account,
                    'log_type': 4,
                    'status': 1
                })
                self.batch_create_work_answer_log(logs_to_create)

            return ResponseUtils.return_success("消息推送成功", data=data)
        else:
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, response_json.get('msg'))

    # 问题识别
    @action(methods=['POST'], detail=False)
    def question_identify(self, request):
        headers = {}
        content_type = "application/json;charset=UTF-8"
        headers["Content-Type"] = content_type
        # 获取操作人的用户ID
        userinfo = UserUtils.get_login_user(request)
        username = userinfo.chinese_name if userinfo else 'api'
        user_account = userinfo.staff_no if userinfo else 'api'

        temp = json.loads(request.body)
        try:
            msgId = temp.get('msgId', [])
            startSeq = temp.get('startSeq', '')
            endSeq = temp.get('endSeq', '')
            type = temp.get('type', 0) #执行类型 0: 按消息ID集合 1: 按seq值
            forceQuestion = temp.get('forceQuestion', 1)  # 是否强制为问题  0: 否 1: 是

        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

        address = question_check_url

        data = {
            "msgId": msgId,
            "startSeq": startSeq,
            "endSeq": endSeq,
            "type": type,
            "forceQuestion": forceQuestion
        }
        req = requests.post(url=address, data=json.dumps(data), headers=headers)
        response_json = req.json()
        if response_json.get("code") == '10000':
            if forceQuestion == 1:
                # 更新手动置为非问题的标识位
                for msg_id in msgId:
                    workchatdata_obj = WorkChatData.objects.filter(msg_id=msg_id, is_valid=True).first()
                    if workchatdata_obj.is_manual_delete:
                        workchatdata_obj.is_manual_delete = False
                        workchatdata_obj.save()
                # 记录日志
                logs_to_create = []
                for msg_id in msgId:
                    logs_to_create.append({
                        'msg_id': msg_id,
                        'user_name': username,
                        'user_account': user_account,
                        'log_type': 1,
                        'manual_setting': "是问题"
                    })
                self.batch_create_work_answer_log(logs_to_create)
            return ResponseUtils.return_success("操作成功", data=data)
        else:
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, response_json.get('msg'))

    # 企微个人消息推送
    def question_push_personal_message(self, msgId):
        try:
            address = notice_url
            params = {}
            if msgId is not None:  # 检查 msg_id 是否有值
                params["msgId"] = msgId

            req = requests.get(url=address, params=params)
            response_json = req.json()
            if response_json.get("code") != '10000':
                logger.error('消息ID:[%s]，企微个人消息推送异常', msgId)
            else:
                logger.info('消息ID:[%s]，企微个人消息推送成功', msgId)
        except Exception as e:
            logger.error(f"请求异常: {e}")

    # 企微群消息推送
    def question_push_room_message(self, msgId, roomId):
        headers = {}
        content_type = "application/json;charset=UTF-8"
        headers["Content-Type"] = content_type
        address = question_msg_url
        data = {
            "msgId": msgId,
            "roomId": roomId
        }
        req = requests.post(url=address, data=json.dumps(data), headers=headers)
        response_json = req.json()
        return response_json

    # 企微群消息推送处理
    def _async_push_room_message(self, msg_id, room_id, username, user_account, workchatdata_obj=None):
        try:
            response_json = self.question_push_room_message(msg_id, room_id)
            if response_json.get("code") == '10000':
                logger.info('消息ID:[%s]，企微群消息推送成功,操作人：[%s]', msg_id, username + user_account)
                with transaction.atomic():
                    if workchatdata_obj:
                        # 更新发送状态
                        workchatdata_obj.status = 1
                        workchatdata_obj.is_send = True
                        workchatdata_obj.comp_time = timezone.now()
                        workchatdata_obj.update_time = timezone.now()
                        workchatdata_obj.comp_user = user_account
                        workchatdata_obj.save()

                        # 记录日志
                        logs_to_create =[]
                        logs_to_create.append({
                            'msg_id': msg_id,
                            'user_name': username,
                            'user_account': user_account,
                            'log_type': 0,
                            'push_flag': True
                        })

                    # 更新附件表状态
                    attachment_obj = WorkAnswerAttachment.objects.filter(msg_id=msg_id, is_valid=True).first()
                    if attachment_obj:
                        attachment_obj.is_valid = False
                        attachment_obj.update_time = timezone.now()
                        attachment_obj.save()
            else:
                logger.error('消息ID:[%s]，企微群消息推送异常，错误信息：[%s]', msg_id, response_json.get('msg'))
        except Exception as e:
            logger.error(f"推送群消息异常: {e}")

    # AI回答
    def question_to_ai_answer(self, msgId):
        try:
            address = settings.AI_ANSWER_URL
            params = {}
            if msgId is not None:  # 检查 msg_id 是否有值
                params["msgId"] = msgId

            req = requests.get(url=address, params=params)
            response_json = req.json()
            if response_json.get("code") != '10000':
                logger.error('消息ID:[%s]，调用AI回复接口失败', msgId)
            else:
                logger.info('消息ID:[%s]，调用AI回复接口成功', msgId)
        except Exception as e:
            logger.error(f"请求异常: {e}")

    # 图片上传接口
    @action(methods=['POST'], detail=False)
    def question_attachment_upload(self, request):
        # 允许的图片文件后缀列表
        ALLOWED_IMAGE_EXTENSIONS = {'.png', '.jpg', '.jpeg', '.gif', '.bmp'}
        # 获取文件，避免 KeyError
        upload_file = request.FILES.get('file')
        if not upload_file:
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION)

        # 获取文件后缀名，并转换为小写以便比较
        file_ext = os.path.splitext(upload_file.name)[1].lower()

        # 检查文件后缀名是否在允许的列表中
        if file_ext not in ALLOWED_IMAGE_EXTENSIONS:
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, '不支持该文件格式')
        if upload_file.size > 50 * 1024 * 1024:  # 限制为10MB
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, '文件大小限制50MB')

        with upload_file.open('rb') as file_obj:
            files = {'file': file_obj}
            response = requests.post(
                url=upload_url,
                files=files,
                timeout=10  # 设置超时时间
            )
            response_json = response.json()
            logger.error(f'返回结果：{response_json}')
            if response_json.get("code") == '10000':
                return ResponseUtils.return_success("上传成功", data=response_json.get('data'))
            else:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, response_json.get('msg'))

    # 更新值班信息
    @action(methods=['GET'], detail=False)
    def update_rota(self, request):
        try:
            room_id = request.GET.get('room_id', None)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

        address = rota_url
        params = {}
        if room_id is not None:  # 检查 room_id 是否有值
            params["room_id"] = room_id

        req = requests.get(url=address, params=params)
        response_json = req.json()
        if response_json.get("code") == '10000':
            return ResponseUtils.return_success("操作成功")
        else:
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, response_json.get('msg'))

    # 解绑合并消息
    @action(methods=['GET'], detail=False)
    def unbind_msg(self, request):
        try:
            parent_msg_id = request.GET.get('parent_msg_id')
            sub_msg_id = request.GET.get('sub_msg_id')
            if  not parent_msg_id or not sub_msg_id:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION)

            address = settings.UNBIND_MSG_URL
            params = {
                "parentMsgId": parent_msg_id,
                "subMsgId": sub_msg_id
            }

            req = requests.get(url=address, params=params)
            response_json = req.json()
            if response_json.get("code") == '10000':
                return ResponseUtils.return_success("操作成功")
            else:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, response_json.get('msg'))
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
    @action(methods=['GET'], detail=False)
    def get_emp_info(self, request):
        """
         查询员工信息
       :param request:  empname ： 必填  员工姓名/编码
                        crop_id : 必填   企业id
       :return:
       """
        try:
            empname = request.GET.get('empname', None)
            crop_id = request.GET.get('crop_id', settings.CROP_ID)
            if not empname or not crop_id:
                return ResponseUtils.return_fail(AppErrors.ERROR_PARAMETER_MISSING)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        # 判断是数字，则直接用编码查询
        if empname.isdigit():
            emp_obj = WorkEmpInfo.objects.filter(emp_account=empname, crop_id=crop_id)
        else:
            emp_obj = WorkEmpInfo.objects.filter(emp_name__icontains=empname, crop_id=crop_id)
        result_list = []
        # 遍历查询集，并为每个对象创建一个包含 emp_account 和 emp_name 字段的字典
        for emp_obj in emp_obj:
            result_dict = {
                'emp_account': emp_obj.emp_account,
                'emp_name': emp_obj.emp_name
            }
            result_list.append(result_dict)
        # 返回结果列表（包含多个字典，每个字典对应一个查询结果）
        return ResponseUtils.return_success("查询成功", data=result_list)

    @action(methods=['GET'], detail=False)
    def get_duty_info(self, request):
        """
         查询值班信息
        问题编辑时，切换业务线时调用
       :param request:  project_type ： 必填  业务线
                        room_id : 必填   群ID

       :return:
       """
        try:
            project_type = request.GET.get('project_type', None)
            room_id = request.GET.get('room_id', None)
            if not project_type or not room_id:
                return ResponseUtils.return_fail(AppErrors.ERROR_PARAMETER_MISSING)

            duty_objs = WorkDutyUser.objects.filter(project_type=project_type, room_id=room_id, is_valid=True)
            if not duty_objs.exists():
                # 取默认值班人
                duty_objs = WorkDutyUser.objects.filter(room_id=room_id, project_type='99', is_valid=True)

            # 构建结果列表
            result_list = [
                {
                    'duty_account': duty_obj.duty_user_no,
                    'duty_name': duty_obj.duty_user,
                    'room_id': room_id,
                    'project_type': project_type
                }
                for duty_obj in duty_objs
            ]
            # 返回结果列表（包含多个字典，每个字典对应一个查询结果）
            return ResponseUtils.return_success("查询成功", data=result_list)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

    @action(methods=['GET'], detail=False)
    def get_room_duty(self, request):
        """
         查询群值班配置
        群值班人配置时，调用
       :param request:  room_id : 必填   群ID

       :return:
       """
        try:
            room_id = request.GET.get('room_id')
            if  not room_id:
                return ResponseUtils.return_fail(AppErrors.ERROR_PARAMETER_MISSING)

            duty_objs = WorkDutyUser.objects.filter(room_id=room_id, is_valid=True)
            # 构建结果列表
            result_list = [
                {
                    'id': duty_obj.id,
                    'duty_user_no': duty_obj.duty_user_no,
                    'duty_user': duty_obj.duty_user,
                    'room_id': room_id,
                    'project_type': duty_obj.project_type
                }
                for duty_obj in duty_objs
            ]
            # 返回结果列表（包含多个字典，每个字典对应一个查询结果）
            return ResponseUtils.return_success("查询成功", data=result_list)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

    @action(methods=['GET'], detail=False)
    def get_room_config(self, request):
        """
         查询群信息
       :param request:  roominfo ： 非必填，群名称
                        wx_manager_no : 非必填，群管理
       :return:
       """
        try:
            roominfo = request.GET.get('roominfo', None)
            wx_manager_no = request.GET.get('wx_manager_no', None)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        q_objects = Q(is_valid=True)
        if roominfo:
            q_objects &= Q(room_name__icontains=roominfo)

        if wx_manager_no:
            if wx_manager_no.isdigit():
                q_objects &= Q(wx_manager_no__icontains=wx_manager_no)
            else:
                empids = WorkEmpInfo.objects.filter(emp_name__icontains=wx_manager_no).values_list('emp_account',
                                                                                                    flat=True)
                q_objects &= Q(wx_manager_no__icontains=empids)

        room_obj = WorkChatConfig.objects.filter(q_objects)
        result_list = []
        # 遍历查询集，并为每个对象字典
        for obj in room_obj:
            result_dict = model_to_dict(obj)  # 将模型实例转换为字典
            # 处理wx_manager_no字段
            if 'wx_manager_no' in result_dict:
                wx_managers = []

                # 如果是逗号分隔的字符串，先转换成列表
                if isinstance(result_dict['wx_manager_no'], str):
                    emp_ids = [x.strip() for x in result_dict['wx_manager_no'].split(',') if x.strip()]
                else:
                    emp_ids = []

                # 查询每个emp_id对应的姓名
                for emp_id in emp_ids:
                    emp_obj = WorkEmpInfo.objects.filter(emp_account=emp_id).first()
                    wx_managers.append({
                        'wx_manager_no': emp_id,
                        'wx_manager_name': emp_obj.emp_name if emp_obj else emp_id
                    })

                result_dict['wx_managers'] = wx_managers
            else:
                result_dict['wx_managers'] = []

            # 增加企业ID
            result_dict['crop_id'] = settings.CROP_ID

            # 增加值班人员
            duty_objs = WorkDutyUser.objects.filter(room_id=obj.room_id, is_valid=True)
            duty_users = [str(duty.duty_user_no) for duty in duty_objs]
            result_dict['duty_users'] = ",".join(duty_users)

            result_list.append(result_dict)
        # 返回结果
        return ResponseUtils.return_success("查询成功", data=result_list)

    # 更新群配置
    @action(methods=['POST'], detail=False)
    def room_update(self, request):
        temp = json.loads(request.body)
        try:
            room_id = int(temp.get('id'))
            reply_toggle = temp.get('reply_toggle',None)
            web_hook = temp.get('web_hook','')
            is_display = temp.get('is_display', None)
            prediction_message_toggle = temp.get('prediction_message_toggle', None)
            room_name = temp.get('room_name')
            wx_manager_no = temp.get('wx_manager_no') #列表 []
            all_business_toggle = temp.get('all_business_toggle') # 是否开启全业务线匹配
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        update_data = {}
        if reply_toggle is not None:
            update_data['reply_toggle'] = reply_toggle
        if web_hook:
            update_data['web_hook'] = web_hook
        if is_display is not None:
            update_data['is_display'] = is_display
        if prediction_message_toggle is not None:
            update_data['prediction_message_toggle'] = prediction_message_toggle
        if room_name:
            update_data['room_name'] = room_name
        if wx_manager_no is not None:
            update_data['wx_manager_no'] = ",".join(wx_manager_no)
        if all_business_toggle is not None:
            update_data['all_business_toggle'] = all_business_toggle

        update_data['update_time'] = timezone.now()
        # 更新数据
        WorkChatConfig.objects.filter(id=room_id).update(**update_data)

        data = {"id": room_id}
        return ResponseUtils.return_success("群配置编辑成功", data=data)

    # 更新消息已读
    @action(methods=['POST'], detail=False)
    def read_status_update(self, request):
        temp = json.loads(request.body)
        try:
            msg_id = temp.get('msg_id')
            if not msg_id:
                return ResponseUtils.return_fail(AppErrors.ERROR_PARAMETER_MISSING)
            else:
                # 更新数据
                WorkChatData.objects.filter(msg_id=msg_id).update(have_append=False, update_time=timezone.now())

                data = {"msg_id": msg_id}
                return ResponseUtils.return_success("操作成功", data=data)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'更新消息已读状态异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

    # 更新知识库
    @action(methods=['POST'], detail=False)
    def knowledge_update(self, request):
        temp = json.loads(request.body)
        # 获取操作人的用户ID
        userinfo = UserUtils.get_login_user(request)
        username = userinfo.chinese_name if userinfo else 'api'
        user_account = userinfo.staff_no if userinfo else 'api'
        try:
            msg_id = temp.get('msg_id')
            question_title = temp.get('question_title')  # 问题描述
            question_answer = temp.get('question_answer') # 问题答案
            type = temp.get('type',  "0") # 0 否  1 是
            if not msg_id:
                return ResponseUtils.return_fail(AppErrors.ERROR_PARAMETER_MISSING)
            else:
                with transaction.atomic():
                    if type  == "1":
                        # 更新知识库表数据
                        WorkVectorKnowledge.objects.update_or_create(
                            msg_id=msg_id,
                            defaults={
                                'question_title': question_title,
                                'question_answer': question_answer,
                                'create_user': user_account,
                                'update_user': user_account,
                                'create_time': timezone.now(),
                                'update_time': timezone.now()
                            }
                        )
                        # 更新主表状态
                        WorkChatData.objects.filter(msg_id=msg_id).update(is_knowledge=1, update_time=timezone.now())
                        is_knowledge = "是"
                    else:
                        # 更新主表状态
                        WorkChatData.objects.filter(msg_id=msg_id).update(is_knowledge=0, update_time=timezone.now())
                        is_knowledge = "否"

                    logs_to_create = []
                    # 收集日志
                    logs_to_create.append({
                        'msg_id': msg_id,
                        'user_name': username,
                        'user_account': user_account,
                        'log_type': 5,
                        'is_knowledge': is_knowledge
                    })
                    self.batch_create_work_answer_log(logs_to_create)

                data = {"msg_id": msg_id}
                return ResponseUtils.return_success("操作成功", data=data)
        except Exception:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'更新异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

    # 更新问题复盘状态
    @action(methods=['POST'], detail=False)
    def question_review_update(self, request):
        temp = json.loads(request.body)
        try:
            msg_id = temp.get('msg_id')
            is_review = temp.get('is_review')  # 是否复盘
            is_valuable = temp.get('is_valuable') # 是否有训练价值
            if not msg_id:
                return ResponseUtils.return_fail(AppErrors.ERROR_PARAMETER_MISSING)
            else:
                # 更新数据
                WorkChatData.objects.update_or_create(
                    msg_id=msg_id,
                    defaults={
                        'is_review': is_review,
                        'is_valuable': is_valuable,
                        'update_time': timezone.now()
                    }
                )

                data = {"msg_id": msg_id}
                return ResponseUtils.return_success("操作成功", data=data)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'更新问题复盘状态异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

    # 更新值班信息
    @action(methods=['POST'], detail=False)
    def room_duty_update(self, request):
        temp = json.loads(request.body)
        try:
            room_id = temp.get('room_id')  # 群ID
            duty_config = temp.get('duty_config')  # 业务线及值班人配置列表 "duty_config": [{"project_id": "99","duty_user_no": "2908"}]
            if duty_config is None or not room_id:
                return ResponseUtils.return_fail(AppErrors.ERROR_PARAMETER_MISSING)
            else:
                with transaction.atomic():
                    # 删除数据 - 删除该room_id下的所有记录
                    WorkDutyUser.objects.filter(crop_id=settings.CROP_ID, room_id=room_id).delete()

                    # 批量写入新数据
                    results = []
                    for config in duty_config:
                        project_id = config.get('project_id')
                        duty_user_no = config.get('duty_user_no')

                        emp_obj = WorkEmpInfo.objects.filter(emp_account=duty_user_no, crop_id=settings.CROP_ID).first()
                        duty_user = emp_obj.emp_name if emp_obj else duty_user_no

                        # 创建新记录
                        duty_obj = WorkDutyUser.objects.create(
                            crop_id=settings.CROP_ID,
                            room_id=room_id,
                            project_type=project_id,
                            duty_user=duty_user,
                            duty_user_no=duty_user_no,
                            create_time=timezone.now(),
                            update_time=timezone.now()
                        )

                        results.append({
                            "project_id": project_id,
                            "duty_user": duty_user,
                            "duty_user_no": duty_user_no,
                            "id": duty_obj.id
                        })

                    data = {
                        "crop_id": settings.CROP_ID,
                        "room_id": room_id,
                        "results": results
                    }
                return ResponseUtils.return_success("操作成功", data=data)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'更新值班人信息异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

    @action(methods=['GET'], detail=False)
    def get_product_info(self, request):
        """
         查询业务线信息
       :param request:  name ： 非必填  业务线名称/编码
       :return:
       """
        try:
            name = request.GET.get('name', None)

            # 判断name未空则查所有
            if not name:
                product_obj = WorkChatProduct.objects.all()
            # 判断是数字，则直接用编码查询
            elif name.isdigit():
                product_obj = WorkChatProduct.objects.filter(product_id=int(name))
            else:
                product_obj = WorkChatProduct.objects.filter(product_name__icontains=name)
            result_list = []
            # 遍历查询集，并为每个对象创建一个包含 product_id 和 product_name 字段的字典
            for obj in product_obj:
                result_dict = {
                    'product_id': obj.product_id,
                    'product_name': obj.product_name
                }
                result_list.append(result_dict)
            # 返回结果列表（包含多个字典，每个字典对应一个查询结果）
            return ResponseUtils.return_success("查询成功", data=result_list)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

    # 获取句子状态
    @action(methods=['GET'], detail=False)
    def get_robot_status(self, request):
        # 返回状态 1:离线 2:在线 3、4、5:异常
        try:
            address = settings.ROBOT_STATUS_URL
            req = requests.get(url=address, timeout=15)
            response_json = req.json()
            if req.status_code == 200:
                return ResponseUtils.return_success("查询成功", data=response_json)
            else:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, response_json)
        except Exception as e:
            logger.error(f"请求异常: {e}")

    # 句子推送群消息
    @action(methods=['GET'], detail=False)
    def robot_send_msg(self, request):
        """
        句子推送群消息
       :param request:  msg_id ： 必填 消息ID
                        answer: 非必填  回复内容
       :return:
        """
        try:
            address = settings.ROBOT_SEND_MSG_URL
            msg_id = request.GET.get('msg_id')
            answer = request.GET.get('answer', settings.ROBOT_SEND_DEFAULT_MSG)

            if not msg_id:
                return ResponseUtils.return_fail(AppErrors.ERROR_PARAMETER_MISSING)
            # 获取问题信息
            data_obj = WorkChatData.objects.filter(msg_id=msg_id).first()

            # 获取微信联系人ID
            if data_obj.send_from:
                emp_obj = WorkEmpInfo.objects.filter(emp_account=data_obj.send_from, crop_id=settings.CROP_ID).first()
                im_contact_id = emp_obj.im_contact_id if emp_obj else None

            # 获取微信群ID
            room_obj = WorkChatConfig.objects.filter(room_id=data_obj.room_id).first()
            wx_room_id =  room_obj.wx_room_id if room_obj else None

            if data_obj.msg_content and wx_room_id:
                params = {}
                params["query"] = data_obj.msg_content
                params["wx_room_id"] = wx_room_id
                params["im_contact_id"] = im_contact_id
                params["answer"] = answer if answer else settings.ROBOT_SEND_DEFAULT_MSG

                req = requests.get(url=address, params=params, timeout=15)
                response_json = req.json()
                if req.status_code == 200:
                    return ResponseUtils.return_success("消息发送成功", data=response_json)
                else:
                    return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, response_json)
        except Exception as e:
            logger.error(f"参数异常: {e}")
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, e)

    # 定义一个函数来解析日期字符串
    def parse_date(self, date_str):
        # 假设日期格式为 '2025Q1第7周'
        year = int(date_str[:4])
        quarter = int(date_str[5])
        week = int(date_str.split('第')[1].split('周')[0])
        return (year, quarter, week)

    # 售后群反馈趋势统计
    @action(methods=['GET'], detail=False)
    def get_question_statistics(self, request):
        try:
            # 获取请求中的时间范围，如果没有则使用默认近10周的数据
            start_date_str = request.GET.get('start_date', None)
            end_date_str = request.GET.get('end_date', None)

            if start_date_str and end_date_str:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
            else:
                # 获取当前日期，并计算近90天的数据
                end_date = timezone.now().date()
                start_date = end_date - timedelta(days=90)
            # 准备一个空列表来存储结果
            result_list = []

            # 获取所有有效的 room_id,用于过滤未开启可见的群数据
            valid_room_ids = WorkChatConfig.objects.filter(is_valid=True, is_display=True).values_list('room_id',
                                                                                                       flat=True)
            # 循环遍历每个季度周，并计算统计结果
            current_date = start_date
            while current_date <= end_date:
                # 计算今天是周几（0=星期一, 1=星期二, ..., 6=星期日）
                today_weekday = current_date.weekday()

                # 计算到本周四的天数差（如果今天是周四，则差值为0）
                days_to_thursday = (3 - today_weekday) % 7  # 3 因为周四的 weekday 值是 3

                # 计算本周的结束日期（周四）
                week_end_date = current_date + timedelta(days=days_to_thursday)
                # 如果周结束日期大于结束时间，则设置为结束时间
                if week_end_date >= end_date:
                    week_end_date = end_date

                # 过滤数据并计算统计结果
                question_count = WorkChatData.objects.filter(
                    msg_date__range=(current_date, week_end_date),
                    is_valid=True, is_question=True,room_id__in=valid_room_ids, channel= '内部'
                ).values('project_type').annotate(count=models.Count('*'))

                # 遍历统计结果，并构建响应数据
                for qc in question_count:
                    week_number = self.get_week_of_year(current_date)
                    result_dict = {
                        'date': f"{current_date.year}Q{(current_date.month - 1) // 3 + 1}第{week_number}周",
                        'project_type': qc['project_type'],
                        'count': qc['count']
                    }
                    result_list.append(result_dict)

                # 移动到下一个周五
                # 所以下一个周期的开始是本周四后的第一天（周五）
                next_cycle_start_date = week_end_date + timedelta(days=1)
                if next_cycle_start_date > end_date:
                    break  # 如果已经超过了 end_date，则退出循环
                else:
                    # 否则，更新 current_date 为下一个周期的开始日期
                    current_date = next_cycle_start_date

            # 按照业务线进行再次聚合：微商城、OMS、四季蝉、海川、商品、组织，剩下的全部归类为其他
            # 创建一个字典来存储按日期和业务线聚合后的结果
            aggregated_dict = defaultdict(lambda: defaultdict(int))

            # 处理 result_list 为空的情况
            if not result_list:
                aggregated_dict = defaultdict(lambda: defaultdict(int))
            else:
                # 遍历 result_list 进行聚合
                for result in result_list:
                    try:
                        date = result['date']
                        project_type = self.PROJECT_TYPE_CLASSIFY_MAP.get(result['project_type'], "其他")
                        count = result['count']

                        # 累加 count 的值
                        aggregated_dict[project_type][date] += count
                    except KeyError as e:
                        print(f"数据异常: {e}")
                        continue

                # 确定所有唯一的日期,升序排序
                all_dates = sorted(set(date for project_type in aggregated_dict for date in aggregated_dict[project_type]),
                                       key=self.parse_date,
                                       reverse=False)

            # 将聚合结果转换为期望的输出格式
            output = []
            for project_type, dates in aggregated_dict.items():
                date_list = []
                # 遍历所有唯一日期
                for date in all_dates:
                    # 检查该项目类型是否包含该日期，如果不包含则添加计数为0
                    count = dates.get(date, 0)
                    date_list.append({
                        "date": date,
                        "count": count
                    })
                output.append({
                    "project_type": project_type,
                    "dates": date_list
                })
            # 返回成功响应
            return ResponseUtils.return_success("查询成功", data=output)

        except Exception as e:
            # 记录错误日志
            error_info = str(e)  # 或者使用更详细的错误记录方式，如traceback.format_exc()
            logger.error(f'参数异常或查询错误：{error_info}')
            # 返回失败响应
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

    # 定义一个函数来计算给定日期的“周五-周四”周的周次
    def get_week_of_year(self, date):
        # 找到当前年份的第一个周五
        year_start = date.replace(month=1, day=1)
        first_friday = year_start + timedelta(days=(4 - year_start.weekday() + 7) % 7)  # 确保总是得到正数天数差
        # 判断当前日期是否是周五
        if date.weekday() == 4:  # 如果是周五，则将其划分到下一周
            this_friday = date + timedelta(days=7)
        else:  # 否则，找到最近的下一个周五
            # this_friday = date - timedelta(days=(date.weekday() - 4 + 7) % 7)
            this_friday = date + timedelta(days=(4 - date.weekday() + 7) % 7)
        # 计算周数（从第一个周五开始，加1以满足用户从1开始计数的期望）
        week_number = ((this_friday - first_friday).days // 7) + 1

        return week_number

    # 售后群AI回复数据统计(统计业务线人工回复和自动回复的数量)
    @action(methods=['GET'], detail=False)
    def get_answer_statistics(self, request):
        try:
            start_date_str = request.GET.get('start_date', None)
            end_date_str = request.GET.get('end_date', None)
            if start_date_str and end_date_str:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
            else:
                # 获取当前日期，并计算近90天的数据
                end_date = timezone.now().date()
                start_date = end_date - timedelta(days=90)

            # 获取所有有效的 room_id,用于过滤未开启可见的群数据
            valid_room_ids = WorkChatConfig.objects.filter(is_valid=True, is_display=True).values_list('room_id',
                                                                                                       flat=True)

            question_list = WorkChatData.objects.filter(msg_date__range=(start_date, end_date),is_send=True,is_question=True, is_valid=True, room_id__in=valid_room_ids, question_type__in=[0,4], channel= '内部').values('msg_id','project_type')
            answer_obj = WorkQuestionAnswer.objects.filter(is_valid=True)
            # 批量获取所有问题 对应的答案对象
            msgids = [item['msg_id'] for item in question_list if item['msg_id']]
            answers = answer_obj.filter(msg_id__in=msgids, is_valid=True)
            answers_by_msgids = {answer.msg_id: answer for answer in answers}
            # 使用defaultdict来统计各业务线的自动回复和人工回复数量
            statistics = defaultdict(lambda: {'total':0, 'auto_count': 0, 'manual_count': 0})

            for question in question_list:
                msg_id = question['msg_id']
                # project_type = question['project_type']
                project_name = self.PROJECT_TYPE_CLASSIFY_MAP.get(question['project_type'], "其他")

                if msg_id in answers_by_msgids:
                    statistics[project_name]['total'] +=1
                    answer = answers_by_msgids[msg_id]
                    if answer.is_auto:
                        statistics[project_name]['auto_count'] += 1
                    else:
                        statistics[project_name]['manual_count'] += 1

            # 准备返回的数据格式
            result_list = []
            for project_name, stats in statistics.items():
                result_list.append({
                    'project_name': project_name,
                    'total':stats['total'],
                    'auto_send_count': stats['auto_count'],
                    'manual_send_count': stats['manual_count']
                })
            # 降序排序
            result_list.sort(key=lambda x: x['total'], reverse=True)
            return ResponseUtils.return_success("查询成功", data=result_list)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

    # 售后群问题处理及时率统计
    @action(methods=['GET'], detail=False)
    def get_question_time_statistics(self, request):
        try:
            start_date_str = request.GET.get('start_date', None)
            end_date_str = request.GET.get('end_date', None)
            comp_user = request.GET.get('comp_user', None)
            query_data = {}
            emp_objs = WorkEmpInfo.objects.filter()
            if comp_user:
                if comp_user.isdigit():
                    query_data['comp_user'] = comp_user
                else:
                    emp_obj =emp_objs.filter(Q(emp_name__icontains=comp_user))
                    if emp_obj:
                        query_data['comp_user__in'] = list(emp_obj.values_list('emp_account', flat=True))

            if start_date_str and end_date_str:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
            else:
                # 查最近一周的数据
                end_date = timezone.now().date()
                start_date = end_date - timedelta(days=90)
            query_data['msg_date__range'] = (start_date, end_date)
            query_data['is_valid'] = True
            query_data['is_send'] = True

            # 获取所有有效的 room_id,用于过滤未开启可见的群数据
            valid_room_ids = WorkChatConfig.objects.filter(is_valid=True, is_display=True).values_list('room_id',
                                                                                                       flat=True)
            query_data['room_id__in'] = valid_room_ids

            question_list = WorkChatData.objects.filter(**query_data)

            answer_obj = WorkQuestionAnswer.objects.filter(is_valid=True)
            # 批量获取所有问题 对应的答案对象
            msgids = [item.msg_id for item in question_list if item.msg_id is not None]
            answers = answer_obj.filter(msg_id__in=msgids, is_valid=True)
            answers_by_msgids = {answer.msg_id: answer for answer in answers}

            # 批量获取员工
            emp_ids = set(question_list.values_list('comp_user', flat=True))
            emp_ids_list = list(emp_ids)
            empinfos = emp_objs.filter(emp_account__in=emp_ids_list)
            emp_by_ids = {empinfo.emp_account: empinfo for empinfo in empinfos}

            # 使用defaultdict来统计各处理人的问题处理时间
            statistics = defaultdict(lambda: {'total_time': 0, 'count': 0})
            for item in question_list:
                if item.msg_id in answers_by_msgids and answers_by_msgids[item.msg_id].is_auto == False:
                    comp_user = item.comp_user
                    comp_time = item.comp_time
                    if comp_time ==None:
                        comp_time = timezone.now()
                    # 将时间差转换为分钟
                    total_time = (comp_time - item.create_time).total_seconds() / 60
                    if comp_user:
                        statistics[comp_user]['total_time'] += total_time
                        statistics[comp_user]['count'] += 1

            # 准备返回的数据格式
            result_list = []
            for user, times in statistics.items():
                avg_time = times['total_time'] / times['count'] if times['count'] > 0 else 0
                emp_info = emp_by_ids.get(user)
                # 如果 emp_info 是 None，说明用户不存在于字典中，使用 user 作为默认值
                emp_name = emp_info.emp_name if emp_info is not None else user
                result_list.append({
                    'comp_user': user,
                    'comp_name':emp_name,
                    'total_time': int(times['total_time']),
                    'count': times['count'],
                    'avg_time': int(avg_time)
                })
            # 按照avg_time升序排序
            sorted_result_list = sorted(result_list, key=lambda x: x['avg_time'], reverse=False)
            return ResponseUtils.return_success("查询成功", data=sorted_result_list)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

    # 获取问题数据接口，提供给HIM调用
    @action(methods=['POST'], detail=False)
    def get_question_data(self, request):
        """
        提供给him的问题查询接口
        参数说明：
            start_date: 开始时间 (必填)
            end_date: 结束时间 (必填)
            send_from: 提交人 (非必填)
            belong_area: 所属大区 (非必填)
            comp_user: 处理人 (非必填)
            project_type: 业务归属 (非必填)
            status: [] 处理状态(非必填)   0: "待受理",1: "已处理",2: "解决中",3: "已关闭", 4: "无需处理"
            question_type: [] 问题类型 (非必填)    0: "咨询类",1: "支持类",2: "BUG",3: "需求类"
            belong_mer_code: 所属商户 (非必填)
            msg_content: 问题描述 (非必填)
            is_auto: 是否AI回答 (非必填)
            pagenum: 页码 (必填)
            pagesize: 条数 (必填)
        :return:
        """
        try:
            temp = json.loads(request.body)

            # 设置默认值并验证必填参数
            page = int(temp.get('pagenum', 1))
            limit = int(temp.get('pagesize', 50))

            # 初始化查询条件
            query_params = {
                'start_date': temp.get('start_date', None),
                'end_date': temp.get('end_date', None),
                'send_from': temp.get('send_from', ''),
                'belong_area': temp.get('belong_area', ''),
                'comp_user': temp.get('comp_user', ''),
                'project_type': temp.get('project_type', []),
                'status': temp.get('status', []),
                'question_type': temp.get('question_type', []),
                'belong_mer_code': temp.get('belong_mer_code', ''),
                'msg_content': temp.get('msg_content', ''),
                'is_auto': temp.get('is_auto', None),
                'is_send': temp.get('is_send', None)
            }
            # 校验开始时间和结束时间必填
            if not query_params['start_date'] or not query_params['end_date']:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, '请输入开始时间和结束时间')

            if query_params['start_date'] and query_params['end_date']:
                start_date = datetime.strptime(query_params['start_date'], '%Y-%m-%d').date()
                end_date = datetime.strptime(query_params['end_date'], '%Y-%m-%d').date()
                if end_date -start_date > timedelta(days=31):
                    return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, '时间跨度不能超过31天')

            answer_obj = WorkQuestionAnswer.objects.filter(is_valid=True)
            emp_obj = WorkEmpInfo.objects.filter()
            # 构建Q对象
            q_objects = Q(is_valid=True, is_question=True)
            if query_params['start_date'] and query_params['end_date']:
                q_objects &= Q(msg_date__range=(query_params['start_date'], query_params['end_date']))

            if query_params['send_from']:
                # 判断是数字，则直接用编码查询
                if query_params['send_from'].isdigit():
                    q_objects &= Q(send_from=query_params['send_from'])
                else:
                    sendids = emp_obj.filter(emp_name__icontains=query_params['send_from']).values_list('emp_account',
                                                                                                        flat=True)
                    q_objects &= Q(send_from__in=sendids)

            if query_params['belong_area']:
                q_objects &= Q(belong_area__icontains=query_params['belong_area'])

            if query_params['comp_user']:
                # 判断是数字，则直接用编码查询
                if query_params['comp_user'].isdigit():
                    q_objects &= Q(comp_user=query_params['comp_user'])
                else:
                    empids = emp_obj.filter(emp_name__icontains=query_params['comp_user']).values_list('emp_account',
                                                                                                       flat=True)
                    q_objects &= Q(comp_user__in=empids)

            if query_params['project_type']:
                q_objects &= Q(project_type__in=query_params['project_type'])

            if query_params['status']:
                q_objects &= Q(status__in=query_params['status'])

            if query_params['question_type']:
                q_objects &= Q(question_type__in=query_params['question_type'])

            if query_params['belong_mer_code']:
                q_objects &= Q(belong_mer_code=query_params['belong_mer_code'])

            if query_params['is_auto']:
                answerids = answer_obj.filter(is_auto=query_params['is_auto']).values_list(
                    'msg_id', flat=True)
                q_objects &= Q(msg_id__in=answerids)

            if query_params['is_send']:
                q_objects &= Q(is_send=query_params['is_send'])

            if query_params['msg_content']:
                search_terms = query_params['msg_content'].split()  # 按空格分割成多个词
                q_objects &= Q(msg_content__icontains=search_terms[0])  # 第一个词必须匹配

                for term in search_terms[1:]:
                    q_objects &= Q(msg_content__icontains=term)  # 后续词也必须匹配

            # 查询并分页
            workchatdata_obj = WorkChatData.objects.filter(q_objects, is_valid=True, is_question=True).order_by('-seq')
            paginator = Paginator(workchatdata_obj, limit)
            data = paginator.page(page)
            data_list = list(data.object_list.values())
            msgids = [item['msg_id'] for item in data_list if item['msg_id']]

            # 答案数据
            answers = answer_obj.filter(msg_id__in=msgids)
            answers_by_msgids = {answer.msg_id: answer for answer in answers}

            # 员工信息
            emp_ids = set(
                [item['duty_user'] for item in data_list if item['duty_user']] +
                [item['comp_user'] for item in data_list if item['comp_user']] +
                [item['send_from'] for item in data_list if item['send_from']]
            )
            emp_by_ids = {
                emp.emp_account: emp
                for emp in WorkEmpInfo.objects.filter(emp_account__in=emp_ids)
            }

            # 批量获取群信息
            room_ids = [item['room_id'] for item in data_list if item['room_id']]
            roominfos = WorkChatConfig.objects.filter(is_valid=True, room_id__in=room_ids, is_display=True)
            roominfo_by_roomids = {roominfo.room_id: roominfo for roominfo in roominfos}

            for item in data_list:
                msg_id = item['msg_id']
                # 答案信息
                answer = answers_by_msgids.get(msg_id)
                item['answers'] = model_to_dict(answer) if answer else {}

                # 员工信息
                duty_user = item['duty_user']
                emp_obj = emp_by_ids.get(duty_user)
                item['duty_user_name'] = emp_obj.emp_name if emp_obj else duty_user

                comp_user = item['comp_user']
                emp_obj = emp_by_ids.get(comp_user)
                item['comp_user_name'] = emp_obj.emp_name if emp_obj else comp_user

                send_from = item['send_from']
                emp_obj = emp_by_ids.get(send_from)
                item['send_from_name'] = emp_obj.emp_name if emp_obj else send_from

                # 消息内容
                item['msg_content'] = self.generate_msg_content(item['msg_type'], msg_id, item['msg_content'])

                # 获取群名称
                if item['room_id'] in roominfo_by_roomids:
                    item['room_name'] = roominfo_by_roomids[item['room_id']].room_name
                else:
                    item['room_name'] = item['room_id']

                # 增加处理时长字段
                # 状态为无需处理时，处理时长为0
                if item['status'] == 4:
                    item['processing_time'] = 0
                elif item['comp_time']:
                    item['processing_time'] = int((item['comp_time'] - item['create_time']).total_seconds() / 60)
                else:
                    item['processing_time'] = int((timezone.now() - item['create_time']).total_seconds() / 60)

                # 创建时间格式化
                item['create_time'] = item['create_time'].strftime('%Y-%m-%d %H:%M:%S')

                # 增加附件
                item['attachment'] = self.get_attachments(msg_id)

            response_data = {
                'pagenum': page,
                'pagesize': limit,
                'total': paginator.count,
                'questions': data_list
            }
            return ResponseUtils.return_success("查询成功", response_data)

        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

    # 导出问题数据
    @action(methods=['POST'], detail=False)
    def export_question_data(self, request):
        """
        导出会话记录列表（Excel格式）
        参数与 question_list 完全一致
        """
        try:
            # 复用 question_list 的查询逻辑
            list_response = self.question_list(request)
            response_data = json.loads(list_response.content.decode('utf-8'))
            if response_data.get('meta', {}).get('status') != 200:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, response_data)

            data = response_data['data']['questions']

            # 创建Excel工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "问题列表"

            # 定义表头
            headers = [
                "消息ID","问题描述", "问题类型", "AI回答内容", "人工回答内容", "问题创建时间", "群名称", "最终产品线", "AI识别的产品线",
                "回答方式",  "问题处理人", "是否复盘", "是否有训练价值", "是否需要更新到知识库", "更新问题描述", "更新问题答案"
            ]
            ws.append(headers)

            # 设置表头样式（居中文本、加粗字体、添加边框、底色灰色、字体调大）
            bold_font = Font(bold=True, size=15)  # 字体调大
            center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            thin_border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'),
                                 bottom=Side(style='thin'))
            grey_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')  # 灰色底色

            for cell in ws[1]:
                cell.font = bold_font
                cell.alignment = center_alignment
                cell.border = thin_border
                cell.fill = grey_fill  # 底色灰色

            # 列宽配置（单位：字符宽度）
            column_widths = {
                "A": 35,  # 消息ID
                "B": 40,  # 问题描述
                "C": 20,  # 问题类型
                "D": 40,  # AI回答内容
                "E": 40,  # 人工回答内容
                "F": 20,  # 问题创建时间
                "G": 20,  # 群名称
                "H": 10,  # 最终产品线
                "I": 10,  # AI识别的产品线
                "J": 15,  # 回答方式
                "K": 20,  # 问题处理人
                "L": 15,  # 是否复盘
                "M": 15,  # 是否有训练价值
                "N": 20,  # 是否需要更新到知识库
                "O": 40,  # 更新问题描述
                "P": 40,  # 更新问题答案
            }

            for col_letter, width in column_widths.items():
                ws.column_dimensions[col_letter].width = width

            # 自动换行 + 顶部对齐
            wrap_alignment = Alignment(wrap_text=True, vertical='top')
            for row in ws.iter_rows(min_row=2):  # 从第2行开始（跳过表头）
                for cell in row:
                    cell.alignment = wrap_alignment

            # 批量查询日志
            msg_ids = [item['msg_id'] for item in data]
            logs = WorkAnswerLog.objects.filter(msg_id__in=msg_ids, log_type=3)
            log_map = {log.msg_id: log for log in logs}

            # 填充数据
            for item in data:
                # 获取问题描述
                text_content = '\n'.join(
                    str(content['media_content'])
                    for content in item.get('msg_content', [])
                    if isinstance(content, dict) and content.get('media_type') == 'text'
                    and content.get('media_content')  # 过滤空内容
                )
                # AI识别的业务线
                ai_project_type = ""
                log = log_map.get(item['msg_id'])
                if log and log.log_content:
                    try:
                        ai_project_type = log.log_content.split("业务线：")[1].split()[0]
                    except (IndexError, AttributeError):
                        match = re.search(r"业务线[：:]\s*(\S+)", log.log_content)
                        if match:
                            ai_project_type = match.group(1)

                ws.append([
                    item.get('msg_id', ''),
                    text_content,
                    self.QUESTION_TYPE_MAP.get(item.get('question_type'), ''),
                    item.get('answers', {}).get('question_answer', ''),
                    item.get('answers', {}).get('question_answer_manual', ''),
                    item.get('msg_time', ''),
                    item.get('room_name', ''),
                    self.get_project_type_map().get(item.get('project_type'), ''),
                    ai_project_type,
                    "AI" if item.get('answers', {}).get('is_auto') == 1 else "人工",
                    f"{item.get('comp_user_name', '')}{item.get('comp_user', '')}",
                    "是" if item.get('is_review') == 1 else "否",
                    "是" if item.get('is_valuable') == 1 else "否",
                    "是" if item.get('is_knowledge') == 1 else ("已更新" if item.get('is_knowledge') == 2 else "否"),
                    item.get('knowledge', {}).get('question_title', ''),
                    item.get('knowledge', {}).get('question_answer', ''),
                ])

            # 生成带时间戳的文件名
            timestamp = timezone.now().strftime("%Y%m%d_%H%M%S")
            filename = f"question_data_{timestamp}.xlsx"
            # # 保存到本地，用于调试
            # export_dir = './download'
            # local_filepath = os.path.join(export_dir, filename)
            # wb.save(local_filepath)

            excel_buffer = BytesIO()
            wb.save(excel_buffer)
            excel_buffer.seek(0)
            # 生成响应
            response = HttpResponse(
                excel_buffer.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                headers={'Content-Disposition': f'attachment; filename="{filename}"'},
            )
            excel_buffer.close()
            return response

        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'导出异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, "导出失败")

    # 导出问题归档数据
    @action(methods=['POST'], detail=False)
    def export_question_archive_data(self, request):
        """
        导出归档数据列表（Excel格式）
        参数与 question_list 完全一致
        """
        try:
            # 复用 question_list 的查询逻辑
            list_response = self.question_list(request)
            response_data = json.loads(list_response.content.decode('utf-8'))
            if response_data.get('meta', {}).get('status') != 200:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, response_data)

            data = response_data['data']['questions']

            # 创建Excel工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "问题归档数据"

            # 定义表头
            headers = [
                "问题时间", "问题描述", "所属商户", "ERP类型",
                "处理人", "问题回复", "问题类型", "业务归属", "售后群名称", "提交人", "所属大区"
            ]
            ws.append(headers)

            # 设置表头样式（居中文本、加粗字体、添加边框、底色灰色、字体调大）
            bold_font = Font(bold=True, size=15)  # 字体调大
            center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            thin_border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'),
                                 bottom=Side(style='thin'))
            grey_fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')  # 灰色底色

            for cell in ws[1]:
                cell.font = bold_font
                cell.alignment = center_alignment
                cell.border = thin_border
                cell.fill = grey_fill  # 底色灰色

            # 列宽配置（单位：字符宽度）
            column_widths = {
                "A": 20,  # 问题时间
                "B": 40,  # 问题描述
                "C": 10,  # 所属商户
                "D": 15,  # ERP类型
                "E": 15,  # 处理人
                "F": 40,  # 问题回复
                "G": 15,  # 问题类型
                "H": 15,  # 业务归属
                "I": 30,  # 售后群名称
                "J": 15,  # 提交人
                "K": 15   # 所属大区
            }

            for col_letter, width in column_widths.items():
                ws.column_dimensions[col_letter].width = width

            # 自动换行 + 顶部对齐
            wrap_alignment = Alignment(wrap_text=True, vertical='top')
            for row in ws.iter_rows(min_row=2):  # 从第2行开始（跳过表头）
                for cell in row:
                    cell.alignment = wrap_alignment

            # 填充数据
            for item in data:
                # 获取问题描述
                text_content = '\n'.join(
                    str(content['media_content'])
                    for content in item.get('msg_content', [])
                    if isinstance(content, dict) and content.get('media_type') == 'text'
                    and content.get('media_content')  # 过滤空内容
                )
                # 商户类型
                mer_type =item.get('mer_type', None)
                if mer_type is not None:
                    mer_type= self.ERP_TYPE_MAP.get(int(mer_type))

                ws.append([
                    item.get('msg_time', ''),
                    text_content,
                    item.get('belong_mer_code', ''),
                    mer_type,
                    f"{item.get('comp_user_name', '')}{item.get('comp_user', '')}",
                    item.get('answers', {}).get('question_answer_manual', ''),
                    self.QUESTION_TYPE_MAP.get(item.get('question_type'), ''),
                    self.get_project_type_map().get(item.get('project_type'), ''),
                    item.get('room_name', ''),
                    f"{item.get('send_from_name', '')}{item.get('send_from', '')}",
                    item.get('belong_area', '')
                ])

            # 生成带时间戳的文件名
            timestamp = timezone.now().strftime("%Y%m%d_%H%M%S")
            filename = f"question_archive_data_{timestamp}.xlsx"
            # # 保存到本地，用于调试
            # export_dir = './download'
            # local_filepath = os.path.join(export_dir, filename)
            # wb.save(local_filepath)

            excel_buffer = BytesIO()
            wb.save(excel_buffer)
            excel_buffer.seek(0)
            # 生成响应
            response = HttpResponse(
                excel_buffer.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                headers={'Content-Disposition': f'attachment; filename="{filename}"'},
            )
            excel_buffer.close()
            return response

        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'导出异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, "导出失败")