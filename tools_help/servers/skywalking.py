import json
import re
import time
from datetime import datetime
from typing import List

import requests

from core.utils import DateUtils
from tools_help.dto import *
from tools_help.exceptions import SkyWalkingError
from tools_help.models import ServiceConfig
from interface_test.models import InterfaceDetail, InterfaceTrafficStatistics, InterfaceTrafficStatisticsLog
from tools_help.servers import swagger
# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()

PRO_BUSINESSES_GATEWAY = 'cHJvX2J1c2luZXNzZXMtZ2F0ZXdheS5qYXI=.1'
GREY_BUSINESSES_GATEWAY = 'Z3JleV9idXNpbmVzc2VzLWdhdGV3YXkuamFy.1'
TEST_BUSINESSES_GATEWAY = 'dGVzdF9idXNpbmVzc2VzLWdhdGV3YXkuamFy.1'
DEV_BUSINESSES_GATEWAY = 'ZGV2X2J1c2luZXNzZXMtZ2F0ZXdheS5qYXI=.1'

# 获取SkyWalking信息的URL，TODO 需要根据环境切换
_BASE_NO_PRO_URL = 'http://skywalking.dev.pt.hydee.cn/graphql'
_BASE_PRO_URL = 'http://skywalking.pt.hydee.cn/graphql'

# SkyWalking 登录鉴权
_BASE_NO_PRO_Authorization = 'Basic ZGV2OnRoYWhraXF1NmllbmdlaVZpZA=='
_BASE_PRO_Authorization = 'Basic ZGV2OnphZWNoM1NvaGxhaVJlaUtlaQ=='


# 获取服务的请求参数
_SERVICE_REQUEST_QUERY = "query queryServices($layer: String!) {\n  services: listServices(layer: $layer) {\n  base64name: id, name: name }\n  }"
# 获取服务接口的请求参数
_SERVICE_INTERFACE_REQUEST_QUERY = "query queryEndpoints($serviceId: ID!) {\n  endpoints: findEndpoint(serviceId: $serviceId, limit: 20) { name: name}\n}"
# 获取服务接口统计的请求参数
_SERVICE_INTERFACE_STATISTICS_REQUEST_QUERY = "query queryData($duration: Duration!,$expression0: String!,$entity0: Entity!) {expression0: execExpression(expression: $expression0, entity: $entity0, duration: $duration){ results { values { name: id value } } }}"



def search_all_service(is_pro) -> List[SkyWalkingServiceResponse.Detail]:
    """ 查询所有服务 """
    detail = SkyWalkingServiceRequest("GENERAL")
    response = __search_skywalking(is_pro, _SERVICE_REQUEST_QUERY, detail, service_response_deserialize_rule)
    return response.data.services


def search_service_all_interface(env, is_pro, service, start, end) -> List[SkyWalkingServiceInterfaceResponse.Detail]:
    """ 查询服务下所有接口 """
    try:
        time.sleep(5)
        duration = SkyWalkingServiceInterfaceRequest.Duration(start, end, 'HOUR')
        detail = SkyWalkingServiceInterfaceRequest(service, duration)
        response = __search_skywalking(is_pro, _SERVICE_INTERFACE_REQUEST_QUERY, detail,
                                       service_interface_response_deserialize_rule)
        return response.data.endpoints
    except Exception as e:
        logger.error('[%s]环境获取服务数据失败！异常[%s]', env, e)
        return []


def search_service_all_interface_statistics(is_pro, service, start, end) -> List[
    SkyWalkingServiceInterfaceStatisticsResponse.Detail]:
    """ 查询服务下所有接口统计 """
    duration = SkyWalkingServiceInterfaceStatisticsRequest.Duration(start, end, 'HOUR')
    entity = SkyWalkingServiceInterfaceStatisticsRequest.Entity(service)
    detail = SkyWalkingServiceInterfaceStatisticsRequest(entity, duration)
    response = __search_skywalking(is_pro, _SERVICE_INTERFACE_STATISTICS_REQUEST_QUERY, detail,
                                   service_interface_statistics_response_deserialize_rule)
    return response.data.details


def deduplication_interface(interfaces):
    interface_dict = {}  # key-服务名，value[Set]-接口列表
    interface_prefix_dict = {}  # key-接口前缀，value[Set]-接口列表

    for interface in interfaces:
        pattern = r'/(?P<service>.*?)/(?P<url>.*)'
        match = re.search(pattern, interface.name)
        if not match:
            continue

        service = match.group('service')
        url = '/' + match.group('url')
        url = re.sub(r'(?<=/)\d+(?=/|$)', swagger.URL_PLACEHOLDER, url)  # 替换商户编码等数字类占位符
        url = re.sub(r'(?<=/)[a-zA-Z0-9]{32}(?=/|$)', swagger.URL_PLACEHOLDER, url)  # 替换UID等占位符

        if service not in interface_dict:
            interface_dict[service] = set()
        interface_dict.get(service).add(url)

        # 路径中包含2个以上的数字
        urls = url.rsplit('/', 1)
        if not re.search(r'\d{2,}', urls[1]):
            continue

        url_prefix = service + urls[0]
        if url_prefix not in interface_prefix_dict:
            interface_prefix_dict[url_prefix] = set()
        interface_prefix_dict.get(url_prefix).add(url)

    # 目录层级一致、最后一个路径不一致且包含数字、匹配次数达到10 则去重
    for url_prefix, url_set in interface_prefix_dict.items():
        if len(url_set) < 10:
            continue

        urls = url_prefix.split('/', 1)
        url_template = '/' + urls[1] + '/' + swagger.URL_PLACEHOLDER
        interfaces = interface_dict[urls[0]]
        interfaces.add(url_template)  # 新增重复URL的模版URL
        logger.info('删除重复的URL[%s]', url_set)
        for url in url_set:  # 删除重复的URL
            interfaces.discard(url)

    return interface_dict


def save_interface_statistics(service_name, begin, end):
    # 跳过无需处理的服务
    service_match = re.search(r'(?P<product>.*)_(?P<service>.*).jar', service_name)
    skywalking_service = service_match.group('service') if service_match else service_name
    service_config = ServiceConfig.objects.filter(skywalking_service=skywalking_service, enable=True).first()
    if service_config is None:
        return

    # 1、获取接口访问频次
    interface_statistics = search_service_all_interface_statistics(True, service_name, begin, end)
    if len(interface_statistics) == 0:
        return

    logger.info('生产环境获取服务[%s]数据[%s]条接口统计', service_name, len(interface_statistics))
    current_date = datetime.now().strftime("%Y-%m-%d")
    current_time = DateUtils.get_current_time()

    # 2、将接口信息存入统计表
    deduplication_interfaces = {}
    no_macth_interfaces = {}
    add_interface_statistics = list()
    add_interface_statistics_log = list()
    update_interface_statistics = list()

    # 版本号数组
    versions = ['1.0', '2.0', '1.0.0']

    for item in interface_statistics:
        # 跳过访问次数为0的接口
        access_count = int(item.value) * 60
        if access_count == 0:
            continue

        # 跳过无效的接口
        interface_match = re.search(r'(?P<method>.*):(?P<url>.*)', item.name)
        if interface_match is None:
            continue

        # 跳过不在swagger接口中的接口
        method = interface_match.group('method')
        url = interface_match.group('url')

        for version in versions:

            # 把版本号替换查一次
            match_url = replace_url(url, version)
            db_interface = InterfaceDetail.objects.filter(service_config_id=service_config.id, interface_way=method,
                                                      interface_address=match_url).first()
            # 如果有匹配结果，则退出循环
            if db_interface is not None:
                break

        # 未匹配到的接口,记录日志表，用于分析
        if db_interface is None:
            # 跳过重复的接口
            no_macth_interface_info =str(service_config.id) + '-' + method + '-' + url + '-' + match_url
            if no_macth_interface_info in no_macth_interfaces:
                continue
            # 记录未匹配的接口数据
            not_match_interface = InterfaceTrafficStatisticsLog.objects.filter(service_config_id=service_config.id,
                                                                               interface_way=method,
                                                                               old_interface_address=url,
                                                                               new_interface_address=match_url).first()
            if not_match_interface is None:
                db_not_match_interface = InterfaceTrafficStatisticsLog(old_interface_address=url,
                                                                       new_interface_address=match_url,
                                                                       interface_way=method,
                                                                       service_config_id=service_config.id,
                                                                       create_time=current_time,
                                                                       )
                add_interface_statistics_log.append(db_not_match_interface)
            # 将操作的接口放入缓存，便于后续跳过重复的统计接口
            no_macth_interfaces[no_macth_interface_info] = str(service_config.id) + '-' + method + '-' + url + '-' + match_url
            continue

        # 跳过重复的接口
        interface_id = db_interface.id
        if interface_id in deduplication_interfaces:
            deduplication_interfaces[interface_id].access_count += access_count
            continue

        # 新增或更新接口统计数据
        db_interface_statistics = InterfaceTrafficStatistics.objects.filter(interface_id=interface_id,
                                                                            statistics_date=current_date).first()
        if db_interface_statistics is None:
            db_interface_statistics = InterfaceTrafficStatistics(interface_id=interface_id, access_count=access_count,
                                                                 statistics_date=current_date, create_time=current_time,
                                                                 update_time=current_time)
            add_interface_statistics.append(db_interface_statistics)
        else:
            db_interface_statistics.access_count = db_interface_statistics.access_count + access_count
            db_interface_statistics.update_time = current_time
            update_interface_statistics.append(db_interface_statistics)

        # 将操作的接口放入缓存，便于后续跳过重复的统计接口
        deduplication_interfaces[interface_id] = db_interface_statistics

    # 不存在则新增统计数据
    if len(add_interface_statistics) > 0:
        InterfaceTrafficStatistics.objects.bulk_create(add_interface_statistics)
    # 存在则更新统计数据
    if len(update_interface_statistics) > 0:
        InterfaceTrafficStatistics.objects.bulk_update(update_interface_statistics,
                                                       fields=['access_count', 'update_time'])
    # 未匹配的接口记录至日志表
    if len(add_interface_statistics_log) > 0:
        InterfaceTrafficStatisticsLog.objects.bulk_create(add_interface_statistics_log)


def __search_skywalking(is_pro, query, variables, callback) -> SkyWalkingResponse:
    """ 查询Skywalking中数据 """

    # 组装请求数据
    url = _BASE_PRO_URL if is_pro else _BASE_NO_PRO_URL
    Authorization = _BASE_PRO_Authorization if is_pro else _BASE_NO_PRO_Authorization
    body = SkyWalkingRequest(query, variables)
    body_json = json.dumps(body, default=lambda obj: obj.__dict__)
    headers = {'Content-Type': 'application/json', 'Authorization': Authorization}

    # 请求skywalking服务器
    response = requests.post(url, data=body_json, headers=headers)
    if response.status_code != 200:
        logger.error("访问SkyWalking数据失败，响应状态码[%s]，响应内容[%s]", response.status_code, response.text)
        raise SkyWalkingError("访问SkyWalking数据失败")

    # 反序列化所有服务信息
    return json.loads(response.text, object_hook=callback)


def service_response_deserialize_rule(obj):
    """ 反序列化服务响应数据 """

    if 'data' in obj:
        return SkyWalkingResponse(obj.get('data'))
    elif 'services' in obj:
        return SkyWalkingServiceResponse(obj.get('services'))
    elif 'base64name' in obj:
        return SkyWalkingServiceResponse.Detail(obj.get('base64name'), obj.get('name'))
    else:
        return obj


def service_interface_response_deserialize_rule(obj):
    """ 反序列化服务接口响应数据 """

    if 'data' in obj:
        return SkyWalkingResponse(obj.get('data'))
    elif 'endpoints' in obj:
        return SkyWalkingServiceInterfaceResponse(obj.get('endpoints'))
    elif 'name' in obj:
        return SkyWalkingServiceInterfaceResponse.Detail(obj.get('name'))
    else:
        return obj


def service_interface_statistics_response_deserialize_rule(obj):
    """ 反序列化服务接口统计响应数据 """

    if 'data' in obj:
        return SkyWalkingResponse(obj.get('data').get('expression0').get('results')[0])
    elif 'values' in obj:
        return SkyWalkingServiceInterfaceStatisticsResponse(obj.get('values'))
    elif 'name' in obj:
        return SkyWalkingServiceInterfaceStatisticsResponse.Detail(obj.get('name'), obj.get('value'))
    else:
        return obj

# 定义一个函数来执行替换操作
def replace_url(url, version):
    # 替换API版本号
    match_url = re.sub(r'\${api.[a-zA-Z0-9]+}', version, url)
    # 替换商户编码等占位符
    match_url = re.sub(r'({[a-zA-Z0-9]+})|([0-9]{6})', swagger.URL_PLACEHOLDER, match_url)
    return match_url