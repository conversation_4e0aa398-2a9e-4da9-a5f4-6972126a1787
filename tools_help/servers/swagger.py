import json
import re
from datetime import datetime
from typing import List

import requests

from tools_help.dto import SwaggerInterface, SwaggerField
from tools_help.exceptions import SwaggerError
from interface_test.models import InterfaceDetail
# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()

# K8S 域名模版
DOMAIN_TEMPLATE = '%s.svc.k8s.dev.%s'
URL_PLACEHOLDER = '{num}'

# 获取开放环境swagger信息的URL
_PATH = 'http://%s/v2/api-docs'
_URL_TEMPLATE = '%s_%s'
_URL_PATTERN = r'{(\w+)}'
_MODEL_PATTERN = r'«(?P<model>.*)»'
_DEFINITION_PATTERN = '#/definitions/'
_SUPPORT_TYPE = {'array', 'object', 'string', 'integer', 'number', 'boolean'}
_FIELD_PATH = '%s.%s'



def save_swagger_interface_by_service(service_config):
    now = datetime.now()
    """ 根据服务配置获取该服务所有接口 """

    # 1、查询并解析swagger中接口数据
    interface_dict = _search_service_all_interface(service_config.swagger_domain)

    # 2、去掉已存在的接口
    service = service_config.service
    interface_details = InterfaceDetail.objects.filter(service=service)
    for detail in interface_details:
        interface_dict.pop(_URL_TEMPLATE % (detail.interface_way, detail.interface_address), None)

    # 3、跳过无新增接口的服务
    if len(interface_dict) == 0:
        return

    # 4、批量组装新增数据
    new_interface_details = list()
    for i in interface_dict.values():
        query_str = json.dumps(i.query, ensure_ascii=False, default=lambda obj: obj.__dict__)
        header_str = json.dumps(i.header, ensure_ascii=False, default=lambda obj: obj.__dict__)
        body_str = json.dumps(i.body, ensure_ascii=False)
        new_interface_details.append(
            InterfaceDetail(service_config_id=service_config.id, service=service, interface_class=i.clazz,
                            interface_way=i.method, interface_address=i.url, interface_description=i.description,
                            interface_parameter=query_str, interface_header=header_str, interface_body=body_str,
                            is_handle=0, create_time=now))

    # 5、将新增数据批量入库
    InterfaceDetail.objects.bulk_create(new_interface_details)


def _search_service_all_interface(swagger_domain):
    # 请求swagger服务器
    headers = {'Content-Type': 'application/json'}
    domain = _PATH % swagger_domain

    logger.info('请求swagger服务[%s]获取接口数据', domain)
    response = requests.post(domain, headers=headers)
    if response.status_code != 200:
        logger.error("访问swagger数据失败，响应状态码[%s]，响应内容[%s]", response.status_code, response.text)
        raise SwaggerError("访问swagger数据失败")

    # 反序列化所有服务信息
    try:
        response_dict = json.loads(response.text)
    except json.JSONDecodeError:
        # 处理swagger用户自定义案例中存在不规范的json数据
        new_response_text = re.sub(r'"example":{ (\w+) },', '"example":{},', response.text)
        response_dict = json.loads(new_response_text)

    model_dict = _parser_model(response_dict['definitions'])
    # 2、获取所有模型
    interface_dict = {}
    for url, other in response_dict['paths'].items():
        url = re.sub(_URL_PATTERN, URL_PLACEHOLDER, url)
        for method, detail in other.items():
            clazz = detail["tags"][0]
            method = method.upper()
            query, header, body = _parser_parameter_and_header_and_body(model_dict, detail.get('parameters'))
            response_schema = _parser_response(model_dict, detail['responses']['200'])
            interface = SwaggerInterface(clazz, url, method, detail['summary'], query, header, body, response_schema)
            interface_dict[_URL_TEMPLATE % (method, url)] = interface
    return interface_dict


def _parser_model(models):
    model_field_ref = {}
    model_dict = {}
    for model, properties in models.items():
        if 'properties' in properties:
            model_dict[model] = _parser_field_value(model_field_ref, model_dict, None, properties['properties'], model)
        else:
            type_ = properties['type']
            if type_ == 'object':  # TODO 或许处理
                model_dict[model] = {}

    # 维护模型之间互相引用，用于检测循环依赖
    model_reference = {}
    for model, ref_model_field_set in model_field_ref.items():
        model_reference[model] = set()
        for ref_model_field in ref_model_field_set:
            model_reference[model].add(ref_model_field.split('.')[0])

    # 填充引用对象
    for model, reference_set in model_field_ref.items():
        for use_field_path in reference_set:
            paths = use_field_path.split('.')

            # 处理循环依赖（场景一：对象A引用B，对象B引用A；场景二：对象A引用对象A）
            if paths[0] in model_reference and model in model_reference[paths[0]]:
                continue

            temp = model_dict
            for index, path in enumerate(paths):
                arr = False
                if path.endswith('[0]'):  # 处理数组中模型引用
                    arr = True
                    path = path.replace('[0]', '')

                # 给引用模型赋值
                if index + 1 == len(paths):
                    if arr:
                        temp[path][0] = model_dict[model]
                    else:
                        temp[path] = model_dict[model]
                else:
                    temp = temp[path][0] if arr else temp[path]

    return model_dict


def _parser_field_value(model_field_ref, model_dict, field_name, field_value, field_abs_path):
    """ 解析属性的值 """

    # 如果字段名称为空或者字段为引用对象，则字段绝对路径不追加。便于后续给字段填充合理的值
    next_field_abs_path = _FIELD_PATH % (field_abs_path, field_name)
    if field_name is None or field_name == '$ref':
        next_field_abs_path = field_abs_path

    # 处理引用类型
    if field_name == '$ref':
        # 注册待填充引用模型（通过field_abs_path寻址）
        ref_model = field_value.replace(_DEFINITION_PATTERN, '')
        if ref_model not in model_field_ref:
            model_field_ref[ref_model] = set()
        model_field_ref[ref_model].add(next_field_abs_path)
        return {}

    # 根据swagger自定义的type值处理数据
    if 'type' in field_value:
        # 处理基本类型 + object + array
        type_ = field_value['type']
        if type_ == 'array':
            current_array_value = list()
            if '$ref' in field_value['items']:  # 处理字数组内部是对象的场景
                next_field_abs_path += '[0]'
                new_prop_value = _parser_field_value(model_field_ref, model_dict, '$ref',
                                                     field_value['items']['$ref'], next_field_abs_path)
                current_array_value.append(new_prop_value)
            return current_array_value
        elif type_ == 'object':
            current_obj_value = {}  # TODO 后续完善
            return current_obj_value
        elif type_ == 'string':
            return 'string'
        elif type_ == 'integer' or type_ == 'number':
            return 0
        elif type_ == 'boolean':
            return True

    # 根据字典处理其它数据
    current_dict_value = {}
    if '$ref' in field_value:
        return _parser_field_value(model_field_ref, model_dict, '$ref', field_value['$ref'], next_field_abs_path)
    else:
        for prop_name, prop_value in field_value.items():
            new_prop_value = _parser_field_value(model_field_ref, model_dict, prop_name, prop_value,
                                                 next_field_abs_path)
            current_dict_value[prop_name] = new_prop_value
        return current_dict_value


def _parser_parameter_and_header_and_body(model_dict, parameters):
    param_list = list()
    header_list = list()
    body = {}

    if parameters is None:
        return param_list, header_list, body

    for param in parameters:
        param_type = param['in']
        if param_type == 'query':
            param_list.append(
                SwaggerField(param['name'], param.pop('description', ''), param.pop('type', ''), param['required']))
        elif param_type == 'header':
            header_list.append(
                SwaggerField(param['name'], param.pop('description', ''), param.pop('type', ''), param['required']))
        elif param_type == 'body':
            schema_ = param['schema']
            if 'type' in schema_:
                if schema_['type'] == 'array':
                    items_ = schema_['items']
                    if '$ref' in items_:
                        model = items_['$ref'].replace(_DEFINITION_PATTERN, '')
                        body = [].append(model_dict[model])
                    else:
                        body = [].append(items_['type'])
                elif schema_['type'] == 'object':  # TODO 后续完善
                    body = {}
                else:
                    body = schema_['type']
            else:
                model = schema_['$ref'].replace(_DEFINITION_PATTERN, '')
                body = model_dict[model]
    return param_list, header_list, body


def _parser_response(model_dict, response) -> List[SwaggerField]:
    if 'schema' in response:
        if '$ref' in response['schema']:
            match = re.search(_MODEL_PATTERN, response['schema']['$ref'])
            if match:
                model_name = match.group('model')
                return model_dict.get(model_name, '解析异常')
    return list()
