# SkyWalking公共请求与响结构
class SkyWalkingRequest:
    def __init__(self, query, variables):
        self.query = query
        self.variables = variables


class SkyWalkingResponse:
    def __init__(self, data):
        self.data = data


# 获取SkyWalking服务
class SkyWalkingServiceRequest:
    def __init__(self, layer):
        self.layer = layer


class SkyWalkingServiceResponse:
    def __init__(self, services):
        self.services = services

    class Detail:
        def __init__(self, base64name, name):
            self.base64name = base64name
            self.name = name


# 获取SkyWalking服务所有请求的接口信息
class SkyWalkingServiceInterfaceRequest:
    def __init__(self, serviceId, duration):
        self.serviceId = serviceId
        self.duration = duration

    class Duration:
        def __init__(self, start, end, step):
            self.start = start
            self.end = end
            self.step = step

        def __str__(self):
            return str(self.__dict__)


class SkyWalkingServiceInterfaceResponse:
    def __init__(self, endpoints):
        self.endpoints = endpoints

    class Detail:
        def __init__(self, name):
            self.name = name

        def __str__(self):
            return str(self.__dict__)


# 获取SkyWalking服务所有请求的接口的统计信息
class SkyWalkingServiceInterfaceStatisticsRequest:
    def __init__(self, entity, duration):
        self.expression0 = "top_n(endpoint_cpm,1000,des)"
        self.entity0 = entity
        self.duration = duration

    class Duration:
        def __init__(self, start, end, step):
            self.start = start
            self.end = end
            self.step = step

        def __str__(self):
            return str(self.__dict__)

    class Entity:
        def __init__(self, service_name):
            self.serviceName = service_name
            self.normal = True

        def __str__(self):
            return str(self.__dict__)


class SkyWalkingServiceInterfaceStatisticsResponse:
    def __init__(self, details):
        self.details = details

    class Detail:
        def __init__(self, name, value):
            self.name = name
            self.value = value

        def __str__(self):
            return str(self.__dict__)


#   ------------------------   swagger  ------------------------

class SwaggerInterface:
    def __init__(self, clazz, url, method, description, query, header, body, response):
        self.clazz = clazz
        self.url = url
        self.method = method
        self.description = description
        self.query = query
        self.header = header
        self.body = body
        self.response = response


class SwaggerField:
    def __init__(self, name, description, type, required):
        self.name = name
        self.description = description
        self.type = type
        self.required = required

