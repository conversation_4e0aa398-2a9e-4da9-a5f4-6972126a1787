# Generated by Django 3.0.6 on 2021-05-13 18:47

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PublishingTaskRunningRecode',
            fields=[
                ('record_id', models.AutoField(primary_key=True, serialize=False)),
                ('batch_number', models.CharField(max_length=128)),
                ('environment', models.CharField(max_length=8)),
                ('package', models.TextField()),
                ('report_content', models.TextField()),
                ('begin_time', models.DateTimeField()),
                ('end_time', models.DateTimeField(null=True)),
                ('status', models.CharField(max_length=8)),
            ],
            options={
                'verbose_name': '发版任务执行记录表',
                'db_table': 'publishing_task_running_record',
                'ordering': ['-begin_time'],
            },
        ),
    ]
