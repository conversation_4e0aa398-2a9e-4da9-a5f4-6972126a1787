# EditSceneDrawer 组件集成操作手册

## 1. 组件概述

`EditSceneDrawer` 是一个功能完整的场景编辑组件，提供了场景的新增、编辑、查看功能，以及完整的案例管理能力。该组件采用抽屉式设计，支持嵌套抽屉操作，包含以下核心功能：

- **场景管理**：新增、编辑、查看场景信息
- **案例管理**：添加、编辑、复制、删除案例
- **参数管理**：前置参数配置、案例参数提取
- **断言配置**：匹配断言和四则运算断言
- **文件上传**：支持案例文件上传功能
- **拖拽排序**：案例列表支持拖拽重新排序
- **场景运行**：支持场景调试和运行

## 2. 完整的集成步骤

### 2.1 组件导入和注册

```javascript
// 在需要使用的页面中导入组件
import EditSceneDrawer from '../../components/EditSceneDrawer.vue'

export default {
  components: {
    EditSceneDrawer
  },
  // ... 其他配置
}
```

### 2.2 模板中的使用方式

```vue
<template>
  <div>
    <!-- 其他页面内容 -->
    
    <!-- 编辑场景抽屉 -->
    <EditSceneDrawer
      ref="editSceneDrawerRef"
      :visible.sync="dialogVisible"
      :title="title"
      :type="type"
      :scene-data="sceneForm"
      :business-list="businessList"
      :loading="addLoading"
      :model="model"
      :table-title="'案例组成'"
      :show-add-case-button="true"
      :table-id="'crTable'"
      :table-border="false"
      :table-style="'width: 100%'"
      @close="sceneDrawerClosed"
      @submit="handleSceneSubmit"
      @run-scene="showSceneRunDialog"
      @add-case="handleAddCase"
      @edit-case="handleEditCase"
      @copy-case="handleCopyCase"
      @delete-case="handleDeleteCase"
      @opened="addDrawerOpened"
      @refresh-list="queryList"
    >
      <!-- 自定义表格列 -->
      <template #table-columns="{ type }">
        <el-table-column type="index" />
        <el-table-column prop="relation_case_id" min-width="100px" label="案例ID" />
        <el-table-column prop="relation_case_name" min-width="120px" label="案例名称" />
        <el-table-column prop="relation_case_type" min-width="80px" label="案例类型" />
        <!-- 更多自定义列... -->
      </template>
    </EditSceneDrawer>
  </div>
</template>
```

### 2.3 数据结构要求和初始化

```javascript
export default {
  data() {
    return {
      // 抽屉显示状态
      dialogVisible: false,
      
      // 抽屉标题
      title: '编辑场景',
      
      // 操作类型：add/update/check
      type: 'add',
      
      // 场景数据
      sceneForm: {
        scene_id: '',
        scene_name: '',
        scene_describe: '',
        business_id: 0,
        isShowPreparam: false,
        before_param: '',
        relation: []
      },
      
      // 业务列表
      businessList: [],
      
      // 加载状态
      addLoading: false,
      
      // API模型名称
      model: 'scene_info'
    }
  }
}
```

### 2.4 事件处理方法的实现

```javascript
methods: {
  // 显示编辑场景弹窗
  async showDialog(isAdd, id, type) {
    this.type = type
    if (this.type === 'add') {
      this.title = '新增场景'
    } else if (this.type === 'update') {
      this.title = '编辑场景'
    } else if (this.type === 'check') {
      this.title = '查看场景'
    }
    await this.$refs.editSceneDrawerRef.showDialog(isAdd, id, type)
  },

  // 处理场景提交
  handleSceneSubmit({ sceneData, flag }) {
    this.$refs.editSceneDrawerRef.submitScene(flag)
  },

  // 处理添加案例
  handleAddCase(index) {
    this.$refs.editSceneDrawerRef.showAddCaseDrawer(index)
  },

  // 处理编辑案例
  handleEditCase({ caseInfo, index }) {
    this.$refs.editSceneDrawerRef.showEditCaseDrawer(caseInfo, index)
  },

  // 处理复制案例
  handleCopyCase(caseInfo, index) {
    this.$refs.editSceneDrawerRef.handleCopyCase(caseInfo, index)
  },

  // 处理删除案例
  handleDeleteCase(index) {
    this.$refs.editSceneDrawerRef.deleteCurrentCase(index)
  },

  // 抽屉关闭处理
  sceneDrawerClosed() {
    // 重置表单数据
    this.sceneForm = {
      scene_name: '',
      scene_describe: '',
      business_id: 0,
      isShowPreparam: false,
      before_param: '',
      relation: []
    }
  },

  // 打开动画结束时的回调
  addDrawerOpened() {
    // 拖拽排序现在由EditSceneDrawer组件处理
  },

  // 刷新列表
  queryList() {
    // 实现列表刷新逻辑
    this.getSceneList()
  }
}
```

## 3. Props 详细说明

| 属性名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `visible` | Boolean | 是 | `false` | 抽屉显示状态，支持.sync修饰符 |
| `title` | String | 否 | `'编辑场景'` | 抽屉标题 |
| `type` | String | 否 | `'add'` | 操作类型：add/update/check |
| `sceneData` | Object | 否 | `{}` | 场景数据对象 |
| `businessList` | Array | 否 | `[]` | 业务列表数据 |
| `model` | String | 否 | `'scene_info'` | API模型名称 |
| `loading` | Boolean | 否 | `false` | 加载状态 |
| `tableTitle` | String | 否 | `'关联案例'` | 表格标题 |
| `showAddCaseButton` | Boolean | 否 | `false` | 是否显示添加案例按钮 |
| `tableId` | String | 否 | `'relationTable'` | 表格ID |
| `rowKey` | String | 否 | `'counterKey'` | 表格行key |
| `tableBorder` | Boolean | 否 | `true` | 表格边框 |
| `tableStyle` | String | 否 | `'width: 100%'` | 表格样式 |
| `tableMaxHeight` | String/Number | 否 | `400` | 表格最大高度 |

## 4. Events 详细说明

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:visible` | `(visible: Boolean)` | 抽屉显示状态变化 |
| `close` | - | 抽屉关闭事件 |
| `submit` | `{ sceneData: Object, flag: Boolean }` | 场景提交事件 |
| `run-scene` | `(sceneId: String)` | 运行场景事件 |
| `add-case` | `(index: Number)` | 添加案例事件 |
| `edit-case` | `{ caseInfo: Object, index: Number }` | 编辑案例事件 |
| `copy-case` | `(caseInfo: Object, index: Number)` | 复制案例事件 |
| `delete-case` | `(index: Number)` | 删除案例事件 |
| `opened` | - | 抽屉打开动画结束事件 |
| `refresh-list` | - | 刷新列表事件 |
| `scene-updated` | - | 场景更新完成事件 |
| `update:loading` | `(loading: Boolean)` | 加载状态变化事件 |

## 5. Slots 使用指南

### 5.1 table-columns 插槽

用于自定义表格列结构，插槽提供 `type` 参数表示当前操作类型。

```vue
<template #table-columns="{ type }">
  <el-table-column type="index" />
  <el-table-column prop="relation_case_id" min-width="100px" label="案例ID" />
  <el-table-column prop="relation_case_name" min-width="120px" label="案例名称" />
  <el-table-column prop="relation_case_type" min-width="80px" label="案例类型" />
  
  <!-- 自定义列 -->
  <el-table-column prop="is_get_param" min-width="80px" label="是否提取参数">
    <template v-slot="slotProps">
      <span v-if="slotProps.row.is_get_param">是</span>
      <span v-else>否</span>
    </template>
  </el-table-column>
  
  <!-- 操作列 -->
  <el-table-column min-width="170px" label="操作" v-if="type !== 'check'">
    <template v-slot="slotProps">
      <el-button type="text" @click="handleEditCase({ caseInfo: slotProps.row, index: slotProps.$index })">编辑</el-button>
      <el-button type="text" @click="handleAddCase(slotProps.$index)">添加</el-button>
      <el-button type="text" @click="handleCopyCase(slotProps.row, slotProps.$index)">复制</el-button>
      <el-button type="text" @click="handleDeleteCase(slotProps.$index)">删除</el-button>
    </template>
  </el-table-column>
</template>
```

## 6. 完整的代码示例

以下是基于 `src/views/dailyMonitoring/DailyMonitoringDetailList.vue` 的完整实现示例：

### 6.1 模板部分

```vue
<template>
  <div>
    <!-- 页面主要内容 -->
    <el-card>
      <!-- 搜索表单 -->
      <el-form :inline="true" :model="queryInfo" class="demo-form-inline">
        <el-form-item label="场景ID">
          <el-input v-model="queryInfo.search_scene_id" placeholder="请输入场景ID"></el-input>
        </el-form-item>
        <el-form-item label="场景名称">
          <el-input v-model="queryInfo.search_scene_name" placeholder="请输入场景名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="queryList">查询</el-button>
          <el-button type="success" @click="showDialog(true, null, 'add')">新增场景</el-button>
        </el-form-item>
      </el-form>

      <!-- 场景列表表格 -->
      <el-table :data="sceneList" border style="width: 100%">
        <el-table-column type="index"></el-table-column>
        <el-table-column prop="scene_id" label="场景ID" min-width="80px"></el-table-column>
        <el-table-column prop="scene_name" label="场景名称" min-width="120px"></el-table-column>
        <el-table-column prop="scene_describe" label="场景描述" min-width="150px"></el-table-column>
        <el-table-column label="操作" min-width="200px">
          <template v-slot="slotProps">
            <el-button type="text" @click="showDialog(false, slotProps.row.scene_id, 'update')">编辑</el-button>
            <el-button type="text" @click="showDialog(false, slotProps.row.scene_id, 'check')">查看</el-button>
            <el-button type="text" @click="showSceneRunDialog(slotProps.row.scene_id)">运行</el-button>
            <el-button type="text" @click="removeSceneById(slotProps.row.scene_id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryInfo.pagenum"
        :page-sizes="[5, 10, 15, 20]"
        :page-size="queryInfo.pagesize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </el-card>

    <!-- 编辑场景抽屉 -->
    <EditSceneDrawer
      ref="editSceneDrawerRef"
      :visible.sync="dialogVisible"
      :title="title"
      :type="type"
      :scene-data="sceneForm"
      :business-list="businessList"
      :loading="addLoading"
      :model="model"
      :table-title="'案例组成'"
      :show-add-case-button="true"
      :table-id="'crTable'"
      :table-border="false"
      :table-style="'width: 100%'"
      @close="sceneDrawerClosed"
      @submit="handleSceneSubmit"
      @run-scene="showSceneRunDialog"
      @add-case="handleAddCase"
      @edit-case="handleEditCase"
      @copy-case="handleCopyCase"
      @delete-case="handleDeleteCase"
      @opened="addDrawerOpened"
      @refresh-list="queryList"
    >
      <template #table-columns="{ type }">
        <el-table-column type="index" />
        <el-table-column prop="relation_case_id" min-width="100px" label="案例ID" />
        <el-table-column prop="relation_case_name" min-width="120px" label="案例名称" />
        <el-table-column prop="relation_case_type" min-width="80px" label="案例类型" />
        <el-table-column prop="is_get_param" min-width="80px" label="是否提取参数">
          <template v-slot="slotProps">
            <span v-if="slotProps.row.is_get_param">是</span>
            <span v-else>否</span>
          </template>
        </el-table-column>
        <el-table-column prop="response_param" min-width="100px" label="返回参数" />
        <el-table-column prop="response_param_alias" min-width="100px" label="返回参数别名" />
        <el-table-column prop="reset_param" min-width="80px" label="重设请求数据">
          <template v-slot="slotProps">
            <span v-if="slotProps.row.reset_param">是</span>
            <span v-else>否</span>
          </template>
        </el-table-column>
        <el-table-column prop="instructions" min-width="100px" label="备注说明" show-overflow-tooltip />
        <el-table-column prop="is_check" min-width="100px" label="是否数据越权">
          <template v-slot="slotProps">
            <el-switch
              v-model="slotProps.row.is_check"
              :disabled="type === 'check'"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column min-width="170px" label="操作" v-if="type !== 'check'">
          <template v-slot="slotProps">
            <el-button type="text" @click="handleEditCase({ caseInfo: slotProps.row, index: slotProps.$index })">编辑</el-button>
            <el-button type="text" @click="handleAddCase(slotProps.$index)">添加</el-button>
            <el-button type="text" @click="handleCopyCase(slotProps.row, slotProps.$index)">复制</el-button>
            <el-button type="text" @click="handleDeleteCase(slotProps.$index)">删除</el-button>
            <el-button type="text" @click="slotProps.row.is_enable = !slotProps.row.is_enable">
              <span v-if="slotProps.row.is_enable">禁用</span>
              <span v-else style="color:red">启用</span>
            </el-button>
          </template>
        </el-table-column>
      </template>
    </EditSceneDrawer>

    <!-- 环境选择对话框 -->
    <el-dialog :visible.sync="enviromentDialogVisible"
                width="30%"
                @close="enviromentDialogClosed"
                v-loading="loading"
                element-loading-text="场景运行中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.8)"
                :close-on-click-modal="false">
      <el-form label-width="130px"
               :model="sceneRunForm"
               ref="sceneRunFormRef"
               :rules="sceneRunFormRules">
        <el-form-item label="请选择运行环境" prop="env_type">
          <el-select v-model="sceneRunForm.env_type">
            <el-option label="测试环境" value="1"></el-option>
            <el-option label="预发环境" value="2"></el-option>
            <el-option label="生产环境" value="3"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="enviromentDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmitSceneRun">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
```

### 6.2 脚本部分

```javascript
<script>
import EditSceneDrawer from '../../components/EditSceneDrawer.vue'

export default {
  components: {
    EditSceneDrawer
  },
  data() {
    return {
      // API模型
      model: 'scene_info',

      // 查询参数
      queryInfo: {
        search_scene_id: null,
        search_scene_name: '',
        creater: window.localStorage.getItem('user_name'),
        business: null,
        pagenum: 1,
        pagesize: 5
      },

      // 场景列表数据
      sceneList: [],
      total: 0,

      // 抽屉相关
      dialogVisible: false,
      title: '',
      type: 'add',

      // 场景表单数据
      sceneForm: {
        scene_name: '',
        scene_describe: '',
        business_id: 0,
        isShowPreparam: false,
        before_param: '',
        relation: []
      },

      // 业务列表
      businessList: [],

      // 加载状态
      addLoading: false,

      // 场景运行相关
      enviromentDialogVisible: false,
      sceneRunForm: {
        scene_id: '',
        env_type: '1'
      },
      sceneRunFormRules: {
        env_type: [
          { required: true, message: '请选择运行环境', trigger: 'blur' }
        ]
      },
      loading: false
    }
  },

  created() {
    this.getSceneList()
    this.getBusinessList()
  },

  methods: {
    // 获取场景列表
    async getSceneList() {
      const { data: res } = await this.$http.get(this.model + '/scene_list/', { params: this.queryInfo })
      if (res.meta.status !== 200) return this.$message.error('获取场景列表失败')
      this.total = res.data.total
      this.sceneList = res.data.scenes
    },

    // 获取业务列表
    async getBusinessList() {
      const { data: res } = await this.$http.get('/user_info/info/', { params: { type: 'businesses' } })
      if (res.meta.status !== 200) return this.$message.error('获取业务列表失败')
      this.businessList = res.data
    },

    // 查询场景
    queryList() {
      this.queryInfo.pagenum = 1
      this.getSceneList()
    },

    // 分页处理
    handleSizeChange(newSize) {
      this.queryInfo.pagesize = newSize
      this.getSceneList()
    },

    handleCurrentChange(newPage) {
      this.queryInfo.pagenum = newPage
      this.getSceneList()
    },

    // 显示编辑场景弹窗
    async showDialog(isAdd, id, type) {
      this.type = type
      if (this.type === 'add') {
        this.title = '新增场景'
      } else if (this.type === 'update') {
        this.title = '编辑场景'
      } else if (this.type === 'check') {
        this.title = '查看场景'
      }
      await this.$refs.editSceneDrawerRef.showDialog(isAdd, id, type)
    },

    // 删除场景
    removeSceneById(id) {
      this.$confirm('此操作将永久删除该场景, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.delete(this.model + '/' + id + '/scene_delete/').then(result => {
          if (result.data.meta.status !== 200) return this.$message.error(result.data.meta.msg)
          this.$message.success('删除场景成功')
          this.getSceneList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    // 抽屉关闭处理
    sceneDrawerClosed() {
      this.sceneForm = {
        scene_name: '',
        scene_describe: '',
        business_id: 0,
        isShowPreparam: false,
        before_param: '',
        relation: []
      }
    },

    // 处理场景提交
    handleSceneSubmit({ sceneData, flag }) {
      this.$refs.editSceneDrawerRef.submitScene(flag)
    },

    // 处理添加案例
    handleAddCase(index) {
      this.$refs.editSceneDrawerRef.showAddCaseDrawer(index)
    },

    // 处理编辑案例
    handleEditCase({ caseInfo, index }) {
      this.$refs.editSceneDrawerRef.showEditCaseDrawer(caseInfo, index)
    },

    // 处理复制案例
    handleCopyCase(caseInfo, index) {
      this.$refs.editSceneDrawerRef.handleCopyCase(caseInfo, index)
    },

    // 处理删除案例
    handleDeleteCase(index) {
      this.$refs.editSceneDrawerRef.deleteCurrentCase(index)
    },

    // 打开场景运行弹窗
    async showSceneRunDialog(id) {
      this.sceneRunForm.scene_id = id
      this.enviromentDialogVisible = true
    },

    // 环境选择对话框关闭
    enviromentDialogClosed() {
      this.$refs.sceneRunFormRef.resetFields()
    },

    // 处理提交场景运行
    handleSubmitSceneRun() {
      // 将环境选择数据传递给EditSceneDrawer
      this.$refs.editSceneDrawerRef.sceneRunForm = this.sceneRunForm
      this.$refs.editSceneDrawerRef.submitSceneRun()
      this.enviromentDialogVisible = false
    },

    // 打开动画结束时的回调
    addDrawerOpened() {
      // 拖拽排序现在由EditSceneDrawer组件处理
    }
  }
}
</script>
```

## 7. 常见问题和注意事项

### 7.1 数据验证

**问题**：场景提交时数据验证失败
**解决方案**：
- 确保 `sceneForm.scene_name` 不为空
- 确保 `sceneForm.relation` 数组至少包含一个案例
- 检查案例数据的完整性，特别是必填字段

```javascript
// 数据验证示例
validateSceneData() {
  if (!this.sceneForm.scene_name) {
    this.$message.error('请输入场景名称')
    return false
  }
  if (this.sceneForm.relation.length === 0) {
    this.$message.error('请添加至少一个案例')
    return false
  }
  return true
}
```

### 7.2 错误处理

**问题**：API调用失败时的错误处理
**解决方案**：
- 组件内部已经包含完整的错误处理机制
- 父组件应该监听相关事件并提供用户反馈
- 建议在关键操作前进行数据校验

```javascript
// 错误处理示例
handleSceneSubmit({ sceneData, flag }) {
  try {
    this.$refs.editSceneDrawerRef.submitScene(flag)
  } catch (error) {
    console.error('场景提交失败:', error)
    this.$message.error('操作失败，请重试')
  }
}
```

### 7.3 性能优化

**建议**：
1. **懒加载**：只在需要时才加载业务列表和案例列表
2. **防抖处理**：对搜索功能添加防抖处理
3. **虚拟滚动**：当案例数量很大时，考虑使用虚拟滚动
4. **缓存策略**：对不经常变化的数据进行缓存

```javascript
// 防抖搜索示例
import { debounce } from 'lodash'

export default {
  methods: {
    // 使用防抖处理搜索
    debouncedSearch: debounce(function() {
      this.queryList()
    }, 300)
  }
}
```

### 7.4 内存管理

**注意事项**：
- 组件已经实现了完善的资源清理机制
- 在页面销毁时，确保清理相关的定时器和事件监听器
- 避免在组件中创建过多的响应式数据

### 7.5 ESC键处理

**特性说明**：
- 组件实现了智能的ESC键处理逻辑
- 当有多个嵌套抽屉时，ESC键会优先关闭最顶层的抽屉
- 无需在父组件中处理ESC键事件

## 8. 与原有代码的迁移指南

### 8.1 识别需要迁移的功能

如果页面已有类似的场景编辑功能，需要识别以下部分：
- 场景表单相关的数据和方法
- 案例管理相关的功能
- 抽屉或弹窗的显示逻辑
- API调用相关的代码

### 8.2 迁移步骤

#### 步骤1：备份原有代码
```bash
# 创建备份
cp YourOriginalFile.vue YourOriginalFile.vue.backup
```

#### 步骤2：移除重复功能
移除以下原有代码：
- 场景编辑相关的模板代码
- 案例管理相关的方法
- 重复的数据定义
- 相关的样式定义

#### 步骤3：集成EditSceneDrawer组件
按照本手册的集成步骤添加组件。

#### 步骤4：数据结构适配
```javascript
// 原有数据结构
const oldSceneData = {
  id: 1,
  name: '测试场景',
  description: '场景描述',
  cases: [...]
}

// 适配为新的数据结构
const newSceneData = {
  scene_id: oldSceneData.id,
  scene_name: oldSceneData.name,
  scene_describe: oldSceneData.description,
  relation: oldSceneData.cases.map(case => ({
    relation_case_id: case.id,
    relation_case_name: case.name,
    // ... 其他字段映射
  }))
}
```

#### 步骤5：方法调用适配
```javascript
// 原有方法调用
this.showEditDialog(sceneId)

// 新的方法调用
this.showDialog(false, sceneId, 'update')
```

#### 步骤6：事件处理适配
```javascript
// 原有事件处理
onSceneUpdated() {
  this.refreshSceneList()
}

// 新的事件处理
handleSceneSubmit({ sceneData, flag }) {
  this.$refs.editSceneDrawerRef.submitScene(flag)
}
```

### 8.3 测试验证

迁移完成后，需要验证以下功能：
- [ ] 场景新增功能正常
- [ ] 场景编辑功能正常
- [ ] 场景查看功能正常
- [ ] 案例添加、编辑、删除功能正常
- [ ] 拖拽排序功能正常
- [ ] 文件上传功能正常（如果使用）
- [ ] 断言配置功能正常
- [ ] ESC键关闭功能正常
- [ ] 数据验证功能正常
- [ ] 错误处理功能正常

## 9. 高级用法

### 9.1 自定义表格列

可以通过插槽完全自定义表格列的显示：

```vue
<template #table-columns="{ type }">
  <!-- 基础列 -->
  <el-table-column type="index" />
  <el-table-column prop="relation_case_id" label="案例ID" />

  <!-- 自定义格式化列 -->
  <el-table-column label="案例类型" min-width="100px">
    <template v-slot="slotProps">
      <el-tag :type="getCaseTypeColor(slotProps.row.relation_case_type)">
        {{ getCaseTypeText(slotProps.row.relation_case_type) }}
      </el-tag>
    </template>
  </el-table-column>

  <!-- 条件显示列 -->
  <el-table-column v-if="showAdvancedColumns" prop="advanced_field" label="高级字段" />

  <!-- 自定义操作列 -->
  <el-table-column label="操作" min-width="200px" v-if="type !== 'check'">
    <template v-slot="slotProps">
      <el-button-group>
        <el-button size="mini" @click="handleEditCase({ caseInfo: slotProps.row, index: slotProps.$index })">编辑</el-button>
        <el-button size="mini" @click="handleCopyCase(slotProps.row, slotProps.$index)">复制</el-button>
        <el-button size="mini" type="danger" @click="handleDeleteCase(slotProps.$index)">删除</el-button>
      </el-button-group>
    </template>
  </el-table-column>
</template>
```

### 9.2 扩展组件功能

如果需要扩展组件功能，可以通过以下方式：

```javascript
// 扩展方法示例
methods: {
  // 批量操作案例
  batchOperateCases(operation, selectedCases) {
    selectedCases.forEach((caseInfo, index) => {
      switch(operation) {
        case 'enable':
          caseInfo.is_enable = true
          break
        case 'disable':
          caseInfo.is_enable = false
          break
        case 'delete':
          this.handleDeleteCase(index)
          break
      }
    })
  },

  // 导入案例配置
  importCaseConfig(configData) {
    this.$refs.editSceneDrawerRef.sceneForm.relation = [
      ...this.$refs.editSceneDrawerRef.sceneForm.relation,
      ...configData.cases
    ]
  },

  // 导出场景配置
  exportSceneConfig() {
    const sceneData = this.$refs.editSceneDrawerRef.sceneForm
    const exportData = {
      scene_name: sceneData.scene_name,
      scene_describe: sceneData.scene_describe,
      cases: sceneData.relation,
      export_time: new Date().toISOString()
    }

    // 下载配置文件
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `scene_${sceneData.scene_name}_config.json`
    a.click()
    URL.revokeObjectURL(url)
  }
}
```


