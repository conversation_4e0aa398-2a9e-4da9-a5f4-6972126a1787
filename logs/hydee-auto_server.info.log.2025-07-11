|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711114052962|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711114052963|MainThread:8571161728|INFO|tool_manager.py:75|开始自动注册工具...
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711114053425|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711114053452|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711114053452|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 下载小程序代码
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711114053452|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试报告
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711114053452|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试用例
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711114053452|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从hops获取异常日志
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711114053452|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从skywalking获取sql
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711114053452|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: jenkins构建
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711114053453|MainThread:8571161728|INFO|tool_manager.py:95|完成工具注册，总数: 6
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140712399|Thread-5 (process_request_thread):6341865472|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_recognize_record_list/，请求方式：POST，请求参数为：b'{"business_id":"","iteration_name":"","creater":"","pagenum":1,"pagesize":5}'
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140712399|Thread-6 (process_request_thread):6358691840|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/info/?type=businesses，请求方式：GET，请求参数为：<QueryDict: {'type': ['businesses']}>
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140712400|Thread-5 (process_request_thread):6341865472|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140712401|Thread-6 (process_request_thread):6358691840|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140712401|Thread-5 (process_request_thread):6341865472|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140712401|Thread-6 (process_request_thread):6358691840|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140712423|Thread-8 (recordSystemLog):6392344576|INFO|commonUtil.py:51|用户 liuyanxia 查询AI生成用例记录，查询条件：需求ID=，迭代名称=，业务线=，创建人= 
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140712423|Thread-7 (recordSystemLog):6375518208|INFO|commonUtil.py:51|来自IP：127.0.0.1的用户 liuyanxia 请求获取全部businesses信息
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140712431|Thread-6 (process_request_thread):6358691840|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": [{"id": 30, "label": "30-厂家端小程序"}, {"id": 29, "label": "29-一键造数"}, {"id": 28, "label": "28-医保云"}, {"id": 27, "label": "27-四季蝉"}, {"id": 26, "label": "26-数据中心"}, {"id": 25, "label": "25-云仓"}, {"id": 24, "label": "24-随心看"}, {"id": 23, "label": "23-直播"}, {"id": 22, "label": "22-服务商"}, {"id": 21, "label": "21-组织云"}, {"id": 20, "label": "20-微商城9"}, {"id": 19, "label": "19-随心享"}, {"id": 18, "label": "18-支付云"}, {"id": 17, "label": "17-优享会员"}, {"id": 4, "label": "4-OMS-B2C-h2（废除）"}, {"id": 11, "label": "11-OMS-B2C"}, {"id": 16, "label": "16-H2_O2O_Order"}, {"id": 15, "label": "15-H2_OMS_B2C"}, {"id": 14, "label": "14-药事云"}, {"id": 13, "label": "13-商品中台"}, {"id": 12, "label": "12-助手"}, {"id": 10, "label": "10-演示"}, {"id": 8, "label": "8-会员"}, {"id": 7, "label": "7-OMS-O2O"}, {"id": 6, "label": "6-营销"}, {"id": 2, "label": "2-自动化"}, {"id": 0, "label": "0-默认"}], "meta": {"msg": "获取成功", "status": 200}, "timestamp": 1752214032431}
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140712432|Thread-6 (process_request_thread):6358691840|INFO|userRequestMiddleware.py:143|当前请求耗时：0.03185319900512695 秒
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140712478|Thread-5 (process_request_thread):6341865472|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 5, "total": 51, "data": [{"id": 52, "iteration_id": "1161969829001002058", "business_id": "26", "requirement_id": 1067014, "requirement_content": "增加【美团待铺货&饿百待铺货】页签，同时支持新品页签的切换。\n店铺待铺货商品明细\n前置条件：先选择一个具体的平台店铺。（仅支持选择美团、饿百平台店铺）\n列表数据来源范围：\n商品信息：公域商品列表中，是否可售状态为【是】且有条形码的商品；同时第三方商品店铺无该商品，但所属门店有该商品。\n字段名称 字段逻辑\n平台店铺 平台 + 店铺名称\n商品信息 商品名称 + 规格 + 批准文号 名称\n条形码\nERP商品编码\n平台近30天总销量 美团所有店铺近30天内的销量汇总；支持按照销量切换排序。\nT-1的30天内（下账时间），已下账的商品数量汇总。\n所属门店 门店名称 + 门店编码\nERP门店库存 ERP同步的门店库存\nERP门店价格 ERP同步的门店价格\nERP加权成本价 ERP同步的加权成本价\n预估毛利率 （店铺铺货价格 - 加权成本价） / 加权成本价 * 100%；根据店铺铺货价格动态计算。\n店铺铺货价格 默认门店价格，支持编辑，数据格式：大于0的两位小数。\n店铺商品分类 下拉单选\n列表排序：默认按平台近30天总销量倒序排序\n页签字段文档描述：\n美团待铺货页签字段：\n商品信息：公域商品列表中，是否可售状态为【是】且有条形码的商品才展示。同时第三方商品店铺无该商品，但所属门店有该商品。\n美团近30天销量：连锁在美团所有店铺近30天内的销售数据汇总（T-1的30天内（下账时间），已下账的商品数量汇总。）\n\n饿百待铺货页签：\n商品信息：公域商品列表中，是否可售状态为【是】且有条形码的商品才展示。同时第三方商品店铺无该商品，但所属门店有该商品。\n饿百近30天销量：连锁在饿百所有店铺近30天内的销售数据汇总（T-1的30天内（下账时间），已下账的商品数量汇总。）\n\n查询条件\n查询条件 查询逻辑\n平台店铺 平台 + 店铺，支持搜索对应平台的店铺\n所属门店 门店编码或门店名称，与平台店铺联动。\n商品名称 模糊匹配查询\nERP商品编码 精确匹配查询，支持查多个，最大100个\n条形码 精确匹配查询，支持查多个，最大100个\n平台销量区间 0-n的区间数量，根据选择的平台店铺，确定查询哪个平台的销量\n新品天数设置\n查看新品过滤及设置调整到页面右上角。\n支持在所有页签设置新品天数和勾选新品天数过滤商品数据。切换页签新品勾选条件不变。\n新品天数文案描述：勾选则表示，只查询线上商品库的商品创建时间在设置的新品天数范围内的商品。\n操作\n1.新品铺货-单个操作：按行操作商品铺货，按照编辑后的店铺铺货价格进行铺货（需二次确认）。铺货操作同步处理，并实时反馈操结果，刷新操作页面。\n校验：价格为0的，不支持铺货，且提示：【铺货价格不允许为0。】\n2.新品铺货-批量操作（仅支持当前页批量-一页最多20条）：批量勾选当前页的商品（需二次确认）。铺货操作同步处理，并实时反馈操结果，若有铺货失败的数据，则展示失败原因，并支持失败数据复制。\n校验：价格为0的，不支持勾选。\n3.支持数据导出（查询结果的列表全量数据）", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：用户选择平台店铺 -> 查询待铺货商品列表 -> 选择单个或批量商品进行铺货操作 -> 验证铺货价格 -> 确认铺货 -> 实时反馈结果 -> 刷新页面\n- **分支流程**：\n  - 正常分支：成功铺货后，实时更新数据\n  - 异常分支：铺货价格为0，提示错误信息并阻止操作\n  - 回退流程：铺货失败的数据支持复制和重新提交\n- **流程节点**：\n  - 输入节点：平台店铺选择、查询条件输入\n  - 处理节点：商品列表查询、铺货价格验证、铺货操作执行\n  - 输出节点：铺货成功/失败的结果反馈\n  - 存储节点：铺货后的商品信息存储\n\n### 1.2 功能模块拆解\n- **核心模块**：\n  - 商品列表展示模块\n  - 铺货操作模块\n  - 数据导出模块\n- **输入输出**：\n  - 商品列表展示模块：输入（查询条件）、输出（商品列表）\n  - 铺货操作模块：输入（铺货价格）、输出（铺货结果）\n  - 数据导出模块：输入（查询结果）、输出（导出文件）\n- **模块接口**：\n  - 商品列表展示模块调用铺货操作模块传递铺货信息\n  - 铺货操作模块返回结果给商品列表展示模块\n  - 数据导出模块从商品列表展示模块获取数据\n\n### 1.3 业务规则提取\n- **核心规则**：\n  - 仅展示可售且有条形码的商品\n  - 第三方商品店铺无该商品，但所属门店有该商品\n  - 默认按平台近30天总销量倒序排序\n- **验证规则**：\n  - 价格为0的，不允许铺货\n  - 批量操作每页最多20条记录\n- **计算逻辑**：\n  - 预估毛利率 = (店铺铺货价格 - 加权成本价) / 加权成本价 * 100%\n  - 新品天数过滤：只查询创建时间在设置的新品天数范围内的商品\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：确保页面加载快速，操作响应及时\n- 异常提示友好性要求：提供清晰的错误提示，如铺货价格为0时显示“铺货价格不允许为0”\n- 角色权限差异：未明确提到角色权限差异\n\n## 3. 异常场景分析\n- 输入异常处理：价格为0时不支持铺货，并提示错误\n- 操作异常处理：铺货失败时显示失败原因，并支持失败数据复制\n- 外部依赖异常处理：PRD中未提供相关信息\n- PRD未明确的隐性异常场景：未明确\n\n## 4. 兼容性需求分析\n- PRD明确的兼容范围：未明确\n\n## 5. 安全性需求分析\n- 数据安全要求：未明确\n- 权限控制要求：未明确\n- 业务安全要求：未明确\n\n## 6. 性能需求分析\n- PRD明确的性能指标：未明确\n- 高并发场景要求：未明确", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-07-08T16:18:29", "create_person": "刘艳霞", "is_active": true, "iteration_name": "【V2.3.4.22】商品云-0703", "total_ai_test_case": 10, "recall_ai_test_case": 0, "business_name": "数据中心"}, {"id": 51, "iteration_id": null, "business_id": "27", "requirement_id": 342342, "requirement_content": "- 将员工组织机构刷到数据授权中（店员、店长）\n\n- 将片区经理的授权门店刷到数据授权中（片区经理）\n\n- 将新增四季蝉按钮级别的资源刷到商户已有的角色资源里", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程  \n- **主流程**：系统从组织结构中获取员工信息 → 判断员工角色（店员、店长、片区经理）→ 更新数据授权配置。  \n- **分支流程**：若员工无对应角色，跳过该员工；若门店信息不存在，记录异常日志。  \n- **流程节点**：输入节点为员工组织结构数据，处理节点为角色判断与权限分配，输出节点为更新后的数据授权表。\n\n### 1.2 功能模块拆解  \n- **核心模块**：组织同步模块、角色识别模块、权限更新模块。  \n- **输入输出**：输入为员工组织数据和角色定义，输出为更新后的数据授权结果。  \n- **模块接口**：组织同步模块调用角色识别模块判断角色类型，权限更新模块接收识别结果进行授权更新。\n\n### 1.3 业务规则提取  \n- **核心规则**：仅支持“店员”、“店长”、“片区经理”三类角色的数据授权更新。  \n- **验证规则**：需验证员工所属门店是否有效，片区经理对应的门店是否存在。  \n- **计算逻辑**：根据员工角色确定授权范围，片区经理自动获得其管理门店的访问权限。\n\n---\n\n## 2. 用户体验需求分析  \n- 交互流程流畅性要求：批量处理员工授权时应有进度提示，避免界面无响应。  \n- 异常提示友好性要求：对于未匹配角色或无效门店的员工，应记录并展示清晰的失败原因。  \n- 角色权限差异：不同角色对应不同的授权范围，需在界面中明确区分显示内容。\n\n---\n\n## 3. 异常场景分析  \n- 输入异常处理：员工数据缺失关键字段（如门店ID、角色类型），系统应拒绝处理并记录错误。  \n- 操作异常处理：授权过程中数据库连接失败，应中断操作并返回友好的错误提示。  \n- 外部依赖异常处理：若组织服务不可用，系统应延迟执行任务或提示重试机制。  \n- PRD未明确的隐性异常场景：员工角色变更后旧授权未清理，可能导致数据访问越权问题。\n\n---\n\n## 4. 兼容性需求分析  \n- PRD中未提供相关信息  \n\n---\n\n## 5. 安全性需求分析  \n- 数据安全要求：授权数据同步过程需加密传输，防止敏感信息泄露。  \n- 权限控制要求：仅允许具备管理员权限的用户触发授权刷新操作。  \n- 业务安全要求：确保片区经理只能访问其管理范围内的门店数据，不能越权查看。\n\n---\n\n## 6. 性能需求分析  \n- PRD中未提供相关信息", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-07-08T12:05:46", "create_person": "刘艳霞", "is_active": true, "iteration_name": "", "total_ai_test_case": 18, "recall_ai_test_case": 0, "business_name": "四季蝉"}, {"id": 50, "iteration_id": "1161969829001002043", "business_id": "29", "requirement_id": 1067351, "requirement_content": "进行中、未开始、已发布成功的活动按照已参与的活动门店记录所属企业；\n\n代建草稿状态无活动门店的活动都按创建人的员工资料中的所属企业创建，记录所属企业；\n\n历史的代建草稿状态的无活动门店的所属企业一律处理为集团；\n\n历史的代建草稿状态的有活动门店的所属企业按活动门店所属企业记录", "requirement_analyze_content": "## 1. 功能需求分析123123\n\n### 1.1 核心业务流程  \n- **主流程**：活动创建时根据活动状态、是否配置门店，决定所属企业的归属逻辑；  \n- **分支流程**：草稿状态无门店活动按创建人企业处理；历史数据特殊处理为集团或门店企业；  \n- **流程节点**：输入为活动状态、门店信息、创建人信息；输出为所属企业字段值。\n\n### 1.2 功能模块拆解  \n- **核心模块**：活动管理模块、企业归属判断模块、用户资料关联模块；  \n- **输入输出**：输入包括活动状态、门店列表、员工所属企业；输出为活动的“所属企业”属性；  \n- **模块接口**：活动模块调用归属判断模块获取所属企业；归属判断模块从用户资料模块读取企业信息。\n\n### 1.3 业务规则提取  \n- **核心规则**：草稿状态无门店活动使用创建人企业；有门店则使用门店所属企业；  \n- **验证规则**：需验证活动状态是否为“代建草稿”、门店是否存在、创建人信息是否完整；  \n- **计算逻辑**：判断优先级为：门店 > 创建人 > 集团（历史无门店草稿）。\n\n---\n\n## 2. 用户体验需求分析  \n- 交互流程流畅性要求：用户创建活动时应自动识别并填充所属企业，无需手动选择；  \n- 异常提示友好性要求：若无法确定所属企业，应给出明确提示说明原因；  \n- 角色权限差异：普通员工可创建草稿，管理员可发布活动，不同角色查看范围受企业限制。\n\n---\n\n## 3. 异常场景分析  \n- 输入异常处理：活动状态无效或门店为空但非草稿状态，系统应阻止保存并提示错误；  \n- 操作异常处理：创建人未绑定所属企业时，应提示“请先完善个人信息”；  \n- 外部依赖异常处理：若门店数据接口异常，应记录日志并提示“无法加载门店信息”；  \n- PRD未明确的隐性异常场景：多门店归属不一致时如何处理、跨企业门店是否允许存在。\n\n---\n\n## 4. 兼容性需求分析  \n- PRD中未提供相关信息。\n\n---\n\n## 5. 安全性需求分析  \n- 数据安全要求：所属企业字段属于敏感业务数据，应加密存储或脱敏展示；  \n- 权限控制要求：仅所属企业人员可查看和操作相关活动数据；  \n- 业务安全要求：防止非法篡改“所属企业”字段以规避数据隔离策略。\n\n---\n\n## 6. 性能需求分析  \n- PRD中未提供相关信息。", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-06-30T16:06:40", "create_person": "刘艳霞", "is_active": true, "iteration_name": "【V3.1.6.0】四季蝉集团化数据隔离改造-0701", "total_ai_test_case": 15, "recall_ai_test_case": 3, "business_name": "一键造数"}, {"id": 49, "iteration_id": "1161969829001002043", "business_id": "27", "requirement_id": 1067351, "requirement_content": "进行中、未开始、已发布成功的活动按照已参与的活动门店记录所属企业；\n\n代建草稿状态无活动门店的活动都按创建人的员工资料中的所属企业创建，记录所属企业；\n\n历史的代建草稿状态的无活动门店的所属企业一律处理为集团；\n\n历史的代建草稿状态的有活动门店的所属企业按活动门店所属企业记录", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：活动创建时判断活动状态和门店信息，决定所属企业的归属规则。\n- **分支流程**：\n  - 活动为“进行中/未开始/已发布成功”且有门店 → 所属企业按门店所属企业记录；\n  - 活动为“代建草稿”状态且无门店 → 所属企业按创建人所属企业记录；\n  - 历史“代建草稿”状态无门店 → 归为集团；\n  - 历史“代建草稿”状态有门店 → 按门店所属企业记录。\n\n- **流程节点**：\n  - 输入节点：活动状态、活动门店信息、创建人所属企业；\n  - 处理节点：根据规则判断活动所属企业；\n  - 输出节点：活动记录中所属企业字段；\n  - 存储节点：活动数据表中保存所属企业信息。\n\n### 1.2 功能模块拆解\n- **核心模块**：\n  - 活动管理模块：负责活动的创建、状态维护及所属企业分配；\n  - 用户管理模块：提供创建人所属企业信息；\n  - 数据存储模块：保存活动及相关企业信息。\n\n- **输入输出**：\n  - 活动管理模块输入：活动状态、门店列表、创建人ID；\n  - 活动管理模块输出：活动记录及其所属企业字段；\n  - 用户管理模块输入/输出：创建人信息查询接口。\n\n- **模块接口**：\n  - 活动管理调用用户管理接口获取创建人所属企业；\n  - 活动管理写入数据存储模块保存活动记录。\n\n### 1.3 业务规则提取\n- **核心规则**：\n  - “进行中/未开始/已发布成功”活动优先依据门店确定所属企业；\n  - “代建草稿”活动无门店则归属创建人企业或默认集团；\n  - 历史数据统一处理规则不可变。\n\n- **验证规则**：\n  - 验证活动状态是否符合分类标准；\n  - 验证门店是否存在并关联有效企业。\n\n- **计算逻辑**：\n  - 根据活动状态和门店是否存在选择不同的企业归属逻辑路径；\n  - 对历史数据应用预设规则批量处理。\n\n---\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：用户创建活动时无需手动指定所属企业，系统自动处理。\n- 异常提示友好性要求：当无法识别所属企业时应提示用户检查输入信息。\n- 角色权限差异：创建人角色影响“代建草稿”状态下企业归属的判定逻辑。\n\n---\n\n## 3. 异常场景分析\n- 输入异常处理：活动状态或门店信息缺失时应触发错误处理机制。\n- 操作异常处理：中途取消或修改活动状态可能影响企业归属逻辑。\n- 外部依赖异常处理：用户信息接口不可用时需有容错机制。\n- PRD未明确的隐性异常场景：多门店情况下的企业归属优先级未说明。\n\n---\n\n## 4. 兼容性需求分析\n- PRD中未提供相关信息。\n\n---\n\n## 5. 安全性需求分析\n- 数据安全要求：确保活动与所属企业的绑定关系不被非法篡改。\n- 权限控制要求：仅允许合法创建人操作其所属企业范围内的活动。\n- 业务安全要求：防止通过伪造门店信息绕过企业归属限制。\n\n---\n\n## 6. 性能需求分析\n- PRD中未提供相关信息。\n- 高并发场景要求：PRD中未提供相关信息。", "requirement_pic": [], "ui_link": "https://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001067351", "tech_link": "https://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001067351", "create_time": "2025-06-24T14:52:41", "create_person": "刘艳霞", "is_active": true, "iteration_name": "【V3.1.6.0】四季蝉集团化数据隔离改造-0701", "total_ai_test_case": 10, "recall_ai_test_case": 2, "business_name": "四季蝉"}, {"id": 48, "iteration_id": "1161969829001002076", "business_id": "0", "requirement_id": 1067581, "requirement_content": "目前售后提交问题后，值班人员分析问题时间可能比较长，但是售后迟迟得不到回应，会产生焦虑，无法知晓是否有技术人员在处理该问题，故增加以下逻辑：\n\nB端及C端在问题详情页增加一个按钮，按钮名称为：【安心告知】，点击后基于原问题推送一条消息，消息内容为：技术人员正在跟进中，请耐心等待\n问题状态不做任何变更，仅仅只是推送一条消息\n按钮需要做判断，只有待受理及解决中状态才可点击\n按钮点击后在未关闭页面的情况下，不支持再次点击", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：用户在问题详情页点击【安心告知】按钮，系统基于原问题推送消息“技术人员正在跟进中，请耐心等待”，按钮状态变更为不可点击。\n- **分支流程**：若问题状态为非“待受理”或“解决中”，按钮不可见或不可点击；页面刷新后恢复初始状态。\n- **流程节点**：输入节点（问题状态、按钮点击事件），处理节点（状态判断、消息推送逻辑），输出节点（消息推送结果），存储节点（无新增存储需求）。\n\n### 1.2 功能模块拆解\n- **核心模块**：问题详情页模块、消息推送模块、状态判断模块。\n- **输入输出**：问题详情页模块（输入：问题ID、问题状态；输出：按钮显示/隐藏状态）、消息推送模块（输入：问题ID、消息模板；输出：推送结果）、状态判断模块（输入：问题状态；输出：是否允许点击）。\n- **模块接口**：问题详情页调用状态判断模块获取按钮状态，调用消息推送模块完成消息发送。\n\n### 1.3 业务规则提取\n- **核心规则**：按钮仅在问题状态为“待受理”或“解决中”时可用，点击后禁止再次点击。\n- **验证规则**：校验问题状态是否符合要求；确保按钮点击后状态变更正确。\n- **计算逻辑**：无复杂计算逻辑，主要依赖状态判断和消息模板填充。\n\n---\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：按钮点击后需即时反馈，避免用户重复操作。\n- 异常提示友好性要求：当按钮不可点击时，需通过视觉方式（如灰化按钮）明确提示。\n- 角色权限差异：PRD未提及角色权限差异，默认所有可访问问题详情页的用户均可见按钮。\n\n---\n\n## 3. 异常场景分析\n- 输入异常处理：若问题状态为空或无效，按钮应默认隐藏或不可点击。\n- 操作异常处理：按钮多次快速点击时需屏蔽重复操作；网络中断时需提示推送失败。\n- 外部依赖异常处理：消息推送失败时记录日志，并提供重试机制。\n- PRD未明确的隐性异常场景：多用户同时操作同一问题详情页时的按钮状态同步问题。\n\n---\n\n## 4. 兼容性需求分析\n- PRD明确的兼容范围：未提及具体兼容性要求，但需确保支持主流浏览器及不同分辨率下的正常显示。\n\n---\n\n## 5. 安全性需求分析\n- 数据安全要求：推送的消息内容需脱敏处理，防止敏感信息泄露。\n- 权限控制要求：按钮显示与点击权限需严格基于问题状态判断。\n- 业务安全要求：防止恶意用户通过技术手段绕过状态限制进行非法操作。\n\n---\n\n## 6. 性能需求分析\n- PRD明确的性能指标：未提及具体性能指标。\n- 高并发场景要求：需确保在多用户同时点击按钮时，系统能够稳定处理并返回结果。", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-06-20T17:44:31", "create_person": "杨逍", "is_active": true, "iteration_name": "【V1.6】鹰眼（当前迭代）", "total_ai_test_case": 10, "recall_ai_test_case": 5, "business_name": "默认"}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752214032478}
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140712478|Thread-5 (process_request_thread):6341865472|INFO|userRequestMiddleware.py:143|当前请求耗时：0.07894706726074219 秒
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140714506|Thread-9 (process_request_thread):6341865472|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/export_to_xmind_by_record/?recognize_record_id=50，请求方式：GET，请求参数为：<QueryDict: {'recognize_record_id': ['50']}>
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140714508|Thread-9 (process_request_thread):6341865472|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140714508|Thread-9 (process_request_thread):6341865472|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140714530|Thread-10 (recordSystemLog):6358691840|INFO|commonUtil.py:51|用户 liuyanxia 导出识别id为 50 的AI生成用例
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140714568|Thread-9 (process_request_thread):6341865472|INFO|userRequestMiddleware.py:143|当前请求耗时：0.06171107292175293 秒
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140741681|Thread-11 (process_request_thread):6341865472|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_generate_test_case_list/，请求方式：POST，请求参数为：b'{"system":"","module":"","status":"","recognize_record_id":50,"pagenum":1,"pagesize":10}'
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140741684|Thread-11 (process_request_thread):6341865472|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140741685|Thread-11 (process_request_thread):6341865472|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140741704|Thread-12 (recordSystemLog):6358691840|INFO|commonUtil.py:51|用户 liuyanxia 查询AI生成用例列表，查询条件：系统=，模块= , 状态= 
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140741720|Thread-11 (process_request_thread):6341865472|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 10, "total": 15, "data": [{"id": 483, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-特殊字符活动名创建-系统兼容处理", "system": "活动管理系统", "module": "活动管理", "premise": "活动名称中包含特殊字符如#、&、@等", "test_steps": "1. 输入活动名为“新年促销#2024”\n2. 提交表单\n3. 检查页面展示与数据库存储", "expected_result": "1. 活动成功创建\n2. 页面正常显示特殊字符\n3. 数据库中保留原始名称，无乱码", "status": "1", "create_time": "2025-06-30T16:08:54", "update_time": "2025-07-04T11:17:06", "update_person": "刘艳霞", "is_active": true}, {"id": 482, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-权限不足用户试图访问其他企业活动-拒绝访问", "system": "活动管理系统", "module": "活动管理", "premise": "用户为普通员工，尝试访问其他企业活动详情页", "test_steps": "1. 用户直接输入URL访问非所属企业活动详情页\n2. 系统验证用户权限\n3. 检查响应码及页面内容", "expected_result": "1. 页面返回403或跳转至无权限提示页\n2. 无权查看该活动详情\n3. 日志中记录非法访问尝试", "status": "1", "create_time": "2025-06-30T16:08:54", "update_time": "2025-07-04T11:17:08", "update_person": "刘艳霞", "is_active": true}, {"id": 481, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-活动所属企业加密存储", "system": "活动管理系统", "module": "活动管理", "premise": "数据库中需对所属企业字段加密存储", "test_steps": "1. 用户创建活动\n2. 检查数据库记录\n3. 验证所属企业字段是否加密", "expected_result": "1. 活动创建成功\n2. 数据库中“所属企业”字段为加密字符串\n3. 前台展示为企业名称而非密文", "status": "1", "create_time": "2025-06-30T16:08:54", "update_time": "2025-07-04T11:17:09", "update_person": "刘艳霞", "is_active": true}, {"id": 480, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-并发创建相同活动名-系统阻止重复创建", "system": "活动管理系统", "module": "活动管理", "premise": "两个用户同时提交相同活动名称和配置", "test_steps": "1. 用户A提交创建活动\n2. 用户B几乎同时提交相同活动\n3. 检查系统是否允许重复创建", "expected_result": "1. 第二个请求被拒绝\n2. 显示提示：“活动名称已存在”\n3. 数据库中仅存在一条记录", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 479, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-门店为空但状态非草稿-阻止保存并提示", "system": "活动管理系统", "module": "活动管理", "premise": "活动状态为“进行中”，门店列表为空", "test_steps": "1. 用户创建活动，设置状态为“进行中”\n2. 不选择任何门店\n3. 提交表单", "expected_result": "1. 页面提示：“门店不能为空”\n2. 所属企业字段为空\n3. 活动未被创建", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 478, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-跨企业门店创建活动-系统自动处理", "system": "活动管理系统", "module": "活动管理", "premise": "用户选择多个门店，其中部分属于不同企业", "test_steps": "1. 用户进入活动创建页\n2. 选择A门店（企业X）、B门店（企业Y）\n3. 提交保存\n4. 检查所属企业字段", "expected_result": "1. 活动成功创建\n2. 所属企业字段为A门店所属企业X\n3. 系统记录多企业门店情况", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 477, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-活动状态无效-阻止保存并提示", "system": "活动管理系统", "module": "活动管理", "premise": "输入的活动状态为系统未定义的值（如“测试状态”）", "test_steps": "1. 创建活动时在状态字段输入非法值\n2. 提交表单\n3. 检查是否出现错误提示", "expected_result": "1. 页面提示：“活动状态无效”\n2. 所属企业字段为空\n3. 活动未被创建", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 476, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-多门店归属不一致-归属优先级最高的门店", "system": "活动管理系统", "module": "活动管理", "premise": "用户配置多个门店，不同门店所属企业不同", "test_steps": "1. 创建活动，选择多个门店（A属于企业X，B属于企业Y）\n2. 提交保存\n3. 检查所属企业字段", "expected_result": "1. 活动成功创建\n2. 所属企业字段为A门店所属企业X\n3. 页面展示正确的企业名称", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 475, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-历史无门店草稿活动-归属集团", "system": "活动管理系统", "module": "活动管理", "premise": "存在历史活动数据，状态为“代建草稿”，未配置门店信息", "test_steps": "1. 用户进入活动编辑页面\n2. 检查所属企业字段\n3. 提交保存操作", "expected_result": "1. 所属企业字段值为集团\n2. 页面不显示错误提示\n3. 活动可正常保存", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 474, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-非法修改所属企业字段-拦截并拒绝请求", "system": "活动管理系统", "module": "活动管理", "premise": "用户尝试通过前端修改器篡改“所属企业”字段值", "test_steps": "1. 用户打开开发者工具，手动修改“所属企业”字段值\n2. 提交活动创建表单\n3. 后端验证字段合法性", "expected_result": "1. 请求被服务器拒绝\n2. 返回错误码403或提示“字段非法操作”\n3. 数据库中未写入非法企业ID", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752214061720}
|hydee-auto_server|************|118281e74ca94d3c895efa5b18a4b0ff|||20250711140741721|Thread-11 (process_request_thread):6341865472|INFO|userRequestMiddleware.py:143|当前请求耗时：0.03914785385131836 秒
|hydee-auto_server|************|5d197b177af14f5eaee61ae0c02fc247|||20250711141324082|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|5d197b177af14f5eaee61ae0c02fc247|||20250711141324082|MainThread:8571161728|INFO|tool_manager.py:75|开始自动注册工具...
|hydee-auto_server|************|5d197b177af14f5eaee61ae0c02fc247|||20250711141324549|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|5d197b177af14f5eaee61ae0c02fc247|||20250711141324578|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|5d197b177af14f5eaee61ae0c02fc247|||20250711141324579|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 下载小程序代码
|hydee-auto_server|************|5d197b177af14f5eaee61ae0c02fc247|||20250711141324579|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试报告
|hydee-auto_server|************|5d197b177af14f5eaee61ae0c02fc247|||20250711141324579|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试用例
|hydee-auto_server|************|5d197b177af14f5eaee61ae0c02fc247|||20250711141324579|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从hops获取异常日志
|hydee-auto_server|************|5d197b177af14f5eaee61ae0c02fc247|||20250711141324579|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从skywalking获取sql
|hydee-auto_server|************|5d197b177af14f5eaee61ae0c02fc247|||20250711141324579|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: jenkins构建
|hydee-auto_server|************|5d197b177af14f5eaee61ae0c02fc247|||20250711141324579|MainThread:8571161728|INFO|tool_manager.py:95|完成工具注册，总数: 6
|hydee-auto_server|************|ffc27bac53804785895285e79486d747|||20250711141402134|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|ffc27bac53804785895285e79486d747|||20250711141402135|MainThread:8571161728|INFO|tool_manager.py:75|开始自动注册工具...
|hydee-auto_server|************|ffc27bac53804785895285e79486d747|||20250711141402557|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|ffc27bac53804785895285e79486d747|||20250711141402584|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|ffc27bac53804785895285e79486d747|||20250711141402585|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 下载小程序代码
|hydee-auto_server|************|ffc27bac53804785895285e79486d747|||20250711141402585|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试报告
|hydee-auto_server|************|ffc27bac53804785895285e79486d747|||20250711141402585|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试用例
|hydee-auto_server|************|ffc27bac53804785895285e79486d747|||20250711141402585|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从hops获取异常日志
|hydee-auto_server|************|ffc27bac53804785895285e79486d747|||20250711141402585|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从skywalking获取sql
|hydee-auto_server|************|ffc27bac53804785895285e79486d747|||20250711141402585|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: jenkins构建
|hydee-auto_server|************|ffc27bac53804785895285e79486d747|||20250711141402585|MainThread:8571161728|INFO|tool_manager.py:95|完成工具注册，总数: 6
|hydee-auto_server|************|665e10c35a064d5c9f23129d7889b317|||20250711141450456|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|665e10c35a064d5c9f23129d7889b317|||20250711141450457|MainThread:8571161728|INFO|tool_manager.py:75|开始自动注册工具...
|hydee-auto_server|************|665e10c35a064d5c9f23129d7889b317|||20250711141450895|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|665e10c35a064d5c9f23129d7889b317|||20250711141450924|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|665e10c35a064d5c9f23129d7889b317|||20250711141450924|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 下载小程序代码
|hydee-auto_server|************|665e10c35a064d5c9f23129d7889b317|||20250711141450924|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试报告
|hydee-auto_server|************|665e10c35a064d5c9f23129d7889b317|||20250711141450925|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试用例
|hydee-auto_server|************|665e10c35a064d5c9f23129d7889b317|||20250711141450925|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从hops获取异常日志
|hydee-auto_server|************|665e10c35a064d5c9f23129d7889b317|||20250711141450925|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从skywalking获取sql
|hydee-auto_server|************|665e10c35a064d5c9f23129d7889b317|||20250711141450925|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: jenkins构建
|hydee-auto_server|************|665e10c35a064d5c9f23129d7889b317|||20250711141450925|MainThread:8571161728|INFO|tool_manager.py:95|完成工具注册，总数: 6
|hydee-auto_server|************|a5128aa352034ae38ea00567b25a6f6d|||20250711141503993|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|a5128aa352034ae38ea00567b25a6f6d|||20250711141503993|MainThread:8571161728|INFO|tool_manager.py:75|开始自动注册工具...
|hydee-auto_server|************|a5128aa352034ae38ea00567b25a6f6d|||20250711141504425|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|a5128aa352034ae38ea00567b25a6f6d|||20250711141504452|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|a5128aa352034ae38ea00567b25a6f6d|||20250711141504452|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 下载小程序代码
|hydee-auto_server|************|a5128aa352034ae38ea00567b25a6f6d|||20250711141504452|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试报告
|hydee-auto_server|************|a5128aa352034ae38ea00567b25a6f6d|||20250711141504452|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试用例
|hydee-auto_server|************|a5128aa352034ae38ea00567b25a6f6d|||20250711141504452|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从hops获取异常日志
|hydee-auto_server|************|a5128aa352034ae38ea00567b25a6f6d|||20250711141504452|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从skywalking获取sql
|hydee-auto_server|************|a5128aa352034ae38ea00567b25a6f6d|||20250711141504453|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: jenkins构建
|hydee-auto_server|************|a5128aa352034ae38ea00567b25a6f6d|||20250711141504453|MainThread:8571161728|INFO|tool_manager.py:95|完成工具注册，总数: 6
|hydee-auto_server|************|c25066e0e0674ac7aafb563b86ecbaf2|||20250711142106865|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|c25066e0e0674ac7aafb563b86ecbaf2|||20250711142106865|MainThread:8571161728|INFO|tool_manager.py:75|开始自动注册工具...
|hydee-auto_server|************|c25066e0e0674ac7aafb563b86ecbaf2|||20250711142107284|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|c25066e0e0674ac7aafb563b86ecbaf2|||20250711142107311|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|c25066e0e0674ac7aafb563b86ecbaf2|||20250711142107311|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 下载小程序代码
|hydee-auto_server|************|c25066e0e0674ac7aafb563b86ecbaf2|||20250711142107311|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试报告
|hydee-auto_server|************|c25066e0e0674ac7aafb563b86ecbaf2|||20250711142107311|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试用例
|hydee-auto_server|************|c25066e0e0674ac7aafb563b86ecbaf2|||20250711142107311|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从hops获取异常日志
|hydee-auto_server|************|c25066e0e0674ac7aafb563b86ecbaf2|||20250711142107311|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从skywalking获取sql
|hydee-auto_server|************|c25066e0e0674ac7aafb563b86ecbaf2|||20250711142107311|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: jenkins构建
|hydee-auto_server|************|c25066e0e0674ac7aafb563b86ecbaf2|||20250711142107312|MainThread:8571161728|INFO|tool_manager.py:95|完成工具注册，总数: 6
|hydee-auto_server|************|c25066e0e0674ac7aafb563b86ecbaf2|||20250711142123023|Thread-5 (process_request_thread):12935360512|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/export_to_xmind_by_record/?recognize_record_id=50，请求方式：GET，请求参数为：<QueryDict: {'recognize_record_id': ['50']}>
|hydee-auto_server|************|c25066e0e0674ac7aafb563b86ecbaf2|||20250711142123027|Thread-5 (process_request_thread):12935360512|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|c25066e0e0674ac7aafb563b86ecbaf2|||20250711142123027|Thread-5 (process_request_thread):12935360512|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|c25066e0e0674ac7aafb563b86ecbaf2|||20250711142123043|Thread-6 (recordSystemLog):12952186880|INFO|commonUtil.py:51|用户 liuyanxia 导出识别id为 50 的AI生成用例
|hydee-auto_server|************|c25066e0e0674ac7aafb563b86ecbaf2|||20250711142123070|Thread-5 (process_request_thread):12935360512|INFO|userRequestMiddleware.py:143|当前请求耗时：0.04791402816772461 秒
|hydee-auto_server|************|b3d5535e181b4345884d9303b8c2797c|||20250711142328473|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|b3d5535e181b4345884d9303b8c2797c|||20250711142328474|MainThread:8571161728|INFO|tool_manager.py:75|开始自动注册工具...
|hydee-auto_server|************|b3d5535e181b4345884d9303b8c2797c|||20250711142329062|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|b3d5535e181b4345884d9303b8c2797c|||20250711142329092|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|b3d5535e181b4345884d9303b8c2797c|||20250711142329092|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 下载小程序代码
|hydee-auto_server|************|b3d5535e181b4345884d9303b8c2797c|||20250711142329092|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试报告
|hydee-auto_server|************|b3d5535e181b4345884d9303b8c2797c|||20250711142329093|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试用例
|hydee-auto_server|************|b3d5535e181b4345884d9303b8c2797c|||20250711142329093|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从hops获取异常日志
|hydee-auto_server|************|b3d5535e181b4345884d9303b8c2797c|||20250711142329093|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从skywalking获取sql
|hydee-auto_server|************|b3d5535e181b4345884d9303b8c2797c|||20250711142329093|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: jenkins构建
|hydee-auto_server|************|b3d5535e181b4345884d9303b8c2797c|||20250711142329093|MainThread:8571161728|INFO|tool_manager.py:95|完成工具注册，总数: 6
|hydee-auto_server|************|cb88d1bbad8641bb912c2e8668a144de|||20250711142410944|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|cb88d1bbad8641bb912c2e8668a144de|||20250711142410944|MainThread:8571161728|INFO|tool_manager.py:75|开始自动注册工具...
|hydee-auto_server|************|cb88d1bbad8641bb912c2e8668a144de|||20250711142411414|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|cb88d1bbad8641bb912c2e8668a144de|||20250711142411445|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|cb88d1bbad8641bb912c2e8668a144de|||20250711142411446|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 下载小程序代码
|hydee-auto_server|************|cb88d1bbad8641bb912c2e8668a144de|||20250711142411446|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试报告
|hydee-auto_server|************|cb88d1bbad8641bb912c2e8668a144de|||20250711142411446|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试用例
|hydee-auto_server|************|cb88d1bbad8641bb912c2e8668a144de|||20250711142411446|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从hops获取异常日志
|hydee-auto_server|************|cb88d1bbad8641bb912c2e8668a144de|||20250711142411446|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从skywalking获取sql
|hydee-auto_server|************|cb88d1bbad8641bb912c2e8668a144de|||20250711142411446|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: jenkins构建
|hydee-auto_server|************|cb88d1bbad8641bb912c2e8668a144de|||20250711142411446|MainThread:8571161728|INFO|tool_manager.py:95|完成工具注册，总数: 6
|hydee-auto_server|************|cb88d1bbad8641bb912c2e8668a144de|||20250711142424174|Thread-5 (process_request_thread):13069578240|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/export_to_xmind_by_record/?recognize_record_id=50，请求方式：GET，请求参数为：<QueryDict: {'recognize_record_id': ['50']}>
|hydee-auto_server|************|cb88d1bbad8641bb912c2e8668a144de|||20250711142424175|Thread-5 (process_request_thread):13069578240|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|cb88d1bbad8641bb912c2e8668a144de|||20250711142424175|Thread-5 (process_request_thread):13069578240|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|cb88d1bbad8641bb912c2e8668a144de|||20250711142424195|Thread-6 (recordSystemLog):13086404608|INFO|commonUtil.py:51|用户 liuyanxia 导出识别id为 50 的AI生成用例
|hydee-auto_server|************|cb88d1bbad8641bb912c2e8668a144de|||20250711142424229|Thread-5 (process_request_thread):13069578240|INFO|userRequestMiddleware.py:143|当前请求耗时：0.055474042892456055 秒
|hydee-auto_server|************|a8f265a6ac57427d8f7da2a384b78de5|||20250711143201684|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|a8f265a6ac57427d8f7da2a384b78de5|||20250711143201685|MainThread:8571161728|INFO|tool_manager.py:75|开始自动注册工具...
|hydee-auto_server|************|a8f265a6ac57427d8f7da2a384b78de5|||20250711143202146|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|a8f265a6ac57427d8f7da2a384b78de5|||20250711143202176|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|a8f265a6ac57427d8f7da2a384b78de5|||20250711143202176|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 下载小程序代码
|hydee-auto_server|************|a8f265a6ac57427d8f7da2a384b78de5|||20250711143202176|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试报告
|hydee-auto_server|************|a8f265a6ac57427d8f7da2a384b78de5|||20250711143202176|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试用例
|hydee-auto_server|************|a8f265a6ac57427d8f7da2a384b78de5|||20250711143202176|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从hops获取异常日志
|hydee-auto_server|************|a8f265a6ac57427d8f7da2a384b78de5|||20250711143202176|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从skywalking获取sql
|hydee-auto_server|************|a8f265a6ac57427d8f7da2a384b78de5|||20250711143202176|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: jenkins构建
|hydee-auto_server|************|a8f265a6ac57427d8f7da2a384b78de5|||20250711143202176|MainThread:8571161728|INFO|tool_manager.py:95|完成工具注册，总数: 6
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711143216060|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711143216061|MainThread:8571161728|INFO|tool_manager.py:75|开始自动注册工具...
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711143216484|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711143216511|MainThread:8571161728|INFO|llm_service.py:23|LangChain LLM服务初始化完成，模型: qwen-max
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711143216512|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 下载小程序代码
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711143216512|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试报告
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711143216512|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 生成测试用例
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711143216512|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从hops获取异常日志
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711143216512|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: 从skywalking获取sql
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711143216512|MainThread:8571161728|INFO|tool_manager.py:71|工具注册成功: jenkins构建
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711143216513|MainThread:8571161728|INFO|tool_manager.py:95|完成工具注册，总数: 6
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151045825|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_generate_test_case_list/，请求方式：POST，请求参数为：b'{"system":"","module":"","status":"","recognize_record_id":50,"pagenum":1,"pagesize":10}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151045827|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151045828|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151045846|Thread-6 (recordSystemLog):13098889216|INFO|commonUtil.py:51|用户 liuyanxia 查询AI生成用例列表，查询条件：系统=，模块= , 状态= 
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151045855|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 10, "total": 15, "data": [{"id": 483, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-特殊字符活动名创建-系统兼容处理", "system": "活动管理系统", "module": "活动管理", "premise": "活动名称中包含特殊字符如#、&、@等", "test_steps": "1. 输入活动名为“新年促销#2024”\n2. 提交表单\n3. 检查页面展示与数据库存储", "expected_result": "1. 活动成功创建\n2. 页面正常显示特殊字符\n3. 数据库中保留原始名称，无乱码", "status": "1", "create_time": "2025-06-30T16:08:54", "update_time": "2025-07-04T11:17:06", "update_person": "刘艳霞", "is_active": true}, {"id": 482, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-权限不足用户试图访问其他企业活动-拒绝访问", "system": "活动管理系统", "module": "活动管理", "premise": "用户为普通员工，尝试访问其他企业活动详情页", "test_steps": "1. 用户直接输入URL访问非所属企业活动详情页\n2. 系统验证用户权限\n3. 检查响应码及页面内容", "expected_result": "1. 页面返回403或跳转至无权限提示页\n2. 无权查看该活动详情\n3. 日志中记录非法访问尝试", "status": "1", "create_time": "2025-06-30T16:08:54", "update_time": "2025-07-04T11:17:08", "update_person": "刘艳霞", "is_active": true}, {"id": 481, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-活动所属企业加密存储", "system": "活动管理系统", "module": "活动管理", "premise": "数据库中需对所属企业字段加密存储", "test_steps": "1. 用户创建活动\n2. 检查数据库记录\n3. 验证所属企业字段是否加密", "expected_result": "1. 活动创建成功\n2. 数据库中“所属企业”字段为加密字符串\n3. 前台展示为企业名称而非密文", "status": "1", "create_time": "2025-06-30T16:08:54", "update_time": "2025-07-04T11:17:09", "update_person": "刘艳霞", "is_active": true}, {"id": 480, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-并发创建相同活动名-系统阻止重复创建", "system": "活动管理系统", "module": "活动管理", "premise": "两个用户同时提交相同活动名称和配置", "test_steps": "1. 用户A提交创建活动\n2. 用户B几乎同时提交相同活动\n3. 检查系统是否允许重复创建", "expected_result": "1. 第二个请求被拒绝\n2. 显示提示：“活动名称已存在”\n3. 数据库中仅存在一条记录", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 479, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-门店为空但状态非草稿-阻止保存并提示", "system": "活动管理系统", "module": "活动管理", "premise": "活动状态为“进行中”，门店列表为空", "test_steps": "1. 用户创建活动，设置状态为“进行中”\n2. 不选择任何门店\n3. 提交表单", "expected_result": "1. 页面提示：“门店不能为空”\n2. 所属企业字段为空\n3. 活动未被创建", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 478, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-跨企业门店创建活动-系统自动处理", "system": "活动管理系统", "module": "活动管理", "premise": "用户选择多个门店，其中部分属于不同企业", "test_steps": "1. 用户进入活动创建页\n2. 选择A门店（企业X）、B门店（企业Y）\n3. 提交保存\n4. 检查所属企业字段", "expected_result": "1. 活动成功创建\n2. 所属企业字段为A门店所属企业X\n3. 系统记录多企业门店情况", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 477, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-活动状态无效-阻止保存并提示", "system": "活动管理系统", "module": "活动管理", "premise": "输入的活动状态为系统未定义的值（如“测试状态”）", "test_steps": "1. 创建活动时在状态字段输入非法值\n2. 提交表单\n3. 检查是否出现错误提示", "expected_result": "1. 页面提示：“活动状态无效”\n2. 所属企业字段为空\n3. 活动未被创建", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 476, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-多门店归属不一致-归属优先级最高的门店", "system": "活动管理系统", "module": "活动管理", "premise": "用户配置多个门店，不同门店所属企业不同", "test_steps": "1. 创建活动，选择多个门店（A属于企业X，B属于企业Y）\n2. 提交保存\n3. 检查所属企业字段", "expected_result": "1. 活动成功创建\n2. 所属企业字段为A门店所属企业X\n3. 页面展示正确的企业名称", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 475, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-历史无门店草稿活动-归属集团", "system": "活动管理系统", "module": "活动管理", "premise": "存在历史活动数据，状态为“代建草稿”，未配置门店信息", "test_steps": "1. 用户进入活动编辑页面\n2. 检查所属企业字段\n3. 提交保存操作", "expected_result": "1. 所属企业字段值为集团\n2. 页面不显示错误提示\n3. 活动可正常保存", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 474, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-非法修改所属企业字段-拦截并拒绝请求", "system": "活动管理系统", "module": "活动管理", "premise": "用户尝试通过前端修改器篡改“所属企业”字段值", "test_steps": "1. 用户打开开发者工具，手动修改“所属企业”字段值\n2. 提交活动创建表单\n3. 后端验证字段合法性", "expected_result": "1. 请求被服务器拒绝\n2. 返回错误码403或提示“字段非法操作”\n3. 数据库中未写入非法企业ID", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752217845855}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151045855|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.02972102165222168 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151048078|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/get_case_detail/?test_case_id=482，请求方式：GET，请求参数为：<QueryDict: {'test_case_id': ['482']}>
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151048078|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151048079|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151048097|Thread-7 (recordSystemLog):13100986368|INFO|commonUtil.py:51|用户 liuyanxia 查看AI用例 482 详情
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151048104|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"id": 482, "requirement_id": 1067351, "case_scene": "活动管理-权限不足用户试图访问其他企业活动-拒绝访问", "system": "活动管理系统", "module": "活动管理", "premise": "用户为普通员工，尝试访问其他企业活动详情页", "test_steps": "1. 用户直接输入URL访问非所属企业活动详情页\n2. 系统验证用户权限\n3. 检查响应码及页面内容", "expected_result": "1. 页面返回403或跳转至无权限提示页\n2. 无权查看该活动详情\n3. 日志中记录非法访问尝试", "status": "1", "update_person": "刘艳霞", "create_time": "2025-06-30 16:08:54", "is_active": true}, "meta": {"msg": "用例详情获取成功", "status": 200}, "timestamp": 1752217848104}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151048104|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.026138782501220703 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151050031|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/update_ai_test_case/，请求方式：POST，请求参数为：b'{"id":482,"requirement_id":1067351,"case_scene":"\xe6\xb4\xbb\xe5\x8a\xa8\xe7\xae\xa1\xe7\x90\x86-\xe6\x9d\x83\xe9\x99\x90\xe4\xb8\x8d\xe8\xb6\xb3\xe7\x94\xa8\xe6\x88\xb7\xe8\xaf\x95\xe5\x9b\xbe\xe8\xae\xbf\xe9\x97\xae\xe5\x85\xb6\xe4\xbb\x96\xe4\xbc\x81\xe4\xb8\x9a\xe6\xb4\xbb\xe5\x8a\xa8-\xe6\x8b\x92\xe7\xbb\x9d\xe8\xae\xbf\xe9\x97\xae","system":"\xe6\xb4\xbb\xe5\x8a\xa8\xe7\xae\xa1\xe7\x90\x86\xe7\xb3\xbb\xe7\xbb\x9f1","module":"\xe6\xb4\xbb\xe5\x8a\xa8\xe7\xae\xa1\xe7\x90\x86","premise":"\xe7\x94\xa8\xe6\x88\xb7\xe4\xb8\xba\xe6\x99\xae\xe9\x80\x9a\xe5\x91\x98\xe5\xb7\xa5\xef\xbc\x8c\xe5\xb0\x9d\xe8\xaf\x95\xe8\xae\xbf\xe9\x97\xae\xe5\x85\xb6\xe4\xbb\x96\xe4\xbc\x81\xe4\xb8\x9a\xe6\xb4\xbb\xe5\x8a\xa8\xe8\xaf\xa6\xe6\x83\x85\xe9\xa1\xb5","test_steps":"1. \xe7\x94\xa8\xe6\x88\xb7\xe7\x9b\xb4\xe6\x8e\xa5\xe8\xbe\x93\xe5\x85\xa5URL\xe8\xae\xbf\xe9\x97\xae\xe9\x9d\x9e\xe6\x89\x80\xe5\xb1\x9e\xe4\xbc\x81\xe4\xb8\x9a\xe6\xb4\xbb\xe5\x8a\xa8\xe8\xaf\xa6\xe6\x83\x85\xe9\xa1\xb5\\n2. \xe7\xb3\xbb\xe7\xbb\x9f\xe9\xaa\x8c\xe8\xaf\x81\xe7\x94\xa8\xe6\x88\xb7\xe6\x9d\x83\xe9\x99\x90\\n3. \xe6\xa3\x80\xe6\x9f\xa5\xe5\x93\x8d\xe5\xba\x94\xe7\xa0\x81\xe5\x8f\x8a\xe9\xa1\xb5\xe9\x9d\xa2\xe5\x86\x85\xe5\xae\xb9","expected_result":"1. \xe9\xa1\xb5\xe9\x9d\xa2\xe8\xbf\x94\xe5\x9b\x9e403\xe6\x88\x96\xe8\xb7\xb3\xe8\xbd\xac\xe8\x87\xb3\xe6\x97\xa0\xe6\x9d\x83\xe9\x99\x90\xe6\x8f\x90\xe7\xa4\xba\xe9\xa1\xb5\\n2. \xe6\x97\xa0\xe6\x9d\x83\xe6\x9f\xa5\xe7\x9c\x8b\xe8\xaf\xa5\xe6\xb4\xbb\xe5\x8a\xa8\xe8\xaf\xa6\xe6\x83\x85\\n3. \xe6\x97\xa5\xe5\xbf\x97\xe4\xb8\xad\xe8\xae\xb0\xe5\xbd\x95\xe9\x9d\x9e\xe6\xb3\x95\xe8\xae\xbf\xe9\x97\xae\xe5\xb0\x9d\xe8\xaf\x95","status":"1","update_person":"\xe5\x88\x98\xe8\x89\xb3\xe9\x9c\x9e","create_time":"2025-06-30 16:08:54","is_active":true}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151050031|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151050032|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151050050|Thread-8 (recordSystemLog):13100986368|INFO|commonUtil.py:51|用户 刘艳霞 编辑AI生成用例成功，编辑用例id: 482
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151050051|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": null, "meta": {"msg": "用例更新成功", "status": 200}, "timestamp": 1752217850051}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151050052|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.019710063934326172 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151050071|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_generate_test_case_list/，请求方式：POST，请求参数为：b'{"system":"","module":"","status":"","recognize_record_id":50,"pagenum":1,"pagesize":10}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151050071|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151050071|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151050084|Thread-9 (recordSystemLog):13100986368|INFO|commonUtil.py:51|用户 liuyanxia 查询AI生成用例列表，查询条件：系统=，模块= , 状态= 
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151050092|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 10, "total": 15, "data": [{"id": 483, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-特殊字符活动名创建-系统兼容处理", "system": "活动管理系统", "module": "活动管理", "premise": "活动名称中包含特殊字符如#、&、@等", "test_steps": "1. 输入活动名为“新年促销#2024”\n2. 提交表单\n3. 检查页面展示与数据库存储", "expected_result": "1. 活动成功创建\n2. 页面正常显示特殊字符\n3. 数据库中保留原始名称，无乱码", "status": "1", "create_time": "2025-06-30T16:08:54", "update_time": "2025-07-04T11:17:06", "update_person": "刘艳霞", "is_active": true}, {"id": 482, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-权限不足用户试图访问其他企业活动-拒绝访问", "system": "活动管理系统1", "module": "活动管理", "premise": "用户为普通员工，尝试访问其他企业活动详情页", "test_steps": "1. 用户直接输入URL访问非所属企业活动详情页\n2. 系统验证用户权限\n3. 检查响应码及页面内容", "expected_result": "1. 页面返回403或跳转至无权限提示页\n2. 无权查看该活动详情\n3. 日志中记录非法访问尝试", "status": "1", "create_time": "2025-06-30T16:08:54", "update_time": "2025-07-11T15:10:50", "update_person": "刘艳霞", "is_active": true}, {"id": 481, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-活动所属企业加密存储", "system": "活动管理系统", "module": "活动管理", "premise": "数据库中需对所属企业字段加密存储", "test_steps": "1. 用户创建活动\n2. 检查数据库记录\n3. 验证所属企业字段是否加密", "expected_result": "1. 活动创建成功\n2. 数据库中“所属企业”字段为加密字符串\n3. 前台展示为企业名称而非密文", "status": "1", "create_time": "2025-06-30T16:08:54", "update_time": "2025-07-04T11:17:09", "update_person": "刘艳霞", "is_active": true}, {"id": 480, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-并发创建相同活动名-系统阻止重复创建", "system": "活动管理系统", "module": "活动管理", "premise": "两个用户同时提交相同活动名称和配置", "test_steps": "1. 用户A提交创建活动\n2. 用户B几乎同时提交相同活动\n3. 检查系统是否允许重复创建", "expected_result": "1. 第二个请求被拒绝\n2. 显示提示：“活动名称已存在”\n3. 数据库中仅存在一条记录", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 479, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-门店为空但状态非草稿-阻止保存并提示", "system": "活动管理系统", "module": "活动管理", "premise": "活动状态为“进行中”，门店列表为空", "test_steps": "1. 用户创建活动，设置状态为“进行中”\n2. 不选择任何门店\n3. 提交表单", "expected_result": "1. 页面提示：“门店不能为空”\n2. 所属企业字段为空\n3. 活动未被创建", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 478, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-跨企业门店创建活动-系统自动处理", "system": "活动管理系统", "module": "活动管理", "premise": "用户选择多个门店，其中部分属于不同企业", "test_steps": "1. 用户进入活动创建页\n2. 选择A门店（企业X）、B门店（企业Y）\n3. 提交保存\n4. 检查所属企业字段", "expected_result": "1. 活动成功创建\n2. 所属企业字段为A门店所属企业X\n3. 系统记录多企业门店情况", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 477, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-活动状态无效-阻止保存并提示", "system": "活动管理系统", "module": "活动管理", "premise": "输入的活动状态为系统未定义的值（如“测试状态”）", "test_steps": "1. 创建活动时在状态字段输入非法值\n2. 提交表单\n3. 检查是否出现错误提示", "expected_result": "1. 页面提示：“活动状态无效”\n2. 所属企业字段为空\n3. 活动未被创建", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 476, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-多门店归属不一致-归属优先级最高的门店", "system": "活动管理系统", "module": "活动管理", "premise": "用户配置多个门店，不同门店所属企业不同", "test_steps": "1. 创建活动，选择多个门店（A属于企业X，B属于企业Y）\n2. 提交保存\n3. 检查所属企业字段", "expected_result": "1. 活动成功创建\n2. 所属企业字段为A门店所属企业X\n3. 页面展示正确的企业名称", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 475, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-历史无门店草稿活动-归属集团", "system": "活动管理系统", "module": "活动管理", "premise": "存在历史活动数据，状态为“代建草稿”，未配置门店信息", "test_steps": "1. 用户进入活动编辑页面\n2. 检查所属企业字段\n3. 提交保存操作", "expected_result": "1. 所属企业字段值为集团\n2. 页面不显示错误提示\n3. 活动可正常保存", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 474, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-非法修改所属企业字段-拦截并拒绝请求", "system": "活动管理系统", "module": "活动管理", "premise": "用户尝试通过前端修改器篡改“所属企业”字段值", "test_steps": "1. 用户打开开发者工具，手动修改“所属企业”字段值\n2. 提交活动创建表单\n3. 后端验证字段合法性", "expected_result": "1. 请求被服务器拒绝\n2. 返回错误码403或提示“字段非法操作”\n3. 数据库中未写入非法企业ID", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752217850092}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151050093|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.021728992462158203 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151056425|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_generate_test_case_list/，请求方式：POST，请求参数为：b'{"system":"","module":"","status":"","recognize_record_id":50,"pagenum":1,"pagesize":10}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151056425|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151056426|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151056438|Thread-10 (recordSystemLog):13100986368|INFO|commonUtil.py:51|用户 liuyanxia 查询AI生成用例列表，查询条件：系统=，模块= , 状态= 
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151056448|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 10, "total": 15, "data": [{"id": 483, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-特殊字符活动名创建-系统兼容处理", "system": "活动管理系统", "module": "活动管理", "premise": "活动名称中包含特殊字符如#、&、@等", "test_steps": "1. 输入活动名为“新年促销#2024”\n2. 提交表单\n3. 检查页面展示与数据库存储", "expected_result": "1. 活动成功创建\n2. 页面正常显示特殊字符\n3. 数据库中保留原始名称，无乱码", "status": "1", "create_time": "2025-06-30T16:08:54", "update_time": "2025-07-04T11:17:06", "update_person": "刘艳霞", "is_active": true}, {"id": 482, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-权限不足用户试图访问其他企业活动-拒绝访问", "system": "活动管理系统1", "module": "活动管理", "premise": "用户为普通员工，尝试访问其他企业活动详情页", "test_steps": "1. 用户直接输入URL访问非所属企业活动详情页\n2. 系统验证用户权限\n3. 检查响应码及页面内容", "expected_result": "1. 页面返回403或跳转至无权限提示页\n2. 无权查看该活动详情\n3. 日志中记录非法访问尝试", "status": "1", "create_time": "2025-06-30T16:08:54", "update_time": "2025-07-11T15:10:50", "update_person": "刘艳霞", "is_active": true}, {"id": 481, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-活动所属企业加密存储", "system": "活动管理系统", "module": "活动管理", "premise": "数据库中需对所属企业字段加密存储", "test_steps": "1. 用户创建活动\n2. 检查数据库记录\n3. 验证所属企业字段是否加密", "expected_result": "1. 活动创建成功\n2. 数据库中“所属企业”字段为加密字符串\n3. 前台展示为企业名称而非密文", "status": "1", "create_time": "2025-06-30T16:08:54", "update_time": "2025-07-04T11:17:09", "update_person": "刘艳霞", "is_active": true}, {"id": 480, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-并发创建相同活动名-系统阻止重复创建", "system": "活动管理系统", "module": "活动管理", "premise": "两个用户同时提交相同活动名称和配置", "test_steps": "1. 用户A提交创建活动\n2. 用户B几乎同时提交相同活动\n3. 检查系统是否允许重复创建", "expected_result": "1. 第二个请求被拒绝\n2. 显示提示：“活动名称已存在”\n3. 数据库中仅存在一条记录", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 479, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-门店为空但状态非草稿-阻止保存并提示", "system": "活动管理系统", "module": "活动管理", "premise": "活动状态为“进行中”，门店列表为空", "test_steps": "1. 用户创建活动，设置状态为“进行中”\n2. 不选择任何门店\n3. 提交表单", "expected_result": "1. 页面提示：“门店不能为空”\n2. 所属企业字段为空\n3. 活动未被创建", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 478, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-跨企业门店创建活动-系统自动处理", "system": "活动管理系统", "module": "活动管理", "premise": "用户选择多个门店，其中部分属于不同企业", "test_steps": "1. 用户进入活动创建页\n2. 选择A门店（企业X）、B门店（企业Y）\n3. 提交保存\n4. 检查所属企业字段", "expected_result": "1. 活动成功创建\n2. 所属企业字段为A门店所属企业X\n3. 系统记录多企业门店情况", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 477, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-活动状态无效-阻止保存并提示", "system": "活动管理系统", "module": "活动管理", "premise": "输入的活动状态为系统未定义的值（如“测试状态”）", "test_steps": "1. 创建活动时在状态字段输入非法值\n2. 提交表单\n3. 检查是否出现错误提示", "expected_result": "1. 页面提示：“活动状态无效”\n2. 所属企业字段为空\n3. 活动未被创建", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 476, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-多门店归属不一致-归属优先级最高的门店", "system": "活动管理系统", "module": "活动管理", "premise": "用户配置多个门店，不同门店所属企业不同", "test_steps": "1. 创建活动，选择多个门店（A属于企业X，B属于企业Y）\n2. 提交保存\n3. 检查所属企业字段", "expected_result": "1. 活动成功创建\n2. 所属企业字段为A门店所属企业X\n3. 页面展示正确的企业名称", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 475, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-历史无门店草稿活动-归属集团", "system": "活动管理系统", "module": "活动管理", "premise": "存在历史活动数据，状态为“代建草稿”，未配置门店信息", "test_steps": "1. 用户进入活动编辑页面\n2. 检查所属企业字段\n3. 提交保存操作", "expected_result": "1. 所属企业字段值为集团\n2. 页面不显示错误提示\n3. 活动可正常保存", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 474, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-非法修改所属企业字段-拦截并拒绝请求", "system": "活动管理系统", "module": "活动管理", "premise": "用户尝试通过前端修改器篡改“所属企业”字段值", "test_steps": "1. 用户打开开发者工具，手动修改“所属企业”字段值\n2. 提交活动创建表单\n3. 后端验证字段合法性", "expected_result": "1. 请求被服务器拒绝\n2. 返回错误码403或提示“字段非法操作”\n3. 数据库中未写入非法企业ID", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752217856448}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151056449|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.02315974235534668 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151057859|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/export_to_xmind_by_record/?recognize_record_id=50，请求方式：GET，请求参数为：<QueryDict: {'recognize_record_id': ['50']}>
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151057861|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151057861|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151057882|Thread-11 (recordSystemLog):13100986368|INFO|commonUtil.py:51|用户 liuyanxia 导出识别id为 50 的AI生成用例
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151057936|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.0771949291229248 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151058157|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/export_to_xmind_by_record/?recognize_record_id=50，请求方式：GET，请求参数为：<QueryDict: {'recognize_record_id': ['50']}>
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151058157|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151058157|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151058173|Thread-12 (recordSystemLog):13100986368|INFO|commonUtil.py:51|用户 liuyanxia 导出识别id为 50 的AI生成用例
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151058226|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.06995415687561035 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151242711|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_generate_test_case_list/，请求方式：POST，请求参数为：b'{"system":"","module":"","status":"","recognize_record_id":50,"pagenum":1,"pagesize":10}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151242733|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151242740|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151242778|Thread-13 (recordSystemLog):13102034944|INFO|commonUtil.py:51|用户 liuyanxia 查询AI生成用例列表，查询条件：系统=，模块= , 状态= 
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151242789|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 10, "total": 15, "data": [{"id": 483, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-特殊字符活动名创建-系统兼容处理", "system": "活动管理系统", "module": "活动管理", "premise": "活动名称中包含特殊字符如#、&、@等", "test_steps": "1. 输入活动名为“新年促销#2024”\n2. 提交表单\n3. 检查页面展示与数据库存储", "expected_result": "1. 活动成功创建\n2. 页面正常显示特殊字符\n3. 数据库中保留原始名称，无乱码", "status": "1", "create_time": "2025-06-30T16:08:54", "update_time": "2025-07-04T11:17:06", "update_person": "刘艳霞", "is_active": true}, {"id": 482, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-权限不足用户试图访问其他企业活动-拒绝访问", "system": "活动管理系统1", "module": "活动管理", "premise": "用户为普通员工，尝试访问其他企业活动详情页", "test_steps": "1. 用户直接输入URL访问非所属企业活动详情页\n2. 系统验证用户权限\n3. 检查响应码及页面内容", "expected_result": "1. 页面返回403或跳转至无权限提示页\n2. 无权查看该活动详情\n3. 日志中记录非法访问尝试", "status": "1", "create_time": "2025-06-30T16:08:54", "update_time": "2025-07-11T15:10:50", "update_person": "刘艳霞", "is_active": true}, {"id": 481, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-活动所属企业加密存储", "system": "活动管理系统", "module": "活动管理", "premise": "数据库中需对所属企业字段加密存储", "test_steps": "1. 用户创建活动\n2. 检查数据库记录\n3. 验证所属企业字段是否加密", "expected_result": "1. 活动创建成功\n2. 数据库中“所属企业”字段为加密字符串\n3. 前台展示为企业名称而非密文", "status": "1", "create_time": "2025-06-30T16:08:54", "update_time": "2025-07-04T11:17:09", "update_person": "刘艳霞", "is_active": true}, {"id": 480, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-并发创建相同活动名-系统阻止重复创建", "system": "活动管理系统", "module": "活动管理", "premise": "两个用户同时提交相同活动名称和配置", "test_steps": "1. 用户A提交创建活动\n2. 用户B几乎同时提交相同活动\n3. 检查系统是否允许重复创建", "expected_result": "1. 第二个请求被拒绝\n2. 显示提示：“活动名称已存在”\n3. 数据库中仅存在一条记录", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 479, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-门店为空但状态非草稿-阻止保存并提示", "system": "活动管理系统", "module": "活动管理", "premise": "活动状态为“进行中”，门店列表为空", "test_steps": "1. 用户创建活动，设置状态为“进行中”\n2. 不选择任何门店\n3. 提交表单", "expected_result": "1. 页面提示：“门店不能为空”\n2. 所属企业字段为空\n3. 活动未被创建", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 478, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-跨企业门店创建活动-系统自动处理", "system": "活动管理系统", "module": "活动管理", "premise": "用户选择多个门店，其中部分属于不同企业", "test_steps": "1. 用户进入活动创建页\n2. 选择A门店（企业X）、B门店（企业Y）\n3. 提交保存\n4. 检查所属企业字段", "expected_result": "1. 活动成功创建\n2. 所属企业字段为A门店所属企业X\n3. 系统记录多企业门店情况", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 477, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-活动状态无效-阻止保存并提示", "system": "活动管理系统", "module": "活动管理", "premise": "输入的活动状态为系统未定义的值（如“测试状态”）", "test_steps": "1. 创建活动时在状态字段输入非法值\n2. 提交表单\n3. 检查是否出现错误提示", "expected_result": "1. 页面提示：“活动状态无效”\n2. 所属企业字段为空\n3. 活动未被创建", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 476, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-多门店归属不一致-归属优先级最高的门店", "system": "活动管理系统", "module": "活动管理", "premise": "用户配置多个门店，不同门店所属企业不同", "test_steps": "1. 创建活动，选择多个门店（A属于企业X，B属于企业Y）\n2. 提交保存\n3. 检查所属企业字段", "expected_result": "1. 活动成功创建\n2. 所属企业字段为A门店所属企业X\n3. 页面展示正确的企业名称", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 475, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-历史无门店草稿活动-归属集团", "system": "活动管理系统", "module": "活动管理", "premise": "存在历史活动数据，状态为“代建草稿”，未配置门店信息", "test_steps": "1. 用户进入活动编辑页面\n2. 检查所属企业字段\n3. 提交保存操作", "expected_result": "1. 所属企业字段值为集团\n2. 页面不显示错误提示\n3. 活动可正常保存", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 474, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-非法修改所属企业字段-拦截并拒绝请求", "system": "活动管理系统", "module": "活动管理", "premise": "用户尝试通过前端修改器篡改“所属企业”字段值", "test_steps": "1. 用户打开开发者工具，手动修改“所属企业”字段值\n2. 提交活动创建表单\n3. 后端验证字段合法性", "expected_result": "1. 请求被服务器拒绝\n2. 返回错误码403或提示“字段非法操作”\n3. 数据库中未写入非法企业ID", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752217962790}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151242791|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.07823300361633301 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151244484|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_generate_test_case_list/，请求方式：POST，请求参数为：b'{"system":"","module":"","status":"","recognize_record_id":50,"pagenum":2,"pagesize":10}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151244485|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151244485|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151244503|Thread-14 (recordSystemLog):13102034944|INFO|commonUtil.py:51|用户 liuyanxia 查询AI生成用例列表，查询条件：系统=，模块= , 状态= 
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151244518|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 2, "pagesize": 10, "total": 15, "data": [{"id": 473, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "企业归属判断-门店数据接口异常-记录日志并提示", "system": "活动管理系统", "module": "企业归属判断", "premise": "门店数据接口不可用或返回500错误", "test_steps": "1. 用户尝试创建带门店的活动\n2. 系统调用门店数据接口获取门店信息\n3. 接口返回异常", "expected_result": "1. 页面提示：'无法加载门店信息，请稍后再试'\n2. 日志中记录接口调用失败的具体信息\n3. 活动未被创建，界面保持原状态", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 472, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-管理员发布活动-可见范围受限", "system": "活动管理系统", "module": "活动管理", "premise": "用户为管理员角色，仅能查看本企业活动", "test_steps": "1. 管理员登录系统\n2. 访问活动管理列表页\n3. 尝试搜索其他企业的活动\n4. 查看筛选条件中的企业选项", "expected_result": "1. 列表中仅显示该管理员所在企业的活动\n2. 无法看到不属于其企业的活动条目\n3. 企业下拉框中只包含管理员所属企业", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 471, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "用户资料关联-普通员工创建草稿但未绑定企业-失败并提示", "system": "活动管理系统", "module": "用户资料关联", "premise": "用户为普通员工且尚未填写所属企业信息", "test_steps": "1. 用户进入活动创建页面\n2. 输入所有必填项\n3. 不选择门店，设置状态为“草稿”\n4. 点击保存按钮", "expected_result": "1. 页面弹出错误提示：'请先完善个人信息'\n2. 所属企业字段为空\n3. 活动未被创建", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 470, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "企业归属判断-创建含门店的活动-归属门店所属企业", "system": "活动管理系统", "module": "企业归属判断", "premise": "用户已登录，活动中配置了至少一个门店，活动状态非草稿", "test_steps": "1. 进入活动创建页面\n2. 填写活动信息\n3. 设置活动状态为“进行中”\n4. 在门店列表中选择多个门店（如A门店、B门店）\n5. 提交保存", "expected_result": "1. 活动成功创建\n2. 所属企业字段为所选门店中的第一个门店所属企业\n3. 页面展示活动详情时所属企业与门店匹配", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 469, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-创建草稿状态无门店活动-归属创建人企业", "system": "活动管理系统", "module": "活动管理", "premise": "用户已登录，处于普通员工角色，未配置门店信息，活动状态为草稿", "test_steps": "1. 进入活动创建页面\n2. 填写活动名称和描述\n3. 设置活动状态为“草稿”\n4. 不选择任何门店\n5. 点击保存按钮", "expected_result": "1. 活动成功创建并跳转至列表页\n2. 所属企业字段值为创建人所属企业\n3. 页面显示提示：“活动所属企业自动识别为您的默认企业”", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752217964518}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151244519|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.03411507606506348 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151250293|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_generate_test_case_list/，请求方式：POST，请求参数为：b'{"system":"","module":"","status":"","recognize_record_id":48,"pagenum":2,"pagesize":10}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151250294|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151250294|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151250312|Thread-15 (recordSystemLog):13102034944|INFO|commonUtil.py:51|用户 liuyanxia 查询AI生成用例列表，查询条件：系统=，模块= , 状态= 
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151250368|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:142|接口请求异常：<HttpResponseServerError status_code=500, "text/html">
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151250368|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.07527685165405273 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151544853|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_recognize_record_list/，请求方式：POST，请求参数为：b'{"business_id":"","iteration_name":"","creater":"","pagenum":1,"pagesize":5}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151544860|Thread-16 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/info/?type=businesses，请求方式：GET，请求参数为：<QueryDict: {'type': ['businesses']}>
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151544862|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151544862|Thread-16 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151544862|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151544863|Thread-16 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151544880|Thread-17 (recordSystemLog):***********|INFO|commonUtil.py:51|用户 liuyanxia 查询AI生成用例记录，查询条件：需求ID=，迭代名称=，业务线=，创建人= 
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151544883|Thread-18 (recordSystemLog):***********|INFO|commonUtil.py:51|来自IP：127.0.0.1的用户 liuyanxia 请求获取全部businesses信息
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151544908|Thread-16 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": [{"id": 30, "label": "30-厂家端小程序"}, {"id": 29, "label": "29-一键造数"}, {"id": 28, "label": "28-医保云"}, {"id": 27, "label": "27-四季蝉"}, {"id": 26, "label": "26-数据中心"}, {"id": 25, "label": "25-云仓"}, {"id": 24, "label": "24-随心看"}, {"id": 23, "label": "23-直播"}, {"id": 22, "label": "22-服务商"}, {"id": 21, "label": "21-组织云"}, {"id": 20, "label": "20-微商城9"}, {"id": 19, "label": "19-随心享"}, {"id": 18, "label": "18-支付云"}, {"id": 17, "label": "17-优享会员"}, {"id": 4, "label": "4-OMS-B2C-h2（废除）"}, {"id": 11, "label": "11-OMS-B2C"}, {"id": 16, "label": "16-H2_O2O_Order"}, {"id": 15, "label": "15-H2_OMS_B2C"}, {"id": 14, "label": "14-药事云"}, {"id": 13, "label": "13-商品中台"}, {"id": 12, "label": "12-助手"}, {"id": 10, "label": "10-演示"}, {"id": 8, "label": "8-会员"}, {"id": 7, "label": "7-OMS-O2O"}, {"id": 6, "label": "6-营销"}, {"id": 2, "label": "2-自动化"}, {"id": 0, "label": "0-默认"}], "meta": {"msg": "获取成功", "status": 200}, "timestamp": 1752218144907}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151544910|Thread-16 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.04746508598327637 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151544956|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 5, "total": 51, "data": [{"id": 52, "iteration_id": "1161969829001002058", "business_id": "26", "requirement_id": 1067014, "requirement_content": "增加【美团待铺货&饿百待铺货】页签，同时支持新品页签的切换。\n店铺待铺货商品明细\n前置条件：先选择一个具体的平台店铺。（仅支持选择美团、饿百平台店铺）\n列表数据来源范围：\n商品信息：公域商品列表中，是否可售状态为【是】且有条形码的商品；同时第三方商品店铺无该商品，但所属门店有该商品。\n字段名称 字段逻辑\n平台店铺 平台 + 店铺名称\n商品信息 商品名称 + 规格 + 批准文号 名称\n条形码\nERP商品编码\n平台近30天总销量 美团所有店铺近30天内的销量汇总；支持按照销量切换排序。\nT-1的30天内（下账时间），已下账的商品数量汇总。\n所属门店 门店名称 + 门店编码\nERP门店库存 ERP同步的门店库存\nERP门店价格 ERP同步的门店价格\nERP加权成本价 ERP同步的加权成本价\n预估毛利率 （店铺铺货价格 - 加权成本价） / 加权成本价 * 100%；根据店铺铺货价格动态计算。\n店铺铺货价格 默认门店价格，支持编辑，数据格式：大于0的两位小数。\n店铺商品分类 下拉单选\n列表排序：默认按平台近30天总销量倒序排序\n页签字段文档描述：\n美团待铺货页签字段：\n商品信息：公域商品列表中，是否可售状态为【是】且有条形码的商品才展示。同时第三方商品店铺无该商品，但所属门店有该商品。\n美团近30天销量：连锁在美团所有店铺近30天内的销售数据汇总（T-1的30天内（下账时间），已下账的商品数量汇总。）\n\n饿百待铺货页签：\n商品信息：公域商品列表中，是否可售状态为【是】且有条形码的商品才展示。同时第三方商品店铺无该商品，但所属门店有该商品。\n饿百近30天销量：连锁在饿百所有店铺近30天内的销售数据汇总（T-1的30天内（下账时间），已下账的商品数量汇总。）\n\n查询条件\n查询条件 查询逻辑\n平台店铺 平台 + 店铺，支持搜索对应平台的店铺\n所属门店 门店编码或门店名称，与平台店铺联动。\n商品名称 模糊匹配查询\nERP商品编码 精确匹配查询，支持查多个，最大100个\n条形码 精确匹配查询，支持查多个，最大100个\n平台销量区间 0-n的区间数量，根据选择的平台店铺，确定查询哪个平台的销量\n新品天数设置\n查看新品过滤及设置调整到页面右上角。\n支持在所有页签设置新品天数和勾选新品天数过滤商品数据。切换页签新品勾选条件不变。\n新品天数文案描述：勾选则表示，只查询线上商品库的商品创建时间在设置的新品天数范围内的商品。\n操作\n1.新品铺货-单个操作：按行操作商品铺货，按照编辑后的店铺铺货价格进行铺货（需二次确认）。铺货操作同步处理，并实时反馈操结果，刷新操作页面。\n校验：价格为0的，不支持铺货，且提示：【铺货价格不允许为0。】\n2.新品铺货-批量操作（仅支持当前页批量-一页最多20条）：批量勾选当前页的商品（需二次确认）。铺货操作同步处理，并实时反馈操结果，若有铺货失败的数据，则展示失败原因，并支持失败数据复制。\n校验：价格为0的，不支持勾选。\n3.支持数据导出（查询结果的列表全量数据）", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：用户选择平台店铺 -> 查询待铺货商品列表 -> 选择单个或批量商品进行铺货操作 -> 验证铺货价格 -> 确认铺货 -> 实时反馈结果 -> 刷新页面\n- **分支流程**：\n  - 正常分支：成功铺货后，实时更新数据\n  - 异常分支：铺货价格为0，提示错误信息并阻止操作\n  - 回退流程：铺货失败的数据支持复制和重新提交\n- **流程节点**：\n  - 输入节点：平台店铺选择、查询条件输入\n  - 处理节点：商品列表查询、铺货价格验证、铺货操作执行\n  - 输出节点：铺货成功/失败的结果反馈\n  - 存储节点：铺货后的商品信息存储\n\n### 1.2 功能模块拆解\n- **核心模块**：\n  - 商品列表展示模块\n  - 铺货操作模块\n  - 数据导出模块\n- **输入输出**：\n  - 商品列表展示模块：输入（查询条件）、输出（商品列表）\n  - 铺货操作模块：输入（铺货价格）、输出（铺货结果）\n  - 数据导出模块：输入（查询结果）、输出（导出文件）\n- **模块接口**：\n  - 商品列表展示模块调用铺货操作模块传递铺货信息\n  - 铺货操作模块返回结果给商品列表展示模块\n  - 数据导出模块从商品列表展示模块获取数据\n\n### 1.3 业务规则提取\n- **核心规则**：\n  - 仅展示可售且有条形码的商品\n  - 第三方商品店铺无该商品，但所属门店有该商品\n  - 默认按平台近30天总销量倒序排序\n- **验证规则**：\n  - 价格为0的，不允许铺货\n  - 批量操作每页最多20条记录\n- **计算逻辑**：\n  - 预估毛利率 = (店铺铺货价格 - 加权成本价) / 加权成本价 * 100%\n  - 新品天数过滤：只查询创建时间在设置的新品天数范围内的商品\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：确保页面加载快速，操作响应及时\n- 异常提示友好性要求：提供清晰的错误提示，如铺货价格为0时显示“铺货价格不允许为0”\n- 角色权限差异：未明确提到角色权限差异\n\n## 3. 异常场景分析\n- 输入异常处理：价格为0时不支持铺货，并提示错误\n- 操作异常处理：铺货失败时显示失败原因，并支持失败数据复制\n- 外部依赖异常处理：PRD中未提供相关信息\n- PRD未明确的隐性异常场景：未明确\n\n## 4. 兼容性需求分析\n- PRD明确的兼容范围：未明确\n\n## 5. 安全性需求分析\n- 数据安全要求：未明确\n- 权限控制要求：未明确\n- 业务安全要求：未明确\n\n## 6. 性能需求分析\n- PRD明确的性能指标：未明确\n- 高并发场景要求：未明确", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-07-08T16:18:29", "create_person": "刘艳霞", "is_active": true, "iteration_name": "【V2.3.4.22】商品云-0703", "total_ai_test_case": 10, "recall_ai_test_case": 0, "business_name": "数据中心"}, {"id": 51, "iteration_id": null, "business_id": "27", "requirement_id": 342342, "requirement_content": "- 将员工组织机构刷到数据授权中（店员、店长）\n\n- 将片区经理的授权门店刷到数据授权中（片区经理）\n\n- 将新增四季蝉按钮级别的资源刷到商户已有的角色资源里", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程  \n- **主流程**：系统从组织结构中获取员工信息 → 判断员工角色（店员、店长、片区经理）→ 更新数据授权配置。  \n- **分支流程**：若员工无对应角色，跳过该员工；若门店信息不存在，记录异常日志。  \n- **流程节点**：输入节点为员工组织结构数据，处理节点为角色判断与权限分配，输出节点为更新后的数据授权表。\n\n### 1.2 功能模块拆解  \n- **核心模块**：组织同步模块、角色识别模块、权限更新模块。  \n- **输入输出**：输入为员工组织数据和角色定义，输出为更新后的数据授权结果。  \n- **模块接口**：组织同步模块调用角色识别模块判断角色类型，权限更新模块接收识别结果进行授权更新。\n\n### 1.3 业务规则提取  \n- **核心规则**：仅支持“店员”、“店长”、“片区经理”三类角色的数据授权更新。  \n- **验证规则**：需验证员工所属门店是否有效，片区经理对应的门店是否存在。  \n- **计算逻辑**：根据员工角色确定授权范围，片区经理自动获得其管理门店的访问权限。\n\n---\n\n## 2. 用户体验需求分析  \n- 交互流程流畅性要求：批量处理员工授权时应有进度提示，避免界面无响应。  \n- 异常提示友好性要求：对于未匹配角色或无效门店的员工，应记录并展示清晰的失败原因。  \n- 角色权限差异：不同角色对应不同的授权范围，需在界面中明确区分显示内容。\n\n---\n\n## 3. 异常场景分析  \n- 输入异常处理：员工数据缺失关键字段（如门店ID、角色类型），系统应拒绝处理并记录错误。  \n- 操作异常处理：授权过程中数据库连接失败，应中断操作并返回友好的错误提示。  \n- 外部依赖异常处理：若组织服务不可用，系统应延迟执行任务或提示重试机制。  \n- PRD未明确的隐性异常场景：员工角色变更后旧授权未清理，可能导致数据访问越权问题。\n\n---\n\n## 4. 兼容性需求分析  \n- PRD中未提供相关信息  \n\n---\n\n## 5. 安全性需求分析  \n- 数据安全要求：授权数据同步过程需加密传输，防止敏感信息泄露。  \n- 权限控制要求：仅允许具备管理员权限的用户触发授权刷新操作。  \n- 业务安全要求：确保片区经理只能访问其管理范围内的门店数据，不能越权查看。\n\n---\n\n## 6. 性能需求分析  \n- PRD中未提供相关信息", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-07-08T12:05:46", "create_person": "刘艳霞", "is_active": true, "iteration_name": "", "total_ai_test_case": 18, "recall_ai_test_case": 0, "business_name": "四季蝉"}, {"id": 50, "iteration_id": "1161969829001002043", "business_id": "29", "requirement_id": 1067351, "requirement_content": "进行中、未开始、已发布成功的活动按照已参与的活动门店记录所属企业；\n\n代建草稿状态无活动门店的活动都按创建人的员工资料中的所属企业创建，记录所属企业；\n\n历史的代建草稿状态的无活动门店的所属企业一律处理为集团；\n\n历史的代建草稿状态的有活动门店的所属企业按活动门店所属企业记录", "requirement_analyze_content": "## 1. 功能需求分析123123\n\n### 1.1 核心业务流程  \n- **主流程**：活动创建时根据活动状态、是否配置门店，决定所属企业的归属逻辑；  \n- **分支流程**：草稿状态无门店活动按创建人企业处理；历史数据特殊处理为集团或门店企业；  \n- **流程节点**：输入为活动状态、门店信息、创建人信息；输出为所属企业字段值。\n\n### 1.2 功能模块拆解  \n- **核心模块**：活动管理模块、企业归属判断模块、用户资料关联模块；  \n- **输入输出**：输入包括活动状态、门店列表、员工所属企业；输出为活动的“所属企业”属性；  \n- **模块接口**：活动模块调用归属判断模块获取所属企业；归属判断模块从用户资料模块读取企业信息。\n\n### 1.3 业务规则提取  \n- **核心规则**：草稿状态无门店活动使用创建人企业；有门店则使用门店所属企业；  \n- **验证规则**：需验证活动状态是否为“代建草稿”、门店是否存在、创建人信息是否完整；  \n- **计算逻辑**：判断优先级为：门店 > 创建人 > 集团（历史无门店草稿）。\n\n---\n\n## 2. 用户体验需求分析  \n- 交互流程流畅性要求：用户创建活动时应自动识别并填充所属企业，无需手动选择；  \n- 异常提示友好性要求：若无法确定所属企业，应给出明确提示说明原因；  \n- 角色权限差异：普通员工可创建草稿，管理员可发布活动，不同角色查看范围受企业限制。\n\n---\n\n## 3. 异常场景分析  \n- 输入异常处理：活动状态无效或门店为空但非草稿状态，系统应阻止保存并提示错误；  \n- 操作异常处理：创建人未绑定所属企业时，应提示“请先完善个人信息”；  \n- 外部依赖异常处理：若门店数据接口异常，应记录日志并提示“无法加载门店信息”；  \n- PRD未明确的隐性异常场景：多门店归属不一致时如何处理、跨企业门店是否允许存在。\n\n---\n\n## 4. 兼容性需求分析  \n- PRD中未提供相关信息。\n\n---\n\n## 5. 安全性需求分析  \n- 数据安全要求：所属企业字段属于敏感业务数据，应加密存储或脱敏展示；  \n- 权限控制要求：仅所属企业人员可查看和操作相关活动数据；  \n- 业务安全要求：防止非法篡改“所属企业”字段以规避数据隔离策略。\n\n---\n\n## 6. 性能需求分析  \n- PRD中未提供相关信息。", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-06-30T16:06:40", "create_person": "刘艳霞", "is_active": true, "iteration_name": "【V3.1.6.0】四季蝉集团化数据隔离改造-0701", "total_ai_test_case": 15, "recall_ai_test_case": 3, "business_name": "一键造数"}, {"id": 49, "iteration_id": "1161969829001002043", "business_id": "27", "requirement_id": 1067351, "requirement_content": "进行中、未开始、已发布成功的活动按照已参与的活动门店记录所属企业；\n\n代建草稿状态无活动门店的活动都按创建人的员工资料中的所属企业创建，记录所属企业；\n\n历史的代建草稿状态的无活动门店的所属企业一律处理为集团；\n\n历史的代建草稿状态的有活动门店的所属企业按活动门店所属企业记录", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：活动创建时判断活动状态和门店信息，决定所属企业的归属规则。\n- **分支流程**：\n  - 活动为“进行中/未开始/已发布成功”且有门店 → 所属企业按门店所属企业记录；\n  - 活动为“代建草稿”状态且无门店 → 所属企业按创建人所属企业记录；\n  - 历史“代建草稿”状态无门店 → 归为集团；\n  - 历史“代建草稿”状态有门店 → 按门店所属企业记录。\n\n- **流程节点**：\n  - 输入节点：活动状态、活动门店信息、创建人所属企业；\n  - 处理节点：根据规则判断活动所属企业；\n  - 输出节点：活动记录中所属企业字段；\n  - 存储节点：活动数据表中保存所属企业信息。\n\n### 1.2 功能模块拆解\n- **核心模块**：\n  - 活动管理模块：负责活动的创建、状态维护及所属企业分配；\n  - 用户管理模块：提供创建人所属企业信息；\n  - 数据存储模块：保存活动及相关企业信息。\n\n- **输入输出**：\n  - 活动管理模块输入：活动状态、门店列表、创建人ID；\n  - 活动管理模块输出：活动记录及其所属企业字段；\n  - 用户管理模块输入/输出：创建人信息查询接口。\n\n- **模块接口**：\n  - 活动管理调用用户管理接口获取创建人所属企业；\n  - 活动管理写入数据存储模块保存活动记录。\n\n### 1.3 业务规则提取\n- **核心规则**：\n  - “进行中/未开始/已发布成功”活动优先依据门店确定所属企业；\n  - “代建草稿”活动无门店则归属创建人企业或默认集团；\n  - 历史数据统一处理规则不可变。\n\n- **验证规则**：\n  - 验证活动状态是否符合分类标准；\n  - 验证门店是否存在并关联有效企业。\n\n- **计算逻辑**：\n  - 根据活动状态和门店是否存在选择不同的企业归属逻辑路径；\n  - 对历史数据应用预设规则批量处理。\n\n---\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：用户创建活动时无需手动指定所属企业，系统自动处理。\n- 异常提示友好性要求：当无法识别所属企业时应提示用户检查输入信息。\n- 角色权限差异：创建人角色影响“代建草稿”状态下企业归属的判定逻辑。\n\n---\n\n## 3. 异常场景分析\n- 输入异常处理：活动状态或门店信息缺失时应触发错误处理机制。\n- 操作异常处理：中途取消或修改活动状态可能影响企业归属逻辑。\n- 外部依赖异常处理：用户信息接口不可用时需有容错机制。\n- PRD未明确的隐性异常场景：多门店情况下的企业归属优先级未说明。\n\n---\n\n## 4. 兼容性需求分析\n- PRD中未提供相关信息。\n\n---\n\n## 5. 安全性需求分析\n- 数据安全要求：确保活动与所属企业的绑定关系不被非法篡改。\n- 权限控制要求：仅允许合法创建人操作其所属企业范围内的活动。\n- 业务安全要求：防止通过伪造门店信息绕过企业归属限制。\n\n---\n\n## 6. 性能需求分析\n- PRD中未提供相关信息。\n- 高并发场景要求：PRD中未提供相关信息。", "requirement_pic": [], "ui_link": "https://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001067351", "tech_link": "https://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001067351", "create_time": "2025-06-24T14:52:41", "create_person": "刘艳霞", "is_active": true, "iteration_name": "【V3.1.6.0】四季蝉集团化数据隔离改造-0701", "total_ai_test_case": 10, "recall_ai_test_case": 2, "business_name": "四季蝉"}, {"id": 48, "iteration_id": "1161969829001002076", "business_id": "0", "requirement_id": 1067581, "requirement_content": "目前售后提交问题后，值班人员分析问题时间可能比较长，但是售后迟迟得不到回应，会产生焦虑，无法知晓是否有技术人员在处理该问题，故增加以下逻辑：\n\nB端及C端在问题详情页增加一个按钮，按钮名称为：【安心告知】，点击后基于原问题推送一条消息，消息内容为：技术人员正在跟进中，请耐心等待\n问题状态不做任何变更，仅仅只是推送一条消息\n按钮需要做判断，只有待受理及解决中状态才可点击\n按钮点击后在未关闭页面的情况下，不支持再次点击", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：用户在问题详情页点击【安心告知】按钮，系统基于原问题推送消息“技术人员正在跟进中，请耐心等待”，按钮状态变更为不可点击。\n- **分支流程**：若问题状态为非“待受理”或“解决中”，按钮不可见或不可点击；页面刷新后恢复初始状态。\n- **流程节点**：输入节点（问题状态、按钮点击事件），处理节点（状态判断、消息推送逻辑），输出节点（消息推送结果），存储节点（无新增存储需求）。\n\n### 1.2 功能模块拆解\n- **核心模块**：问题详情页模块、消息推送模块、状态判断模块。\n- **输入输出**：问题详情页模块（输入：问题ID、问题状态；输出：按钮显示/隐藏状态）、消息推送模块（输入：问题ID、消息模板；输出：推送结果）、状态判断模块（输入：问题状态；输出：是否允许点击）。\n- **模块接口**：问题详情页调用状态判断模块获取按钮状态，调用消息推送模块完成消息发送。\n\n### 1.3 业务规则提取\n- **核心规则**：按钮仅在问题状态为“待受理”或“解决中”时可用，点击后禁止再次点击。\n- **验证规则**：校验问题状态是否符合要求；确保按钮点击后状态变更正确。\n- **计算逻辑**：无复杂计算逻辑，主要依赖状态判断和消息模板填充。\n\n---\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：按钮点击后需即时反馈，避免用户重复操作。\n- 异常提示友好性要求：当按钮不可点击时，需通过视觉方式（如灰化按钮）明确提示。\n- 角色权限差异：PRD未提及角色权限差异，默认所有可访问问题详情页的用户均可见按钮。\n\n---\n\n## 3. 异常场景分析\n- 输入异常处理：若问题状态为空或无效，按钮应默认隐藏或不可点击。\n- 操作异常处理：按钮多次快速点击时需屏蔽重复操作；网络中断时需提示推送失败。\n- 外部依赖异常处理：消息推送失败时记录日志，并提供重试机制。\n- PRD未明确的隐性异常场景：多用户同时操作同一问题详情页时的按钮状态同步问题。\n\n---\n\n## 4. 兼容性需求分析\n- PRD明确的兼容范围：未提及具体兼容性要求，但需确保支持主流浏览器及不同分辨率下的正常显示。\n\n---\n\n## 5. 安全性需求分析\n- 数据安全要求：推送的消息内容需脱敏处理，防止敏感信息泄露。\n- 权限控制要求：按钮显示与点击权限需严格基于问题状态判断。\n- 业务安全要求：防止恶意用户通过技术手段绕过状态限制进行非法操作。\n\n---\n\n## 6. 性能需求分析\n- PRD明确的性能指标：未提及具体性能指标。\n- 高并发场景要求：需确保在多用户同时点击按钮时，系统能够稳定处理并返回结果。", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-06-20T17:44:31", "create_person": "杨逍", "is_active": true, "iteration_name": "【V1.6】鹰眼（当前迭代）", "total_ai_test_case": 10, "recall_ai_test_case": 5, "business_name": "默认"}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752218144956}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151544956|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.1037130355834961 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151546756|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_generate_test_case_list/，请求方式：POST，请求参数为：b'{"system":"","module":"","status":"","recognize_record_id":50,"pagenum":1,"pagesize":10}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151546757|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151546758|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151546780|Thread-19 (recordSystemLog):***********|INFO|commonUtil.py:51|用户 liuyanxia 查询AI生成用例列表，查询条件：系统=，模块= , 状态= 
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151546794|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 10, "total": 15, "data": [{"id": 483, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-特殊字符活动名创建-系统兼容处理", "system": "活动管理系统", "module": "活动管理", "premise": "活动名称中包含特殊字符如#、&、@等", "test_steps": "1. 输入活动名为“新年促销#2024”\n2. 提交表单\n3. 检查页面展示与数据库存储", "expected_result": "1. 活动成功创建\n2. 页面正常显示特殊字符\n3. 数据库中保留原始名称，无乱码", "status": "1", "create_time": "2025-06-30T16:08:54", "update_time": "2025-07-04T11:17:06", "update_person": "刘艳霞", "is_active": true}, {"id": 482, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-权限不足用户试图访问其他企业活动-拒绝访问", "system": "活动管理系统1", "module": "活动管理", "premise": "用户为普通员工，尝试访问其他企业活动详情页", "test_steps": "1. 用户直接输入URL访问非所属企业活动详情页\n2. 系统验证用户权限\n3. 检查响应码及页面内容", "expected_result": "1. 页面返回403或跳转至无权限提示页\n2. 无权查看该活动详情\n3. 日志中记录非法访问尝试", "status": "1", "create_time": "2025-06-30T16:08:54", "update_time": "2025-07-11T15:10:50", "update_person": "刘艳霞", "is_active": true}, {"id": 481, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-活动所属企业加密存储", "system": "活动管理系统", "module": "活动管理", "premise": "数据库中需对所属企业字段加密存储", "test_steps": "1. 用户创建活动\n2. 检查数据库记录\n3. 验证所属企业字段是否加密", "expected_result": "1. 活动创建成功\n2. 数据库中“所属企业”字段为加密字符串\n3. 前台展示为企业名称而非密文", "status": "1", "create_time": "2025-06-30T16:08:54", "update_time": "2025-07-04T11:17:09", "update_person": "刘艳霞", "is_active": true}, {"id": 480, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-并发创建相同活动名-系统阻止重复创建", "system": "活动管理系统", "module": "活动管理", "premise": "两个用户同时提交相同活动名称和配置", "test_steps": "1. 用户A提交创建活动\n2. 用户B几乎同时提交相同活动\n3. 检查系统是否允许重复创建", "expected_result": "1. 第二个请求被拒绝\n2. 显示提示：“活动名称已存在”\n3. 数据库中仅存在一条记录", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 479, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-门店为空但状态非草稿-阻止保存并提示", "system": "活动管理系统", "module": "活动管理", "premise": "活动状态为“进行中”，门店列表为空", "test_steps": "1. 用户创建活动，设置状态为“进行中”\n2. 不选择任何门店\n3. 提交表单", "expected_result": "1. 页面提示：“门店不能为空”\n2. 所属企业字段为空\n3. 活动未被创建", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 478, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-跨企业门店创建活动-系统自动处理", "system": "活动管理系统", "module": "活动管理", "premise": "用户选择多个门店，其中部分属于不同企业", "test_steps": "1. 用户进入活动创建页\n2. 选择A门店（企业X）、B门店（企业Y）\n3. 提交保存\n4. 检查所属企业字段", "expected_result": "1. 活动成功创建\n2. 所属企业字段为A门店所属企业X\n3. 系统记录多企业门店情况", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 477, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-活动状态无效-阻止保存并提示", "system": "活动管理系统", "module": "活动管理", "premise": "输入的活动状态为系统未定义的值（如“测试状态”）", "test_steps": "1. 创建活动时在状态字段输入非法值\n2. 提交表单\n3. 检查是否出现错误提示", "expected_result": "1. 页面提示：“活动状态无效”\n2. 所属企业字段为空\n3. 活动未被创建", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 476, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-多门店归属不一致-归属优先级最高的门店", "system": "活动管理系统", "module": "活动管理", "premise": "用户配置多个门店，不同门店所属企业不同", "test_steps": "1. 创建活动，选择多个门店（A属于企业X，B属于企业Y）\n2. 提交保存\n3. 检查所属企业字段", "expected_result": "1. 活动成功创建\n2. 所属企业字段为A门店所属企业X\n3. 页面展示正确的企业名称", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 475, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-历史无门店草稿活动-归属集团", "system": "活动管理系统", "module": "活动管理", "premise": "存在历史活动数据，状态为“代建草稿”，未配置门店信息", "test_steps": "1. 用户进入活动编辑页面\n2. 检查所属企业字段\n3. 提交保存操作", "expected_result": "1. 所属企业字段值为集团\n2. 页面不显示错误提示\n3. 活动可正常保存", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 474, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-非法修改所属企业字段-拦截并拒绝请求", "system": "活动管理系统", "module": "活动管理", "premise": "用户尝试通过前端修改器篡改“所属企业”字段值", "test_steps": "1. 用户打开开发者工具，手动修改“所属企业”字段值\n2. 提交活动创建表单\n3. 后端验证字段合法性", "expected_result": "1. 请求被服务器拒绝\n2. 返回错误码403或提示“字段非法操作”\n3. 数据库中未写入非法企业ID", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752218146794}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151546795|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.03808712959289551 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151548526|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_generate_test_case_list/，请求方式：POST，请求参数为：b'{"system":"","module":"","status":"","recognize_record_id":50,"pagenum":2,"pagesize":10}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151548526|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151548527|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151548548|Thread-20 (recordSystemLog):***********|INFO|commonUtil.py:51|用户 liuyanxia 查询AI生成用例列表，查询条件：系统=，模块= , 状态= 
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151548564|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 2, "pagesize": 10, "total": 15, "data": [{"id": 473, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "企业归属判断-门店数据接口异常-记录日志并提示", "system": "活动管理系统", "module": "企业归属判断", "premise": "门店数据接口不可用或返回500错误", "test_steps": "1. 用户尝试创建带门店的活动\n2. 系统调用门店数据接口获取门店信息\n3. 接口返回异常", "expected_result": "1. 页面提示：'无法加载门店信息，请稍后再试'\n2. 日志中记录接口调用失败的具体信息\n3. 活动未被创建，界面保持原状态", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 472, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-管理员发布活动-可见范围受限", "system": "活动管理系统", "module": "活动管理", "premise": "用户为管理员角色，仅能查看本企业活动", "test_steps": "1. 管理员登录系统\n2. 访问活动管理列表页\n3. 尝试搜索其他企业的活动\n4. 查看筛选条件中的企业选项", "expected_result": "1. 列表中仅显示该管理员所在企业的活动\n2. 无法看到不属于其企业的活动条目\n3. 企业下拉框中只包含管理员所属企业", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 471, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "用户资料关联-普通员工创建草稿但未绑定企业-失败并提示", "system": "活动管理系统", "module": "用户资料关联", "premise": "用户为普通员工且尚未填写所属企业信息", "test_steps": "1. 用户进入活动创建页面\n2. 输入所有必填项\n3. 不选择门店，设置状态为“草稿”\n4. 点击保存按钮", "expected_result": "1. 页面弹出错误提示：'请先完善个人信息'\n2. 所属企业字段为空\n3. 活动未被创建", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 470, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "企业归属判断-创建含门店的活动-归属门店所属企业", "system": "活动管理系统", "module": "企业归属判断", "premise": "用户已登录，活动中配置了至少一个门店，活动状态非草稿", "test_steps": "1. 进入活动创建页面\n2. 填写活动信息\n3. 设置活动状态为“进行中”\n4. 在门店列表中选择多个门店（如A门店、B门店）\n5. 提交保存", "expected_result": "1. 活动成功创建\n2. 所属企业字段为所选门店中的第一个门店所属企业\n3. 页面展示活动详情时所属企业与门店匹配", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}, {"id": 469, "recognize_record_id": 50, "requirement_id": 1067351, "case_scene": "活动管理-创建草稿状态无门店活动-归属创建人企业", "system": "活动管理系统", "module": "活动管理", "premise": "用户已登录，处于普通员工角色，未配置门店信息，活动状态为草稿", "test_steps": "1. 进入活动创建页面\n2. 填写活动名称和描述\n3. 设置活动状态为“草稿”\n4. 不选择任何门店\n5. 点击保存按钮", "expected_result": "1. 活动成功创建并跳转至列表页\n2. 所属企业字段值为创建人所属企业\n3. 页面显示提示：“活动所属企业自动识别为您的默认企业”", "status": "0", "create_time": "2025-06-30T16:08:54", "update_time": "2025-06-30T16:08:54", "update_person": "刘艳霞", "is_active": true}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752218148564}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151548566|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.03844618797302246 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151549914|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_recognize_record_list/，请求方式：POST，请求参数为：b'{"business_id":"","iteration_name":"","creater":"","pagenum":1,"pagesize":5}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151549915|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151549916|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151549934|Thread-21 (recordSystemLog):***********|INFO|commonUtil.py:51|用户 liuyanxia 查询AI生成用例记录，查询条件：需求ID=，迭代名称=，业务线=，创建人= 
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151550033|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 5, "total": 51, "data": [{"id": 52, "iteration_id": "1161969829001002058", "business_id": "26", "requirement_id": 1067014, "requirement_content": "增加【美团待铺货&饿百待铺货】页签，同时支持新品页签的切换。\n店铺待铺货商品明细\n前置条件：先选择一个具体的平台店铺。（仅支持选择美团、饿百平台店铺）\n列表数据来源范围：\n商品信息：公域商品列表中，是否可售状态为【是】且有条形码的商品；同时第三方商品店铺无该商品，但所属门店有该商品。\n字段名称 字段逻辑\n平台店铺 平台 + 店铺名称\n商品信息 商品名称 + 规格 + 批准文号 名称\n条形码\nERP商品编码\n平台近30天总销量 美团所有店铺近30天内的销量汇总；支持按照销量切换排序。\nT-1的30天内（下账时间），已下账的商品数量汇总。\n所属门店 门店名称 + 门店编码\nERP门店库存 ERP同步的门店库存\nERP门店价格 ERP同步的门店价格\nERP加权成本价 ERP同步的加权成本价\n预估毛利率 （店铺铺货价格 - 加权成本价） / 加权成本价 * 100%；根据店铺铺货价格动态计算。\n店铺铺货价格 默认门店价格，支持编辑，数据格式：大于0的两位小数。\n店铺商品分类 下拉单选\n列表排序：默认按平台近30天总销量倒序排序\n页签字段文档描述：\n美团待铺货页签字段：\n商品信息：公域商品列表中，是否可售状态为【是】且有条形码的商品才展示。同时第三方商品店铺无该商品，但所属门店有该商品。\n美团近30天销量：连锁在美团所有店铺近30天内的销售数据汇总（T-1的30天内（下账时间），已下账的商品数量汇总。）\n\n饿百待铺货页签：\n商品信息：公域商品列表中，是否可售状态为【是】且有条形码的商品才展示。同时第三方商品店铺无该商品，但所属门店有该商品。\n饿百近30天销量：连锁在饿百所有店铺近30天内的销售数据汇总（T-1的30天内（下账时间），已下账的商品数量汇总。）\n\n查询条件\n查询条件 查询逻辑\n平台店铺 平台 + 店铺，支持搜索对应平台的店铺\n所属门店 门店编码或门店名称，与平台店铺联动。\n商品名称 模糊匹配查询\nERP商品编码 精确匹配查询，支持查多个，最大100个\n条形码 精确匹配查询，支持查多个，最大100个\n平台销量区间 0-n的区间数量，根据选择的平台店铺，确定查询哪个平台的销量\n新品天数设置\n查看新品过滤及设置调整到页面右上角。\n支持在所有页签设置新品天数和勾选新品天数过滤商品数据。切换页签新品勾选条件不变。\n新品天数文案描述：勾选则表示，只查询线上商品库的商品创建时间在设置的新品天数范围内的商品。\n操作\n1.新品铺货-单个操作：按行操作商品铺货，按照编辑后的店铺铺货价格进行铺货（需二次确认）。铺货操作同步处理，并实时反馈操结果，刷新操作页面。\n校验：价格为0的，不支持铺货，且提示：【铺货价格不允许为0。】\n2.新品铺货-批量操作（仅支持当前页批量-一页最多20条）：批量勾选当前页的商品（需二次确认）。铺货操作同步处理，并实时反馈操结果，若有铺货失败的数据，则展示失败原因，并支持失败数据复制。\n校验：价格为0的，不支持勾选。\n3.支持数据导出（查询结果的列表全量数据）", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：用户选择平台店铺 -> 查询待铺货商品列表 -> 选择单个或批量商品进行铺货操作 -> 验证铺货价格 -> 确认铺货 -> 实时反馈结果 -> 刷新页面\n- **分支流程**：\n  - 正常分支：成功铺货后，实时更新数据\n  - 异常分支：铺货价格为0，提示错误信息并阻止操作\n  - 回退流程：铺货失败的数据支持复制和重新提交\n- **流程节点**：\n  - 输入节点：平台店铺选择、查询条件输入\n  - 处理节点：商品列表查询、铺货价格验证、铺货操作执行\n  - 输出节点：铺货成功/失败的结果反馈\n  - 存储节点：铺货后的商品信息存储\n\n### 1.2 功能模块拆解\n- **核心模块**：\n  - 商品列表展示模块\n  - 铺货操作模块\n  - 数据导出模块\n- **输入输出**：\n  - 商品列表展示模块：输入（查询条件）、输出（商品列表）\n  - 铺货操作模块：输入（铺货价格）、输出（铺货结果）\n  - 数据导出模块：输入（查询结果）、输出（导出文件）\n- **模块接口**：\n  - 商品列表展示模块调用铺货操作模块传递铺货信息\n  - 铺货操作模块返回结果给商品列表展示模块\n  - 数据导出模块从商品列表展示模块获取数据\n\n### 1.3 业务规则提取\n- **核心规则**：\n  - 仅展示可售且有条形码的商品\n  - 第三方商品店铺无该商品，但所属门店有该商品\n  - 默认按平台近30天总销量倒序排序\n- **验证规则**：\n  - 价格为0的，不允许铺货\n  - 批量操作每页最多20条记录\n- **计算逻辑**：\n  - 预估毛利率 = (店铺铺货价格 - 加权成本价) / 加权成本价 * 100%\n  - 新品天数过滤：只查询创建时间在设置的新品天数范围内的商品\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：确保页面加载快速，操作响应及时\n- 异常提示友好性要求：提供清晰的错误提示，如铺货价格为0时显示“铺货价格不允许为0”\n- 角色权限差异：未明确提到角色权限差异\n\n## 3. 异常场景分析\n- 输入异常处理：价格为0时不支持铺货，并提示错误\n- 操作异常处理：铺货失败时显示失败原因，并支持失败数据复制\n- 外部依赖异常处理：PRD中未提供相关信息\n- PRD未明确的隐性异常场景：未明确\n\n## 4. 兼容性需求分析\n- PRD明确的兼容范围：未明确\n\n## 5. 安全性需求分析\n- 数据安全要求：未明确\n- 权限控制要求：未明确\n- 业务安全要求：未明确\n\n## 6. 性能需求分析\n- PRD明确的性能指标：未明确\n- 高并发场景要求：未明确", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-07-08T16:18:29", "create_person": "刘艳霞", "is_active": true, "iteration_name": "【V2.3.4.22】商品云-0703", "total_ai_test_case": 10, "recall_ai_test_case": 0, "business_name": "数据中心"}, {"id": 51, "iteration_id": null, "business_id": "27", "requirement_id": 342342, "requirement_content": "- 将员工组织机构刷到数据授权中（店员、店长）\n\n- 将片区经理的授权门店刷到数据授权中（片区经理）\n\n- 将新增四季蝉按钮级别的资源刷到商户已有的角色资源里", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程  \n- **主流程**：系统从组织结构中获取员工信息 → 判断员工角色（店员、店长、片区经理）→ 更新数据授权配置。  \n- **分支流程**：若员工无对应角色，跳过该员工；若门店信息不存在，记录异常日志。  \n- **流程节点**：输入节点为员工组织结构数据，处理节点为角色判断与权限分配，输出节点为更新后的数据授权表。\n\n### 1.2 功能模块拆解  \n- **核心模块**：组织同步模块、角色识别模块、权限更新模块。  \n- **输入输出**：输入为员工组织数据和角色定义，输出为更新后的数据授权结果。  \n- **模块接口**：组织同步模块调用角色识别模块判断角色类型，权限更新模块接收识别结果进行授权更新。\n\n### 1.3 业务规则提取  \n- **核心规则**：仅支持“店员”、“店长”、“片区经理”三类角色的数据授权更新。  \n- **验证规则**：需验证员工所属门店是否有效，片区经理对应的门店是否存在。  \n- **计算逻辑**：根据员工角色确定授权范围，片区经理自动获得其管理门店的访问权限。\n\n---\n\n## 2. 用户体验需求分析  \n- 交互流程流畅性要求：批量处理员工授权时应有进度提示，避免界面无响应。  \n- 异常提示友好性要求：对于未匹配角色或无效门店的员工，应记录并展示清晰的失败原因。  \n- 角色权限差异：不同角色对应不同的授权范围，需在界面中明确区分显示内容。\n\n---\n\n## 3. 异常场景分析  \n- 输入异常处理：员工数据缺失关键字段（如门店ID、角色类型），系统应拒绝处理并记录错误。  \n- 操作异常处理：授权过程中数据库连接失败，应中断操作并返回友好的错误提示。  \n- 外部依赖异常处理：若组织服务不可用，系统应延迟执行任务或提示重试机制。  \n- PRD未明确的隐性异常场景：员工角色变更后旧授权未清理，可能导致数据访问越权问题。\n\n---\n\n## 4. 兼容性需求分析  \n- PRD中未提供相关信息  \n\n---\n\n## 5. 安全性需求分析  \n- 数据安全要求：授权数据同步过程需加密传输，防止敏感信息泄露。  \n- 权限控制要求：仅允许具备管理员权限的用户触发授权刷新操作。  \n- 业务安全要求：确保片区经理只能访问其管理范围内的门店数据，不能越权查看。\n\n---\n\n## 6. 性能需求分析  \n- PRD中未提供相关信息", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-07-08T12:05:46", "create_person": "刘艳霞", "is_active": true, "iteration_name": "", "total_ai_test_case": 18, "recall_ai_test_case": 0, "business_name": "四季蝉"}, {"id": 50, "iteration_id": "1161969829001002043", "business_id": "29", "requirement_id": 1067351, "requirement_content": "进行中、未开始、已发布成功的活动按照已参与的活动门店记录所属企业；\n\n代建草稿状态无活动门店的活动都按创建人的员工资料中的所属企业创建，记录所属企业；\n\n历史的代建草稿状态的无活动门店的所属企业一律处理为集团；\n\n历史的代建草稿状态的有活动门店的所属企业按活动门店所属企业记录", "requirement_analyze_content": "## 1. 功能需求分析123123\n\n### 1.1 核心业务流程  \n- **主流程**：活动创建时根据活动状态、是否配置门店，决定所属企业的归属逻辑；  \n- **分支流程**：草稿状态无门店活动按创建人企业处理；历史数据特殊处理为集团或门店企业；  \n- **流程节点**：输入为活动状态、门店信息、创建人信息；输出为所属企业字段值。\n\n### 1.2 功能模块拆解  \n- **核心模块**：活动管理模块、企业归属判断模块、用户资料关联模块；  \n- **输入输出**：输入包括活动状态、门店列表、员工所属企业；输出为活动的“所属企业”属性；  \n- **模块接口**：活动模块调用归属判断模块获取所属企业；归属判断模块从用户资料模块读取企业信息。\n\n### 1.3 业务规则提取  \n- **核心规则**：草稿状态无门店活动使用创建人企业；有门店则使用门店所属企业；  \n- **验证规则**：需验证活动状态是否为“代建草稿”、门店是否存在、创建人信息是否完整；  \n- **计算逻辑**：判断优先级为：门店 > 创建人 > 集团（历史无门店草稿）。\n\n---\n\n## 2. 用户体验需求分析  \n- 交互流程流畅性要求：用户创建活动时应自动识别并填充所属企业，无需手动选择；  \n- 异常提示友好性要求：若无法确定所属企业，应给出明确提示说明原因；  \n- 角色权限差异：普通员工可创建草稿，管理员可发布活动，不同角色查看范围受企业限制。\n\n---\n\n## 3. 异常场景分析  \n- 输入异常处理：活动状态无效或门店为空但非草稿状态，系统应阻止保存并提示错误；  \n- 操作异常处理：创建人未绑定所属企业时，应提示“请先完善个人信息”；  \n- 外部依赖异常处理：若门店数据接口异常，应记录日志并提示“无法加载门店信息”；  \n- PRD未明确的隐性异常场景：多门店归属不一致时如何处理、跨企业门店是否允许存在。\n\n---\n\n## 4. 兼容性需求分析  \n- PRD中未提供相关信息。\n\n---\n\n## 5. 安全性需求分析  \n- 数据安全要求：所属企业字段属于敏感业务数据，应加密存储或脱敏展示；  \n- 权限控制要求：仅所属企业人员可查看和操作相关活动数据；  \n- 业务安全要求：防止非法篡改“所属企业”字段以规避数据隔离策略。\n\n---\n\n## 6. 性能需求分析  \n- PRD中未提供相关信息。", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-06-30T16:06:40", "create_person": "刘艳霞", "is_active": true, "iteration_name": "【V3.1.6.0】四季蝉集团化数据隔离改造-0701", "total_ai_test_case": 15, "recall_ai_test_case": 3, "business_name": "一键造数"}, {"id": 49, "iteration_id": "1161969829001002043", "business_id": "27", "requirement_id": 1067351, "requirement_content": "进行中、未开始、已发布成功的活动按照已参与的活动门店记录所属企业；\n\n代建草稿状态无活动门店的活动都按创建人的员工资料中的所属企业创建，记录所属企业；\n\n历史的代建草稿状态的无活动门店的所属企业一律处理为集团；\n\n历史的代建草稿状态的有活动门店的所属企业按活动门店所属企业记录", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：活动创建时判断活动状态和门店信息，决定所属企业的归属规则。\n- **分支流程**：\n  - 活动为“进行中/未开始/已发布成功”且有门店 → 所属企业按门店所属企业记录；\n  - 活动为“代建草稿”状态且无门店 → 所属企业按创建人所属企业记录；\n  - 历史“代建草稿”状态无门店 → 归为集团；\n  - 历史“代建草稿”状态有门店 → 按门店所属企业记录。\n\n- **流程节点**：\n  - 输入节点：活动状态、活动门店信息、创建人所属企业；\n  - 处理节点：根据规则判断活动所属企业；\n  - 输出节点：活动记录中所属企业字段；\n  - 存储节点：活动数据表中保存所属企业信息。\n\n### 1.2 功能模块拆解\n- **核心模块**：\n  - 活动管理模块：负责活动的创建、状态维护及所属企业分配；\n  - 用户管理模块：提供创建人所属企业信息；\n  - 数据存储模块：保存活动及相关企业信息。\n\n- **输入输出**：\n  - 活动管理模块输入：活动状态、门店列表、创建人ID；\n  - 活动管理模块输出：活动记录及其所属企业字段；\n  - 用户管理模块输入/输出：创建人信息查询接口。\n\n- **模块接口**：\n  - 活动管理调用用户管理接口获取创建人所属企业；\n  - 活动管理写入数据存储模块保存活动记录。\n\n### 1.3 业务规则提取\n- **核心规则**：\n  - “进行中/未开始/已发布成功”活动优先依据门店确定所属企业；\n  - “代建草稿”活动无门店则归属创建人企业或默认集团；\n  - 历史数据统一处理规则不可变。\n\n- **验证规则**：\n  - 验证活动状态是否符合分类标准；\n  - 验证门店是否存在并关联有效企业。\n\n- **计算逻辑**：\n  - 根据活动状态和门店是否存在选择不同的企业归属逻辑路径；\n  - 对历史数据应用预设规则批量处理。\n\n---\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：用户创建活动时无需手动指定所属企业，系统自动处理。\n- 异常提示友好性要求：当无法识别所属企业时应提示用户检查输入信息。\n- 角色权限差异：创建人角色影响“代建草稿”状态下企业归属的判定逻辑。\n\n---\n\n## 3. 异常场景分析\n- 输入异常处理：活动状态或门店信息缺失时应触发错误处理机制。\n- 操作异常处理：中途取消或修改活动状态可能影响企业归属逻辑。\n- 外部依赖异常处理：用户信息接口不可用时需有容错机制。\n- PRD未明确的隐性异常场景：多门店情况下的企业归属优先级未说明。\n\n---\n\n## 4. 兼容性需求分析\n- PRD中未提供相关信息。\n\n---\n\n## 5. 安全性需求分析\n- 数据安全要求：确保活动与所属企业的绑定关系不被非法篡改。\n- 权限控制要求：仅允许合法创建人操作其所属企业范围内的活动。\n- 业务安全要求：防止通过伪造门店信息绕过企业归属限制。\n\n---\n\n## 6. 性能需求分析\n- PRD中未提供相关信息。\n- 高并发场景要求：PRD中未提供相关信息。", "requirement_pic": [], "ui_link": "https://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001067351", "tech_link": "https://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001067351", "create_time": "2025-06-24T14:52:41", "create_person": "刘艳霞", "is_active": true, "iteration_name": "【V3.1.6.0】四季蝉集团化数据隔离改造-0701", "total_ai_test_case": 10, "recall_ai_test_case": 2, "business_name": "四季蝉"}, {"id": 48, "iteration_id": "1161969829001002076", "business_id": "0", "requirement_id": 1067581, "requirement_content": "目前售后提交问题后，值班人员分析问题时间可能比较长，但是售后迟迟得不到回应，会产生焦虑，无法知晓是否有技术人员在处理该问题，故增加以下逻辑：\n\nB端及C端在问题详情页增加一个按钮，按钮名称为：【安心告知】，点击后基于原问题推送一条消息，消息内容为：技术人员正在跟进中，请耐心等待\n问题状态不做任何变更，仅仅只是推送一条消息\n按钮需要做判断，只有待受理及解决中状态才可点击\n按钮点击后在未关闭页面的情况下，不支持再次点击", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：用户在问题详情页点击【安心告知】按钮，系统基于原问题推送消息“技术人员正在跟进中，请耐心等待”，按钮状态变更为不可点击。\n- **分支流程**：若问题状态为非“待受理”或“解决中”，按钮不可见或不可点击；页面刷新后恢复初始状态。\n- **流程节点**：输入节点（问题状态、按钮点击事件），处理节点（状态判断、消息推送逻辑），输出节点（消息推送结果），存储节点（无新增存储需求）。\n\n### 1.2 功能模块拆解\n- **核心模块**：问题详情页模块、消息推送模块、状态判断模块。\n- **输入输出**：问题详情页模块（输入：问题ID、问题状态；输出：按钮显示/隐藏状态）、消息推送模块（输入：问题ID、消息模板；输出：推送结果）、状态判断模块（输入：问题状态；输出：是否允许点击）。\n- **模块接口**：问题详情页调用状态判断模块获取按钮状态，调用消息推送模块完成消息发送。\n\n### 1.3 业务规则提取\n- **核心规则**：按钮仅在问题状态为“待受理”或“解决中”时可用，点击后禁止再次点击。\n- **验证规则**：校验问题状态是否符合要求；确保按钮点击后状态变更正确。\n- **计算逻辑**：无复杂计算逻辑，主要依赖状态判断和消息模板填充。\n\n---\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：按钮点击后需即时反馈，避免用户重复操作。\n- 异常提示友好性要求：当按钮不可点击时，需通过视觉方式（如灰化按钮）明确提示。\n- 角色权限差异：PRD未提及角色权限差异，默认所有可访问问题详情页的用户均可见按钮。\n\n---\n\n## 3. 异常场景分析\n- 输入异常处理：若问题状态为空或无效，按钮应默认隐藏或不可点击。\n- 操作异常处理：按钮多次快速点击时需屏蔽重复操作；网络中断时需提示推送失败。\n- 外部依赖异常处理：消息推送失败时记录日志，并提供重试机制。\n- PRD未明确的隐性异常场景：多用户同时操作同一问题详情页时的按钮状态同步问题。\n\n---\n\n## 4. 兼容性需求分析\n- PRD明确的兼容范围：未提及具体兼容性要求，但需确保支持主流浏览器及不同分辨率下的正常显示。\n\n---\n\n## 5. 安全性需求分析\n- 数据安全要求：推送的消息内容需脱敏处理，防止敏感信息泄露。\n- 权限控制要求：按钮显示与点击权限需严格基于问题状态判断。\n- 业务安全要求：防止恶意用户通过技术手段绕过状态限制进行非法操作。\n\n---\n\n## 6. 性能需求分析\n- PRD明确的性能指标：未提及具体性能指标。\n- 高并发场景要求：需确保在多用户同时点击按钮时，系统能够稳定处理并返回结果。", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-06-20T17:44:31", "create_person": "杨逍", "is_active": true, "iteration_name": "【V1.6】鹰眼（当前迭代）", "total_ai_test_case": 10, "recall_ai_test_case": 5, "business_name": "默认"}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752218150033}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151550034|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.11841297149658203 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151550921|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_generate_test_case_list/，请求方式：POST，请求参数为：b'{"system":"","module":"","status":"","recognize_record_id":48,"pagenum":1,"pagesize":10}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151550922|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151550923|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151550938|Thread-22 (recordSystemLog):***********|INFO|commonUtil.py:51|用户 liuyanxia 查询AI生成用例列表，查询条件：系统=，模块= , 状态= 
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151550956|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 10, "total": 10, "data": [{"id": 458, "recognize_record_id": 48, "requirement_id": 1067581, "case_scene": "问题详情页-边界值测试-问题状态切换", "system": "客户支持系统", "module": "问题详情页模块", "premise": "问题状态为“待受理”，用户已登录并访问问题详情页。", "test_steps": "1. 打开问题详情页，页面加载成功。\n2. 修改问题状态为“解决中”。\n3. 验证【安心告知】按钮是否可见且可点击。\n4. 再次修改问题状态为“已完成”。\n5. 验证【安心告知】按钮是否隐藏。", "expected_result": "1. 问题状态为“解决中”时，按钮可见且可点击。\n2. 问题状态为“已完成”时，按钮隐藏。", "status": "1", "create_time": "2025-06-20T17:46:35", "update_time": "2025-06-20T17:55:07", "update_person": "杨逍", "is_active": true}, {"id": 457, "recognize_record_id": 48, "requirement_id": 1067581, "case_scene": "问题详情页-多用户同时操作-按钮状态同步", "system": "客户支持系统", "module": "问题详情页模块", "premise": "问题状态为“待受理”，多个用户同时访问同一问题详情页。", "test_steps": "1. 用户A打开问题详情页，页面加载成功。\n2. 用户B打开同一问题详情页，页面加载成功。\n3. 用户A点击【安心告知】按钮。\n4. 验证用户B的页面按钮状态是否同步更新为不可点击。", "expected_result": "1. 用户B的页面按钮状态同步更新为不可点击。\n2. 用户B无法再次点击按钮。", "status": "1", "create_time": "2025-06-20T17:46:35", "update_time": "2025-06-20T17:55:07", "update_person": "杨逍", "is_active": true}, {"id": 456, "recognize_record_id": 48, "requirement_id": 1067581, "case_scene": "消息推送模块-脱敏处理验证-数据安全", "system": "客户支持系统", "module": "消息推送模块", "premise": "问题状态为“待受理”，用户已登录并访问问题详情页，消息内容包含敏感信息。", "test_steps": "1. 打开问题详情页，页面加载成功。\n2. 点击【安心告知】按钮。\n3. 验证推送的消息内容是否经过脱敏处理。", "expected_result": "1. 推送的消息内容不包含任何敏感信息。\n2. 系统记录脱敏处理日志。", "status": "1", "create_time": "2025-06-20T17:46:35", "update_time": "2025-06-20T17:55:13", "update_person": "杨逍", "is_active": true}, {"id": 455, "recognize_record_id": 48, "requirement_id": 1067581, "case_scene": "状态判断模块-非法状态跳转拦截-验证权限控制", "system": "客户支持系统", "module": "状态判断模块", "premise": "问题状态为“已完成”，用户尝试通过技术手段修改状态为“待受理”。", "test_steps": "1. 模拟用户通过技术手段修改问题状态为“待受理”。\n2. 打开问题详情页，页面加载成功。\n3. 验证【安心告知】按钮是否隐藏。", "expected_result": "1. 【安心告知】按钮不可见。\n2. 系统记录非法操作日志。", "status": "0", "create_time": "2025-06-20T17:46:35", "update_time": "2025-06-20T17:46:35", "update_person": "杨逍", "is_active": true}, {"id": 454, "recognize_record_id": 48, "requirement_id": 1067581, "case_scene": "消息推送模块-推送失败重试机制-验证重试逻辑", "system": "客户支持系统", "module": "消息推送模块", "premise": "问题状态为“待受理”，用户已登录并访问问题详情页，模拟消息推送失败。", "test_steps": "1. 打开问题详情页，页面加载成功。\n2. 点击【安心告知】按钮。\n3. 模拟消息推送失败。\n4. 验证系统是否自动触发重试机制。", "expected_result": "1. 系统记录推送失败日志。\n2. 系统触发重试机制，尝试再次推送消息。", "status": "0", "create_time": "2025-06-20T17:46:35", "update_time": "2025-06-20T17:46:35", "update_person": "杨逍", "is_active": true}, {"id": 453, "recognize_record_id": 48, "requirement_id": 1067581, "case_scene": "问题详情页-无效问题状态处理-按钮隐藏", "system": "客户支持系统", "module": "问题详情页模块", "premise": "问题状态为空或无效，用户已登录并访问问题详情页。", "test_steps": "1. 打开问题详情页，页面加载成功。\n2. 验证【安心告知】按钮是否隐藏。", "expected_result": "1. 【安心告知】按钮不可见。\n2. 页面刷新后按钮状态保持隐藏。", "status": "0", "create_time": "2025-06-20T17:46:35", "update_time": "2025-06-20T17:46:35", "update_person": "杨逍", "is_active": true}, {"id": 452, "recognize_record_id": 48, "requirement_id": 1067581, "case_scene": "问题详情页-网络中断推送失败-提示用户", "system": "客户支持系统", "module": "问题详情页模块", "premise": "问题状态为“待受理”，用户已登录并访问问题详情页，模拟网络中断环境。", "test_steps": "1. 打开问题详情页，页面加载成功。\n2. 点击【安心告知】按钮。\n3. 模拟网络中断。\n4. 验证页面是否显示错误提示。", "expected_result": "1. 页面显示错误提示“消息推送失败，请稍后再试”。\n2. 按钮状态恢复为可点击。", "status": "0", "create_time": "2025-06-20T17:46:35", "update_time": "2025-06-20T17:46:35", "update_person": "杨逍", "is_active": true}, {"id": 451, "recognize_record_id": 48, "requirement_id": 1067581, "case_scene": "问题详情页-多次点击按钮-防止重复操作", "system": "客户支持系统", "module": "问题详情页模块", "premise": "问题状态为“待受理”，用户已登录并访问问题详情页。", "test_steps": "1. 打开问题详情页，页面加载成功。\n2. 点击【安心告知】按钮。\n3. 连续快速点击按钮多次。\n4. 验证按钮状态是否始终为不可点击。", "expected_result": "1. 第一次点击后按钮变为不可点击。\n2. 后续点击无任何响应，避免重复推送消息。", "status": "0", "create_time": "2025-06-20T17:46:35", "update_time": "2025-06-20T17:46:35", "update_person": "杨逍", "is_active": true}, {"id": 450, "recognize_record_id": 48, "requirement_id": 1067581, "case_scene": "问题详情页-非待受理状态按钮隐藏-验证隐藏状态", "system": "客户支持系统", "module": "问题详情页模块", "premise": "问题状态为“已关闭”或“已完成”，用户已登录并访问问题详情页。", "test_steps": "1. 打开问题详情页，页面加载成功。\n2. 验证【安心告知】按钮是否隐藏。", "expected_result": "1. 【安心告知】按钮不可见。\n2. 页面刷新后按钮状态保持隐藏。", "status": "1", "create_time": "2025-06-20T17:46:35", "update_time": "2025-06-20T17:55:31", "update_person": "杨逍", "is_active": true}, {"id": 449, "recognize_record_id": 48, "requirement_id": 1067581, "case_scene": "问题详情页-安心告知按钮点击-成功推送消息", "system": "客户支持系统", "module": "问题详情页模块", "premise": "问题状态为“待受理”或“解决中”，用户已登录并访问问题详情页。", "test_steps": "1. 打开问题详情页，页面加载成功。\n2. 验证【安心告知】按钮是否可见且可点击。\n3. 点击【安心告知】按钮。\n4. 验证按钮状态是否变更为不可点击。\n5. 检查页面是否显示提示“技术人员正在跟进中，请耐心等待”。", "expected_result": "1. 按钮点击后状态变为不可点击。\n2. 页面显示消息“技术人员正在跟进中，请耐心等待”。\n3. 推送结果记录在日志中，状态为成功。", "status": "1", "create_time": "2025-06-20T17:46:35", "update_time": "2025-06-20T17:55:37", "update_person": "杨逍", "is_active": true}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752218150956}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711151550957|Thread-5 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.035230159759521484 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711152206723|Thread-24 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_recognize_record_list/，请求方式：POST，请求参数为：b'{"business_id":"","iteration_name":"","creater":"","pagenum":1,"pagesize":5}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711152206727|Thread-25 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/info/?type=businesses，请求方式：GET，请求参数为：<QueryDict: {'type': ['businesses']}>
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711152206727|Thread-24 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711152206727|Thread-25 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711152206728|Thread-24 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711152206728|Thread-25 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711152206741|Thread-23 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/menu/，请求方式：GET，请求参数为：<QueryDict: {}>
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711152206742|Thread-23 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711152206742|Thread-23 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711152206751|Thread-27 (recordSystemLog):***********|INFO|commonUtil.py:51|来自IP：127.0.0.1的用户 liuyanxia 请求获取全部businesses信息
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711152206751|Thread-26 (recordSystemLog):***********|INFO|commonUtil.py:51|用户 liuyanxia 查询AI生成用例记录，查询条件：需求ID=，迭代名称=，业务线=，创建人= 
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711152206761|Thread-23 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": [{"id": 1, "authName": "系统配置", "path": "system-configure", "children": [{"id": 7, "authName": "用户管理", "path": "users", "children": []}, {"id": 8, "authName": "角色管理", "path": "roles", "children": []}, {"id": 9, "authName": "权限管理", "path": "rights", "children": []}]}, {"id": 2, "authName": "数据配置", "path": "user-configure", "children": [{"id": 47, "authName": "数据库", "path": "databases", "children": []}, {"id": 48, "authName": "测试参数", "path": "parameters", "children": []}, {"id": 50, "authName": "邮件模板", "path": "emails", "children": []}]}, {"id": 3, "authName": "接口测试", "path": "api-test", "children": [{"id": 16, "authName": "域名管理", "path": "projects", "children": []}, {"id": 17, "authName": "接口管理", "path": "interfaces", "children": []}, {"id": 18, "authName": "案例管理", "path": "api-cases", "children": []}, {"id": 19, "authName": "业务线管理", "path": "businesses", "children": []}, {"id": 54, "authName": "场景管理", "path": "scenes", "children": []}, {"id": 56, "authName": "场景任务", "path": "scene-task", "children": []}, {"id": 57, "authName": "待处理接口管理", "path": "interfaceCompare", "children": []}, {"id": 58, "authName": "接口流量统计", "path": "InterfaceTrafficStatistics", "children": []}]}, {"id": 4, "authName": "自动化监控", "path": "daily-monitoring", "children": [{"id": 66, "authName": "日常监控", "path": "daily-monitoring-list", "children": []}]}, {"id": 6, "authName": "测试助手", "path": "test-helper", "children": [{"id": 43, "authName": "日志列表", "path": "logs", "children": []}, {"id": 44, "authName": "发版列表", "path": "releases", "children": []}, {"id": 51, "authName": "工具列表", "path": "utils", "children": []}, {"id": 52, "authName": "优惠计算器", "path": "money-calculate", "children": []}, {"id": 53, "authName": "请求头转换", "path": "headers", "children": []}]}, {"id": 59, "authName": "鹰眼", "path": "after-sale", "children": [{"id": 60, "authName": "问题列表", "path": "question-list", "children": []}, {"id": 61, "authName": "群聊记录列表", "path": "chatRecord-list", "children": []}, {"id": 62, "authName": "售后问题统计", "path": "question-statistics", "children": []}, {"id": 70, "authName": "数据归档", "path": "data-archiving", "children": []}, {"id": 74, "authName": "群初始化配置", "path": "room-init-config", "children": []}]}, {"id": 71, "authName": "测试用例库", "path": "test-case", "children": [{"id": 63, "authName": "测试用例管理", "path": "testcaseManagement", "children": []}, {"id": 72, "authName": "AI测试用例库", "path": "AI-test-case", "children": []}, {"id": 75, "authName": "AI生成用例", "path": "ai-generate-testcases", "children": []}]}], "meta": {"msg": "获取菜单列表成功", "status": 200}, "timestamp": 1752218526761}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711152206762|Thread-25 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": [{"id": 30, "label": "30-厂家端小程序"}, {"id": 29, "label": "29-一键造数"}, {"id": 28, "label": "28-医保云"}, {"id": 27, "label": "27-四季蝉"}, {"id": 26, "label": "26-数据中心"}, {"id": 25, "label": "25-云仓"}, {"id": 24, "label": "24-随心看"}, {"id": 23, "label": "23-直播"}, {"id": 22, "label": "22-服务商"}, {"id": 21, "label": "21-组织云"}, {"id": 20, "label": "20-微商城9"}, {"id": 19, "label": "19-随心享"}, {"id": 18, "label": "18-支付云"}, {"id": 17, "label": "17-优享会员"}, {"id": 4, "label": "4-OMS-B2C-h2（废除）"}, {"id": 11, "label": "11-OMS-B2C"}, {"id": 16, "label": "16-H2_O2O_Order"}, {"id": 15, "label": "15-H2_OMS_B2C"}, {"id": 14, "label": "14-药事云"}, {"id": 13, "label": "13-商品中台"}, {"id": 12, "label": "12-助手"}, {"id": 10, "label": "10-演示"}, {"id": 8, "label": "8-会员"}, {"id": 7, "label": "7-OMS-O2O"}, {"id": 6, "label": "6-营销"}, {"id": 2, "label": "2-自动化"}, {"id": 0, "label": "0-默认"}], "meta": {"msg": "获取成功", "status": 200}, "timestamp": 1752218526762}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711152206763|Thread-23 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.01956486701965332 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711152206764|Thread-25 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.03546714782714844 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711152206810|Thread-24 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 5, "total": 51, "data": [{"id": 52, "iteration_id": "1161969829001002058", "business_id": "26", "requirement_id": 1067014, "requirement_content": "增加【美团待铺货&饿百待铺货】页签，同时支持新品页签的切换。\n店铺待铺货商品明细\n前置条件：先选择一个具体的平台店铺。（仅支持选择美团、饿百平台店铺）\n列表数据来源范围：\n商品信息：公域商品列表中，是否可售状态为【是】且有条形码的商品；同时第三方商品店铺无该商品，但所属门店有该商品。\n字段名称 字段逻辑\n平台店铺 平台 + 店铺名称\n商品信息 商品名称 + 规格 + 批准文号 名称\n条形码\nERP商品编码\n平台近30天总销量 美团所有店铺近30天内的销量汇总；支持按照销量切换排序。\nT-1的30天内（下账时间），已下账的商品数量汇总。\n所属门店 门店名称 + 门店编码\nERP门店库存 ERP同步的门店库存\nERP门店价格 ERP同步的门店价格\nERP加权成本价 ERP同步的加权成本价\n预估毛利率 （店铺铺货价格 - 加权成本价） / 加权成本价 * 100%；根据店铺铺货价格动态计算。\n店铺铺货价格 默认门店价格，支持编辑，数据格式：大于0的两位小数。\n店铺商品分类 下拉单选\n列表排序：默认按平台近30天总销量倒序排序\n页签字段文档描述：\n美团待铺货页签字段：\n商品信息：公域商品列表中，是否可售状态为【是】且有条形码的商品才展示。同时第三方商品店铺无该商品，但所属门店有该商品。\n美团近30天销量：连锁在美团所有店铺近30天内的销售数据汇总（T-1的30天内（下账时间），已下账的商品数量汇总。）\n\n饿百待铺货页签：\n商品信息：公域商品列表中，是否可售状态为【是】且有条形码的商品才展示。同时第三方商品店铺无该商品，但所属门店有该商品。\n饿百近30天销量：连锁在饿百所有店铺近30天内的销售数据汇总（T-1的30天内（下账时间），已下账的商品数量汇总。）\n\n查询条件\n查询条件 查询逻辑\n平台店铺 平台 + 店铺，支持搜索对应平台的店铺\n所属门店 门店编码或门店名称，与平台店铺联动。\n商品名称 模糊匹配查询\nERP商品编码 精确匹配查询，支持查多个，最大100个\n条形码 精确匹配查询，支持查多个，最大100个\n平台销量区间 0-n的区间数量，根据选择的平台店铺，确定查询哪个平台的销量\n新品天数设置\n查看新品过滤及设置调整到页面右上角。\n支持在所有页签设置新品天数和勾选新品天数过滤商品数据。切换页签新品勾选条件不变。\n新品天数文案描述：勾选则表示，只查询线上商品库的商品创建时间在设置的新品天数范围内的商品。\n操作\n1.新品铺货-单个操作：按行操作商品铺货，按照编辑后的店铺铺货价格进行铺货（需二次确认）。铺货操作同步处理，并实时反馈操结果，刷新操作页面。\n校验：价格为0的，不支持铺货，且提示：【铺货价格不允许为0。】\n2.新品铺货-批量操作（仅支持当前页批量-一页最多20条）：批量勾选当前页的商品（需二次确认）。铺货操作同步处理，并实时反馈操结果，若有铺货失败的数据，则展示失败原因，并支持失败数据复制。\n校验：价格为0的，不支持勾选。\n3.支持数据导出（查询结果的列表全量数据）", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：用户选择平台店铺 -> 查询待铺货商品列表 -> 选择单个或批量商品进行铺货操作 -> 验证铺货价格 -> 确认铺货 -> 实时反馈结果 -> 刷新页面\n- **分支流程**：\n  - 正常分支：成功铺货后，实时更新数据\n  - 异常分支：铺货价格为0，提示错误信息并阻止操作\n  - 回退流程：铺货失败的数据支持复制和重新提交\n- **流程节点**：\n  - 输入节点：平台店铺选择、查询条件输入\n  - 处理节点：商品列表查询、铺货价格验证、铺货操作执行\n  - 输出节点：铺货成功/失败的结果反馈\n  - 存储节点：铺货后的商品信息存储\n\n### 1.2 功能模块拆解\n- **核心模块**：\n  - 商品列表展示模块\n  - 铺货操作模块\n  - 数据导出模块\n- **输入输出**：\n  - 商品列表展示模块：输入（查询条件）、输出（商品列表）\n  - 铺货操作模块：输入（铺货价格）、输出（铺货结果）\n  - 数据导出模块：输入（查询结果）、输出（导出文件）\n- **模块接口**：\n  - 商品列表展示模块调用铺货操作模块传递铺货信息\n  - 铺货操作模块返回结果给商品列表展示模块\n  - 数据导出模块从商品列表展示模块获取数据\n\n### 1.3 业务规则提取\n- **核心规则**：\n  - 仅展示可售且有条形码的商品\n  - 第三方商品店铺无该商品，但所属门店有该商品\n  - 默认按平台近30天总销量倒序排序\n- **验证规则**：\n  - 价格为0的，不允许铺货\n  - 批量操作每页最多20条记录\n- **计算逻辑**：\n  - 预估毛利率 = (店铺铺货价格 - 加权成本价) / 加权成本价 * 100%\n  - 新品天数过滤：只查询创建时间在设置的新品天数范围内的商品\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：确保页面加载快速，操作响应及时\n- 异常提示友好性要求：提供清晰的错误提示，如铺货价格为0时显示“铺货价格不允许为0”\n- 角色权限差异：未明确提到角色权限差异\n\n## 3. 异常场景分析\n- 输入异常处理：价格为0时不支持铺货，并提示错误\n- 操作异常处理：铺货失败时显示失败原因，并支持失败数据复制\n- 外部依赖异常处理：PRD中未提供相关信息\n- PRD未明确的隐性异常场景：未明确\n\n## 4. 兼容性需求分析\n- PRD明确的兼容范围：未明确\n\n## 5. 安全性需求分析\n- 数据安全要求：未明确\n- 权限控制要求：未明确\n- 业务安全要求：未明确\n\n## 6. 性能需求分析\n- PRD明确的性能指标：未明确\n- 高并发场景要求：未明确", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-07-08T16:18:29", "create_person": "刘艳霞", "is_active": true, "iteration_name": "【V2.3.4.22】商品云-0703", "total_ai_test_case": 10, "recall_ai_test_case": 0, "business_name": "数据中心"}, {"id": 51, "iteration_id": null, "business_id": "27", "requirement_id": 342342, "requirement_content": "- 将员工组织机构刷到数据授权中（店员、店长）\n\n- 将片区经理的授权门店刷到数据授权中（片区经理）\n\n- 将新增四季蝉按钮级别的资源刷到商户已有的角色资源里", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程  \n- **主流程**：系统从组织结构中获取员工信息 → 判断员工角色（店员、店长、片区经理）→ 更新数据授权配置。  \n- **分支流程**：若员工无对应角色，跳过该员工；若门店信息不存在，记录异常日志。  \n- **流程节点**：输入节点为员工组织结构数据，处理节点为角色判断与权限分配，输出节点为更新后的数据授权表。\n\n### 1.2 功能模块拆解  \n- **核心模块**：组织同步模块、角色识别模块、权限更新模块。  \n- **输入输出**：输入为员工组织数据和角色定义，输出为更新后的数据授权结果。  \n- **模块接口**：组织同步模块调用角色识别模块判断角色类型，权限更新模块接收识别结果进行授权更新。\n\n### 1.3 业务规则提取  \n- **核心规则**：仅支持“店员”、“店长”、“片区经理”三类角色的数据授权更新。  \n- **验证规则**：需验证员工所属门店是否有效，片区经理对应的门店是否存在。  \n- **计算逻辑**：根据员工角色确定授权范围，片区经理自动获得其管理门店的访问权限。\n\n---\n\n## 2. 用户体验需求分析  \n- 交互流程流畅性要求：批量处理员工授权时应有进度提示，避免界面无响应。  \n- 异常提示友好性要求：对于未匹配角色或无效门店的员工，应记录并展示清晰的失败原因。  \n- 角色权限差异：不同角色对应不同的授权范围，需在界面中明确区分显示内容。\n\n---\n\n## 3. 异常场景分析  \n- 输入异常处理：员工数据缺失关键字段（如门店ID、角色类型），系统应拒绝处理并记录错误。  \n- 操作异常处理：授权过程中数据库连接失败，应中断操作并返回友好的错误提示。  \n- 外部依赖异常处理：若组织服务不可用，系统应延迟执行任务或提示重试机制。  \n- PRD未明确的隐性异常场景：员工角色变更后旧授权未清理，可能导致数据访问越权问题。\n\n---\n\n## 4. 兼容性需求分析  \n- PRD中未提供相关信息  \n\n---\n\n## 5. 安全性需求分析  \n- 数据安全要求：授权数据同步过程需加密传输，防止敏感信息泄露。  \n- 权限控制要求：仅允许具备管理员权限的用户触发授权刷新操作。  \n- 业务安全要求：确保片区经理只能访问其管理范围内的门店数据，不能越权查看。\n\n---\n\n## 6. 性能需求分析  \n- PRD中未提供相关信息", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-07-08T12:05:46", "create_person": "刘艳霞", "is_active": true, "iteration_name": "", "total_ai_test_case": 18, "recall_ai_test_case": 0, "business_name": "四季蝉"}, {"id": 50, "iteration_id": "1161969829001002043", "business_id": "29", "requirement_id": 1067351, "requirement_content": "进行中、未开始、已发布成功的活动按照已参与的活动门店记录所属企业；\n\n代建草稿状态无活动门店的活动都按创建人的员工资料中的所属企业创建，记录所属企业；\n\n历史的代建草稿状态的无活动门店的所属企业一律处理为集团；\n\n历史的代建草稿状态的有活动门店的所属企业按活动门店所属企业记录", "requirement_analyze_content": "## 1. 功能需求分析123123\n\n### 1.1 核心业务流程  \n- **主流程**：活动创建时根据活动状态、是否配置门店，决定所属企业的归属逻辑；  \n- **分支流程**：草稿状态无门店活动按创建人企业处理；历史数据特殊处理为集团或门店企业；  \n- **流程节点**：输入为活动状态、门店信息、创建人信息；输出为所属企业字段值。\n\n### 1.2 功能模块拆解  \n- **核心模块**：活动管理模块、企业归属判断模块、用户资料关联模块；  \n- **输入输出**：输入包括活动状态、门店列表、员工所属企业；输出为活动的“所属企业”属性；  \n- **模块接口**：活动模块调用归属判断模块获取所属企业；归属判断模块从用户资料模块读取企业信息。\n\n### 1.3 业务规则提取  \n- **核心规则**：草稿状态无门店活动使用创建人企业；有门店则使用门店所属企业；  \n- **验证规则**：需验证活动状态是否为“代建草稿”、门店是否存在、创建人信息是否完整；  \n- **计算逻辑**：判断优先级为：门店 > 创建人 > 集团（历史无门店草稿）。\n\n---\n\n## 2. 用户体验需求分析  \n- 交互流程流畅性要求：用户创建活动时应自动识别并填充所属企业，无需手动选择；  \n- 异常提示友好性要求：若无法确定所属企业，应给出明确提示说明原因；  \n- 角色权限差异：普通员工可创建草稿，管理员可发布活动，不同角色查看范围受企业限制。\n\n---\n\n## 3. 异常场景分析  \n- 输入异常处理：活动状态无效或门店为空但非草稿状态，系统应阻止保存并提示错误；  \n- 操作异常处理：创建人未绑定所属企业时，应提示“请先完善个人信息”；  \n- 外部依赖异常处理：若门店数据接口异常，应记录日志并提示“无法加载门店信息”；  \n- PRD未明确的隐性异常场景：多门店归属不一致时如何处理、跨企业门店是否允许存在。\n\n---\n\n## 4. 兼容性需求分析  \n- PRD中未提供相关信息。\n\n---\n\n## 5. 安全性需求分析  \n- 数据安全要求：所属企业字段属于敏感业务数据，应加密存储或脱敏展示；  \n- 权限控制要求：仅所属企业人员可查看和操作相关活动数据；  \n- 业务安全要求：防止非法篡改“所属企业”字段以规避数据隔离策略。\n\n---\n\n## 6. 性能需求分析  \n- PRD中未提供相关信息。", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-06-30T16:06:40", "create_person": "刘艳霞", "is_active": true, "iteration_name": "【V3.1.6.0】四季蝉集团化数据隔离改造-0701", "total_ai_test_case": 15, "recall_ai_test_case": 3, "business_name": "一键造数"}, {"id": 49, "iteration_id": "1161969829001002043", "business_id": "27", "requirement_id": 1067351, "requirement_content": "进行中、未开始、已发布成功的活动按照已参与的活动门店记录所属企业；\n\n代建草稿状态无活动门店的活动都按创建人的员工资料中的所属企业创建，记录所属企业；\n\n历史的代建草稿状态的无活动门店的所属企业一律处理为集团；\n\n历史的代建草稿状态的有活动门店的所属企业按活动门店所属企业记录", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：活动创建时判断活动状态和门店信息，决定所属企业的归属规则。\n- **分支流程**：\n  - 活动为“进行中/未开始/已发布成功”且有门店 → 所属企业按门店所属企业记录；\n  - 活动为“代建草稿”状态且无门店 → 所属企业按创建人所属企业记录；\n  - 历史“代建草稿”状态无门店 → 归为集团；\n  - 历史“代建草稿”状态有门店 → 按门店所属企业记录。\n\n- **流程节点**：\n  - 输入节点：活动状态、活动门店信息、创建人所属企业；\n  - 处理节点：根据规则判断活动所属企业；\n  - 输出节点：活动记录中所属企业字段；\n  - 存储节点：活动数据表中保存所属企业信息。\n\n### 1.2 功能模块拆解\n- **核心模块**：\n  - 活动管理模块：负责活动的创建、状态维护及所属企业分配；\n  - 用户管理模块：提供创建人所属企业信息；\n  - 数据存储模块：保存活动及相关企业信息。\n\n- **输入输出**：\n  - 活动管理模块输入：活动状态、门店列表、创建人ID；\n  - 活动管理模块输出：活动记录及其所属企业字段；\n  - 用户管理模块输入/输出：创建人信息查询接口。\n\n- **模块接口**：\n  - 活动管理调用用户管理接口获取创建人所属企业；\n  - 活动管理写入数据存储模块保存活动记录。\n\n### 1.3 业务规则提取\n- **核心规则**：\n  - “进行中/未开始/已发布成功”活动优先依据门店确定所属企业；\n  - “代建草稿”活动无门店则归属创建人企业或默认集团；\n  - 历史数据统一处理规则不可变。\n\n- **验证规则**：\n  - 验证活动状态是否符合分类标准；\n  - 验证门店是否存在并关联有效企业。\n\n- **计算逻辑**：\n  - 根据活动状态和门店是否存在选择不同的企业归属逻辑路径；\n  - 对历史数据应用预设规则批量处理。\n\n---\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：用户创建活动时无需手动指定所属企业，系统自动处理。\n- 异常提示友好性要求：当无法识别所属企业时应提示用户检查输入信息。\n- 角色权限差异：创建人角色影响“代建草稿”状态下企业归属的判定逻辑。\n\n---\n\n## 3. 异常场景分析\n- 输入异常处理：活动状态或门店信息缺失时应触发错误处理机制。\n- 操作异常处理：中途取消或修改活动状态可能影响企业归属逻辑。\n- 外部依赖异常处理：用户信息接口不可用时需有容错机制。\n- PRD未明确的隐性异常场景：多门店情况下的企业归属优先级未说明。\n\n---\n\n## 4. 兼容性需求分析\n- PRD中未提供相关信息。\n\n---\n\n## 5. 安全性需求分析\n- 数据安全要求：确保活动与所属企业的绑定关系不被非法篡改。\n- 权限控制要求：仅允许合法创建人操作其所属企业范围内的活动。\n- 业务安全要求：防止通过伪造门店信息绕过企业归属限制。\n\n---\n\n## 6. 性能需求分析\n- PRD中未提供相关信息。\n- 高并发场景要求：PRD中未提供相关信息。", "requirement_pic": [], "ui_link": "https://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001067351", "tech_link": "https://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001067351", "create_time": "2025-06-24T14:52:41", "create_person": "刘艳霞", "is_active": true, "iteration_name": "【V3.1.6.0】四季蝉集团化数据隔离改造-0701", "total_ai_test_case": 10, "recall_ai_test_case": 2, "business_name": "四季蝉"}, {"id": 48, "iteration_id": "1161969829001002076", "business_id": "0", "requirement_id": 1067581, "requirement_content": "目前售后提交问题后，值班人员分析问题时间可能比较长，但是售后迟迟得不到回应，会产生焦虑，无法知晓是否有技术人员在处理该问题，故增加以下逻辑：\n\nB端及C端在问题详情页增加一个按钮，按钮名称为：【安心告知】，点击后基于原问题推送一条消息，消息内容为：技术人员正在跟进中，请耐心等待\n问题状态不做任何变更，仅仅只是推送一条消息\n按钮需要做判断，只有待受理及解决中状态才可点击\n按钮点击后在未关闭页面的情况下，不支持再次点击", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：用户在问题详情页点击【安心告知】按钮，系统基于原问题推送消息“技术人员正在跟进中，请耐心等待”，按钮状态变更为不可点击。\n- **分支流程**：若问题状态为非“待受理”或“解决中”，按钮不可见或不可点击；页面刷新后恢复初始状态。\n- **流程节点**：输入节点（问题状态、按钮点击事件），处理节点（状态判断、消息推送逻辑），输出节点（消息推送结果），存储节点（无新增存储需求）。\n\n### 1.2 功能模块拆解\n- **核心模块**：问题详情页模块、消息推送模块、状态判断模块。\n- **输入输出**：问题详情页模块（输入：问题ID、问题状态；输出：按钮显示/隐藏状态）、消息推送模块（输入：问题ID、消息模板；输出：推送结果）、状态判断模块（输入：问题状态；输出：是否允许点击）。\n- **模块接口**：问题详情页调用状态判断模块获取按钮状态，调用消息推送模块完成消息发送。\n\n### 1.3 业务规则提取\n- **核心规则**：按钮仅在问题状态为“待受理”或“解决中”时可用，点击后禁止再次点击。\n- **验证规则**：校验问题状态是否符合要求；确保按钮点击后状态变更正确。\n- **计算逻辑**：无复杂计算逻辑，主要依赖状态判断和消息模板填充。\n\n---\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：按钮点击后需即时反馈，避免用户重复操作。\n- 异常提示友好性要求：当按钮不可点击时，需通过视觉方式（如灰化按钮）明确提示。\n- 角色权限差异：PRD未提及角色权限差异，默认所有可访问问题详情页的用户均可见按钮。\n\n---\n\n## 3. 异常场景分析\n- 输入异常处理：若问题状态为空或无效，按钮应默认隐藏或不可点击。\n- 操作异常处理：按钮多次快速点击时需屏蔽重复操作；网络中断时需提示推送失败。\n- 外部依赖异常处理：消息推送失败时记录日志，并提供重试机制。\n- PRD未明确的隐性异常场景：多用户同时操作同一问题详情页时的按钮状态同步问题。\n\n---\n\n## 4. 兼容性需求分析\n- PRD明确的兼容范围：未提及具体兼容性要求，但需确保支持主流浏览器及不同分辨率下的正常显示。\n\n---\n\n## 5. 安全性需求分析\n- 数据安全要求：推送的消息内容需脱敏处理，防止敏感信息泄露。\n- 权限控制要求：按钮显示与点击权限需严格基于问题状态判断。\n- 业务安全要求：防止恶意用户通过技术手段绕过状态限制进行非法操作。\n\n---\n\n## 6. 性能需求分析\n- PRD明确的性能指标：未提及具体性能指标。\n- 高并发场景要求：需确保在多用户同时点击按钮时，系统能够稳定处理并返回结果。", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-06-20T17:44:31", "create_person": "杨逍", "is_active": true, "iteration_name": "【V1.6】鹰眼（当前迭代）", "total_ai_test_case": 10, "recall_ai_test_case": 5, "business_name": "默认"}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752218526811}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250711152206811|Thread-24 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.08801722526550293 秒
