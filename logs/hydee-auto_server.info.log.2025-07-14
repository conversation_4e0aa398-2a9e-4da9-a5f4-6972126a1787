|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092609898|Thread-30 (process_request_thread):6224900096|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_recognize_record_list/，请求方式：POST，请求参数为：b'{"business_id":"","iteration_name":"","creater":"","pagenum":1,"pagesize":5}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092609904|Thread-29 (process_request_thread):6208073728|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/info/?type=businesses，请求方式：GET，请求参数为：<QueryDict: {'type': ['businesses']}>
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092609908|Thread-28 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/menu/，请求方式：GET，请求参数为：<QueryDict: {}>
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092609946|Thread-30 (process_request_thread):6224900096|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092609947|Thread-29 (process_request_thread):6208073728|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092609947|Thread-28 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092609948|Thread-30 (process_request_thread):6224900096|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092609948|Thread-29 (process_request_thread):6208073728|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092609949|Thread-28 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092610021|Thread-31 (recordSystemLog):6241726464|INFO|commonUtil.py:51|来自IP：127.0.0.1的用户 liuyanxia 请求获取全部businesses信息
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092610029|Thread-32 (recordSystemLog):6258552832|INFO|commonUtil.py:51|用户 liuyanxia 查询AI生成用例记录，查询条件：需求ID=，迭代名称=，业务线=，创建人= 
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092610042|Thread-28 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": [{"id": 1, "authName": "系统配置", "path": "system-configure", "children": [{"id": 7, "authName": "用户管理", "path": "users", "children": []}, {"id": 8, "authName": "角色管理", "path": "roles", "children": []}, {"id": 9, "authName": "权限管理", "path": "rights", "children": []}]}, {"id": 2, "authName": "数据配置", "path": "user-configure", "children": [{"id": 47, "authName": "数据库", "path": "databases", "children": []}, {"id": 48, "authName": "测试参数", "path": "parameters", "children": []}, {"id": 50, "authName": "邮件模板", "path": "emails", "children": []}]}, {"id": 3, "authName": "接口测试", "path": "api-test", "children": [{"id": 16, "authName": "域名管理", "path": "projects", "children": []}, {"id": 17, "authName": "接口管理", "path": "interfaces", "children": []}, {"id": 18, "authName": "案例管理", "path": "api-cases", "children": []}, {"id": 19, "authName": "业务线管理", "path": "businesses", "children": []}, {"id": 54, "authName": "场景管理", "path": "scenes", "children": []}, {"id": 56, "authName": "场景任务", "path": "scene-task", "children": []}, {"id": 57, "authName": "待处理接口管理", "path": "interfaceCompare", "children": []}, {"id": 58, "authName": "接口流量统计", "path": "InterfaceTrafficStatistics", "children": []}]}, {"id": 4, "authName": "自动化监控", "path": "daily-monitoring", "children": [{"id": 66, "authName": "日常监控", "path": "daily-monitoring-list", "children": []}]}, {"id": 6, "authName": "测试助手", "path": "test-helper", "children": [{"id": 43, "authName": "日志列表", "path": "logs", "children": []}, {"id": 44, "authName": "发版列表", "path": "releases", "children": []}, {"id": 51, "authName": "工具列表", "path": "utils", "children": []}, {"id": 52, "authName": "优惠计算器", "path": "money-calculate", "children": []}, {"id": 53, "authName": "请求头转换", "path": "headers", "children": []}]}, {"id": 59, "authName": "鹰眼", "path": "after-sale", "children": [{"id": 60, "authName": "问题列表", "path": "question-list", "children": []}, {"id": 61, "authName": "群聊记录列表", "path": "chatRecord-list", "children": []}, {"id": 62, "authName": "售后问题统计", "path": "question-statistics", "children": []}, {"id": 70, "authName": "数据归档", "path": "data-archiving", "children": []}, {"id": 74, "authName": "群初始化配置", "path": "room-init-config", "children": []}]}, {"id": 71, "authName": "测试用例库", "path": "test-case", "children": [{"id": 63, "authName": "测试用例管理", "path": "testcaseManagement", "children": []}, {"id": 72, "authName": "AI测试用例库", "path": "AI-test-case", "children": []}, {"id": 75, "authName": "AI生成用例", "path": "ai-generate-testcases", "children": []}]}], "meta": {"msg": "获取菜单列表成功", "status": 200}, "timestamp": 1752456370041}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092610051|Thread-29 (process_request_thread):6208073728|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": [{"id": 30, "label": "30-厂家端小程序"}, {"id": 29, "label": "29-一键造数"}, {"id": 28, "label": "28-医保云"}, {"id": 27, "label": "27-四季蝉"}, {"id": 26, "label": "26-数据中心"}, {"id": 25, "label": "25-云仓"}, {"id": 24, "label": "24-随心看"}, {"id": 23, "label": "23-直播"}, {"id": 22, "label": "22-服务商"}, {"id": 21, "label": "21-组织云"}, {"id": 20, "label": "20-微商城9"}, {"id": 19, "label": "19-随心享"}, {"id": 18, "label": "18-支付云"}, {"id": 17, "label": "17-优享会员"}, {"id": 4, "label": "4-OMS-B2C-h2（废除）"}, {"id": 11, "label": "11-OMS-B2C"}, {"id": 16, "label": "16-H2_O2O_Order"}, {"id": 15, "label": "15-H2_OMS_B2C"}, {"id": 14, "label": "14-药事云"}, {"id": 13, "label": "13-商品中台"}, {"id": 12, "label": "12-助手"}, {"id": 10, "label": "10-演示"}, {"id": 8, "label": "8-会员"}, {"id": 7, "label": "7-OMS-O2O"}, {"id": 6, "label": "6-营销"}, {"id": 2, "label": "2-自动化"}, {"id": 0, "label": "0-默认"}], "meta": {"msg": "获取成功", "status": 200}, "timestamp": 1752456370051}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092610056|Thread-28 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:143|当前请求耗时：0.13389897346496582 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092610057|Thread-29 (process_request_thread):6208073728|INFO|userRequestMiddleware.py:143|当前请求耗时：0.14666986465454102 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092610162|Thread-30 (process_request_thread):6224900096|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 5, "total": 51, "data": [{"id": 52, "iteration_id": "1161969829001002058", "business_id": "26", "requirement_id": 1067014, "requirement_content": "增加【美团待铺货&饿百待铺货】页签，同时支持新品页签的切换。\n店铺待铺货商品明细\n前置条件：先选择一个具体的平台店铺。（仅支持选择美团、饿百平台店铺）\n列表数据来源范围：\n商品信息：公域商品列表中，是否可售状态为【是】且有条形码的商品；同时第三方商品店铺无该商品，但所属门店有该商品。\n字段名称 字段逻辑\n平台店铺 平台 + 店铺名称\n商品信息 商品名称 + 规格 + 批准文号 名称\n条形码\nERP商品编码\n平台近30天总销量 美团所有店铺近30天内的销量汇总；支持按照销量切换排序。\nT-1的30天内（下账时间），已下账的商品数量汇总。\n所属门店 门店名称 + 门店编码\nERP门店库存 ERP同步的门店库存\nERP门店价格 ERP同步的门店价格\nERP加权成本价 ERP同步的加权成本价\n预估毛利率 （店铺铺货价格 - 加权成本价） / 加权成本价 * 100%；根据店铺铺货价格动态计算。\n店铺铺货价格 默认门店价格，支持编辑，数据格式：大于0的两位小数。\n店铺商品分类 下拉单选\n列表排序：默认按平台近30天总销量倒序排序\n页签字段文档描述：\n美团待铺货页签字段：\n商品信息：公域商品列表中，是否可售状态为【是】且有条形码的商品才展示。同时第三方商品店铺无该商品，但所属门店有该商品。\n美团近30天销量：连锁在美团所有店铺近30天内的销售数据汇总（T-1的30天内（下账时间），已下账的商品数量汇总。）\n\n饿百待铺货页签：\n商品信息：公域商品列表中，是否可售状态为【是】且有条形码的商品才展示。同时第三方商品店铺无该商品，但所属门店有该商品。\n饿百近30天销量：连锁在饿百所有店铺近30天内的销售数据汇总（T-1的30天内（下账时间），已下账的商品数量汇总。）\n\n查询条件\n查询条件 查询逻辑\n平台店铺 平台 + 店铺，支持搜索对应平台的店铺\n所属门店 门店编码或门店名称，与平台店铺联动。\n商品名称 模糊匹配查询\nERP商品编码 精确匹配查询，支持查多个，最大100个\n条形码 精确匹配查询，支持查多个，最大100个\n平台销量区间 0-n的区间数量，根据选择的平台店铺，确定查询哪个平台的销量\n新品天数设置\n查看新品过滤及设置调整到页面右上角。\n支持在所有页签设置新品天数和勾选新品天数过滤商品数据。切换页签新品勾选条件不变。\n新品天数文案描述：勾选则表示，只查询线上商品库的商品创建时间在设置的新品天数范围内的商品。\n操作\n1.新品铺货-单个操作：按行操作商品铺货，按照编辑后的店铺铺货价格进行铺货（需二次确认）。铺货操作同步处理，并实时反馈操结果，刷新操作页面。\n校验：价格为0的，不支持铺货，且提示：【铺货价格不允许为0。】\n2.新品铺货-批量操作（仅支持当前页批量-一页最多20条）：批量勾选当前页的商品（需二次确认）。铺货操作同步处理，并实时反馈操结果，若有铺货失败的数据，则展示失败原因，并支持失败数据复制。\n校验：价格为0的，不支持勾选。\n3.支持数据导出（查询结果的列表全量数据）", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：用户选择平台店铺 -> 查询待铺货商品列表 -> 选择单个或批量商品进行铺货操作 -> 验证铺货价格 -> 确认铺货 -> 实时反馈结果 -> 刷新页面\n- **分支流程**：\n  - 正常分支：成功铺货后，实时更新数据\n  - 异常分支：铺货价格为0，提示错误信息并阻止操作\n  - 回退流程：铺货失败的数据支持复制和重新提交\n- **流程节点**：\n  - 输入节点：平台店铺选择、查询条件输入\n  - 处理节点：商品列表查询、铺货价格验证、铺货操作执行\n  - 输出节点：铺货成功/失败的结果反馈\n  - 存储节点：铺货后的商品信息存储\n\n### 1.2 功能模块拆解\n- **核心模块**：\n  - 商品列表展示模块\n  - 铺货操作模块\n  - 数据导出模块\n- **输入输出**：\n  - 商品列表展示模块：输入（查询条件）、输出（商品列表）\n  - 铺货操作模块：输入（铺货价格）、输出（铺货结果）\n  - 数据导出模块：输入（查询结果）、输出（导出文件）\n- **模块接口**：\n  - 商品列表展示模块调用铺货操作模块传递铺货信息\n  - 铺货操作模块返回结果给商品列表展示模块\n  - 数据导出模块从商品列表展示模块获取数据\n\n### 1.3 业务规则提取\n- **核心规则**：\n  - 仅展示可售且有条形码的商品\n  - 第三方商品店铺无该商品，但所属门店有该商品\n  - 默认按平台近30天总销量倒序排序\n- **验证规则**：\n  - 价格为0的，不允许铺货\n  - 批量操作每页最多20条记录\n- **计算逻辑**：\n  - 预估毛利率 = (店铺铺货价格 - 加权成本价) / 加权成本价 * 100%\n  - 新品天数过滤：只查询创建时间在设置的新品天数范围内的商品\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：确保页面加载快速，操作响应及时\n- 异常提示友好性要求：提供清晰的错误提示，如铺货价格为0时显示“铺货价格不允许为0”\n- 角色权限差异：未明确提到角色权限差异\n\n## 3. 异常场景分析\n- 输入异常处理：价格为0时不支持铺货，并提示错误\n- 操作异常处理：铺货失败时显示失败原因，并支持失败数据复制\n- 外部依赖异常处理：PRD中未提供相关信息\n- PRD未明确的隐性异常场景：未明确\n\n## 4. 兼容性需求分析\n- PRD明确的兼容范围：未明确\n\n## 5. 安全性需求分析\n- 数据安全要求：未明确\n- 权限控制要求：未明确\n- 业务安全要求：未明确\n\n## 6. 性能需求分析\n- PRD明确的性能指标：未明确\n- 高并发场景要求：未明确", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-07-08T16:18:29", "create_person": "刘艳霞", "is_active": true, "iteration_name": "【V2.3.4.22】商品云-0703", "total_ai_test_case": 10, "recall_ai_test_case": 0, "business_name": "数据中心"}, {"id": 51, "iteration_id": null, "business_id": "27", "requirement_id": 342342, "requirement_content": "- 将员工组织机构刷到数据授权中（店员、店长）\n\n- 将片区经理的授权门店刷到数据授权中（片区经理）\n\n- 将新增四季蝉按钮级别的资源刷到商户已有的角色资源里", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程  \n- **主流程**：系统从组织结构中获取员工信息 → 判断员工角色（店员、店长、片区经理）→ 更新数据授权配置。  \n- **分支流程**：若员工无对应角色，跳过该员工；若门店信息不存在，记录异常日志。  \n- **流程节点**：输入节点为员工组织结构数据，处理节点为角色判断与权限分配，输出节点为更新后的数据授权表。\n\n### 1.2 功能模块拆解  \n- **核心模块**：组织同步模块、角色识别模块、权限更新模块。  \n- **输入输出**：输入为员工组织数据和角色定义，输出为更新后的数据授权结果。  \n- **模块接口**：组织同步模块调用角色识别模块判断角色类型，权限更新模块接收识别结果进行授权更新。\n\n### 1.3 业务规则提取  \n- **核心规则**：仅支持“店员”、“店长”、“片区经理”三类角色的数据授权更新。  \n- **验证规则**：需验证员工所属门店是否有效，片区经理对应的门店是否存在。  \n- **计算逻辑**：根据员工角色确定授权范围，片区经理自动获得其管理门店的访问权限。\n\n---\n\n## 2. 用户体验需求分析  \n- 交互流程流畅性要求：批量处理员工授权时应有进度提示，避免界面无响应。  \n- 异常提示友好性要求：对于未匹配角色或无效门店的员工，应记录并展示清晰的失败原因。  \n- 角色权限差异：不同角色对应不同的授权范围，需在界面中明确区分显示内容。\n\n---\n\n## 3. 异常场景分析  \n- 输入异常处理：员工数据缺失关键字段（如门店ID、角色类型），系统应拒绝处理并记录错误。  \n- 操作异常处理：授权过程中数据库连接失败，应中断操作并返回友好的错误提示。  \n- 外部依赖异常处理：若组织服务不可用，系统应延迟执行任务或提示重试机制。  \n- PRD未明确的隐性异常场景：员工角色变更后旧授权未清理，可能导致数据访问越权问题。\n\n---\n\n## 4. 兼容性需求分析  \n- PRD中未提供相关信息  \n\n---\n\n## 5. 安全性需求分析  \n- 数据安全要求：授权数据同步过程需加密传输，防止敏感信息泄露。  \n- 权限控制要求：仅允许具备管理员权限的用户触发授权刷新操作。  \n- 业务安全要求：确保片区经理只能访问其管理范围内的门店数据，不能越权查看。\n\n---\n\n## 6. 性能需求分析  \n- PRD中未提供相关信息", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-07-08T12:05:46", "create_person": "刘艳霞", "is_active": true, "iteration_name": "", "total_ai_test_case": 18, "recall_ai_test_case": 0, "business_name": "四季蝉"}, {"id": 50, "iteration_id": "1161969829001002043", "business_id": "29", "requirement_id": 1067351, "requirement_content": "进行中、未开始、已发布成功的活动按照已参与的活动门店记录所属企业；\n\n代建草稿状态无活动门店的活动都按创建人的员工资料中的所属企业创建，记录所属企业；\n\n历史的代建草稿状态的无活动门店的所属企业一律处理为集团；\n\n历史的代建草稿状态的有活动门店的所属企业按活动门店所属企业记录", "requirement_analyze_content": "## 1. 功能需求分析123123\n\n### 1.1 核心业务流程  \n- **主流程**：活动创建时根据活动状态、是否配置门店，决定所属企业的归属逻辑；  \n- **分支流程**：草稿状态无门店活动按创建人企业处理；历史数据特殊处理为集团或门店企业；  \n- **流程节点**：输入为活动状态、门店信息、创建人信息；输出为所属企业字段值。\n\n### 1.2 功能模块拆解  \n- **核心模块**：活动管理模块、企业归属判断模块、用户资料关联模块；  \n- **输入输出**：输入包括活动状态、门店列表、员工所属企业；输出为活动的“所属企业”属性；  \n- **模块接口**：活动模块调用归属判断模块获取所属企业；归属判断模块从用户资料模块读取企业信息。\n\n### 1.3 业务规则提取  \n- **核心规则**：草稿状态无门店活动使用创建人企业；有门店则使用门店所属企业；  \n- **验证规则**：需验证活动状态是否为“代建草稿”、门店是否存在、创建人信息是否完整；  \n- **计算逻辑**：判断优先级为：门店 > 创建人 > 集团（历史无门店草稿）。\n\n---\n\n## 2. 用户体验需求分析  \n- 交互流程流畅性要求：用户创建活动时应自动识别并填充所属企业，无需手动选择；  \n- 异常提示友好性要求：若无法确定所属企业，应给出明确提示说明原因；  \n- 角色权限差异：普通员工可创建草稿，管理员可发布活动，不同角色查看范围受企业限制。\n\n---\n\n## 3. 异常场景分析  \n- 输入异常处理：活动状态无效或门店为空但非草稿状态，系统应阻止保存并提示错误；  \n- 操作异常处理：创建人未绑定所属企业时，应提示“请先完善个人信息”；  \n- 外部依赖异常处理：若门店数据接口异常，应记录日志并提示“无法加载门店信息”；  \n- PRD未明确的隐性异常场景：多门店归属不一致时如何处理、跨企业门店是否允许存在。\n\n---\n\n## 4. 兼容性需求分析  \n- PRD中未提供相关信息。\n\n---\n\n## 5. 安全性需求分析  \n- 数据安全要求：所属企业字段属于敏感业务数据，应加密存储或脱敏展示；  \n- 权限控制要求：仅所属企业人员可查看和操作相关活动数据；  \n- 业务安全要求：防止非法篡改“所属企业”字段以规避数据隔离策略。\n\n---\n\n## 6. 性能需求分析  \n- PRD中未提供相关信息。", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-06-30T16:06:40", "create_person": "刘艳霞", "is_active": true, "iteration_name": "【V3.1.6.0】四季蝉集团化数据隔离改造-0701", "total_ai_test_case": 15, "recall_ai_test_case": 3, "business_name": "一键造数"}, {"id": 49, "iteration_id": "1161969829001002043", "business_id": "27", "requirement_id": 1067351, "requirement_content": "进行中、未开始、已发布成功的活动按照已参与的活动门店记录所属企业；\n\n代建草稿状态无活动门店的活动都按创建人的员工资料中的所属企业创建，记录所属企业；\n\n历史的代建草稿状态的无活动门店的所属企业一律处理为集团；\n\n历史的代建草稿状态的有活动门店的所属企业按活动门店所属企业记录", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：活动创建时判断活动状态和门店信息，决定所属企业的归属规则。\n- **分支流程**：\n  - 活动为“进行中/未开始/已发布成功”且有门店 → 所属企业按门店所属企业记录；\n  - 活动为“代建草稿”状态且无门店 → 所属企业按创建人所属企业记录；\n  - 历史“代建草稿”状态无门店 → 归为集团；\n  - 历史“代建草稿”状态有门店 → 按门店所属企业记录。\n\n- **流程节点**：\n  - 输入节点：活动状态、活动门店信息、创建人所属企业；\n  - 处理节点：根据规则判断活动所属企业；\n  - 输出节点：活动记录中所属企业字段；\n  - 存储节点：活动数据表中保存所属企业信息。\n\n### 1.2 功能模块拆解\n- **核心模块**：\n  - 活动管理模块：负责活动的创建、状态维护及所属企业分配；\n  - 用户管理模块：提供创建人所属企业信息；\n  - 数据存储模块：保存活动及相关企业信息。\n\n- **输入输出**：\n  - 活动管理模块输入：活动状态、门店列表、创建人ID；\n  - 活动管理模块输出：活动记录及其所属企业字段；\n  - 用户管理模块输入/输出：创建人信息查询接口。\n\n- **模块接口**：\n  - 活动管理调用用户管理接口获取创建人所属企业；\n  - 活动管理写入数据存储模块保存活动记录。\n\n### 1.3 业务规则提取\n- **核心规则**：\n  - “进行中/未开始/已发布成功”活动优先依据门店确定所属企业；\n  - “代建草稿”活动无门店则归属创建人企业或默认集团；\n  - 历史数据统一处理规则不可变。\n\n- **验证规则**：\n  - 验证活动状态是否符合分类标准；\n  - 验证门店是否存在并关联有效企业。\n\n- **计算逻辑**：\n  - 根据活动状态和门店是否存在选择不同的企业归属逻辑路径；\n  - 对历史数据应用预设规则批量处理。\n\n---\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：用户创建活动时无需手动指定所属企业，系统自动处理。\n- 异常提示友好性要求：当无法识别所属企业时应提示用户检查输入信息。\n- 角色权限差异：创建人角色影响“代建草稿”状态下企业归属的判定逻辑。\n\n---\n\n## 3. 异常场景分析\n- 输入异常处理：活动状态或门店信息缺失时应触发错误处理机制。\n- 操作异常处理：中途取消或修改活动状态可能影响企业归属逻辑。\n- 外部依赖异常处理：用户信息接口不可用时需有容错机制。\n- PRD未明确的隐性异常场景：多门店情况下的企业归属优先级未说明。\n\n---\n\n## 4. 兼容性需求分析\n- PRD中未提供相关信息。\n\n---\n\n## 5. 安全性需求分析\n- 数据安全要求：确保活动与所属企业的绑定关系不被非法篡改。\n- 权限控制要求：仅允许合法创建人操作其所属企业范围内的活动。\n- 业务安全要求：防止通过伪造门店信息绕过企业归属限制。\n\n---\n\n## 6. 性能需求分析\n- PRD中未提供相关信息。\n- 高并发场景要求：PRD中未提供相关信息。", "requirement_pic": [], "ui_link": "https://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001067351", "tech_link": "https://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001067351", "create_time": "2025-06-24T14:52:41", "create_person": "刘艳霞", "is_active": true, "iteration_name": "【V3.1.6.0】四季蝉集团化数据隔离改造-0701", "total_ai_test_case": 10, "recall_ai_test_case": 2, "business_name": "四季蝉"}, {"id": 48, "iteration_id": "1161969829001002076", "business_id": "0", "requirement_id": 1067581, "requirement_content": "目前售后提交问题后，值班人员分析问题时间可能比较长，但是售后迟迟得不到回应，会产生焦虑，无法知晓是否有技术人员在处理该问题，故增加以下逻辑：\n\nB端及C端在问题详情页增加一个按钮，按钮名称为：【安心告知】，点击后基于原问题推送一条消息，消息内容为：技术人员正在跟进中，请耐心等待\n问题状态不做任何变更，仅仅只是推送一条消息\n按钮需要做判断，只有待受理及解决中状态才可点击\n按钮点击后在未关闭页面的情况下，不支持再次点击", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：用户在问题详情页点击【安心告知】按钮，系统基于原问题推送消息“技术人员正在跟进中，请耐心等待”，按钮状态变更为不可点击。\n- **分支流程**：若问题状态为非“待受理”或“解决中”，按钮不可见或不可点击；页面刷新后恢复初始状态。\n- **流程节点**：输入节点（问题状态、按钮点击事件），处理节点（状态判断、消息推送逻辑），输出节点（消息推送结果），存储节点（无新增存储需求）。\n\n### 1.2 功能模块拆解\n- **核心模块**：问题详情页模块、消息推送模块、状态判断模块。\n- **输入输出**：问题详情页模块（输入：问题ID、问题状态；输出：按钮显示/隐藏状态）、消息推送模块（输入：问题ID、消息模板；输出：推送结果）、状态判断模块（输入：问题状态；输出：是否允许点击）。\n- **模块接口**：问题详情页调用状态判断模块获取按钮状态，调用消息推送模块完成消息发送。\n\n### 1.3 业务规则提取\n- **核心规则**：按钮仅在问题状态为“待受理”或“解决中”时可用，点击后禁止再次点击。\n- **验证规则**：校验问题状态是否符合要求；确保按钮点击后状态变更正确。\n- **计算逻辑**：无复杂计算逻辑，主要依赖状态判断和消息模板填充。\n\n---\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：按钮点击后需即时反馈，避免用户重复操作。\n- 异常提示友好性要求：当按钮不可点击时，需通过视觉方式（如灰化按钮）明确提示。\n- 角色权限差异：PRD未提及角色权限差异，默认所有可访问问题详情页的用户均可见按钮。\n\n---\n\n## 3. 异常场景分析\n- 输入异常处理：若问题状态为空或无效，按钮应默认隐藏或不可点击。\n- 操作异常处理：按钮多次快速点击时需屏蔽重复操作；网络中断时需提示推送失败。\n- 外部依赖异常处理：消息推送失败时记录日志，并提供重试机制。\n- PRD未明确的隐性异常场景：多用户同时操作同一问题详情页时的按钮状态同步问题。\n\n---\n\n## 4. 兼容性需求分析\n- PRD明确的兼容范围：未提及具体兼容性要求，但需确保支持主流浏览器及不同分辨率下的正常显示。\n\n---\n\n## 5. 安全性需求分析\n- 数据安全要求：推送的消息内容需脱敏处理，防止敏感信息泄露。\n- 权限控制要求：按钮显示与点击权限需严格基于问题状态判断。\n- 业务安全要求：防止恶意用户通过技术手段绕过状态限制进行非法操作。\n\n---\n\n## 6. 性能需求分析\n- PRD明确的性能指标：未提及具体性能指标。\n- 高并发场景要求：需确保在多用户同时点击按钮时，系统能够稳定处理并返回结果。", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-06-20T17:44:31", "create_person": "杨逍", "is_active": true, "iteration_name": "【V1.6】鹰眼（当前迭代）", "total_ai_test_case": 10, "recall_ai_test_case": 5, "business_name": "默认"}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752456370161}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092610162|Thread-30 (process_request_thread):6224900096|INFO|userRequestMiddleware.py:143|当前请求耗时：0.26431894302368164 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092631574|Thread-33 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_get_analyze_prd/，请求方式：POST，请求参数为：b'{"requirement_content":"\xe4\xba\xac\xe4\xb8\x9c\xe7\xa7\x92\xe9\x80\x81\xe6\xb5\xb7\xe5\x85\xb8\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe6\xa8\xa1\xe5\xbc\x8f\\n\xe8\x83\x8c\xe6\x99\xaf\\n\xe5\xbd\x93\xe5\x89\x8d\xe4\xba\xac\xe4\xb8\x9c\xe7\xa7\x92\xe9\x80\x81\xe6\x8e\x88\xe6\x9d\x83OMS\xe9\x9c\x80\xe8\xa6\x81\xe4\xb8\xba\xe6\xaf\x8f\xe4\xb8\xaa\xe5\x95\x86\xe5\xae\xb6\xe5\x8d\x95\xe7\x8b\xac\xe5\xbb\xba\xe7\xab\x8b\xe4\xb8\x80\xe4\xb8\xaa\xe5\xba\x94\xe7\x94\xa8\xef\xbc\x8c\xe5\x8d\x95\xe4\xbd\x93\xe5\xba\x97\xe5\x92\x8c\xe9\x97\xa8\xe5\xba\x97\xe8\xbe\x83\xe5\xb0\x91\xe7\x9a\x84\xe8\xbf\x9e\xe9\x94\x81\xe8\xbe\x83\xe5\xa4\x9a\xef\xbc\x8c\xe9\x9c\x80\xe8\xa6\x81\xe4\xbb\xb6\xe5\xbe\x88\xe5\xa4\x9a\xe7\x9a\x84\xe5\xba\x94\xe7\x94\xa8\xef\xbc\x8c\xe6\x8e\x88\xe6\x9d\x83\xe6\x95\x88\xe7\x8e\x87\xe4\xbd\x8e\xe3\x80\x82\xe4\xb8\xba\xe4\xba\x86\xe6\x8f\x90\xe5\x8d\x87\xe6\x95\x88\xe7\x8e\x87\xef\xbc\x8c\xe4\xba\xac\xe4\xb8\x9c\xe7\xa7\x92\xe9\x80\x81\xe7\xbb\x99\xe6\xb5\xb7\xe5\x85\xb8\xe5\xbc\x80\xe7\xab\x8b\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe6\x80\xbb\xe8\xb4\xa6\xe5\x8f\xb7\xef\xbc\x8c\xe5\x8d\x95\xe4\xbd\x93\xe5\xba\x97\xe6\x88\x96\xe8\xbf\x9e\xe9\x94\x81\xe4\xb8\x8d\xe9\x9c\x80\xe8\xa6\x81\xe5\x86\x8d\xe6\x96\xb0\xe5\xbb\xba\xe5\xba\x94\xe7\x94\xa8\xef\xbc\x8c\xe7\x9b\xb4\xe6\x8e\xa5\xe5\xb0\x86\xe5\xba\x97\xe9\x93\xba\xe7\xbb\x9f\xe4\xb8\x80\xe6\x8e\x88\xe6\x9d\x83\xe7\xbb\x99\xe6\xb5\xb7\xe5\x85\xb8\xe5\xba\x94\xe7\x94\xa8\xef\xbc\x8c\xe5\x8d\xb3\xe5\x8f\xaf\xe5\xae\x8c\xe6\x88\x90\xe6\x8e\x88\xe6\x9d\x83\xe3\x80\x82\\n\\n\xe4\xba\xac\xe4\xb8\x9c\xe7\xa7\x92\xe9\x80\x81\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe6\xa8\xa1\xe5\xbc\x8f\xe5\xaf\xb9\xe6\x8e\xa5\xe6\x96\x87\xe6\xa1\xa3\\nhttps://drive.weixin.qq.com/s?k=AOcA-wd2AAw1pTwpT7AKMAQgZ5ALQ \\n\\n\xe5\xaf\xb9\xe6\x8e\xa5\xe8\xb0\x83\xe7\xa0\x94\\n\xe4\xba\xac\xe4\xb8\x9c\xe7\xa7\x92\xe9\x80\x81\xe8\xb0\x83\xe7\xa0\x94\xe6\xb2\x9f\xe9\x80\x9a\xef\xbc\x9a\\n1.\xe5\xa6\x82\xe4\xbd\x95\xe5\xbb\xba\xe7\xab\x8b\xe6\xb5\xb7\xe5\x85\xb8\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xef\xbc\x8c\xe4\xb8\x8e\xe5\x85\xb6\xe4\xbb\x96\xe5\xba\x94\xe7\x94\xa8\xe6\x9c\x89\xe4\xbb\x80\xe4\xb9\x88\xe4\xb8\x8d\xe5\x90\x8c\xe3\x80\x82\\n\xe7\xad\x94\xef\xbc\x9a\\n2.\xe5\x95\x86\xe5\xae\xb6\xe5\xa6\x82\xe4\xbd\x95\xe5\xb0\x86\xe5\xba\x97\xe9\x93\xba\xe6\x8e\x88\xe6\x9d\x83\xe7\xbb\x99\xe6\xb5\xb7\xe5\x85\xb8\xe5\xba\x94\xe7\x94\xa8\xef\xbc\x9a\xe6\xb6\x88\xe6\x81\xaf\xe6\xa8\xa1\xe5\xbc\x8f\xe3\x80\x81\xe4\xb8\xbb\xe5\x8a\xa8\xe6\x8b\x89\xe5\x8e\xbb\xe6\xa8\xa1\xe5\xbc\x8f\xef\xbc\x9f\\n\xe7\xad\x94\xef\xbc\x9a\xe6\xb6\x88\xe6\x81\xaf\xe6\xa8\xa1\xe5\xbc\x8f\xef\xbc\x8c\xe5\x95\x86\xe5\x93\x81\xe6\x8e\x88\xe6\x9d\x83\xe5\x90\x8e\xef\xbc\x8c\xe4\xbc\x9a\xe6\x8e\xa8\xe9\x80\x81\xe6\x8e\x88\xe6\x9d\x83\xe6\xb6\x88\xe6\x81\xaf\xe3\x80\x82\\n3.\xe6\xb5\xb7\xe5\x85\xb8\xe5\xa6\x82\xe4\xbd\x95\xe5\x8c\xba\xe5\x88\x86\xe4\xb8\x8d\xe5\x90\x8c\xe5\x95\x86\xe5\xae\xb6\xe6\x8e\x88\xe6\x9d\x83\xe7\x9a\x84\xe5\xba\x97\xe9\x93\xba\xe4\xbf\xa1\xe6\x81\xaf\xe3\x80\x82\\n\xe7\xad\x94\xef\xbc\x9a\xe9\x80\x9a\xe8\xbf\x87\xe4\xb8\x8d\xe5\x90\x8c\xe5\x95\x86\xe5\xae\xb6\xe7\x9a\x84\xe7\xa7\x98\xe9\x92\xa5\xe8\xb0\x83\xe7\x94\xa8\xe6\x8e\xa5\xe5\x8f\xa3\xef\xbc\x8c\xe8\x8e\xb7\xe5\x8f\x96\xe5\xaf\xb9\xe5\xba\x94\xe5\x95\x86\xe5\xae\xb6\xe7\x9a\x84\xe5\xba\x97\xe9\x93\xba\xe4\xbf\xa1\xe6\x81\xaf\xe3\x80\x82\\n4.\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe7\x94\xa8\xe4\xb8\x8e\xe5\x95\x86\xe5\xae\xb6\xe7\x8b\xac\xe7\xab\x8b\xe5\xba\x94\xe7\x94\xa8\xe6\x9c\x89\xe4\xbd\x95\xe4\xb8\x8d\xe5\x90\x8c\xef\xbc\x8c\xe8\xb0\x83\xe7\x94\xa8\xe8\xbf\x87\xe7\xa8\x8b\xe4\xb8\xad\xe5\xa6\x82\xe4\xbd\x95\xe5\x8c\xba\xe5\x88\x86\xe6\x98\xaf\xe5\x93\xaa\xe4\xb8\xaa\xe5\x95\x86\xe5\xae\xb6\xe7\x9a\x84\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe7\x94\xa8\xe8\xaf\xb7\xe6\xb1\x82\xe3\x80\x82\\n\xe7\xad\x94\xef\xbc\x9a\xe9\x80\x9a\xe8\xbf\x87\xe5\x95\x86\xe5\xae\xb6\xe7\x8b\xac\xe7\xab\x8b\xe7\x9a\x84\xe7\xa7\x98\xe9\x92\xa5\xe6\x9d\xa5\xe8\xb0\x83\xe7\x94\xa8\xe6\x8e\xa5\xe5\x8f\xa3\xef\xbc\x8c\xe5\xa4\x84\xe7\x90\x86\xe4\xb8\x8d\xe5\x90\x8c\xe5\x95\x86\xe5\xae\xb6\xe7\x9a\x84\xe4\xb8\x9a\xe5\x8a\xa1\xe3\x80\x82\\n5.\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe7\x94\xa8\xe7\x9a\x84\xe7\xa7\x98\xe9\x92\xa5\xe5\x85\xb1\xe7\x94\xa8\xe4\xb8\x80\xe5\xa5\x97\xef\xbc\x8c\xe8\xbf\x98\xe6\x98\xaf\xe6\xaf\x8f\xe4\xb8\xaa\xe5\x95\x86\xe5\xae\xb6\xe6\x88\x96\xe9\x97\xa8\xe5\xba\x97\xe9\x83\xbd\xe6\x9c\x89\xe7\x8b\xac\xe7\xab\x8b\xe7\x9a\x84\xe7\xa7\x98\xe9\x92\xa5\xe3\x80\x82\\n\xe7\xad\x94\xef\xbc\x9a\xe6\xaf\x8f\xe4\xb8\xaa\xe6\x8e\x88\xe6\x9d\x83\xe5\x95\x86\xe5\xae\xb6\xe9\x83\xbd\xe6\x9c\x89\xe7\x8b\xac\xe7\xab\x8b\xe7\x9a\x84\xe7\xa7\x98\xe9\x92\xa5\xe3\x80\x82\\n6.\xe6\xb6\x88\xe6\x81\xaf\xe5\x9b\x9e\xe8\xb0\x83\xe5\x85\xb1\xe7\x94\xa8\xe7\x9a\x84\xe6\x98\xaf\xe5\x90\x8c\xe4\xb8\x80\xe4\xb8\xaa\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xef\xbc\x8c\xe5\xa6\x82\xe4\xbd\x95\xe5\x8c\xba\xe5\x88\x86\xe6\x98\xaf\xe5\x93\xaa\xe4\xb8\xaa\xe5\x95\x86\xe5\xae\xb6\xe7\x9a\x84\xe5\x9b\x9e\xe8\xb0\x83\xe6\xb6\x88\xe6\x81\xaf\xe3\x80\x82\\n\xe7\xad\x94\xef\xbc\x9a\xe7\x94\xb1OMS\xe9\x80\x9a\xe8\xbf\x87\xe9\x97\xa8\xe5\xba\x97ID\xe5\x88\x86\xe5\x8f\x91\xe3\x80\x82\\n\\nOMS\xe7\x9b\xb8\xe5\x85\xb3\xe6\x94\xb9\xe5\x8a\xa8\\n\xe7\x94\xb3\xe8\xaf\xb7\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xbc\x80\xe5\x8f\x91\xe8\x80\x85\xe8\xb4\xa6\xe5\x8f\xb7\xe3\x80\x82\\n\xe6\x8e\x88\xe6\x9d\x83\xe6\xb5\x81\xe7\xa8\x8b\xe5\x8f\x91\xe7\x94\x9f\xe5\x8f\x98\xe5\x8c\x96\xe3\x80\x82\\n\xe6\x8e\x88\xe6\x9d\x83\xe5\x90\x8e\xe7\x9a\x84\xe9\x97\xa8\xe5\xba\x97\xe4\xb8\x8e\xe5\x95\x86\xe5\xae\xb6\xe6\x97\xa0\xe5\x85\xb3\xe8\x81\x94\xe5\x85\xb3\xe7\xb3\xbb\xef\xbc\x8c\xe9\x9c\x80\xe8\xa6\x81OMS\xe5\xa4\x84\xe7\x90\x86\xe5\x95\x86\xe5\xae\xb6\xe4\xb8\x8e\xe9\x97\xa8\xe5\xba\x97\xe7\x9a\x84\xe7\xbb\x91\xe5\xae\x9a\xe5\x85\xb3\xe7\xb3\xbb\xef\xbc\x9b\xe7\xad\x89\xe5\xbe\x85\xe4\xb8\x8e\xe5\xb9\xb3\xe5\x8f\xb0\xe7\xa1\xae\xe8\xae\xa4\xe8\xa7\xa3\xe5\x86\xb3\xe6\x96\xb9\xe6\xa1\x88\xef\xbc\x8c\xe6\x9c\x80\xe5\xb7\xae\xe6\x83\x85\xe5\x86\xb5\xe9\x9c\x80\xe8\xa6\x81\xe4\xba\xba\xe5\xb7\xa5\xe5\x88\xa4\xe6\x96\xad\xe6\x89\x8b\xe5\x8a\xa8\xe7\xbb\x91\xe5\xae\x9a\xef\xbc\x9b\\n\xe6\xaf\x8f\xe4\xb8\xaa\xe6\x8e\xa5\xe5\x8f\xa3\xe9\x83\xbd\xe9\x9c\x80\xe8\xa6\x81\xe8\xb0\x83\xe8\xaf\x95\xe6\x96\xb0\xe6\xa8\xa1\xe5\xbc\x8f\xe4\xb8\x8b\xe7\x9a\x84\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe7\x94\xa8\xe9\x80\xbb\xe8\xbe\x91\xe3\x80\x82\\n\xe5\x9b\x9e\xe8\xb0\x83\xe6\xb6\x88\xe6\x81\xaf\xe5\x88\x86\xe5\x8f\x91\xe3\x80\x82\\n\\n\xe4\xb8\xbb\xe8\xa6\x81\xe6\x98\xaf\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe9\x97\xa8\xe5\xba\x97\xe6\x8e\x88\xe6\x9d\x83\xe8\xa7\x84\xe5\x88\x99\xe6\x94\xb9\xe5\x8f\x98\xe5\xaf\xbc\xe8\x87\xb4\xe5\x90\x8e\xe7\xbb\xad\xe7\x9a\x84\xe6\x94\xb9\xe5\x8a\xa8\xe5\x92\x8c\xe8\x81\x94\xe8\xb0\x83\xef\xbc\x9a\\n1\xe3\x80\x81\xe9\x97\xa8\xe5\xba\x97\xe6\x8e\x88\xe6\x9d\x83\xe9\x80\xbb\xe8\xbe\x91\\n2\xe3\x80\x81\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe6\x8e\x88\xe6\x9d\x83\xe5\x8f\x8a\xe8\xa7\x84\xe5\x88\x99\\n3\xe3\x80\x81\xe4\xb8\x9a\xe5\x8a\xa1\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe8\xaf\x95\xef\xbc\x9a\\n\xe8\xae\xa2\xe5\x8d\x95&\xe5\x94\xae\xe5\x90\x8e\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe8\xaf\x95\\n\xe5\x95\x86\xe5\x93\x81\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe8\xaf\x95\\n\xe9\x97\xa8\xe5\xba\x97\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe8\xaf\x95\\n\xe5\xaf\xb9\xe8\xb4\xa6\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe8\xaf\x95\\n\xe8\xaf\x84\xe4\xbb\xb7\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe6\x95\xb4\\n4\xe3\x80\x81\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe6\x94\xb9\xe9\x80\xa0\\n5\xe3\x80\x81\xe4\xba\xac\xe4\xb8\x9c\xe7\xa7\x92\xe9\x80\x81\xe5\xb9\xb3\xe5\x8f\xb0\xe8\x81\x94\xe8\xb0\x83\\n\\n\xe8\xaf\xa6\xe7\xbb\x86\xe6\x96\xb9\xe6\xa1\x88\xe8\xae\xbe\xe8\xae\xa1\\n\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe5\xbc\x80\xe5\x8f\x91\xe8\x80\x85\xe8\xb4\xa6\xe5\x8f\xb7\xe5\x88\x9b\xe5\xbb\xba\xe6\xb5\xb7\xe5\x85\xb8\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\\n\xe5\xba\x94\xe7\x94\xa8\xe5\x88\x9b\xe5\xbb\xba\\n\xe5\x88\x9b\xe5\xbb\xba\xe6\xb5\xb7\xe5\x85\xb8\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xef\xbc\x8c\xe4\xbd\xbf\xe7\x94\xa8\xe5\x94\xaf\xe4\xb8\x80\xe7\x9a\x84\xe6\xb5\xb7\xe5\x85\xb8\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xef\xbc\x8c\xe5\xaf\xb9\xe6\x8e\xa5\xe6\x89\x80\xe6\x9c\x89\xe5\x95\x86\xe6\x88\xb7\xe6\x8e\x88\xe6\x9d\x83\xe3\x80\x82\\n\\nhttps://drive.weixin.qq.com/s?k=AOcA-wd2AAwAk7Sjl8AKMAQgZ5ALQ \\n\xe5\xba\x94\xe7\x94\xa8\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xe7\xbb\xb4\xe6\x8a\xa4\\n\xe7\xbb\xb4\xe6\x8a\xa4\xe5\xba\x94\xe7\x94\xa8\xe7\x9a\x84\xe7\x9b\xb8\xe5\x85\xb3\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xef\xbc\x9a\\n\xe5\xa6\x82\xef\xbc\x9a\xe6\x8e\x88\xe6\x9d\x83\xe6\x8e\xa5\xe6\x94\xb6\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xef\xbc\x8c\xe8\x87\xaa\xe5\x8a\xa8\xe6\x8e\x88\xe6\x9d\x83\xe5\xbb\xb6\xe6\x9c\x9f\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xef\xbc\x8c\xe8\xae\xa2\xe5\x8d\x95\xe7\x9b\xb8\xe5\x85\xb3\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xe7\xad\x89\xe3\x80\x82\\n\\n\\n\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe5\x88\x9b\xe5\xbb\xba\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xe8\xb4\xa6\xe5\x8f\xb7\\n\xe5\x9c\xa8\xe5\x90\x8e\xe5\x8f\xb0\xe6\x8e\x88\xe6\x9d\x83\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xe8\xb4\xa6\xe5\x8f\xb7\xef\xbc\x8c\xe4\xbd\x9c\xe4\xb8\xba\xe6\xb5\xb7\xe5\x85\xb8\xe7\x9a\x84\xe5\xba\x94\xe7\x94\xa8\xe6\x80\xbb\xe8\xb4\xa6\xe5\x8f\xb7\xef\xbc\x8c\xe6\x89\x80\xe6\x9c\x89\xe4\xb8\x8e\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe7\x9b\xb8\xe5\x85\xb3\xe7\x9a\x84\xe4\xb8\x9a\xe5\x8a\xa1\xe4\xbf\xa1\xe6\x81\xaf\xe9\x83\xbd\xe9\x80\x9a\xe8\xbf\x87\xe8\xaf\xa5\xe5\xba\x94\xe7\x94\xa8\xe8\xb4\xa6\xe5\x8f\xb7\xe8\xbf\x9b\xe8\xa1\x8c\xe5\xaf\xb9\xe6\x8e\xa5\xef\xbc\x9b\\n\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe5\x92\x8cOMS\xe9\x83\xbd\xe5\x9c\xa8\xe5\x90\x8e\xe5\x8f\xb0\xe4\xbf\x9d\xe5\xad\x98\xe8\xb4\xa6\xe5\x8f\xb7\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x8c\xe4\xb8\x8d\xe9\x9c\x80\xe8\xa6\x81\xe5\xb1\x95\xe7\xa4\xba\xe5\x9c\xa8\xe5\x89\x8d\xe7\xab\xaf\xe3\x80\x82\\n\xe5\xba\x94\xe7\x94\xa8\xe7\x9a\x84\xe4\xbd\xbf\xe7\x94\xa8\\n\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe6\xa0\xb9\xe6\x8d\xaeOMS\xe8\xb0\x83\xe7\x94\xa8\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xbc\xa0\xe5\x85\xa5\xe7\x9a\x84\xe5\x8f\x82\xe6\x95\xb0\xe5\x88\xa4\xe6\x96\xad\xe6\xa8\xa1\xe5\xbc\x8f\xe6\x98\xaf\xe5\x90\xa6\xe4\xb8\xba\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe6\xa8\xa1\xe5\xbc\x8f\xef\xbc\x8c\xe5\xb9\xb6\xe6\x9b\xb4\xe5\x8a\xa0\xe5\x95\x86\xe5\xae\xb6ID\xe5\x85\xa5\xe5\x8f\x82\xe8\x8e\xb7\xe5\x8f\x96\xe5\xaf\xb9\xe5\xba\x94\xe7\x9a\x84\xe7\xa7\x98\xe9\x92\xa5\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x8c\xe8\xb0\x83\xe7\x94\xa8\xe5\xb9\xb3\xe5\x8f\xb0\xe6\x8e\xa5\xe5\x8f\xa3\xe3\x80\x82\\n\\n\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe5\x95\x86\xe6\x88\xb7\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe6\xa8\xa1\xe5\xbc\x8f\xe6\x8e\x88\xe6\x9d\x83\\n\xe6\x8e\x88\xe6\x9d\x83\xe6\xb5\x81\xe7\xa8\x8b\\n\\n\\n\xe6\xb3\xa8\xe6\x84\x8f\xef\xbc\x9a\xe5\x95\x86\xe6\x88\xb7\xe6\x8e\x88\xe6\x9d\x83\xe5\x90\x8e\xe8\xb7\xb3\xe8\xbd\xac\xe7\x9a\x84\xe7\x99\xbb\xe5\xbd\x95\xe9\xa1\xb5\xe9\x9d\xa2\xe7\x9a\x84\xe9\x93\xbe\xe6\x8e\xa5\xe9\x95\xbf\xe6\x9c\x9f\xe6\x9c\x89\xe6\x95\x88\xe5\x8f\xaf\xe4\xbb\xa5\xe4\xbd\xbf\xe7\x94\xa8\xe6\xad\xa4\xe9\x93\xbe\xe6\x8e\xa5\xe5\xb5\x8c\xe5\x85\xa5OMS\xe7\x9a\x84\xe6\x8e\x88\xe6\x9d\x83\xe9\xa1\xb5\xe9\x9d\xa2\xef\xbc\x8c\xe4\xbd\x9c\xe4\xb8\xba\xe5\x95\x86\xe5\xae\xb6\xe6\x8e\x88\xe6\x9d\x83\xe7\x9a\x84\xe6\x93\x8d\xe4\xbd\x9c\xe5\x85\xa5\xe5\x8f\xa3\xe3\x80\x82\xe5\x8d\xb3\xe4\xbd\xbf\xe7\x94\xa8\xe5\x9b\xba\xe5\xae\x9a\xe7\x9a\x84URL\xe4\xbe\x9b\xe5\x95\x86\xe6\x88\xb7\xe8\xbf\x9b\xe8\xa1\x8c\xe6\x8e\x88\xe6\x9d\x83\xe3\x80\x82\\n\xe7\xbd\x91\xe5\xba\x97\xe6\x8e\x88\xe6\x9d\x83\xe7\xae\xa1\xe7\x90\x86\xe5\xa2\x9e\xe5\x8a\xa0\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xe6\x8e\x88\xe6\x9d\x83\\n\xe5\x88\x9b\xe5\xbb\xba\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xe6\x8e\x88\xe6\x9d\x83\xe7\xbd\x91\xe5\xba\x97\\n\\n\\n\\n\xe6\xb3\xa8\xe6\x84\x8f\xef\xbc\x9a\\n\xe7\xbd\x91\xe5\xba\x97\xe5\x90\x8d\xe7\xa7\xb0\xe5\x92\x8c\xe5\x95\x86\xe5\xae\xb6ID\xe4\xbf\x9d\xe5\xad\x98\xe6\x97\xb6\xe5\x89\x8d\xe5\x90\x8e\xe5\x8e\xbb\xe7\xa9\xba\xe6\xa0\xbc\xe5\x9b\x9e\xe8\xbd\xa6\\n\xe6\xa0\xa1\xe9\xaa\x8c\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe5\xb9\xb3\xe5\x8f\xb0\xe7\x9a\x84\xe7\x8e\xb0\xe6\x9c\x89\xe6\x8e\x88\xe6\x9d\x83\xe7\xbd\x91\xe5\xba\x97\xe4\xb8\xad\xef\xbc\x8c\xe6\x98\xaf\xe5\x90\xa6\xe5\xb7\xb2\xe6\x9c\x89\xe8\xaf\xa5\xe5\x95\x86\xe5\xae\xb6ID\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe6\x9c\x89\xe5\x88\x99\xe6\x8f\x90\xe7\xa4\xba\xef\xbc\x9a\xe3\x80\x90\xe8\xaf\xa5\xe5\x95\x86\xe5\xae\xb6ID\xe5\xb7\xb2\xe6\x8e\x88\xe6\x9d\x83\xe8\x87\xb3OMS\xef\xbc\x88\xe5\x95\x86\xe5\xae\xb6\xe5\x90\x8d\xe7\xa7\xb0 + \xe5\x95\x86\xe5\xae\xb6\xe7\xbd\x91\xe5\xba\x97\xe5\x90\x8d\xe7\xa7\xb0\xef\xbc\x89\xef\xbc\x8c\xe4\xb8\x8d\xe5\x85\x81\xe8\xae\xb8\xe5\x86\x8d\xe6\xac\xa1\xe6\x8e\x88\xe6\x9d\x83\xef\xbc\x8c\xe8\xaf\xb7\xe7\xa1\xae\xe8\xae\xa4\xe5\x95\x86\xe5\xae\xb6ID\xe9\x87\x8d\xe6\x96\xb0\xe6\x8e\x88\xe6\x9d\x83\xef\xbc\x81\xe3\x80\x91\\n\xe6\xa0\xa1\xe9\xaa\x8c\xe9\x80\x9a\xe8\xbf\x87\xef\xbc\x8c\xe5\x88\x99\xe5\x85\x81\xe8\xae\xb8\xe4\xbf\x9d\xe5\xad\x98\xe3\x80\x82\\n\xe6\xa0\xa1\xe9\xaa\x8c\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe5\xb9\xb3\xe5\x8f\xb0\xe7\x9a\x84\xe7\x8e\xb0\xe6\x9c\x89\xe6\x8e\x88\xe6\x9d\x83\xe7\xbd\x91\xe5\xba\x97\xe4\xb8\xad\xef\xbc\x8c\xe6\x98\xaf\xe5\x90\xa6\xe5\xb7\xb2\xe6\x9c\x89\xe7\xbd\x91\xe5\xba\x97\xe5\x90\x8d\xe7\xa7\xb0\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe6\x9c\x89\xe5\x88\x99\xe6\x8f\x90\xe7\xa4\xba\xef\xbc\x9a\xe3\x80\x90\xe7\xbd\x91\xe5\xba\x97\xe5\x90\x8d\xe7\xa7\xb0\xe5\xb7\xb2\xe8\xa2\xab\xe4\xbd\xbf\xe7\x94\xa8\xef\xbc\x8c\xe8\xaf\xb7\xe4\xbf\xae\xe6\x94\xb9\xe7\xbd\x91\xe5\xba\x97\xe5\x90\x8d\xe7\xa7\xb0\xef\xbc\x81\xe3\x80\x91\\n\xe6\xa0\xa1\xe9\xaa\x8c\xe9\x80\x9a\xe8\xbf\x87\xef\xbc\x8c\xe5\x88\x99\xe5\x85\x81\xe8\xae\xb8\xe4\xbf\x9d\xe5\xad\x98\xe3\x80\x82\\n\xe6\x8e\x88\xe6\x9d\x83\xe7\xb1\xbb\xe5\x9e\x8b\xe6\x96\x87\xe6\xa1\x88\xe8\xaf\xb4\xe6\x98\x8e\xef\xbc\x9a\\n\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xe6\x8e\x88\xe6\x9d\x83\xef\xbc\x9a\xe8\xa1\xa8\xe7\xa4\xba\xe5\x95\x86\xe5\xae\xb6\xe6\x8e\x88\xe6\x9d\x83\xe7\xbb\x99\xe6\xb5\xb7\xe5\x85\xb8\xe7\x9a\x84\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xe7\xbb\x9f\xe4\xb8\x80\xe7\xae\xa1\xe7\x90\x86\xef\xbc\x8c\xe5\x95\x86\xe5\xae\xb6\xe4\xb8\x8d\xe9\x9c\x80\xe8\xa6\x81\xe7\x94\xb3\xe8\xaf\xb7\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe7\x9a\x84\xe5\xbc\x80\xe5\x8f\x91\xe8\x80\x85\xe8\xb4\xa6\xe5\x8f\xb7\xef\xbc\x9b\\n\xe5\x95\x86\xe5\xae\xb6\xe5\xba\x94\xe7\x94\xa8\xe6\x8e\x88\xe6\x9d\x83\xef\xbc\x9a\xe8\xa1\xa8\xe7\xa4\xba\xe5\x95\x86\xe5\xae\xb6\xe9\x9c\x80\xe8\xa6\x81\xe5\x85\x88\xe7\x94\xb3\xe8\xaf\xb7\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe7\x9a\x84\xe5\xbc\x80\xe5\x8f\x91\xe8\x80\x85\xe8\xb4\xa6\xe5\x8f\xb7\xef\xbc\x8c\xe7\x94\xb3\xe8\xaf\xb7\xe6\x88\x90\xe5\x8a\x9f\xe5\x90\x8e\xef\xbc\x8c\xe5\x86\x8d\xe5\xb0\x86\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe7\x9a\x84\xe5\x95\x86\xe5\xae\xb6\xe5\xba\x94\xe7\x94\xa8\xe6\x8e\x88\xe6\x9d\x83\xe7\xbb\x99OMS\xe3\x80\x82\\n\\n\xe7\x82\xb9\xe5\x87\xbb\xe3\x80\x90\xe4\xbf\x9d\xe5\xad\x98\xe5\xb9\xb6\xe6\x8e\x88\xe6\x9d\x83\xe3\x80\x91\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x9a\\n1.\xe4\xba\x8c\xe6\xac\xa1\xe6\x8f\x90\xe9\x86\x92\xe7\xa1\xae\xe8\xae\xa4\xef\xbc\x9a\xe8\xaf\xb7\xe7\xa1\xae\xe4\xbf\x9d\xe5\x95\x86\xe5\xae\xb6ID\xe5\xa1\xab\xe5\x86\x99\xe5\x87\x86\xe7\xa1\xae\xef\xbc\x8c\xe4\xbf\x9d\xe5\xad\x98\xe5\x90\x8e\xef\xbc\x8c\xe5\x95\x86\xe5\xae\xb6ID\xe4\xb8\x8d\xe5\x8f\xaf\xe4\xbf\xae\xe6\x94\xb9\xe3\x80\x82\xe7\x82\xb9\xe5\x87\xbb\xe7\xa1\xae\xe8\xae\xa4\xe5\x90\x8e\xef\xbc\x8c\xe6\x89\xa7\xe8\xa1\x8c\xe5\xa6\x82\xe4\xb8\x8b\xe5\x8a\xa8\xe4\xbd\x9c\xe3\x80\x82\\n2.\xe8\x87\xaa\xe5\x8a\xa8\xe8\xb0\x83\xe7\x94\xa8\xe5\x9b\xba\xe5\xae\x9a\xe7\x9a\x84\xe5\xba\x94\xe7\x94\xa8\xe6\x8e\x88\xe6\x9d\x83URL\xef\xbc\x8c\xe5\x95\x86\xe5\xae\xb6\xe7\x99\xbb\xe9\x99\x86\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe5\xbe\x97\xe5\x95\x86\xe5\xae\xb6\xe8\xb4\xa6\xe5\x8f\xb7\xe5\xae\x8c\xe6\x88\x90\xe6\x8e\x88\xe6\x9d\x83\xe3\x80\x82\\n\xe5\x9b\xba\xe5\xae\x9a\xe6\x8e\x88\xe6\x9d\x83\xe9\x93\xbe\xe6\x8e\xa5\xe7\x94\xb1\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe6\x8f\x90\xe4\xbe\x9b\xe3\x80\x82\\n\\n3.\xe5\x90\x8c\xe6\x97\xb6\xef\xbc\x8cOMS\xe5\x88\x9b\xe5\xbb\xba\xe7\x8a\xb6\xe6\x80\x81\xe4\xb8\xba\xe5\xbc\x82\xe5\xb8\xb8\xe7\x9a\x84\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe7\xbd\x91\xe5\xba\x97\xe4\xbf\xa1\xe6\x81\xaf\xe3\x80\x82\\n\xe5\xbc\x82\xe5\xb8\xb8\xe7\x8a\xb6\xe6\x80\x81\xe6\x96\x87\xe6\xa1\x88\xef\xbc\x9a\xe6\x96\xb0\xe5\x88\x9b\xe5\xbb\xba\xe7\x9a\x84\xe7\xbd\x91\xe5\xba\x97\xe7\x8a\xb6\xe6\x80\x81\xe4\xb8\xba\xe5\xbc\x82\xe5\xb8\xb8\xef\xbc\x8c\xe9\x9c\x80\xe8\xa6\x81\xe7\xad\x89\xe5\xbe\x852\xe5\x88\x86\xe9\x92\x9f\xe5\xb7\xa6\xe5\x8f\xb3\xe4\xbc\x9a\xe8\x87\xaa\xe5\x8a\xa8\xe6\x9b\xb4\xe6\x96\xb0\xe4\xb8\xba\xe6\xad\xa3\xe5\xb8\xb8\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe9\x95\xbf\xe6\x97\xb6\xe9\x97\xb4\xe5\xa4\x84\xe4\xba\x8e\xe5\xbc\x82\xe5\xb8\xb8\xe7\x8a\xb6\xe6\x80\x81\xef\xbc\x8c\xe8\xaf\xb7\xe6\xa3\x80\xe6\x9f\xa5\xe5\x95\x86\xe5\xae\xb6ID\xe6\x98\xaf\xe5\x90\xa6\xe6\xad\xa3\xe7\xa1\xae\xef\xbc\x8c\xe8\x8b\xa5\xe5\x95\x86\xe5\xae\xb6ID\xe6\xad\xa3\xe7\xa1\xae\xef\xbc\x8c\xe8\xbf\x9b\xe5\x85\xa5\xe7\xbc\x96\xe8\xbe\x91\xe9\xa1\xb5\xe9\x9d\xa2\xe9\x87\x8d\xe6\x96\xb0\xe6\x8e\x88\xe6\x9d\x83\xef\xbc\x8c\xe8\x8b\xa5\xe5\x95\x86\xe5\xae\xb6ID\xe9\x94\x99\xe8\xaf\xaf\xef\xbc\x8c\xe8\xaf\xb7\xe5\x88\xa0\xe9\x99\xa4\xe5\xbc\x82\xe5\xb8\xb8\xe7\xbd\x91\xe5\xba\x97\xef\xbc\x8c\xe9\x87\x8d\xe6\x96\xb0\xe6\xb7\xbb\xe5\x8a\xa0\xe6\x96\xb0\xe7\x9a\x84\xe7\xbd\x91\xe5\xba\x97\xe3\x80\x82\\n\\n\\n\xe7\xbd\x91\xe5\xba\x97\xe7\xae\xa1\xe7\x90\x86\xe5\x88\x97\xe8\xa1\xa8\\n\xe5\x88\x97\xe8\xa1\xa8\xe6\x93\x8d\xe4\xbd\x9c\xe6\x8c\x89\xe9\x92\xae\\n\xe5\xa6\x82\xe6\x9e\x9c\xe6\x8e\x88\xe6\x9d\x83\xe6\xa8\xa1\xe5\xbc\x8f\xe6\x98\xaf\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xe6\x8e\x88\xe6\x9d\x83\xef\xbc\x8c\xe5\x88\x99\xe9\x9a\x90\xe8\x97\x8f\xe3\x80\x90\xe6\x9f\xa5\xe7\x9c\x8b\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xe3\x80\x91\xe6\x8c\x89\xe9\x92\xae\xe3\x80\x82\\n\\n\xe5\x90\x8c\xe6\xad\xa5\xe9\x97\xa8\xe5\xba\x97\xe4\xb8\x8e\xe5\x88\xa0\xe9\x99\xa4\xe6\x93\x8d\xe4\xbd\x9c\xe6\x8c\x89\xe9\x92\xae\xe4\xb8\x8e\xe5\x8e\x9f\xe6\x9c\x89\xe9\x80\xbb\xe8\xbe\x91\xe4\xb8\x80\xe8\x87\xb4\xe3\x80\x82\\n\\n\xe7\xbc\x96\xe8\xbe\x91\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xe6\x8e\x88\xe6\x9d\x83\xe7\xbd\x91\xe5\xba\x97\\n\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe6\x8e\x88\xe6\x9d\x83\xe6\xa8\xa1\xe5\xbc\x8f\xe7\xbc\x96\xe8\xbe\x91\xe7\xbd\x91\xe5\xba\x97\xe4\xbf\xa1\xe6\x81\xaf\\n\\n\xe5\x8f\xaa\xe5\x85\x81\xe8\xae\xb8\xe7\xbc\x96\xe8\xbe\x91\xe7\xbd\x91\xe5\xba\x97\xe5\x90\x8d\xe7\xa7\xb0\xe3\x80\x82\\n\xe6\x8e\x88\xe6\x9d\x83\xe7\xb1\xbb\xe5\x9e\x8b\xe3\x80\x81\xe5\x95\x86\xe5\xae\xb6ID\xe4\xb8\x8d\xe5\x8f\xaf\xe4\xbf\xae\xe6\x94\xb9\xef\xbc\x9b\\n\xe7\x82\xb9\xe5\x87\xbb\xe3\x80\x90\xe9\x87\x8d\xe6\x96\xb0\xe6\x8e\x88\xe6\x9d\x83\xe3\x80\x91\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x8c\xe8\x87\xaa\xe5\x8a\xa8\xe8\xb0\x83\xe7\x94\xa8\xe5\x9b\xba\xe5\xae\x9a\xe7\x9a\x84\xe5\xba\x94\xe7\x94\xa8\xe6\x8e\x88\xe6\x9d\x83URL\xef\xbc\x8c\xe5\x95\x86\xe5\xae\xb6\xe7\x99\xbb\xe9\x99\x86\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe5\xbe\x97\xe5\x95\x86\xe5\xae\xb6\xe8\xb4\xa6\xe5\x8f\xb7\xe5\xae\x8c\xe6\x88\x90\xe6\x8e\x88\xe6\x9d\x83\xe3\x80\x82\\n\xe7\x82\xb9\xe5\x87\xbb\xe4\xbf\x9d\xe5\xad\x98\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x8c\xe4\xbf\x9d\xe5\xad\x98\xe6\x9b\xb4\xe6\x96\xb0\xe7\x9a\x84\xe7\xbd\x91\xe5\xba\x97\xe4\xbf\xa1\xe6\x81\xaf\xe3\x80\x82\xef\xbc\x88\xe4\xbf\x9d\xe5\xad\x98\xe5\x89\x8d\xe7\x9a\x84\xe6\xa0\xa1\xe9\xaa\x8c\xe9\x80\xbb\xe8\xbe\x91\xe4\xb8\x8e\xe6\x96\xb0\xe5\xa2\x9e\xe4\xb8\x80\xe8\x87\xb4\xe3\x80\x82\xef\xbc\x89\\n\\n\\n\xe6\x8e\xa5\xe6\x94\xb6\xe5\x95\x86\xe6\x88\xb7\xe6\x8e\x88\xe6\x9d\x83\xe6\xb6\x88\xe6\x81\xaf\\n\xe5\x95\x86\xe6\x88\xb7\xe9\x80\x9a\xe8\xbf\x87\xe6\x8e\x88\xe6\x9d\x83URL\xe9\x93\xbe\xe6\x8e\xa5\xef\xbc\x8c\xe7\xa1\xae\xe8\xae\xa4\xe6\x8e\x88\xe6\x9d\x83\xe5\x90\x8e\xef\xbc\x8c\xe9\x80\x9a\xe8\xbf\x87token\xe6\x8e\xa5\xe6\x94\xb6\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xef\xbc\x8c\xe6\x8e\xa5\xe6\x8e\x88\xe6\x9d\x83\xe4\xbf\xa1\xe6\x81\xaf\xe6\x8e\xa8\xe9\x80\x81\xe7\xbb\x99\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe3\x80\x82\\n\\n\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\\n\xe6\x8e\xa5\xe6\x94\xb6\xe5\xb9\xb3\xe5\x8f\xb0\xe6\x8e\xa8\xe9\x80\x81\xe7\x9a\x84\xe5\x95\x86\xe6\x88\xb7\xe6\x8e\x88\xe6\x9d\x83\xe6\xb6\x88\xe6\x81\xaf\xef\xbc\x9a\\n\xef\xbc\x881\xef\xbc\x89\xe8\x8b\xa5\xe5\x95\x86\xe6\x88\xb7ID\xe4\xb8\x8d\xe5\xad\x98\xe5\x9c\xa8\xef\xbc\x8c\xe5\x88\x99\xe5\x88\x9b\xe5\xbb\xba\xe6\x96\xb0\xe5\x95\x86\xe6\x88\xb7ID\xe7\x9a\x84\xe6\x8e\x88\xe6\x9d\x83\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x8c\xe5\xb9\xb6\xe4\xbf\x9d\xe5\xad\x98\xe5\xaf\xb9\xe5\xba\x94\xe7\x9a\x84\xe5\x95\x86\xe6\x88\xb7ID\xe5\x8f\x8a\xe6\x8e\x88\xe6\x9d\x83\xe7\xa7\x98\xe9\x92\xa5\xe4\xbf\xa1\xe6\x81\xaf\xe3\x80\x82\\n\xef\xbc\x882\xef\xbc\x89\xe8\x8b\xa5\xe5\x95\x86\xe6\x88\xb7ID\xe5\xb7\xb2\xe5\xad\x98\xe5\x9c\xa8\xef\xbc\x8c\xe5\x88\x99\xe6\x9b\xb4\xe6\x96\xb0\xe5\x95\x86\xe6\x88\xb7ID\xe7\x9a\x84\xe6\x8e\x88\xe6\x9d\x83\xe7\xa7\x98\xe9\x92\xa5\xe4\xbf\xa1\xe6\x81\xaf\xe3\x80\x82\\n\xe5\x95\x86\xe6\x88\xb7\xe7\xa7\x98\xe9\x92\xa5\xe5\xa6\x82\xe4\xbd\x95\xe8\x87\xaa\xe5\x8a\xa8\xe6\x9b\xb4\xe6\x96\xb0\xef\xbc\x9f\xef\xbc\x9f\xef\xbc\x9f\xef\xbc\x9f\\n\xe6\x9b\xb4\xe6\x96\xb0token\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x9atoken\xe5\xbf\xab\xe5\x88\xb0\xe6\x9c\x9f\xe6\x97\xb6\xef\xbc\x8c\xe7\xb3\xbb\xe7\xbb\x9f\xe8\x83\xbd\xe8\x87\xaa\xe5\x8a\xa8\xe8\xb0\x83\xe7\x94\xa8\xe5\xb9\xb3\xe5\x8f\xb0\xe6\x8e\xa5\xe5\x8f\xa3\xef\xbc\x8c\xe8\x87\xaa\xe5\x8a\xa8\xe6\x9b\xb4\xe6\x96\xb0token\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x9f\xef\xbc\x9f\xef\xbc\x9f\\nOMS\\n\xe6\x8e\xa5\xe6\x94\xb6\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe6\x8e\xa8\xe9\x80\x81\xe7\x9a\x84\xe5\x95\x86\xe6\x88\xb7\xe6\x8e\x88\xe6\x9d\x83\xe6\xb6\x88\xe6\x81\xaf\xef\xbc\x8c\xe9\x80\x9a\xe8\xbf\x87\xe5\x95\x86\xe5\xae\xb6ID\xe8\xaf\x86\xe5\x88\xab\xe7\xbd\x91\xe5\xba\x97\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x9a\\n\xef\xbc\x881\xef\xbc\x89\xe8\x8b\xa5\xe7\xbd\x91\xe5\xba\x97\xe7\x8a\xb6\xe6\x80\x81\xe4\xb8\xba\xe5\xbc\x82\xe5\xb8\xb8\xef\xbc\x8c\xe5\x88\x99\xe6\x9b\xb4\xe6\x96\xb0\xe7\xbd\x91\xe5\xba\x97\xe7\x8a\xb6\xe6\x80\x81\xe4\xb8\xba\xe3\x80\x90\xe6\xad\xa3\xe5\xb8\xb8\xe3\x80\x91\xef\xbc\x8c\xe5\x90\x8c\xe6\x97\xb6\xe8\x87\xaa\xe5\x8a\xa8\xe8\xb0\x83\xe7\x94\xa8\xe9\x97\xa8\xe5\xba\x97\xe6\x8e\xa5\xe5\x8f\xa3\xef\xbc\x8c\xe6\x8b\x89\xe5\x8f\x96\xe7\xbd\x91\xe5\xba\x97\xe7\x9a\x84\xe9\x97\xa8\xe5\xba\x97\xe4\xbf\xa1\xe6\x81\xaf\xe3\x80\x82\\n\xef\xbc\x882\xef\xbc\x89\xe8\x8b\xa5\xe7\xbd\x91\xe5\xba\x97\xe7\x8a\xb6\xe6\x80\x81\xe4\xb8\xba\xe6\xad\xa3\xe5\xb8\xb8\xef\xbc\x8c\xe5\x88\x99\xe4\xb8\x8d\xe5\x81\x9a\xe4\xbb\xbb\xe4\xbd\x95\xe5\xa4\x84\xe7\x90\x86\xe3\x80\x82\\n\\n\\nOMS&\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe7\x9b\xb8\xe5\x85\xb3\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe6\x95\xb4\\n\xe6\xb6\x88\xe6\x81\xaf\xe5\xa4\x84\xe7\x90\x86\\n\xe8\xae\xa2\xe5\x8d\x95\xe7\x9b\xb8\xe5\x85\xb3\xe6\xb6\x88\xe6\x81\xaf\xe5\xa4\x84\xe7\x90\x86\\n\\nOMS\xe6\x8e\xa5\xe6\x94\xb6\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe6\x8e\xa8\xe9\x80\x81\xe7\x9a\x84\xe6\xb6\x88\xe6\x81\xaf\xef\xbc\x9a\\n1.\xe4\xbe\x9d\xe6\x8d\xae\xe6\xb6\x88\xe6\x81\xaf\xe5\x88\x9b\xe5\x8d\x95\xe6\x97\xb6\xef\xbc\x8c\xe9\x9c\x80\xe8\xa6\x81\xe9\x80\x9a\xe8\xbf\x87\xe7\xba\xbf\xe4\xb8\x8a\xe9\x97\xa8\xe5\xba\x97\xe7\xbc\x96\xe7\xa0\x81\xe8\xaf\x86\xe5\x88\xab\xe8\xaf\xa5\xe5\x8d\x95\xe6\x8d\xae\xe6\x98\xaf\xe5\xb1\x9e\xe4\xba\x8e\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe7\x9a\x84\xe5\x93\xaa\xe4\xb8\xaa\xe5\x95\x86\xe6\x88\xb7\xe7\x9a\x84\xe5\x93\xaa\xe4\xb8\xaa\xe7\xbd\x91\xe5\xba\x97\xef\xbc\x8c\xe5\xb9\xb6\xe5\xb0\x86\xe5\xaf\xb9\xe5\xba\x94\xe7\x9a\x84\xe7\xbd\x91\xe5\xba\x97\xe4\xbf\xa1\xe6\x81\xaf\xe5\x92\x8c\xe5\x95\x86\xe6\x88\xb7\xe4\xbf\xa1\xe6\x81\xaf\xe4\xbf\x9d\xe5\xad\x98\xe5\x88\xb0\xe5\x8d\x95\xe6\x8d\xae\xe4\xb8\xad\xef\xbc\x9b\xef\xbc\x88\xe5\x8c\x85\xe6\x8b\xac\xe6\xad\xa3\xe5\x90\x91\xe8\xae\xa2\xe5\x8d\x95\xe5\x92\x8c\xe9\x80\x86\xe8\xa2\xad\xe9\x80\x80\xe6\xac\xbe\xe5\x8d\x95\xe7\xad\x89\xef\xbc\x89\\n2.\xe6\x8e\xa5\xe6\x94\xb6\xe5\x8d\x95\xe6\x8d\xae\xe5\x8f\x98\xe6\x9b\xb4\xe6\xb6\x88\xe6\x81\xaf\xe6\x97\xb6\xef\xbc\x8c\xe5\x90\x8c\xe6\xa0\xb7\xe9\x9c\x80\xe8\xa6\x81\xe6\xa0\xb9\xe6\x8d\xae\xe6\xb6\x88\xe6\x81\xaf\xe4\xb8\xad\xe7\x9a\x84\xe7\xba\xbf\xe4\xb8\x8a\xe9\x97\xa8\xe5\xba\x97\xe7\xbc\x96\xe7\xa0\x81\xe8\xaf\x86\xe5\x88\xab\xe8\xaf\xa5\xe5\x8d\x95\xe6\x8d\xae\xe6\x98\xaf\xe5\xb1\x9e\xe4\xba\x8e\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe7\x9a\x84\xe5\x93\xaa\xe4\xb8\xaa\xe5\x95\x86\xe6\x88\xb7\xe7\x9a\x84\xe5\x93\xaa\xe4\xb8\xaa\xe7\xbd\x91\xe5\xba\x97\xe3\x80\x82\\n\\n\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe7\x94\xa8\\n\xe8\xb0\x83\xe7\x94\xa8\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe6\x8e\xa5\xe5\x8f\xa3\xe9\x9c\x80\xe8\xa6\x81\xe9\x87\x8d\xe6\x96\xb0\xe8\xbd\xac\xe5\x8c\x96\xe4\xb8\xba\xe6\x80\xbb\xe8\xb4\xa6\xe5\x8f\xb7\xe7\x9a\x84\xe5\x85\xa5\xe5\x8f\x82\xe6\x95\xb0\xe6\x8d\xae\xef\xbc\x8c\xe5\xa2\x9e\xe5\x8a\xa0\xe5\x95\x86\xe6\x88\xb7ID\xe5\x85\xa5\xe5\x8f\x82\xef\xbc\x9b\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe9\x80\x9a\xe8\xbf\x87clientid\xe5\x88\xa4\xe6\x96\xad\xe8\xb0\x83\xe7\x94\xa8\xe5\x93\xaa\xe4\xb8\xaa\xe6\xa8\xa1\xe5\xbc\x8f\xe7\x9a\x84\xe6\x8e\xa5\xe5\x8f\xa3\xef\xbc\x9b\\n\xe7\x9b\xb8\xe5\x85\xb3\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe7\x94\xa8\xe6\xa8\xa1\xe5\x9d\x97\xe8\xb0\x83\xe6\x95\xb4\\n\xe5\xba\x97\xe9\x93\xba\xe7\xae\xa1\xe7\x90\x86\\n\xe5\x95\x86\xe5\x93\x81\xe7\xae\xa1\xe7\x90\x86\\n\xe8\xae\xa2\xe5\x8d\x95\xe7\xae\xa1\xe7\x90\x86\\n\xe5\xb9\xb3\xe5\x8f\xb0\xe5\xaf\xb9\xe8\xb4\xa6\\n\xe8\xaf\x84\xe4\xbb\xb7\xe6\x8e\xa5\xe5\x8f\xa3\\n\xe4\xba\xac\xe4\xb8\x9c\xe7\xa7\x92\xe9\x80\x81O2O\xe8\xbd\xacB2C\xe4\xb8\x9a\xe5\x8a\xa1\xe6\x8e\xa5\xe5\x8f\xa3\xef\xbc\x9a\xe8\xae\xa2\xe5\x8d\x95\xe3\x80\x81\xe5\x8f\x91\xe8\xb4\xa7\xe3\x80\x81\xe9\x80\x80\xe6\xac\xbe\xe5\xae\xa1\xe6\xa0\xb8\xe7\xad\x89\\n\xe6\xb3\xa8\xe6\x84\x8f\xef\xbc\x9a\xe6\x98\xaf\xe6\x89\x80\xe6\x9c\x89\xe7\x9a\x84\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe6\x8e\xa5\xe5\x8f\xa3\xe9\x83\xbd\xe9\x9c\x80\xe8\xa6\x81\xe8\xb0\x83\xe6\x95\xb4\xef\xbc\x8c\xe4\xb8\x8d\xe8\x83\xbd\xe9\x81\x97\xe6\xbc\x8f\xe3\x80\x82\\n\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe7\x9b\xb8\xe5\x85\xb3\xe6\x8e\xa5\xe5\x8f\xa3\xe9\x93\xbe\xe6\x8e\xa5\xef\xbc\x9ahttps://medplatform.hydee.cn/web/#/4/109 \\n\xe5\xbd\xb1\xe5\x93\x8d\xe8\x8c\x83\xe5\x9b\xb4\\n1.\xe5\x8c\x85\xe6\x8b\xacOMS\xe7\x9a\x84\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6O2O\xe3\x80\x81B2C\xe3\x80\x81\xe5\xba\x97\xe9\x93\xba\xe3\x80\x81\xe8\xae\xa2\xe5\x8d\x95\xe3\x80\x81\xe5\x95\x86\xe5\x93\x81\xe3\x80\x81\xe5\xaf\xb9\xe8\xb4\xa6\xe3\x80\x81\xe8\xaf\x84\xe4\xbb\xb7\xe7\xad\x89\xe4\xb8\x9a\xe5\x8a\xa1\xe3\x80\x82\\n2.\xe5\x85\xbc\xe5\xae\xb9\xe5\x95\x86\xe5\xae\xb6\xe5\x92\x8c\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe4\xb8\xa4\xe7\xa7\x8d\xe6\xa8\xa1\xe5\xbc\x8f\xe3\x80\x82\\n"}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092631578|Thread-33 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092631579|Thread-33 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092731613|Thread-33 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：商家访问固定URL进行授权 -> 商家登录京东到家账号确认授权 -> 授权信息通过回调地址发送给接口中台 -> OMS根据商家ID更新状态并拉取门店信息。\n- **分支流程**：商家授权失败（例如，输入错误的商家ID）-> 需要重新授权；商家ID已存在或网店名称重复 -> 提示错误，并要求修改后重试。\n- **流程节点**：\n  - 输入节点：商家ID、网店名称\n  - 处理节点：授权确认、秘钥和授权信息保存\n  - 输出节点：授权成功/失败消息\n  - 存储节点：OMS中的商家信息及授权状态\n\n### 1.2 功能模块拆解\n- **核心模块**：授权管理、接口中台、OMS\n- **输入输出**：授权管理模块接收商家ID与网店名称作为输入，输出为授权结果；接口中台接收授权信息，输出为更新后的秘钥信息；OMS接收秘钥信息，输出为更新后的商家状态。\n- **模块接口**：授权管理通过回调地址向接口中台传递授权信息；接口中台处理后将数据传递给OMS。\n\n### 1.3 业务规则提取\n- **核心规则**：只有未被授权过的商家ID才能添加新的授权请求；每个商家都有独立的秘钥。\n- **验证规则**：确保商家ID唯一性，防止重复授权；校验商家ID是否已存在于系统中。\n- **计算逻辑**：无直接提及的重要计算逻辑，主要涉及数据的校验与存储。\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：授权页面需简洁明了，减少用户操作步骤。\n- 异常提示友好性要求：当出现授权失败时，给出明确的错误原因及解决方案。\n- 角色权限差异：普通商户只能查看自己的授权情况，而服务商可以管理多个商家的授权。\n\n## 3. 异常场景分析\n- 输入异常处理：对无效的商家ID或重复的网店名称提供即时反馈。\n- 操作异常处理：授权过程中断或失败时，允许用户重新尝试授权。\n- 外部依赖异常处理：如京东到家平台不可用时，应有备用方案以保证服务连续性。\n- PRD未明确的隐性异常场景：未特别指出其他可能的异常情况。\n\n## 4. 兼容性需求分析\n- PRD明确的兼容范围：兼容现有的商家和服务商两种模式，确保在不破坏现有功能的基础上新增服务商模式的支持。\n\n## 5. 安全性需求分析\n- 数据安全要求：保护商家ID与秘钥的安全，禁止未经授权的访问。\n- 权限控制要求：不同的用户角色具有不同的操作权限，如服务商可管理多个商家的信息。\n- 业务安全要求：确保所有授权动作都是在商家知情的情况下完成，且授权信息准确无误地传输。\n\n## 6. 性能需求分析\n- PRD明确的性能指标：文档中没有具体提到性能指标。\n- 高并发场景要求：能够同时处理多个商家的授权请求，特别是在促销活动期间。", "meta": {"msg": "需求分析成功", "status": 200}, "timestamp": 1752456451612}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092731613|Thread-33 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:143|当前请求耗时：60.03838801383972 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092731648|Thread-33 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_recognize_record_list/，请求方式：POST，请求参数为：b'{"business_id":"","iteration_name":"","creater":"","pagenum":1,"pagesize":5}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092731648|Thread-33 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092731648|Thread-33 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092731663|Thread-34 (recordSystemLog):6208073728|INFO|commonUtil.py:51|用户 liuyanxia 查询AI生成用例记录，查询条件：需求ID=，迭代名称=，业务线=，创建人= 
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092731712|Thread-33 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 5, "total": 51, "data": [{"id": 52, "iteration_id": "1161969829001002058", "business_id": "26", "requirement_id": 1067014, "requirement_content": "增加【美团待铺货&饿百待铺货】页签，同时支持新品页签的切换。\n店铺待铺货商品明细\n前置条件：先选择一个具体的平台店铺。（仅支持选择美团、饿百平台店铺）\n列表数据来源范围：\n商品信息：公域商品列表中，是否可售状态为【是】且有条形码的商品；同时第三方商品店铺无该商品，但所属门店有该商品。\n字段名称 字段逻辑\n平台店铺 平台 + 店铺名称\n商品信息 商品名称 + 规格 + 批准文号 名称\n条形码\nERP商品编码\n平台近30天总销量 美团所有店铺近30天内的销量汇总；支持按照销量切换排序。\nT-1的30天内（下账时间），已下账的商品数量汇总。\n所属门店 门店名称 + 门店编码\nERP门店库存 ERP同步的门店库存\nERP门店价格 ERP同步的门店价格\nERP加权成本价 ERP同步的加权成本价\n预估毛利率 （店铺铺货价格 - 加权成本价） / 加权成本价 * 100%；根据店铺铺货价格动态计算。\n店铺铺货价格 默认门店价格，支持编辑，数据格式：大于0的两位小数。\n店铺商品分类 下拉单选\n列表排序：默认按平台近30天总销量倒序排序\n页签字段文档描述：\n美团待铺货页签字段：\n商品信息：公域商品列表中，是否可售状态为【是】且有条形码的商品才展示。同时第三方商品店铺无该商品，但所属门店有该商品。\n美团近30天销量：连锁在美团所有店铺近30天内的销售数据汇总（T-1的30天内（下账时间），已下账的商品数量汇总。）\n\n饿百待铺货页签：\n商品信息：公域商品列表中，是否可售状态为【是】且有条形码的商品才展示。同时第三方商品店铺无该商品，但所属门店有该商品。\n饿百近30天销量：连锁在饿百所有店铺近30天内的销售数据汇总（T-1的30天内（下账时间），已下账的商品数量汇总。）\n\n查询条件\n查询条件 查询逻辑\n平台店铺 平台 + 店铺，支持搜索对应平台的店铺\n所属门店 门店编码或门店名称，与平台店铺联动。\n商品名称 模糊匹配查询\nERP商品编码 精确匹配查询，支持查多个，最大100个\n条形码 精确匹配查询，支持查多个，最大100个\n平台销量区间 0-n的区间数量，根据选择的平台店铺，确定查询哪个平台的销量\n新品天数设置\n查看新品过滤及设置调整到页面右上角。\n支持在所有页签设置新品天数和勾选新品天数过滤商品数据。切换页签新品勾选条件不变。\n新品天数文案描述：勾选则表示，只查询线上商品库的商品创建时间在设置的新品天数范围内的商品。\n操作\n1.新品铺货-单个操作：按行操作商品铺货，按照编辑后的店铺铺货价格进行铺货（需二次确认）。铺货操作同步处理，并实时反馈操结果，刷新操作页面。\n校验：价格为0的，不支持铺货，且提示：【铺货价格不允许为0。】\n2.新品铺货-批量操作（仅支持当前页批量-一页最多20条）：批量勾选当前页的商品（需二次确认）。铺货操作同步处理，并实时反馈操结果，若有铺货失败的数据，则展示失败原因，并支持失败数据复制。\n校验：价格为0的，不支持勾选。\n3.支持数据导出（查询结果的列表全量数据）", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：用户选择平台店铺 -> 查询待铺货商品列表 -> 选择单个或批量商品进行铺货操作 -> 验证铺货价格 -> 确认铺货 -> 实时反馈结果 -> 刷新页面\n- **分支流程**：\n  - 正常分支：成功铺货后，实时更新数据\n  - 异常分支：铺货价格为0，提示错误信息并阻止操作\n  - 回退流程：铺货失败的数据支持复制和重新提交\n- **流程节点**：\n  - 输入节点：平台店铺选择、查询条件输入\n  - 处理节点：商品列表查询、铺货价格验证、铺货操作执行\n  - 输出节点：铺货成功/失败的结果反馈\n  - 存储节点：铺货后的商品信息存储\n\n### 1.2 功能模块拆解\n- **核心模块**：\n  - 商品列表展示模块\n  - 铺货操作模块\n  - 数据导出模块\n- **输入输出**：\n  - 商品列表展示模块：输入（查询条件）、输出（商品列表）\n  - 铺货操作模块：输入（铺货价格）、输出（铺货结果）\n  - 数据导出模块：输入（查询结果）、输出（导出文件）\n- **模块接口**：\n  - 商品列表展示模块调用铺货操作模块传递铺货信息\n  - 铺货操作模块返回结果给商品列表展示模块\n  - 数据导出模块从商品列表展示模块获取数据\n\n### 1.3 业务规则提取\n- **核心规则**：\n  - 仅展示可售且有条形码的商品\n  - 第三方商品店铺无该商品，但所属门店有该商品\n  - 默认按平台近30天总销量倒序排序\n- **验证规则**：\n  - 价格为0的，不允许铺货\n  - 批量操作每页最多20条记录\n- **计算逻辑**：\n  - 预估毛利率 = (店铺铺货价格 - 加权成本价) / 加权成本价 * 100%\n  - 新品天数过滤：只查询创建时间在设置的新品天数范围内的商品\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：确保页面加载快速，操作响应及时\n- 异常提示友好性要求：提供清晰的错误提示，如铺货价格为0时显示“铺货价格不允许为0”\n- 角色权限差异：未明确提到角色权限差异\n\n## 3. 异常场景分析\n- 输入异常处理：价格为0时不支持铺货，并提示错误\n- 操作异常处理：铺货失败时显示失败原因，并支持失败数据复制\n- 外部依赖异常处理：PRD中未提供相关信息\n- PRD未明确的隐性异常场景：未明确\n\n## 4. 兼容性需求分析\n- PRD明确的兼容范围：未明确\n\n## 5. 安全性需求分析\n- 数据安全要求：未明确\n- 权限控制要求：未明确\n- 业务安全要求：未明确\n\n## 6. 性能需求分析\n- PRD明确的性能指标：未明确\n- 高并发场景要求：未明确", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-07-08T16:18:29", "create_person": "刘艳霞", "is_active": true, "iteration_name": "【V2.3.4.22】商品云-0703", "total_ai_test_case": 10, "recall_ai_test_case": 0, "business_name": "数据中心"}, {"id": 51, "iteration_id": null, "business_id": "27", "requirement_id": 342342, "requirement_content": "- 将员工组织机构刷到数据授权中（店员、店长）\n\n- 将片区经理的授权门店刷到数据授权中（片区经理）\n\n- 将新增四季蝉按钮级别的资源刷到商户已有的角色资源里", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程  \n- **主流程**：系统从组织结构中获取员工信息 → 判断员工角色（店员、店长、片区经理）→ 更新数据授权配置。  \n- **分支流程**：若员工无对应角色，跳过该员工；若门店信息不存在，记录异常日志。  \n- **流程节点**：输入节点为员工组织结构数据，处理节点为角色判断与权限分配，输出节点为更新后的数据授权表。\n\n### 1.2 功能模块拆解  \n- **核心模块**：组织同步模块、角色识别模块、权限更新模块。  \n- **输入输出**：输入为员工组织数据和角色定义，输出为更新后的数据授权结果。  \n- **模块接口**：组织同步模块调用角色识别模块判断角色类型，权限更新模块接收识别结果进行授权更新。\n\n### 1.3 业务规则提取  \n- **核心规则**：仅支持“店员”、“店长”、“片区经理”三类角色的数据授权更新。  \n- **验证规则**：需验证员工所属门店是否有效，片区经理对应的门店是否存在。  \n- **计算逻辑**：根据员工角色确定授权范围，片区经理自动获得其管理门店的访问权限。\n\n---\n\n## 2. 用户体验需求分析  \n- 交互流程流畅性要求：批量处理员工授权时应有进度提示，避免界面无响应。  \n- 异常提示友好性要求：对于未匹配角色或无效门店的员工，应记录并展示清晰的失败原因。  \n- 角色权限差异：不同角色对应不同的授权范围，需在界面中明确区分显示内容。\n\n---\n\n## 3. 异常场景分析  \n- 输入异常处理：员工数据缺失关键字段（如门店ID、角色类型），系统应拒绝处理并记录错误。  \n- 操作异常处理：授权过程中数据库连接失败，应中断操作并返回友好的错误提示。  \n- 外部依赖异常处理：若组织服务不可用，系统应延迟执行任务或提示重试机制。  \n- PRD未明确的隐性异常场景：员工角色变更后旧授权未清理，可能导致数据访问越权问题。\n\n---\n\n## 4. 兼容性需求分析  \n- PRD中未提供相关信息  \n\n---\n\n## 5. 安全性需求分析  \n- 数据安全要求：授权数据同步过程需加密传输，防止敏感信息泄露。  \n- 权限控制要求：仅允许具备管理员权限的用户触发授权刷新操作。  \n- 业务安全要求：确保片区经理只能访问其管理范围内的门店数据，不能越权查看。\n\n---\n\n## 6. 性能需求分析  \n- PRD中未提供相关信息", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-07-08T12:05:46", "create_person": "刘艳霞", "is_active": true, "iteration_name": "", "total_ai_test_case": 18, "recall_ai_test_case": 0, "business_name": "四季蝉"}, {"id": 50, "iteration_id": "1161969829001002043", "business_id": "29", "requirement_id": 1067351, "requirement_content": "进行中、未开始、已发布成功的活动按照已参与的活动门店记录所属企业；\n\n代建草稿状态无活动门店的活动都按创建人的员工资料中的所属企业创建，记录所属企业；\n\n历史的代建草稿状态的无活动门店的所属企业一律处理为集团；\n\n历史的代建草稿状态的有活动门店的所属企业按活动门店所属企业记录", "requirement_analyze_content": "## 1. 功能需求分析123123\n\n### 1.1 核心业务流程  \n- **主流程**：活动创建时根据活动状态、是否配置门店，决定所属企业的归属逻辑；  \n- **分支流程**：草稿状态无门店活动按创建人企业处理；历史数据特殊处理为集团或门店企业；  \n- **流程节点**：输入为活动状态、门店信息、创建人信息；输出为所属企业字段值。\n\n### 1.2 功能模块拆解  \n- **核心模块**：活动管理模块、企业归属判断模块、用户资料关联模块；  \n- **输入输出**：输入包括活动状态、门店列表、员工所属企业；输出为活动的“所属企业”属性；  \n- **模块接口**：活动模块调用归属判断模块获取所属企业；归属判断模块从用户资料模块读取企业信息。\n\n### 1.3 业务规则提取  \n- **核心规则**：草稿状态无门店活动使用创建人企业；有门店则使用门店所属企业；  \n- **验证规则**：需验证活动状态是否为“代建草稿”、门店是否存在、创建人信息是否完整；  \n- **计算逻辑**：判断优先级为：门店 > 创建人 > 集团（历史无门店草稿）。\n\n---\n\n## 2. 用户体验需求分析  \n- 交互流程流畅性要求：用户创建活动时应自动识别并填充所属企业，无需手动选择；  \n- 异常提示友好性要求：若无法确定所属企业，应给出明确提示说明原因；  \n- 角色权限差异：普通员工可创建草稿，管理员可发布活动，不同角色查看范围受企业限制。\n\n---\n\n## 3. 异常场景分析  \n- 输入异常处理：活动状态无效或门店为空但非草稿状态，系统应阻止保存并提示错误；  \n- 操作异常处理：创建人未绑定所属企业时，应提示“请先完善个人信息”；  \n- 外部依赖异常处理：若门店数据接口异常，应记录日志并提示“无法加载门店信息”；  \n- PRD未明确的隐性异常场景：多门店归属不一致时如何处理、跨企业门店是否允许存在。\n\n---\n\n## 4. 兼容性需求分析  \n- PRD中未提供相关信息。\n\n---\n\n## 5. 安全性需求分析  \n- 数据安全要求：所属企业字段属于敏感业务数据，应加密存储或脱敏展示；  \n- 权限控制要求：仅所属企业人员可查看和操作相关活动数据；  \n- 业务安全要求：防止非法篡改“所属企业”字段以规避数据隔离策略。\n\n---\n\n## 6. 性能需求分析  \n- PRD中未提供相关信息。", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-06-30T16:06:40", "create_person": "刘艳霞", "is_active": true, "iteration_name": "【V3.1.6.0】四季蝉集团化数据隔离改造-0701", "total_ai_test_case": 15, "recall_ai_test_case": 3, "business_name": "一键造数"}, {"id": 49, "iteration_id": "1161969829001002043", "business_id": "27", "requirement_id": 1067351, "requirement_content": "进行中、未开始、已发布成功的活动按照已参与的活动门店记录所属企业；\n\n代建草稿状态无活动门店的活动都按创建人的员工资料中的所属企业创建，记录所属企业；\n\n历史的代建草稿状态的无活动门店的所属企业一律处理为集团；\n\n历史的代建草稿状态的有活动门店的所属企业按活动门店所属企业记录", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：活动创建时判断活动状态和门店信息，决定所属企业的归属规则。\n- **分支流程**：\n  - 活动为“进行中/未开始/已发布成功”且有门店 → 所属企业按门店所属企业记录；\n  - 活动为“代建草稿”状态且无门店 → 所属企业按创建人所属企业记录；\n  - 历史“代建草稿”状态无门店 → 归为集团；\n  - 历史“代建草稿”状态有门店 → 按门店所属企业记录。\n\n- **流程节点**：\n  - 输入节点：活动状态、活动门店信息、创建人所属企业；\n  - 处理节点：根据规则判断活动所属企业；\n  - 输出节点：活动记录中所属企业字段；\n  - 存储节点：活动数据表中保存所属企业信息。\n\n### 1.2 功能模块拆解\n- **核心模块**：\n  - 活动管理模块：负责活动的创建、状态维护及所属企业分配；\n  - 用户管理模块：提供创建人所属企业信息；\n  - 数据存储模块：保存活动及相关企业信息。\n\n- **输入输出**：\n  - 活动管理模块输入：活动状态、门店列表、创建人ID；\n  - 活动管理模块输出：活动记录及其所属企业字段；\n  - 用户管理模块输入/输出：创建人信息查询接口。\n\n- **模块接口**：\n  - 活动管理调用用户管理接口获取创建人所属企业；\n  - 活动管理写入数据存储模块保存活动记录。\n\n### 1.3 业务规则提取\n- **核心规则**：\n  - “进行中/未开始/已发布成功”活动优先依据门店确定所属企业；\n  - “代建草稿”活动无门店则归属创建人企业或默认集团；\n  - 历史数据统一处理规则不可变。\n\n- **验证规则**：\n  - 验证活动状态是否符合分类标准；\n  - 验证门店是否存在并关联有效企业。\n\n- **计算逻辑**：\n  - 根据活动状态和门店是否存在选择不同的企业归属逻辑路径；\n  - 对历史数据应用预设规则批量处理。\n\n---\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：用户创建活动时无需手动指定所属企业，系统自动处理。\n- 异常提示友好性要求：当无法识别所属企业时应提示用户检查输入信息。\n- 角色权限差异：创建人角色影响“代建草稿”状态下企业归属的判定逻辑。\n\n---\n\n## 3. 异常场景分析\n- 输入异常处理：活动状态或门店信息缺失时应触发错误处理机制。\n- 操作异常处理：中途取消或修改活动状态可能影响企业归属逻辑。\n- 外部依赖异常处理：用户信息接口不可用时需有容错机制。\n- PRD未明确的隐性异常场景：多门店情况下的企业归属优先级未说明。\n\n---\n\n## 4. 兼容性需求分析\n- PRD中未提供相关信息。\n\n---\n\n## 5. 安全性需求分析\n- 数据安全要求：确保活动与所属企业的绑定关系不被非法篡改。\n- 权限控制要求：仅允许合法创建人操作其所属企业范围内的活动。\n- 业务安全要求：防止通过伪造门店信息绕过企业归属限制。\n\n---\n\n## 6. 性能需求分析\n- PRD中未提供相关信息。\n- 高并发场景要求：PRD中未提供相关信息。", "requirement_pic": [], "ui_link": "https://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001067351", "tech_link": "https://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001067351", "create_time": "2025-06-24T14:52:41", "create_person": "刘艳霞", "is_active": true, "iteration_name": "【V3.1.6.0】四季蝉集团化数据隔离改造-0701", "total_ai_test_case": 10, "recall_ai_test_case": 2, "business_name": "四季蝉"}, {"id": 48, "iteration_id": "1161969829001002076", "business_id": "0", "requirement_id": 1067581, "requirement_content": "目前售后提交问题后，值班人员分析问题时间可能比较长，但是售后迟迟得不到回应，会产生焦虑，无法知晓是否有技术人员在处理该问题，故增加以下逻辑：\n\nB端及C端在问题详情页增加一个按钮，按钮名称为：【安心告知】，点击后基于原问题推送一条消息，消息内容为：技术人员正在跟进中，请耐心等待\n问题状态不做任何变更，仅仅只是推送一条消息\n按钮需要做判断，只有待受理及解决中状态才可点击\n按钮点击后在未关闭页面的情况下，不支持再次点击", "requirement_analyze_content": "## 1. 功能需求分析\n\n### 1.1 核心业务流程\n- **主流程**：用户在问题详情页点击【安心告知】按钮，系统基于原问题推送消息“技术人员正在跟进中，请耐心等待”，按钮状态变更为不可点击。\n- **分支流程**：若问题状态为非“待受理”或“解决中”，按钮不可见或不可点击；页面刷新后恢复初始状态。\n- **流程节点**：输入节点（问题状态、按钮点击事件），处理节点（状态判断、消息推送逻辑），输出节点（消息推送结果），存储节点（无新增存储需求）。\n\n### 1.2 功能模块拆解\n- **核心模块**：问题详情页模块、消息推送模块、状态判断模块。\n- **输入输出**：问题详情页模块（输入：问题ID、问题状态；输出：按钮显示/隐藏状态）、消息推送模块（输入：问题ID、消息模板；输出：推送结果）、状态判断模块（输入：问题状态；输出：是否允许点击）。\n- **模块接口**：问题详情页调用状态判断模块获取按钮状态，调用消息推送模块完成消息发送。\n\n### 1.3 业务规则提取\n- **核心规则**：按钮仅在问题状态为“待受理”或“解决中”时可用，点击后禁止再次点击。\n- **验证规则**：校验问题状态是否符合要求；确保按钮点击后状态变更正确。\n- **计算逻辑**：无复杂计算逻辑，主要依赖状态判断和消息模板填充。\n\n---\n\n## 2. 用户体验需求分析\n- 交互流程流畅性要求：按钮点击后需即时反馈，避免用户重复操作。\n- 异常提示友好性要求：当按钮不可点击时，需通过视觉方式（如灰化按钮）明确提示。\n- 角色权限差异：PRD未提及角色权限差异，默认所有可访问问题详情页的用户均可见按钮。\n\n---\n\n## 3. 异常场景分析\n- 输入异常处理：若问题状态为空或无效，按钮应默认隐藏或不可点击。\n- 操作异常处理：按钮多次快速点击时需屏蔽重复操作；网络中断时需提示推送失败。\n- 外部依赖异常处理：消息推送失败时记录日志，并提供重试机制。\n- PRD未明确的隐性异常场景：多用户同时操作同一问题详情页时的按钮状态同步问题。\n\n---\n\n## 4. 兼容性需求分析\n- PRD明确的兼容范围：未提及具体兼容性要求，但需确保支持主流浏览器及不同分辨率下的正常显示。\n\n---\n\n## 5. 安全性需求分析\n- 数据安全要求：推送的消息内容需脱敏处理，防止敏感信息泄露。\n- 权限控制要求：按钮显示与点击权限需严格基于问题状态判断。\n- 业务安全要求：防止恶意用户通过技术手段绕过状态限制进行非法操作。\n\n---\n\n## 6. 性能需求分析\n- PRD明确的性能指标：未提及具体性能指标。\n- 高并发场景要求：需确保在多用户同时点击按钮时，系统能够稳定处理并返回结果。", "requirement_pic": [], "ui_link": "", "tech_link": "", "create_time": "2025-06-20T17:44:31", "create_person": "杨逍", "is_active": true, "iteration_name": "【V1.6】鹰眼（当前迭代）", "total_ai_test_case": 10, "recall_ai_test_case": 5, "business_name": "默认"}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752456451712}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714092731712|Thread-33 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:143|当前请求耗时：0.06400203704833984 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714102911153|Thread-35 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_generate_testcases/，请求方式：POST，请求参数为：b'{"business_id":11,"requirement_id":"534535","requirement_content":"\xe4\xba\xac\xe4\xb8\x9c\xe7\xa7\x92\xe9\x80\x81\xe6\xb5\xb7\xe5\x85\xb8\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe6\xa8\xa1\xe5\xbc\x8f\\n\xe8\x83\x8c\xe6\x99\xaf\\n\xe5\xbd\x93\xe5\x89\x8d\xe4\xba\xac\xe4\xb8\x9c\xe7\xa7\x92\xe9\x80\x81\xe6\x8e\x88\xe6\x9d\x83OMS\xe9\x9c\x80\xe8\xa6\x81\xe4\xb8\xba\xe6\xaf\x8f\xe4\xb8\xaa\xe5\x95\x86\xe5\xae\xb6\xe5\x8d\x95\xe7\x8b\xac\xe5\xbb\xba\xe7\xab\x8b\xe4\xb8\x80\xe4\xb8\xaa\xe5\xba\x94\xe7\x94\xa8\xef\xbc\x8c\xe5\x8d\x95\xe4\xbd\x93\xe5\xba\x97\xe5\x92\x8c\xe9\x97\xa8\xe5\xba\x97\xe8\xbe\x83\xe5\xb0\x91\xe7\x9a\x84\xe8\xbf\x9e\xe9\x94\x81\xe8\xbe\x83\xe5\xa4\x9a\xef\xbc\x8c\xe9\x9c\x80\xe8\xa6\x81\xe4\xbb\xb6\xe5\xbe\x88\xe5\xa4\x9a\xe7\x9a\x84\xe5\xba\x94\xe7\x94\xa8\xef\xbc\x8c\xe6\x8e\x88\xe6\x9d\x83\xe6\x95\x88\xe7\x8e\x87\xe4\xbd\x8e\xe3\x80\x82\xe4\xb8\xba\xe4\xba\x86\xe6\x8f\x90\xe5\x8d\x87\xe6\x95\x88\xe7\x8e\x87\xef\xbc\x8c\xe4\xba\xac\xe4\xb8\x9c\xe7\xa7\x92\xe9\x80\x81\xe7\xbb\x99\xe6\xb5\xb7\xe5\x85\xb8\xe5\xbc\x80\xe7\xab\x8b\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe6\x80\xbb\xe8\xb4\xa6\xe5\x8f\xb7\xef\xbc\x8c\xe5\x8d\x95\xe4\xbd\x93\xe5\xba\x97\xe6\x88\x96\xe8\xbf\x9e\xe9\x94\x81\xe4\xb8\x8d\xe9\x9c\x80\xe8\xa6\x81\xe5\x86\x8d\xe6\x96\xb0\xe5\xbb\xba\xe5\xba\x94\xe7\x94\xa8\xef\xbc\x8c\xe7\x9b\xb4\xe6\x8e\xa5\xe5\xb0\x86\xe5\xba\x97\xe9\x93\xba\xe7\xbb\x9f\xe4\xb8\x80\xe6\x8e\x88\xe6\x9d\x83\xe7\xbb\x99\xe6\xb5\xb7\xe5\x85\xb8\xe5\xba\x94\xe7\x94\xa8\xef\xbc\x8c\xe5\x8d\xb3\xe5\x8f\xaf\xe5\xae\x8c\xe6\x88\x90\xe6\x8e\x88\xe6\x9d\x83\xe3\x80\x82\\n\\n\xe4\xba\xac\xe4\xb8\x9c\xe7\xa7\x92\xe9\x80\x81\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe6\xa8\xa1\xe5\xbc\x8f\xe5\xaf\xb9\xe6\x8e\xa5\xe6\x96\x87\xe6\xa1\xa3\\nhttps://drive.weixin.qq.com/s?k=AOcA-wd2AAw1pTwpT7AKMAQgZ5ALQ \\n\\n\xe5\xaf\xb9\xe6\x8e\xa5\xe8\xb0\x83\xe7\xa0\x94\\n\xe4\xba\xac\xe4\xb8\x9c\xe7\xa7\x92\xe9\x80\x81\xe8\xb0\x83\xe7\xa0\x94\xe6\xb2\x9f\xe9\x80\x9a\xef\xbc\x9a\\n1.\xe5\xa6\x82\xe4\xbd\x95\xe5\xbb\xba\xe7\xab\x8b\xe6\xb5\xb7\xe5\x85\xb8\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xef\xbc\x8c\xe4\xb8\x8e\xe5\x85\xb6\xe4\xbb\x96\xe5\xba\x94\xe7\x94\xa8\xe6\x9c\x89\xe4\xbb\x80\xe4\xb9\x88\xe4\xb8\x8d\xe5\x90\x8c\xe3\x80\x82\\n\xe7\xad\x94\xef\xbc\x9a\\n2.\xe5\x95\x86\xe5\xae\xb6\xe5\xa6\x82\xe4\xbd\x95\xe5\xb0\x86\xe5\xba\x97\xe9\x93\xba\xe6\x8e\x88\xe6\x9d\x83\xe7\xbb\x99\xe6\xb5\xb7\xe5\x85\xb8\xe5\xba\x94\xe7\x94\xa8\xef\xbc\x9a\xe6\xb6\x88\xe6\x81\xaf\xe6\xa8\xa1\xe5\xbc\x8f\xe3\x80\x81\xe4\xb8\xbb\xe5\x8a\xa8\xe6\x8b\x89\xe5\x8e\xbb\xe6\xa8\xa1\xe5\xbc\x8f\xef\xbc\x9f\\n\xe7\xad\x94\xef\xbc\x9a\xe6\xb6\x88\xe6\x81\xaf\xe6\xa8\xa1\xe5\xbc\x8f\xef\xbc\x8c\xe5\x95\x86\xe5\x93\x81\xe6\x8e\x88\xe6\x9d\x83\xe5\x90\x8e\xef\xbc\x8c\xe4\xbc\x9a\xe6\x8e\xa8\xe9\x80\x81\xe6\x8e\x88\xe6\x9d\x83\xe6\xb6\x88\xe6\x81\xaf\xe3\x80\x82\\n3.\xe6\xb5\xb7\xe5\x85\xb8\xe5\xa6\x82\xe4\xbd\x95\xe5\x8c\xba\xe5\x88\x86\xe4\xb8\x8d\xe5\x90\x8c\xe5\x95\x86\xe5\xae\xb6\xe6\x8e\x88\xe6\x9d\x83\xe7\x9a\x84\xe5\xba\x97\xe9\x93\xba\xe4\xbf\xa1\xe6\x81\xaf\xe3\x80\x82\\n\xe7\xad\x94\xef\xbc\x9a\xe9\x80\x9a\xe8\xbf\x87\xe4\xb8\x8d\xe5\x90\x8c\xe5\x95\x86\xe5\xae\xb6\xe7\x9a\x84\xe7\xa7\x98\xe9\x92\xa5\xe8\xb0\x83\xe7\x94\xa8\xe6\x8e\xa5\xe5\x8f\xa3\xef\xbc\x8c\xe8\x8e\xb7\xe5\x8f\x96\xe5\xaf\xb9\xe5\xba\x94\xe5\x95\x86\xe5\xae\xb6\xe7\x9a\x84\xe5\xba\x97\xe9\x93\xba\xe4\xbf\xa1\xe6\x81\xaf\xe3\x80\x82\\n4.\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe7\x94\xa8\xe4\xb8\x8e\xe5\x95\x86\xe5\xae\xb6\xe7\x8b\xac\xe7\xab\x8b\xe5\xba\x94\xe7\x94\xa8\xe6\x9c\x89\xe4\xbd\x95\xe4\xb8\x8d\xe5\x90\x8c\xef\xbc\x8c\xe8\xb0\x83\xe7\x94\xa8\xe8\xbf\x87\xe7\xa8\x8b\xe4\xb8\xad\xe5\xa6\x82\xe4\xbd\x95\xe5\x8c\xba\xe5\x88\x86\xe6\x98\xaf\xe5\x93\xaa\xe4\xb8\xaa\xe5\x95\x86\xe5\xae\xb6\xe7\x9a\x84\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe7\x94\xa8\xe8\xaf\xb7\xe6\xb1\x82\xe3\x80\x82\\n\xe7\xad\x94\xef\xbc\x9a\xe9\x80\x9a\xe8\xbf\x87\xe5\x95\x86\xe5\xae\xb6\xe7\x8b\xac\xe7\xab\x8b\xe7\x9a\x84\xe7\xa7\x98\xe9\x92\xa5\xe6\x9d\xa5\xe8\xb0\x83\xe7\x94\xa8\xe6\x8e\xa5\xe5\x8f\xa3\xef\xbc\x8c\xe5\xa4\x84\xe7\x90\x86\xe4\xb8\x8d\xe5\x90\x8c\xe5\x95\x86\xe5\xae\xb6\xe7\x9a\x84\xe4\xb8\x9a\xe5\x8a\xa1\xe3\x80\x82\\n5.\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe7\x94\xa8\xe7\x9a\x84\xe7\xa7\x98\xe9\x92\xa5\xe5\x85\xb1\xe7\x94\xa8\xe4\xb8\x80\xe5\xa5\x97\xef\xbc\x8c\xe8\xbf\x98\xe6\x98\xaf\xe6\xaf\x8f\xe4\xb8\xaa\xe5\x95\x86\xe5\xae\xb6\xe6\x88\x96\xe9\x97\xa8\xe5\xba\x97\xe9\x83\xbd\xe6\x9c\x89\xe7\x8b\xac\xe7\xab\x8b\xe7\x9a\x84\xe7\xa7\x98\xe9\x92\xa5\xe3\x80\x82\\n\xe7\xad\x94\xef\xbc\x9a\xe6\xaf\x8f\xe4\xb8\xaa\xe6\x8e\x88\xe6\x9d\x83\xe5\x95\x86\xe5\xae\xb6\xe9\x83\xbd\xe6\x9c\x89\xe7\x8b\xac\xe7\xab\x8b\xe7\x9a\x84\xe7\xa7\x98\xe9\x92\xa5\xe3\x80\x82\\n6.\xe6\xb6\x88\xe6\x81\xaf\xe5\x9b\x9e\xe8\xb0\x83\xe5\x85\xb1\xe7\x94\xa8\xe7\x9a\x84\xe6\x98\xaf\xe5\x90\x8c\xe4\xb8\x80\xe4\xb8\xaa\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xef\xbc\x8c\xe5\xa6\x82\xe4\xbd\x95\xe5\x8c\xba\xe5\x88\x86\xe6\x98\xaf\xe5\x93\xaa\xe4\xb8\xaa\xe5\x95\x86\xe5\xae\xb6\xe7\x9a\x84\xe5\x9b\x9e\xe8\xb0\x83\xe6\xb6\x88\xe6\x81\xaf\xe3\x80\x82\\n\xe7\xad\x94\xef\xbc\x9a\xe7\x94\xb1OMS\xe9\x80\x9a\xe8\xbf\x87\xe9\x97\xa8\xe5\xba\x97ID\xe5\x88\x86\xe5\x8f\x91\xe3\x80\x82\\n\\nOMS\xe7\x9b\xb8\xe5\x85\xb3\xe6\x94\xb9\xe5\x8a\xa8\\n\xe7\x94\xb3\xe8\xaf\xb7\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xbc\x80\xe5\x8f\x91\xe8\x80\x85\xe8\xb4\xa6\xe5\x8f\xb7\xe3\x80\x82\\n\xe6\x8e\x88\xe6\x9d\x83\xe6\xb5\x81\xe7\xa8\x8b\xe5\x8f\x91\xe7\x94\x9f\xe5\x8f\x98\xe5\x8c\x96\xe3\x80\x82\\n\xe6\x8e\x88\xe6\x9d\x83\xe5\x90\x8e\xe7\x9a\x84\xe9\x97\xa8\xe5\xba\x97\xe4\xb8\x8e\xe5\x95\x86\xe5\xae\xb6\xe6\x97\xa0\xe5\x85\xb3\xe8\x81\x94\xe5\x85\xb3\xe7\xb3\xbb\xef\xbc\x8c\xe9\x9c\x80\xe8\xa6\x81OMS\xe5\xa4\x84\xe7\x90\x86\xe5\x95\x86\xe5\xae\xb6\xe4\xb8\x8e\xe9\x97\xa8\xe5\xba\x97\xe7\x9a\x84\xe7\xbb\x91\xe5\xae\x9a\xe5\x85\xb3\xe7\xb3\xbb\xef\xbc\x9b\xe7\xad\x89\xe5\xbe\x85\xe4\xb8\x8e\xe5\xb9\xb3\xe5\x8f\xb0\xe7\xa1\xae\xe8\xae\xa4\xe8\xa7\xa3\xe5\x86\xb3\xe6\x96\xb9\xe6\xa1\x88\xef\xbc\x8c\xe6\x9c\x80\xe5\xb7\xae\xe6\x83\x85\xe5\x86\xb5\xe9\x9c\x80\xe8\xa6\x81\xe4\xba\xba\xe5\xb7\xa5\xe5\x88\xa4\xe6\x96\xad\xe6\x89\x8b\xe5\x8a\xa8\xe7\xbb\x91\xe5\xae\x9a\xef\xbc\x9b\\n\xe6\xaf\x8f\xe4\xb8\xaa\xe6\x8e\xa5\xe5\x8f\xa3\xe9\x83\xbd\xe9\x9c\x80\xe8\xa6\x81\xe8\xb0\x83\xe8\xaf\x95\xe6\x96\xb0\xe6\xa8\xa1\xe5\xbc\x8f\xe4\xb8\x8b\xe7\x9a\x84\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe7\x94\xa8\xe9\x80\xbb\xe8\xbe\x91\xe3\x80\x82\\n\xe5\x9b\x9e\xe8\xb0\x83\xe6\xb6\x88\xe6\x81\xaf\xe5\x88\x86\xe5\x8f\x91\xe3\x80\x82\\n\\n\xe4\xb8\xbb\xe8\xa6\x81\xe6\x98\xaf\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe9\x97\xa8\xe5\xba\x97\xe6\x8e\x88\xe6\x9d\x83\xe8\xa7\x84\xe5\x88\x99\xe6\x94\xb9\xe5\x8f\x98\xe5\xaf\xbc\xe8\x87\xb4\xe5\x90\x8e\xe7\xbb\xad\xe7\x9a\x84\xe6\x94\xb9\xe5\x8a\xa8\xe5\x92\x8c\xe8\x81\x94\xe8\xb0\x83\xef\xbc\x9a\\n1\xe3\x80\x81\xe9\x97\xa8\xe5\xba\x97\xe6\x8e\x88\xe6\x9d\x83\xe9\x80\xbb\xe8\xbe\x91\\n2\xe3\x80\x81\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe6\x8e\x88\xe6\x9d\x83\xe5\x8f\x8a\xe8\xa7\x84\xe5\x88\x99\\n3\xe3\x80\x81\xe4\xb8\x9a\xe5\x8a\xa1\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe8\xaf\x95\xef\xbc\x9a\\n\xe8\xae\xa2\xe5\x8d\x95&\xe5\x94\xae\xe5\x90\x8e\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe8\xaf\x95\\n\xe5\x95\x86\xe5\x93\x81\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe8\xaf\x95\\n\xe9\x97\xa8\xe5\xba\x97\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe8\xaf\x95\\n\xe5\xaf\xb9\xe8\xb4\xa6\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe8\xaf\x95\\n\xe8\xaf\x84\xe4\xbb\xb7\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe6\x95\xb4\\n4\xe3\x80\x81\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe6\x94\xb9\xe9\x80\xa0\\n5\xe3\x80\x81\xe4\xba\xac\xe4\xb8\x9c\xe7\xa7\x92\xe9\x80\x81\xe5\xb9\xb3\xe5\x8f\xb0\xe8\x81\x94\xe8\xb0\x83\\n\\n\xe8\xaf\xa6\xe7\xbb\x86\xe6\x96\xb9\xe6\xa1\x88\xe8\xae\xbe\xe8\xae\xa1\\n\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe5\xbc\x80\xe5\x8f\x91\xe8\x80\x85\xe8\xb4\xa6\xe5\x8f\xb7\xe5\x88\x9b\xe5\xbb\xba\xe6\xb5\xb7\xe5\x85\xb8\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\\n\xe5\xba\x94\xe7\x94\xa8\xe5\x88\x9b\xe5\xbb\xba\\n\xe5\x88\x9b\xe5\xbb\xba\xe6\xb5\xb7\xe5\x85\xb8\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xef\xbc\x8c\xe4\xbd\xbf\xe7\x94\xa8\xe5\x94\xaf\xe4\xb8\x80\xe7\x9a\x84\xe6\xb5\xb7\xe5\x85\xb8\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xef\xbc\x8c\xe5\xaf\xb9\xe6\x8e\xa5\xe6\x89\x80\xe6\x9c\x89\xe5\x95\x86\xe6\x88\xb7\xe6\x8e\x88\xe6\x9d\x83\xe3\x80\x82\\n\\nhttps://drive.weixin.qq.com/s?k=AOcA-wd2AAwAk7Sjl8AKMAQgZ5ALQ \\n\xe5\xba\x94\xe7\x94\xa8\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xe7\xbb\xb4\xe6\x8a\xa4\\n\xe7\xbb\xb4\xe6\x8a\xa4\xe5\xba\x94\xe7\x94\xa8\xe7\x9a\x84\xe7\x9b\xb8\xe5\x85\xb3\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xef\xbc\x9a\\n\xe5\xa6\x82\xef\xbc\x9a\xe6\x8e\x88\xe6\x9d\x83\xe6\x8e\xa5\xe6\x94\xb6\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xef\xbc\x8c\xe8\x87\xaa\xe5\x8a\xa8\xe6\x8e\x88\xe6\x9d\x83\xe5\xbb\xb6\xe6\x9c\x9f\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xef\xbc\x8c\xe8\xae\xa2\xe5\x8d\x95\xe7\x9b\xb8\xe5\x85\xb3\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xe7\xad\x89\xe3\x80\x82\\n\\n\\n\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe5\x88\x9b\xe5\xbb\xba\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xe8\xb4\xa6\xe5\x8f\xb7\\n\xe5\x9c\xa8\xe5\x90\x8e\xe5\x8f\xb0\xe6\x8e\x88\xe6\x9d\x83\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xe8\xb4\xa6\xe5\x8f\xb7\xef\xbc\x8c\xe4\xbd\x9c\xe4\xb8\xba\xe6\xb5\xb7\xe5\x85\xb8\xe7\x9a\x84\xe5\xba\x94\xe7\x94\xa8\xe6\x80\xbb\xe8\xb4\xa6\xe5\x8f\xb7\xef\xbc\x8c\xe6\x89\x80\xe6\x9c\x89\xe4\xb8\x8e\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe7\x9b\xb8\xe5\x85\xb3\xe7\x9a\x84\xe4\xb8\x9a\xe5\x8a\xa1\xe4\xbf\xa1\xe6\x81\xaf\xe9\x83\xbd\xe9\x80\x9a\xe8\xbf\x87\xe8\xaf\xa5\xe5\xba\x94\xe7\x94\xa8\xe8\xb4\xa6\xe5\x8f\xb7\xe8\xbf\x9b\xe8\xa1\x8c\xe5\xaf\xb9\xe6\x8e\xa5\xef\xbc\x9b\\n\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe5\x92\x8cOMS\xe9\x83\xbd\xe5\x9c\xa8\xe5\x90\x8e\xe5\x8f\xb0\xe4\xbf\x9d\xe5\xad\x98\xe8\xb4\xa6\xe5\x8f\xb7\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x8c\xe4\xb8\x8d\xe9\x9c\x80\xe8\xa6\x81\xe5\xb1\x95\xe7\xa4\xba\xe5\x9c\xa8\xe5\x89\x8d\xe7\xab\xaf\xe3\x80\x82\\n\xe5\xba\x94\xe7\x94\xa8\xe7\x9a\x84\xe4\xbd\xbf\xe7\x94\xa8\\n\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe6\xa0\xb9\xe6\x8d\xaeOMS\xe8\xb0\x83\xe7\x94\xa8\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xbc\xa0\xe5\x85\xa5\xe7\x9a\x84\xe5\x8f\x82\xe6\x95\xb0\xe5\x88\xa4\xe6\x96\xad\xe6\xa8\xa1\xe5\xbc\x8f\xe6\x98\xaf\xe5\x90\xa6\xe4\xb8\xba\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe6\xa8\xa1\xe5\xbc\x8f\xef\xbc\x8c\xe5\xb9\xb6\xe6\x9b\xb4\xe5\x8a\xa0\xe5\x95\x86\xe5\xae\xb6ID\xe5\x85\xa5\xe5\x8f\x82\xe8\x8e\xb7\xe5\x8f\x96\xe5\xaf\xb9\xe5\xba\x94\xe7\x9a\x84\xe7\xa7\x98\xe9\x92\xa5\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x8c\xe8\xb0\x83\xe7\x94\xa8\xe5\xb9\xb3\xe5\x8f\xb0\xe6\x8e\xa5\xe5\x8f\xa3\xe3\x80\x82\\n\\n\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe5\x95\x86\xe6\x88\xb7\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe6\xa8\xa1\xe5\xbc\x8f\xe6\x8e\x88\xe6\x9d\x83\\n\xe6\x8e\x88\xe6\x9d\x83\xe6\xb5\x81\xe7\xa8\x8b\\n\\n\\n\xe6\xb3\xa8\xe6\x84\x8f\xef\xbc\x9a\xe5\x95\x86\xe6\x88\xb7\xe6\x8e\x88\xe6\x9d\x83\xe5\x90\x8e\xe8\xb7\xb3\xe8\xbd\xac\xe7\x9a\x84\xe7\x99\xbb\xe5\xbd\x95\xe9\xa1\xb5\xe9\x9d\xa2\xe7\x9a\x84\xe9\x93\xbe\xe6\x8e\xa5\xe9\x95\xbf\xe6\x9c\x9f\xe6\x9c\x89\xe6\x95\x88\xe5\x8f\xaf\xe4\xbb\xa5\xe4\xbd\xbf\xe7\x94\xa8\xe6\xad\xa4\xe9\x93\xbe\xe6\x8e\xa5\xe5\xb5\x8c\xe5\x85\xa5OMS\xe7\x9a\x84\xe6\x8e\x88\xe6\x9d\x83\xe9\xa1\xb5\xe9\x9d\xa2\xef\xbc\x8c\xe4\xbd\x9c\xe4\xb8\xba\xe5\x95\x86\xe5\xae\xb6\xe6\x8e\x88\xe6\x9d\x83\xe7\x9a\x84\xe6\x93\x8d\xe4\xbd\x9c\xe5\x85\xa5\xe5\x8f\xa3\xe3\x80\x82\xe5\x8d\xb3\xe4\xbd\xbf\xe7\x94\xa8\xe5\x9b\xba\xe5\xae\x9a\xe7\x9a\x84URL\xe4\xbe\x9b\xe5\x95\x86\xe6\x88\xb7\xe8\xbf\x9b\xe8\xa1\x8c\xe6\x8e\x88\xe6\x9d\x83\xe3\x80\x82\\n\xe7\xbd\x91\xe5\xba\x97\xe6\x8e\x88\xe6\x9d\x83\xe7\xae\xa1\xe7\x90\x86\xe5\xa2\x9e\xe5\x8a\xa0\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xe6\x8e\x88\xe6\x9d\x83\\n\xe5\x88\x9b\xe5\xbb\xba\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xe6\x8e\x88\xe6\x9d\x83\xe7\xbd\x91\xe5\xba\x97\\n\\n\\n\\n\xe6\xb3\xa8\xe6\x84\x8f\xef\xbc\x9a\\n\xe7\xbd\x91\xe5\xba\x97\xe5\x90\x8d\xe7\xa7\xb0\xe5\x92\x8c\xe5\x95\x86\xe5\xae\xb6ID\xe4\xbf\x9d\xe5\xad\x98\xe6\x97\xb6\xe5\x89\x8d\xe5\x90\x8e\xe5\x8e\xbb\xe7\xa9\xba\xe6\xa0\xbc\xe5\x9b\x9e\xe8\xbd\xa6\\n\xe6\xa0\xa1\xe9\xaa\x8c\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe5\xb9\xb3\xe5\x8f\xb0\xe7\x9a\x84\xe7\x8e\xb0\xe6\x9c\x89\xe6\x8e\x88\xe6\x9d\x83\xe7\xbd\x91\xe5\xba\x97\xe4\xb8\xad\xef\xbc\x8c\xe6\x98\xaf\xe5\x90\xa6\xe5\xb7\xb2\xe6\x9c\x89\xe8\xaf\xa5\xe5\x95\x86\xe5\xae\xb6ID\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe6\x9c\x89\xe5\x88\x99\xe6\x8f\x90\xe7\xa4\xba\xef\xbc\x9a\xe3\x80\x90\xe8\xaf\xa5\xe5\x95\x86\xe5\xae\xb6ID\xe5\xb7\xb2\xe6\x8e\x88\xe6\x9d\x83\xe8\x87\xb3OMS\xef\xbc\x88\xe5\x95\x86\xe5\xae\xb6\xe5\x90\x8d\xe7\xa7\xb0 + \xe5\x95\x86\xe5\xae\xb6\xe7\xbd\x91\xe5\xba\x97\xe5\x90\x8d\xe7\xa7\xb0\xef\xbc\x89\xef\xbc\x8c\xe4\xb8\x8d\xe5\x85\x81\xe8\xae\xb8\xe5\x86\x8d\xe6\xac\xa1\xe6\x8e\x88\xe6\x9d\x83\xef\xbc\x8c\xe8\xaf\xb7\xe7\xa1\xae\xe8\xae\xa4\xe5\x95\x86\xe5\xae\xb6ID\xe9\x87\x8d\xe6\x96\xb0\xe6\x8e\x88\xe6\x9d\x83\xef\xbc\x81\xe3\x80\x91\\n\xe6\xa0\xa1\xe9\xaa\x8c\xe9\x80\x9a\xe8\xbf\x87\xef\xbc\x8c\xe5\x88\x99\xe5\x85\x81\xe8\xae\xb8\xe4\xbf\x9d\xe5\xad\x98\xe3\x80\x82\\n\xe6\xa0\xa1\xe9\xaa\x8c\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe5\xb9\xb3\xe5\x8f\xb0\xe7\x9a\x84\xe7\x8e\xb0\xe6\x9c\x89\xe6\x8e\x88\xe6\x9d\x83\xe7\xbd\x91\xe5\xba\x97\xe4\xb8\xad\xef\xbc\x8c\xe6\x98\xaf\xe5\x90\xa6\xe5\xb7\xb2\xe6\x9c\x89\xe7\xbd\x91\xe5\xba\x97\xe5\x90\x8d\xe7\xa7\xb0\xef\xbc\x8c\xe5\xa6\x82\xe6\x9e\x9c\xe6\x9c\x89\xe5\x88\x99\xe6\x8f\x90\xe7\xa4\xba\xef\xbc\x9a\xe3\x80\x90\xe7\xbd\x91\xe5\xba\x97\xe5\x90\x8d\xe7\xa7\xb0\xe5\xb7\xb2\xe8\xa2\xab\xe4\xbd\xbf\xe7\x94\xa8\xef\xbc\x8c\xe8\xaf\xb7\xe4\xbf\xae\xe6\x94\xb9\xe7\xbd\x91\xe5\xba\x97\xe5\x90\x8d\xe7\xa7\xb0\xef\xbc\x81\xe3\x80\x91\\n\xe6\xa0\xa1\xe9\xaa\x8c\xe9\x80\x9a\xe8\xbf\x87\xef\xbc\x8c\xe5\x88\x99\xe5\x85\x81\xe8\xae\xb8\xe4\xbf\x9d\xe5\xad\x98\xe3\x80\x82\\n\xe6\x8e\x88\xe6\x9d\x83\xe7\xb1\xbb\xe5\x9e\x8b\xe6\x96\x87\xe6\xa1\x88\xe8\xaf\xb4\xe6\x98\x8e\xef\xbc\x9a\\n\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xe6\x8e\x88\xe6\x9d\x83\xef\xbc\x9a\xe8\xa1\xa8\xe7\xa4\xba\xe5\x95\x86\xe5\xae\xb6\xe6\x8e\x88\xe6\x9d\x83\xe7\xbb\x99\xe6\xb5\xb7\xe5\x85\xb8\xe7\x9a\x84\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xe7\xbb\x9f\xe4\xb8\x80\xe7\xae\xa1\xe7\x90\x86\xef\xbc\x8c\xe5\x95\x86\xe5\xae\xb6\xe4\xb8\x8d\xe9\x9c\x80\xe8\xa6\x81\xe7\x94\xb3\xe8\xaf\xb7\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe7\x9a\x84\xe5\xbc\x80\xe5\x8f\x91\xe8\x80\x85\xe8\xb4\xa6\xe5\x8f\xb7\xef\xbc\x9b\\n\xe5\x95\x86\xe5\xae\xb6\xe5\xba\x94\xe7\x94\xa8\xe6\x8e\x88\xe6\x9d\x83\xef\xbc\x9a\xe8\xa1\xa8\xe7\xa4\xba\xe5\x95\x86\xe5\xae\xb6\xe9\x9c\x80\xe8\xa6\x81\xe5\x85\x88\xe7\x94\xb3\xe8\xaf\xb7\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe7\x9a\x84\xe5\xbc\x80\xe5\x8f\x91\xe8\x80\x85\xe8\xb4\xa6\xe5\x8f\xb7\xef\xbc\x8c\xe7\x94\xb3\xe8\xaf\xb7\xe6\x88\x90\xe5\x8a\x9f\xe5\x90\x8e\xef\xbc\x8c\xe5\x86\x8d\xe5\xb0\x86\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe7\x9a\x84\xe5\x95\x86\xe5\xae\xb6\xe5\xba\x94\xe7\x94\xa8\xe6\x8e\x88\xe6\x9d\x83\xe7\xbb\x99OMS\xe3\x80\x82\\n\\n\xe7\x82\xb9\xe5\x87\xbb\xe3\x80\x90\xe4\xbf\x9d\xe5\xad\x98\xe5\xb9\xb6\xe6\x8e\x88\xe6\x9d\x83\xe3\x80\x91\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x9a\\n1.\xe4\xba\x8c\xe6\xac\xa1\xe6\x8f\x90\xe9\x86\x92\xe7\xa1\xae\xe8\xae\xa4\xef\xbc\x9a\xe8\xaf\xb7\xe7\xa1\xae\xe4\xbf\x9d\xe5\x95\x86\xe5\xae\xb6ID\xe5\xa1\xab\xe5\x86\x99\xe5\x87\x86\xe7\xa1\xae\xef\xbc\x8c\xe4\xbf\x9d\xe5\xad\x98\xe5\x90\x8e\xef\xbc\x8c\xe5\x95\x86\xe5\xae\xb6ID\xe4\xb8\x8d\xe5\x8f\xaf\xe4\xbf\xae\xe6\x94\xb9\xe3\x80\x82\xe7\x82\xb9\xe5\x87\xbb\xe7\xa1\xae\xe8\xae\xa4\xe5\x90\x8e\xef\xbc\x8c\xe6\x89\xa7\xe8\xa1\x8c\xe5\xa6\x82\xe4\xb8\x8b\xe5\x8a\xa8\xe4\xbd\x9c\xe3\x80\x82\\n2.\xe8\x87\xaa\xe5\x8a\xa8\xe8\xb0\x83\xe7\x94\xa8\xe5\x9b\xba\xe5\xae\x9a\xe7\x9a\x84\xe5\xba\x94\xe7\x94\xa8\xe6\x8e\x88\xe6\x9d\x83URL\xef\xbc\x8c\xe5\x95\x86\xe5\xae\xb6\xe7\x99\xbb\xe9\x99\x86\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe5\xbe\x97\xe5\x95\x86\xe5\xae\xb6\xe8\xb4\xa6\xe5\x8f\xb7\xe5\xae\x8c\xe6\x88\x90\xe6\x8e\x88\xe6\x9d\x83\xe3\x80\x82\\n\xe5\x9b\xba\xe5\xae\x9a\xe6\x8e\x88\xe6\x9d\x83\xe9\x93\xbe\xe6\x8e\xa5\xe7\x94\xb1\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe6\x8f\x90\xe4\xbe\x9b\xe3\x80\x82\\n\\n3.\xe5\x90\x8c\xe6\x97\xb6\xef\xbc\x8cOMS\xe5\x88\x9b\xe5\xbb\xba\xe7\x8a\xb6\xe6\x80\x81\xe4\xb8\xba\xe5\xbc\x82\xe5\xb8\xb8\xe7\x9a\x84\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe7\xbd\x91\xe5\xba\x97\xe4\xbf\xa1\xe6\x81\xaf\xe3\x80\x82\\n\xe5\xbc\x82\xe5\xb8\xb8\xe7\x8a\xb6\xe6\x80\x81\xe6\x96\x87\xe6\xa1\x88\xef\xbc\x9a\xe6\x96\xb0\xe5\x88\x9b\xe5\xbb\xba\xe7\x9a\x84\xe7\xbd\x91\xe5\xba\x97\xe7\x8a\xb6\xe6\x80\x81\xe4\xb8\xba\xe5\xbc\x82\xe5\xb8\xb8\xef\xbc\x8c\xe9\x9c\x80\xe8\xa6\x81\xe7\xad\x89\xe5\xbe\x852\xe5\x88\x86\xe9\x92\x9f\xe5\xb7\xa6\xe5\x8f\xb3\xe4\xbc\x9a\xe8\x87\xaa\xe5\x8a\xa8\xe6\x9b\xb4\xe6\x96\xb0\xe4\xb8\xba\xe6\xad\xa3\xe5\xb8\xb8\xe3\x80\x82\xe5\xa6\x82\xe6\x9e\x9c\xe9\x95\xbf\xe6\x97\xb6\xe9\x97\xb4\xe5\xa4\x84\xe4\xba\x8e\xe5\xbc\x82\xe5\xb8\xb8\xe7\x8a\xb6\xe6\x80\x81\xef\xbc\x8c\xe8\xaf\xb7\xe6\xa3\x80\xe6\x9f\xa5\xe5\x95\x86\xe5\xae\xb6ID\xe6\x98\xaf\xe5\x90\xa6\xe6\xad\xa3\xe7\xa1\xae\xef\xbc\x8c\xe8\x8b\xa5\xe5\x95\x86\xe5\xae\xb6ID\xe6\xad\xa3\xe7\xa1\xae\xef\xbc\x8c\xe8\xbf\x9b\xe5\x85\xa5\xe7\xbc\x96\xe8\xbe\x91\xe9\xa1\xb5\xe9\x9d\xa2\xe9\x87\x8d\xe6\x96\xb0\xe6\x8e\x88\xe6\x9d\x83\xef\xbc\x8c\xe8\x8b\xa5\xe5\x95\x86\xe5\xae\xb6ID\xe9\x94\x99\xe8\xaf\xaf\xef\xbc\x8c\xe8\xaf\xb7\xe5\x88\xa0\xe9\x99\xa4\xe5\xbc\x82\xe5\xb8\xb8\xe7\xbd\x91\xe5\xba\x97\xef\xbc\x8c\xe9\x87\x8d\xe6\x96\xb0\xe6\xb7\xbb\xe5\x8a\xa0\xe6\x96\xb0\xe7\x9a\x84\xe7\xbd\x91\xe5\xba\x97\xe3\x80\x82\\n\\n\\n\xe7\xbd\x91\xe5\xba\x97\xe7\xae\xa1\xe7\x90\x86\xe5\x88\x97\xe8\xa1\xa8\\n\xe5\x88\x97\xe8\xa1\xa8\xe6\x93\x8d\xe4\xbd\x9c\xe6\x8c\x89\xe9\x92\xae\\n\xe5\xa6\x82\xe6\x9e\x9c\xe6\x8e\x88\xe6\x9d\x83\xe6\xa8\xa1\xe5\xbc\x8f\xe6\x98\xaf\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xe6\x8e\x88\xe6\x9d\x83\xef\xbc\x8c\xe5\x88\x99\xe9\x9a\x90\xe8\x97\x8f\xe3\x80\x90\xe6\x9f\xa5\xe7\x9c\x8b\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xe3\x80\x91\xe6\x8c\x89\xe9\x92\xae\xe3\x80\x82\\n\\n\xe5\x90\x8c\xe6\xad\xa5\xe9\x97\xa8\xe5\xba\x97\xe4\xb8\x8e\xe5\x88\xa0\xe9\x99\xa4\xe6\x93\x8d\xe4\xbd\x9c\xe6\x8c\x89\xe9\x92\xae\xe4\xb8\x8e\xe5\x8e\x9f\xe6\x9c\x89\xe9\x80\xbb\xe8\xbe\x91\xe4\xb8\x80\xe8\x87\xb4\xe3\x80\x82\\n\\n\xe7\xbc\x96\xe8\xbe\x91\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\xba\x94\xe7\x94\xa8\xe6\x8e\x88\xe6\x9d\x83\xe7\xbd\x91\xe5\xba\x97\\n\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe6\x8e\x88\xe6\x9d\x83\xe6\xa8\xa1\xe5\xbc\x8f\xe7\xbc\x96\xe8\xbe\x91\xe7\xbd\x91\xe5\xba\x97\xe4\xbf\xa1\xe6\x81\xaf\\n\\n\xe5\x8f\xaa\xe5\x85\x81\xe8\xae\xb8\xe7\xbc\x96\xe8\xbe\x91\xe7\xbd\x91\xe5\xba\x97\xe5\x90\x8d\xe7\xa7\xb0\xe3\x80\x82\\n\xe6\x8e\x88\xe6\x9d\x83\xe7\xb1\xbb\xe5\x9e\x8b\xe3\x80\x81\xe5\x95\x86\xe5\xae\xb6ID\xe4\xb8\x8d\xe5\x8f\xaf\xe4\xbf\xae\xe6\x94\xb9\xef\xbc\x9b\\n\xe7\x82\xb9\xe5\x87\xbb\xe3\x80\x90\xe9\x87\x8d\xe6\x96\xb0\xe6\x8e\x88\xe6\x9d\x83\xe3\x80\x91\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x8c\xe8\x87\xaa\xe5\x8a\xa8\xe8\xb0\x83\xe7\x94\xa8\xe5\x9b\xba\xe5\xae\x9a\xe7\x9a\x84\xe5\xba\x94\xe7\x94\xa8\xe6\x8e\x88\xe6\x9d\x83URL\xef\xbc\x8c\xe5\x95\x86\xe5\xae\xb6\xe7\x99\xbb\xe9\x99\x86\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe5\xbe\x97\xe5\x95\x86\xe5\xae\xb6\xe8\xb4\xa6\xe5\x8f\xb7\xe5\xae\x8c\xe6\x88\x90\xe6\x8e\x88\xe6\x9d\x83\xe3\x80\x82\\n\xe7\x82\xb9\xe5\x87\xbb\xe4\xbf\x9d\xe5\xad\x98\xe6\x8c\x89\xe9\x92\xae\xef\xbc\x8c\xe4\xbf\x9d\xe5\xad\x98\xe6\x9b\xb4\xe6\x96\xb0\xe7\x9a\x84\xe7\xbd\x91\xe5\xba\x97\xe4\xbf\xa1\xe6\x81\xaf\xe3\x80\x82\xef\xbc\x88\xe4\xbf\x9d\xe5\xad\x98\xe5\x89\x8d\xe7\x9a\x84\xe6\xa0\xa1\xe9\xaa\x8c\xe9\x80\xbb\xe8\xbe\x91\xe4\xb8\x8e\xe6\x96\xb0\xe5\xa2\x9e\xe4\xb8\x80\xe8\x87\xb4\xe3\x80\x82\xef\xbc\x89\\n\\n\\n\xe6\x8e\xa5\xe6\x94\xb6\xe5\x95\x86\xe6\x88\xb7\xe6\x8e\x88\xe6\x9d\x83\xe6\xb6\x88\xe6\x81\xaf\\n\xe5\x95\x86\xe6\x88\xb7\xe9\x80\x9a\xe8\xbf\x87\xe6\x8e\x88\xe6\x9d\x83URL\xe9\x93\xbe\xe6\x8e\xa5\xef\xbc\x8c\xe7\xa1\xae\xe8\xae\xa4\xe6\x8e\x88\xe6\x9d\x83\xe5\x90\x8e\xef\xbc\x8c\xe9\x80\x9a\xe8\xbf\x87token\xe6\x8e\xa5\xe6\x94\xb6\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xef\xbc\x8c\xe6\x8e\xa5\xe6\x8e\x88\xe6\x9d\x83\xe4\xbf\xa1\xe6\x81\xaf\xe6\x8e\xa8\xe9\x80\x81\xe7\xbb\x99\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe3\x80\x82\\n\\n\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\\n\xe6\x8e\xa5\xe6\x94\xb6\xe5\xb9\xb3\xe5\x8f\xb0\xe6\x8e\xa8\xe9\x80\x81\xe7\x9a\x84\xe5\x95\x86\xe6\x88\xb7\xe6\x8e\x88\xe6\x9d\x83\xe6\xb6\x88\xe6\x81\xaf\xef\xbc\x9a\\n\xef\xbc\x881\xef\xbc\x89\xe8\x8b\xa5\xe5\x95\x86\xe6\x88\xb7ID\xe4\xb8\x8d\xe5\xad\x98\xe5\x9c\xa8\xef\xbc\x8c\xe5\x88\x99\xe5\x88\x9b\xe5\xbb\xba\xe6\x96\xb0\xe5\x95\x86\xe6\x88\xb7ID\xe7\x9a\x84\xe6\x8e\x88\xe6\x9d\x83\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x8c\xe5\xb9\xb6\xe4\xbf\x9d\xe5\xad\x98\xe5\xaf\xb9\xe5\xba\x94\xe7\x9a\x84\xe5\x95\x86\xe6\x88\xb7ID\xe5\x8f\x8a\xe6\x8e\x88\xe6\x9d\x83\xe7\xa7\x98\xe9\x92\xa5\xe4\xbf\xa1\xe6\x81\xaf\xe3\x80\x82\\n\xef\xbc\x882\xef\xbc\x89\xe8\x8b\xa5\xe5\x95\x86\xe6\x88\xb7ID\xe5\xb7\xb2\xe5\xad\x98\xe5\x9c\xa8\xef\xbc\x8c\xe5\x88\x99\xe6\x9b\xb4\xe6\x96\xb0\xe5\x95\x86\xe6\x88\xb7ID\xe7\x9a\x84\xe6\x8e\x88\xe6\x9d\x83\xe7\xa7\x98\xe9\x92\xa5\xe4\xbf\xa1\xe6\x81\xaf\xe3\x80\x82\\n\xe5\x95\x86\xe6\x88\xb7\xe7\xa7\x98\xe9\x92\xa5\xe5\xa6\x82\xe4\xbd\x95\xe8\x87\xaa\xe5\x8a\xa8\xe6\x9b\xb4\xe6\x96\xb0\xef\xbc\x9f\xef\xbc\x9f\xef\xbc\x9f\xef\xbc\x9f\\n\xe6\x9b\xb4\xe6\x96\xb0token\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x9atoken\xe5\xbf\xab\xe5\x88\xb0\xe6\x9c\x9f\xe6\x97\xb6\xef\xbc\x8c\xe7\xb3\xbb\xe7\xbb\x9f\xe8\x83\xbd\xe8\x87\xaa\xe5\x8a\xa8\xe8\xb0\x83\xe7\x94\xa8\xe5\xb9\xb3\xe5\x8f\xb0\xe6\x8e\xa5\xe5\x8f\xa3\xef\xbc\x8c\xe8\x87\xaa\xe5\x8a\xa8\xe6\x9b\xb4\xe6\x96\xb0token\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x9f\xef\xbc\x9f\xef\xbc\x9f\\nOMS\\n\xe6\x8e\xa5\xe6\x94\xb6\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe6\x8e\xa8\xe9\x80\x81\xe7\x9a\x84\xe5\x95\x86\xe6\x88\xb7\xe6\x8e\x88\xe6\x9d\x83\xe6\xb6\x88\xe6\x81\xaf\xef\xbc\x8c\xe9\x80\x9a\xe8\xbf\x87\xe5\x95\x86\xe5\xae\xb6ID\xe8\xaf\x86\xe5\x88\xab\xe7\xbd\x91\xe5\xba\x97\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x9a\\n\xef\xbc\x881\xef\xbc\x89\xe8\x8b\xa5\xe7\xbd\x91\xe5\xba\x97\xe7\x8a\xb6\xe6\x80\x81\xe4\xb8\xba\xe5\xbc\x82\xe5\xb8\xb8\xef\xbc\x8c\xe5\x88\x99\xe6\x9b\xb4\xe6\x96\xb0\xe7\xbd\x91\xe5\xba\x97\xe7\x8a\xb6\xe6\x80\x81\xe4\xb8\xba\xe3\x80\x90\xe6\xad\xa3\xe5\xb8\xb8\xe3\x80\x91\xef\xbc\x8c\xe5\x90\x8c\xe6\x97\xb6\xe8\x87\xaa\xe5\x8a\xa8\xe8\xb0\x83\xe7\x94\xa8\xe9\x97\xa8\xe5\xba\x97\xe6\x8e\xa5\xe5\x8f\xa3\xef\xbc\x8c\xe6\x8b\x89\xe5\x8f\x96\xe7\xbd\x91\xe5\xba\x97\xe7\x9a\x84\xe9\x97\xa8\xe5\xba\x97\xe4\xbf\xa1\xe6\x81\xaf\xe3\x80\x82\\n\xef\xbc\x882\xef\xbc\x89\xe8\x8b\xa5\xe7\xbd\x91\xe5\xba\x97\xe7\x8a\xb6\xe6\x80\x81\xe4\xb8\xba\xe6\xad\xa3\xe5\xb8\xb8\xef\xbc\x8c\xe5\x88\x99\xe4\xb8\x8d\xe5\x81\x9a\xe4\xbb\xbb\xe4\xbd\x95\xe5\xa4\x84\xe7\x90\x86\xe3\x80\x82\\n\\n\\nOMS&\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe7\x9b\xb8\xe5\x85\xb3\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe6\x95\xb4\\n\xe6\xb6\x88\xe6\x81\xaf\xe5\xa4\x84\xe7\x90\x86\\n\xe8\xae\xa2\xe5\x8d\x95\xe7\x9b\xb8\xe5\x85\xb3\xe6\xb6\x88\xe6\x81\xaf\xe5\xa4\x84\xe7\x90\x86\\n\\nOMS\xe6\x8e\xa5\xe6\x94\xb6\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe6\x8e\xa8\xe9\x80\x81\xe7\x9a\x84\xe6\xb6\x88\xe6\x81\xaf\xef\xbc\x9a\\n1.\xe4\xbe\x9d\xe6\x8d\xae\xe6\xb6\x88\xe6\x81\xaf\xe5\x88\x9b\xe5\x8d\x95\xe6\x97\xb6\xef\xbc\x8c\xe9\x9c\x80\xe8\xa6\x81\xe9\x80\x9a\xe8\xbf\x87\xe7\xba\xbf\xe4\xb8\x8a\xe9\x97\xa8\xe5\xba\x97\xe7\xbc\x96\xe7\xa0\x81\xe8\xaf\x86\xe5\x88\xab\xe8\xaf\xa5\xe5\x8d\x95\xe6\x8d\xae\xe6\x98\xaf\xe5\xb1\x9e\xe4\xba\x8e\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe7\x9a\x84\xe5\x93\xaa\xe4\xb8\xaa\xe5\x95\x86\xe6\x88\xb7\xe7\x9a\x84\xe5\x93\xaa\xe4\xb8\xaa\xe7\xbd\x91\xe5\xba\x97\xef\xbc\x8c\xe5\xb9\xb6\xe5\xb0\x86\xe5\xaf\xb9\xe5\xba\x94\xe7\x9a\x84\xe7\xbd\x91\xe5\xba\x97\xe4\xbf\xa1\xe6\x81\xaf\xe5\x92\x8c\xe5\x95\x86\xe6\x88\xb7\xe4\xbf\xa1\xe6\x81\xaf\xe4\xbf\x9d\xe5\xad\x98\xe5\x88\xb0\xe5\x8d\x95\xe6\x8d\xae\xe4\xb8\xad\xef\xbc\x9b\xef\xbc\x88\xe5\x8c\x85\xe6\x8b\xac\xe6\xad\xa3\xe5\x90\x91\xe8\xae\xa2\xe5\x8d\x95\xe5\x92\x8c\xe9\x80\x86\xe8\xa2\xad\xe9\x80\x80\xe6\xac\xbe\xe5\x8d\x95\xe7\xad\x89\xef\xbc\x89\\n2.\xe6\x8e\xa5\xe6\x94\xb6\xe5\x8d\x95\xe6\x8d\xae\xe5\x8f\x98\xe6\x9b\xb4\xe6\xb6\x88\xe6\x81\xaf\xe6\x97\xb6\xef\xbc\x8c\xe5\x90\x8c\xe6\xa0\xb7\xe9\x9c\x80\xe8\xa6\x81\xe6\xa0\xb9\xe6\x8d\xae\xe6\xb6\x88\xe6\x81\xaf\xe4\xb8\xad\xe7\x9a\x84\xe7\xba\xbf\xe4\xb8\x8a\xe9\x97\xa8\xe5\xba\x97\xe7\xbc\x96\xe7\xa0\x81\xe8\xaf\x86\xe5\x88\xab\xe8\xaf\xa5\xe5\x8d\x95\xe6\x8d\xae\xe6\x98\xaf\xe5\xb1\x9e\xe4\xba\x8e\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe7\x9a\x84\xe5\x93\xaa\xe4\xb8\xaa\xe5\x95\x86\xe6\x88\xb7\xe7\x9a\x84\xe5\x93\xaa\xe4\xb8\xaa\xe7\xbd\x91\xe5\xba\x97\xe3\x80\x82\\n\\n\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe7\x94\xa8\\n\xe8\xb0\x83\xe7\x94\xa8\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe6\x8e\xa5\xe5\x8f\xa3\xe9\x9c\x80\xe8\xa6\x81\xe9\x87\x8d\xe6\x96\xb0\xe8\xbd\xac\xe5\x8c\x96\xe4\xb8\xba\xe6\x80\xbb\xe8\xb4\xa6\xe5\x8f\xb7\xe7\x9a\x84\xe5\x85\xa5\xe5\x8f\x82\xe6\x95\xb0\xe6\x8d\xae\xef\xbc\x8c\xe5\xa2\x9e\xe5\x8a\xa0\xe5\x95\x86\xe6\x88\xb7ID\xe5\x85\xa5\xe5\x8f\x82\xef\xbc\x9b\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe9\x80\x9a\xe8\xbf\x87clientid\xe5\x88\xa4\xe6\x96\xad\xe8\xb0\x83\xe7\x94\xa8\xe5\x93\xaa\xe4\xb8\xaa\xe6\xa8\xa1\xe5\xbc\x8f\xe7\x9a\x84\xe6\x8e\xa5\xe5\x8f\xa3\xef\xbc\x9b\\n\xe7\x9b\xb8\xe5\x85\xb3\xe6\x8e\xa5\xe5\x8f\xa3\xe8\xb0\x83\xe7\x94\xa8\xe6\xa8\xa1\xe5\x9d\x97\xe8\xb0\x83\xe6\x95\xb4\\n\xe5\xba\x97\xe9\x93\xba\xe7\xae\xa1\xe7\x90\x86\\n\xe5\x95\x86\xe5\x93\x81\xe7\xae\xa1\xe7\x90\x86\\n\xe8\xae\xa2\xe5\x8d\x95\xe7\xae\xa1\xe7\x90\x86\\n\xe5\xb9\xb3\xe5\x8f\xb0\xe5\xaf\xb9\xe8\xb4\xa6\\n\xe8\xaf\x84\xe4\xbb\xb7\xe6\x8e\xa5\xe5\x8f\xa3\\n\xe4\xba\xac\xe4\xb8\x9c\xe7\xa7\x92\xe9\x80\x81O2O\xe8\xbd\xacB2C\xe4\xb8\x9a\xe5\x8a\xa1\xe6\x8e\xa5\xe5\x8f\xa3\xef\xbc\x9a\xe8\xae\xa2\xe5\x8d\x95\xe3\x80\x81\xe5\x8f\x91\xe8\xb4\xa7\xe3\x80\x81\xe9\x80\x80\xe6\xac\xbe\xe5\xae\xa1\xe6\xa0\xb8\xe7\xad\x89\\n\xe6\xb3\xa8\xe6\x84\x8f\xef\xbc\x9a\xe6\x98\xaf\xe6\x89\x80\xe6\x9c\x89\xe7\x9a\x84\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe6\x8e\xa5\xe5\x8f\xa3\xe9\x83\xbd\xe9\x9c\x80\xe8\xa6\x81\xe8\xb0\x83\xe6\x95\xb4\xef\xbc\x8c\xe4\xb8\x8d\xe8\x83\xbd\xe9\x81\x97\xe6\xbc\x8f\xe3\x80\x82\\n\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe7\x9b\xb8\xe5\x85\xb3\xe6\x8e\xa5\xe5\x8f\xa3\xe9\x93\xbe\xe6\x8e\xa5\xef\xbc\x9ahttps://medplatform.hydee.cn/web/#/4/109 \\n\xe5\xbd\xb1\xe5\x93\x8d\xe8\x8c\x83\xe5\x9b\xb4\\n1.\xe5\x8c\x85\xe6\x8b\xacOMS\xe7\x9a\x84\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6O2O\xe3\x80\x81B2C\xe3\x80\x81\xe5\xba\x97\xe9\x93\xba\xe3\x80\x81\xe8\xae\xa2\xe5\x8d\x95\xe3\x80\x81\xe5\x95\x86\xe5\x93\x81\xe3\x80\x81\xe5\xaf\xb9\xe8\xb4\xa6\xe3\x80\x81\xe8\xaf\x84\xe4\xbb\xb7\xe7\xad\x89\xe4\xb8\x9a\xe5\x8a\xa1\xe3\x80\x82\\n2.\xe5\x85\xbc\xe5\xae\xb9\xe5\x95\x86\xe5\xae\xb6\xe5\x92\x8c\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe4\xb8\xa4\xe7\xa7\x8d\xe6\xa8\xa1\xe5\xbc\x8f\xe3\x80\x82\\n","requirementAnalyzePrd":"## 1. \xe5\x8a\x9f\xe8\x83\xbd\xe9\x9c\x80\xe6\xb1\x82\xe5\x88\x86\xe6\x9e\x90\\n\\n### 1.1 \xe6\xa0\xb8\xe5\xbf\x83\xe4\xb8\x9a\xe5\x8a\xa1\xe6\xb5\x81\xe7\xa8\x8b\\n- **\xe4\xb8\xbb\xe6\xb5\x81\xe7\xa8\x8b**\xef\xbc\x9a\xe5\x95\x86\xe5\xae\xb6\xe8\xae\xbf\xe9\x97\xae\xe5\x9b\xba\xe5\xae\x9aURL\xe8\xbf\x9b\xe8\xa1\x8c\xe6\x8e\x88\xe6\x9d\x83 -> \xe5\x95\x86\xe5\xae\xb6\xe7\x99\xbb\xe5\xbd\x95\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe8\xb4\xa6\xe5\x8f\xb7\xe7\xa1\xae\xe8\xae\xa4\xe6\x8e\x88\xe6\x9d\x83 -> \xe6\x8e\x88\xe6\x9d\x83\xe4\xbf\xa1\xe6\x81\xaf\xe9\x80\x9a\xe8\xbf\x87\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xe5\x8f\x91\xe9\x80\x81\xe7\xbb\x99\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0 -> OMS\xe6\xa0\xb9\xe6\x8d\xae\xe5\x95\x86\xe5\xae\xb6ID\xe6\x9b\xb4\xe6\x96\xb0\xe7\x8a\xb6\xe6\x80\x81\xe5\xb9\xb6\xe6\x8b\x89\xe5\x8f\x96\xe9\x97\xa8\xe5\xba\x97\xe4\xbf\xa1\xe6\x81\xaf\xe3\x80\x82\\n- **\xe5\x88\x86\xe6\x94\xaf\xe6\xb5\x81\xe7\xa8\x8b**\xef\xbc\x9a\xe5\x95\x86\xe5\xae\xb6\xe6\x8e\x88\xe6\x9d\x83\xe5\xa4\xb1\xe8\xb4\xa5\xef\xbc\x88\xe4\xbe\x8b\xe5\xa6\x82\xef\xbc\x8c\xe8\xbe\x93\xe5\x85\xa5\xe9\x94\x99\xe8\xaf\xaf\xe7\x9a\x84\xe5\x95\x86\xe5\xae\xb6ID\xef\xbc\x89-> \xe9\x9c\x80\xe8\xa6\x81\xe9\x87\x8d\xe6\x96\xb0\xe6\x8e\x88\xe6\x9d\x83\xef\xbc\x9b\xe5\x95\x86\xe5\xae\xb6ID\xe5\xb7\xb2\xe5\xad\x98\xe5\x9c\xa8\xe6\x88\x96\xe7\xbd\x91\xe5\xba\x97\xe5\x90\x8d\xe7\xa7\xb0\xe9\x87\x8d\xe5\xa4\x8d -> \xe6\x8f\x90\xe7\xa4\xba\xe9\x94\x99\xe8\xaf\xaf\xef\xbc\x8c\xe5\xb9\xb6\xe8\xa6\x81\xe6\xb1\x82\xe4\xbf\xae\xe6\x94\xb9\xe5\x90\x8e\xe9\x87\x8d\xe8\xaf\x95\xe3\x80\x82\\n- **\xe6\xb5\x81\xe7\xa8\x8b\xe8\x8a\x82\xe7\x82\xb9**\xef\xbc\x9a\\n  - \xe8\xbe\x93\xe5\x85\xa5\xe8\x8a\x82\xe7\x82\xb9\xef\xbc\x9a\xe5\x95\x86\xe5\xae\xb6ID\xe3\x80\x81\xe7\xbd\x91\xe5\xba\x97\xe5\x90\x8d\xe7\xa7\xb0\\n  - \xe5\xa4\x84\xe7\x90\x86\xe8\x8a\x82\xe7\x82\xb9\xef\xbc\x9a\xe6\x8e\x88\xe6\x9d\x83\xe7\xa1\xae\xe8\xae\xa4\xe3\x80\x81\xe7\xa7\x98\xe9\x92\xa5\xe5\x92\x8c\xe6\x8e\x88\xe6\x9d\x83\xe4\xbf\xa1\xe6\x81\xaf\xe4\xbf\x9d\xe5\xad\x98\\n  - \xe8\xbe\x93\xe5\x87\xba\xe8\x8a\x82\xe7\x82\xb9\xef\xbc\x9a\xe6\x8e\x88\xe6\x9d\x83\xe6\x88\x90\xe5\x8a\x9f/\xe5\xa4\xb1\xe8\xb4\xa5\xe6\xb6\x88\xe6\x81\xaf\\n  - \xe5\xad\x98\xe5\x82\xa8\xe8\x8a\x82\xe7\x82\xb9\xef\xbc\x9aOMS\xe4\xb8\xad\xe7\x9a\x84\xe5\x95\x86\xe5\xae\xb6\xe4\xbf\xa1\xe6\x81\xaf\xe5\x8f\x8a\xe6\x8e\x88\xe6\x9d\x83\xe7\x8a\xb6\xe6\x80\x81\\n\\n### 1.2 \xe5\x8a\x9f\xe8\x83\xbd\xe6\xa8\xa1\xe5\x9d\x97\xe6\x8b\x86\xe8\xa7\xa3\\n- **\xe6\xa0\xb8\xe5\xbf\x83\xe6\xa8\xa1\xe5\x9d\x97**\xef\xbc\x9a\xe6\x8e\x88\xe6\x9d\x83\xe7\xae\xa1\xe7\x90\x86\xe3\x80\x81\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe3\x80\x81OMS\\n- **\xe8\xbe\x93\xe5\x85\xa5\xe8\xbe\x93\xe5\x87\xba**\xef\xbc\x9a\xe6\x8e\x88\xe6\x9d\x83\xe7\xae\xa1\xe7\x90\x86\xe6\xa8\xa1\xe5\x9d\x97\xe6\x8e\xa5\xe6\x94\xb6\xe5\x95\x86\xe5\xae\xb6ID\xe4\xb8\x8e\xe7\xbd\x91\xe5\xba\x97\xe5\x90\x8d\xe7\xa7\xb0\xe4\xbd\x9c\xe4\xb8\xba\xe8\xbe\x93\xe5\x85\xa5\xef\xbc\x8c\xe8\xbe\x93\xe5\x87\xba\xe4\xb8\xba\xe6\x8e\x88\xe6\x9d\x83\xe7\xbb\x93\xe6\x9e\x9c\xef\xbc\x9b\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe6\x8e\xa5\xe6\x94\xb6\xe6\x8e\x88\xe6\x9d\x83\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x8c\xe8\xbe\x93\xe5\x87\xba\xe4\xb8\xba\xe6\x9b\xb4\xe6\x96\xb0\xe5\x90\x8e\xe7\x9a\x84\xe7\xa7\x98\xe9\x92\xa5\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x9bOMS\xe6\x8e\xa5\xe6\x94\xb6\xe7\xa7\x98\xe9\x92\xa5\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x8c\xe8\xbe\x93\xe5\x87\xba\xe4\xb8\xba\xe6\x9b\xb4\xe6\x96\xb0\xe5\x90\x8e\xe7\x9a\x84\xe5\x95\x86\xe5\xae\xb6\xe7\x8a\xb6\xe6\x80\x81\xe3\x80\x82\\n- **\xe6\xa8\xa1\xe5\x9d\x97\xe6\x8e\xa5\xe5\x8f\xa3**\xef\xbc\x9a\xe6\x8e\x88\xe6\x9d\x83\xe7\xae\xa1\xe7\x90\x86\xe9\x80\x9a\xe8\xbf\x87\xe5\x9b\x9e\xe8\xb0\x83\xe5\x9c\xb0\xe5\x9d\x80\xe5\x90\x91\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe4\xbc\xa0\xe9\x80\x92\xe6\x8e\x88\xe6\x9d\x83\xe4\xbf\xa1\xe6\x81\xaf\xef\xbc\x9b\xe6\x8e\xa5\xe5\x8f\xa3\xe4\xb8\xad\xe5\x8f\xb0\xe5\xa4\x84\xe7\x90\x86\xe5\x90\x8e\xe5\xb0\x86\xe6\x95\xb0\xe6\x8d\xae\xe4\xbc\xa0\xe9\x80\x92\xe7\xbb\x99OMS\xe3\x80\x82\\n\\n### 1.3 \xe4\xb8\x9a\xe5\x8a\xa1\xe8\xa7\x84\xe5\x88\x99\xe6\x8f\x90\xe5\x8f\x96\\n- **\xe6\xa0\xb8\xe5\xbf\x83\xe8\xa7\x84\xe5\x88\x99**\xef\xbc\x9a\xe5\x8f\xaa\xe6\x9c\x89\xe6\x9c\xaa\xe8\xa2\xab\xe6\x8e\x88\xe6\x9d\x83\xe8\xbf\x87\xe7\x9a\x84\xe5\x95\x86\xe5\xae\xb6ID\xe6\x89\x8d\xe8\x83\xbd\xe6\xb7\xbb\xe5\x8a\xa0\xe6\x96\xb0\xe7\x9a\x84\xe6\x8e\x88\xe6\x9d\x83\xe8\xaf\xb7\xe6\xb1\x82\xef\xbc\x9b\xe6\xaf\x8f\xe4\xb8\xaa\xe5\x95\x86\xe5\xae\xb6\xe9\x83\xbd\xe6\x9c\x89\xe7\x8b\xac\xe7\xab\x8b\xe7\x9a\x84\xe7\xa7\x98\xe9\x92\xa5\xe3\x80\x82\\n- **\xe9\xaa\x8c\xe8\xaf\x81\xe8\xa7\x84\xe5\x88\x99**\xef\xbc\x9a\xe7\xa1\xae\xe4\xbf\x9d\xe5\x95\x86\xe5\xae\xb6ID\xe5\x94\xaf\xe4\xb8\x80\xe6\x80\xa7\xef\xbc\x8c\xe9\x98\xb2\xe6\xad\xa2\xe9\x87\x8d\xe5\xa4\x8d\xe6\x8e\x88\xe6\x9d\x83\xef\xbc\x9b\xe6\xa0\xa1\xe9\xaa\x8c\xe5\x95\x86\xe5\xae\xb6ID\xe6\x98\xaf\xe5\x90\xa6\xe5\xb7\xb2\xe5\xad\x98\xe5\x9c\xa8\xe4\xba\x8e\xe7\xb3\xbb\xe7\xbb\x9f\xe4\xb8\xad\xe3\x80\x82\\n- **\xe8\xae\xa1\xe7\xae\x97\xe9\x80\xbb\xe8\xbe\x91**\xef\xbc\x9a\xe6\x97\xa0\xe7\x9b\xb4\xe6\x8e\xa5\xe6\x8f\x90\xe5\x8f\x8a\xe7\x9a\x84\xe9\x87\x8d\xe8\xa6\x81\xe8\xae\xa1\xe7\xae\x97\xe9\x80\xbb\xe8\xbe\x91\xef\xbc\x8c\xe4\xb8\xbb\xe8\xa6\x81\xe6\xb6\x89\xe5\x8f\x8a\xe6\x95\xb0\xe6\x8d\xae\xe7\x9a\x84\xe6\xa0\xa1\xe9\xaa\x8c\xe4\xb8\x8e\xe5\xad\x98\xe5\x82\xa8\xe3\x80\x82\\n\\n## 2. \xe7\x94\xa8\xe6\x88\xb7\xe4\xbd\x93\xe9\xaa\x8c\xe9\x9c\x80\xe6\xb1\x82\xe5\x88\x86\xe6\x9e\x90\\n- \xe4\xba\xa4\xe4\xba\x92\xe6\xb5\x81\xe7\xa8\x8b\xe6\xb5\x81\xe7\x95\x85\xe6\x80\xa7\xe8\xa6\x81\xe6\xb1\x82\xef\xbc\x9a\xe6\x8e\x88\xe6\x9d\x83\xe9\xa1\xb5\xe9\x9d\xa2\xe9\x9c\x80\xe7\xae\x80\xe6\xb4\x81\xe6\x98\x8e\xe4\xba\x86\xef\xbc\x8c\xe5\x87\x8f\xe5\xb0\x91\xe7\x94\xa8\xe6\x88\xb7\xe6\x93\x8d\xe4\xbd\x9c\xe6\xad\xa5\xe9\xaa\xa4\xe3\x80\x82\\n- \xe5\xbc\x82\xe5\xb8\xb8\xe6\x8f\x90\xe7\xa4\xba\xe5\x8f\x8b\xe5\xa5\xbd\xe6\x80\xa7\xe8\xa6\x81\xe6\xb1\x82\xef\xbc\x9a\xe5\xbd\x93\xe5\x87\xba\xe7\x8e\xb0\xe6\x8e\x88\xe6\x9d\x83\xe5\xa4\xb1\xe8\xb4\xa5\xe6\x97\xb6\xef\xbc\x8c\xe7\xbb\x99\xe5\x87\xba\xe6\x98\x8e\xe7\xa1\xae\xe7\x9a\x84\xe9\x94\x99\xe8\xaf\xaf\xe5\x8e\x9f\xe5\x9b\xa0\xe5\x8f\x8a\xe8\xa7\xa3\xe5\x86\xb3\xe6\x96\xb9\xe6\xa1\x88\xe3\x80\x82\\n- \xe8\xa7\x92\xe8\x89\xb2\xe6\x9d\x83\xe9\x99\x90\xe5\xb7\xae\xe5\xbc\x82\xef\xbc\x9a\xe6\x99\xae\xe9\x80\x9a\xe5\x95\x86\xe6\x88\xb7\xe5\x8f\xaa\xe8\x83\xbd\xe6\x9f\xa5\xe7\x9c\x8b\xe8\x87\xaa\xe5\xb7\xb1\xe7\x9a\x84\xe6\x8e\x88\xe6\x9d\x83\xe6\x83\x85\xe5\x86\xb5\xef\xbc\x8c\xe8\x80\x8c\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\x8f\xaf\xe4\xbb\xa5\xe7\xae\xa1\xe7\x90\x86\xe5\xa4\x9a\xe4\xb8\xaa\xe5\x95\x86\xe5\xae\xb6\xe7\x9a\x84\xe6\x8e\x88\xe6\x9d\x83\xe3\x80\x82\\n\\n## 3. \xe5\xbc\x82\xe5\xb8\xb8\xe5\x9c\xba\xe6\x99\xaf\xe5\x88\x86\xe6\x9e\x90\\n- \xe8\xbe\x93\xe5\x85\xa5\xe5\xbc\x82\xe5\xb8\xb8\xe5\xa4\x84\xe7\x90\x86\xef\xbc\x9a\xe5\xaf\xb9\xe6\x97\xa0\xe6\x95\x88\xe7\x9a\x84\xe5\x95\x86\xe5\xae\xb6ID\xe6\x88\x96\xe9\x87\x8d\xe5\xa4\x8d\xe7\x9a\x84\xe7\xbd\x91\xe5\xba\x97\xe5\x90\x8d\xe7\xa7\xb0\xe6\x8f\x90\xe4\xbe\x9b\xe5\x8d\xb3\xe6\x97\xb6\xe5\x8f\x8d\xe9\xa6\x88\xe3\x80\x82\\n- \xe6\x93\x8d\xe4\xbd\x9c\xe5\xbc\x82\xe5\xb8\xb8\xe5\xa4\x84\xe7\x90\x86\xef\xbc\x9a\xe6\x8e\x88\xe6\x9d\x83\xe8\xbf\x87\xe7\xa8\x8b\xe4\xb8\xad\xe6\x96\xad\xe6\x88\x96\xe5\xa4\xb1\xe8\xb4\xa5\xe6\x97\xb6\xef\xbc\x8c\xe5\x85\x81\xe8\xae\xb8\xe7\x94\xa8\xe6\x88\xb7\xe9\x87\x8d\xe6\x96\xb0\xe5\xb0\x9d\xe8\xaf\x95\xe6\x8e\x88\xe6\x9d\x83\xe3\x80\x82\\n- \xe5\xa4\x96\xe9\x83\xa8\xe4\xbe\x9d\xe8\xb5\x96\xe5\xbc\x82\xe5\xb8\xb8\xe5\xa4\x84\xe7\x90\x86\xef\xbc\x9a\xe5\xa6\x82\xe4\xba\xac\xe4\xb8\x9c\xe5\x88\xb0\xe5\xae\xb6\xe5\xb9\xb3\xe5\x8f\xb0\xe4\xb8\x8d\xe5\x8f\xaf\xe7\x94\xa8\xe6\x97\xb6\xef\xbc\x8c\xe5\xba\x94\xe6\x9c\x89\xe5\xa4\x87\xe7\x94\xa8\xe6\x96\xb9\xe6\xa1\x88\xe4\xbb\xa5\xe4\xbf\x9d\xe8\xaf\x81\xe6\x9c\x8d\xe5\x8a\xa1\xe8\xbf\x9e\xe7\xbb\xad\xe6\x80\xa7\xe3\x80\x82\\n- PRD\xe6\x9c\xaa\xe6\x98\x8e\xe7\xa1\xae\xe7\x9a\x84\xe9\x9a\x90\xe6\x80\xa7\xe5\xbc\x82\xe5\xb8\xb8\xe5\x9c\xba\xe6\x99\xaf\xef\xbc\x9a\xe6\x9c\xaa\xe7\x89\xb9\xe5\x88\xab\xe6\x8c\x87\xe5\x87\xba\xe5\x85\xb6\xe4\xbb\x96\xe5\x8f\xaf\xe8\x83\xbd\xe7\x9a\x84\xe5\xbc\x82\xe5\xb8\xb8\xe6\x83\x85\xe5\x86\xb5\xe3\x80\x82\\n\\n## 4. \xe5\x85\xbc\xe5\xae\xb9\xe6\x80\xa7\xe9\x9c\x80\xe6\xb1\x82\xe5\x88\x86\xe6\x9e\x90\\n- PRD\xe6\x98\x8e\xe7\xa1\xae\xe7\x9a\x84\xe5\x85\xbc\xe5\xae\xb9\xe8\x8c\x83\xe5\x9b\xb4\xef\xbc\x9a\xe5\x85\xbc\xe5\xae\xb9\xe7\x8e\xb0\xe6\x9c\x89\xe7\x9a\x84\xe5\x95\x86\xe5\xae\xb6\xe5\x92\x8c\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe4\xb8\xa4\xe7\xa7\x8d\xe6\xa8\xa1\xe5\xbc\x8f\xef\xbc\x8c\xe7\xa1\xae\xe4\xbf\x9d\xe5\x9c\xa8\xe4\xb8\x8d\xe7\xa0\xb4\xe5\x9d\x8f\xe7\x8e\xb0\xe6\x9c\x89\xe5\x8a\x9f\xe8\x83\xbd\xe7\x9a\x84\xe5\x9f\xba\xe7\xa1\x80\xe4\xb8\x8a\xe6\x96\xb0\xe5\xa2\x9e\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe6\xa8\xa1\xe5\xbc\x8f\xe7\x9a\x84\xe6\x94\xaf\xe6\x8c\x81\xe3\x80\x82\\n\\n## 5. \xe5\xae\x89\xe5\x85\xa8\xe6\x80\xa7\xe9\x9c\x80\xe6\xb1\x82\xe5\x88\x86\xe6\x9e\x90\\n- \xe6\x95\xb0\xe6\x8d\xae\xe5\xae\x89\xe5\x85\xa8\xe8\xa6\x81\xe6\xb1\x82\xef\xbc\x9a\xe4\xbf\x9d\xe6\x8a\xa4\xe5\x95\x86\xe5\xae\xb6ID\xe4\xb8\x8e\xe7\xa7\x98\xe9\x92\xa5\xe7\x9a\x84\xe5\xae\x89\xe5\x85\xa8\xef\xbc\x8c\xe7\xa6\x81\xe6\xad\xa2\xe6\x9c\xaa\xe7\xbb\x8f\xe6\x8e\x88\xe6\x9d\x83\xe7\x9a\x84\xe8\xae\xbf\xe9\x97\xae\xe3\x80\x82\\n- \xe6\x9d\x83\xe9\x99\x90\xe6\x8e\xa7\xe5\x88\xb6\xe8\xa6\x81\xe6\xb1\x82\xef\xbc\x9a\xe4\xb8\x8d\xe5\x90\x8c\xe7\x9a\x84\xe7\x94\xa8\xe6\x88\xb7\xe8\xa7\x92\xe8\x89\xb2\xe5\x85\xb7\xe6\x9c\x89\xe4\xb8\x8d\xe5\x90\x8c\xe7\x9a\x84\xe6\x93\x8d\xe4\xbd\x9c\xe6\x9d\x83\xe9\x99\x90\xef\xbc\x8c\xe5\xa6\x82\xe6\x9c\x8d\xe5\x8a\xa1\xe5\x95\x86\xe5\x8f\xaf\xe7\xae\xa1\xe7\x90\x86\xe5\xa4\x9a\xe4\xb8\xaa\xe5\x95\x86\xe5\xae\xb6\xe7\x9a\x84\xe4\xbf\xa1\xe6\x81\xaf\xe3\x80\x82\\n- \xe4\xb8\x9a\xe5\x8a\xa1\xe5\xae\x89\xe5\x85\xa8\xe8\xa6\x81\xe6\xb1\x82\xef\xbc\x9a\xe7\xa1\xae\xe4\xbf\x9d\xe6\x89\x80\xe6\x9c\x89\xe6\x8e\x88\xe6\x9d\x83\xe5\x8a\xa8\xe4\xbd\x9c\xe9\x83\xbd\xe6\x98\xaf\xe5\x9c\xa8\xe5\x95\x86\xe5\xae\xb6\xe7\x9f\xa5\xe6\x83\x85\xe7\x9a\x84\xe6\x83\x85\xe5\x86\xb5\xe4\xb8\x8b\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe4\xb8\x94\xe6\x8e\x88\xe6\x9d\x83\xe4\xbf\xa1\xe6\x81\xaf\xe5\x87\x86\xe7\xa1\xae\xe6\x97\xa0\xe8\xaf\xaf\xe5\x9c\xb0\xe4\xbc\xa0\xe8\xbe\x93\xe3\x80\x82\\n\\n## 6. \xe6\x80\xa7\xe8\x83\xbd\xe9\x9c\x80\xe6\xb1\x82\xe5\x88\x86\xe6\x9e\x90\\n- PRD\xe6\x98\x8e\xe7\xa1\xae\xe7\x9a\x84\xe6\x80\xa7\xe8\x83\xbd\xe6\x8c\x87\xe6\xa0\x87\xef\xbc\x9a\xe6\x96\x87\xe6\xa1\xa3\xe4\xb8\xad\xe6\xb2\xa1\xe6\x9c\x89\xe5\x85\xb7\xe4\xbd\x93\xe6\x8f\x90\xe5\x88\xb0\xe6\x80\xa7\xe8\x83\xbd\xe6\x8c\x87\xe6\xa0\x87\xe3\x80\x82\\n- \xe9\xab\x98\xe5\xb9\xb6\xe5\x8f\x91\xe5\x9c\xba\xe6\x99\xaf\xe8\xa6\x81\xe6\xb1\x82\xef\xbc\x9a\xe8\x83\xbd\xe5\xa4\x9f\xe5\x90\x8c\xe6\x97\xb6\xe5\xa4\x84\xe7\x90\x86\xe5\xa4\x9a\xe4\xb8\xaa\xe5\x95\x86\xe5\xae\xb6\xe7\x9a\x84\xe6\x8e\x88\xe6\x9d\x83\xe8\xaf\xb7\xe6\xb1\x82\xef\xbc\x8c\xe7\x89\xb9\xe5\x88\xab\xe6\x98\xaf\xe5\x9c\xa8\xe4\xbf\x83\xe9\x94\x80\xe6\xb4\xbb\xe5\x8a\xa8\xe6\x9c\x9f\xe9\x97\xb4\xe3\x80\x82","ui_link":"","tech_link":"","requirement_pic":[]}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714102911155|Thread-35 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714102911155|Thread-35 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714102911197|Thread-35 (process_request_thread):6191247360|INFO|views.py:1149|AI开始生成需求【534535】的用例
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714103027103|Thread-35 (process_request_thread):6191247360|INFO|testcase_tools.py:318|原始响应 (类型: <class 'str'>): [
  {
    "需求名称": "0010001-商家授权成功",
    "系统": "授权管理系统",
    "模块": "授权管理",
    "场景": "授权管理-商家授权成功-成功",
    "前提": "商家访问固定URL进行授权，输入正确的商家ID和网店名称",
    "步骤": [
      "1. 商家访问固定URL进行授权",
      "2. 输入正确的商家ID和网店名称",
      "3. 点击确认授权按钮",
      "4. 授权信息通过回调地址发送给接口中台",
      "5. OMS根据商家ID更新状态并拉取门店信息"
    ],
    "预期结果": [
      "1. 商家成功登录京东到家账号并确认授权",
      "2. 回调地址接收到授权信息",
      "3. OMS成功更新商家状态并拉取门店信息"
    ],
    "关键词": "授权管理"
  },
  {
    "需求名称": "0010002-商家授权失败",
    "系统": "授权管理系统",
    "模块": "授权管理",
    "场景": "授权管理-商家授权失败-异常",
    "前提": "商家访问固定URL进行授权，输入错误的商家ID",
    "步骤": [
      "1. 商家访问固定URL进行授权",
      "2. 输入错误的商家ID和正确的网店名称",
      "3. 点击确认授权按钮"
    ],
    "预期结果": [
      "1. 显示授权失败提示",
      "2. 提示错误原因（例如：商家ID不存在）",
      "3. 允许用户重新尝试授权"
    ],
    "关键词": "授权管理"
  },
  {
    "需求名称": "0010003-重复授权检测",
    "系统": "授权管理系统",
    "模块": "授权管理",
    "场景": "授权管理-重复授权检测-异常",
    "前提": "商家已经授权过，再次尝试授权",
    "步骤": [
      "1. 商家访问固定URL进行授权",
      "2. 输入已经授权过的商家ID和网店名称",
      "3. 点击确认授权按钮"
    ],
    "预期结果": [
      "1. 显示授权失败提示",
      "2. 提示错误原因（例如：商家ID已存在或网店名称重复）",
      "3. 要求修改后重试"
    ],
    "关键词": "授权管理"
  },
  {
    "需求名称": "0010004-特殊字符兼容",
    "系统": "授权管理系统",
    "模块": "授权管理",
    "场景": "授权管理-包含特殊字符的网店名称-成功",
    "前提": "商家访问固定URL进行授权，输入包含特殊字符的网店名称",
    "步骤": [
      "1. 商家访问固定URL进行授权",
      "2. 输入正确的商家ID和包含特殊字符的网店名称",
      "3. 点击确认授权按钮"
    ],
    "预期结果": [
      "1. 商家成功登录京东到家账号并确认授权",
      "2. 回调地址接收到授权信息",
      "3. OMS成功更新商家状态并拉取门店信息"
    ],
    "关键词": "授权管理"
  },
  {
    "需求名称": "0010005-边界值测试",
    "系统": "授权管理系统",
    "模块": "授权管理",
    "场景": "授权管理-边界值测试-成功",
    "前提": "商家访问固定URL进行授权，输入最大长度的商家ID和网店名称",
    "步骤": [
      "1. 商家访问固定URL进行授权",
      "2. 输入最大长度的商家ID和网店名称",
      "3. 点击确认授权按钮"
    ],
    "预期结果": [
      "1. 商家成功登录京东到家账号并确认授权",
      "2. 回调地址接收到授权信息",
      "3. OMS成功更新商家状态并拉取门店信息"
    ],
    "关键词": "授权管理"
  },
  {
    "需求名称": "0010006-并发冲突测试",
    "系统": "授权管理系统",
    "模块": "授权管理",
    "场景": "授权管理-并发冲突测试-异常",
    "前提": "多个商家同时访问固定URL进行授权",
    "步骤": [
      "1. 多个商家同时访问固定URL进行授权",
      "2. 每个商家输入不同的商家ID和相同的网店名称",
      "3. 同时点击确认授权按钮"
    ],
    "预期结果": [
      "1. 只有一个商家成功授权",
      "2. 其他商家显示授权失败提示",
      "3. 提示错误原因（例如：网店名称重复）"
    ],
    "关键词": "授权管理"
  },
  {
    "需求名称": "0010007-服务商权限验证",
    "系统": "授权管理系统",
    "模块": "授权管理",
    "场景": "授权管理-服务商权限验证-安全",
    "前提": "服务商登录系统，尝试管理多个商家的授权信息",
    "步骤": [
      "1. 服务商登录系统",
      "2. 尝试查看多个商家的授权信息",
      "3. 尝试修改某个商家的授权信息"
    ],
    "预期结果": [
      "1. 服务商成功查看多个商家的授权信息",
      "2. 服务商成功修改某个商家的授权信息"
    ],
    "关键词": "授权管理"
  },
  {
    "需求名称": "0010008-外部依赖异常处理",
    "系统": "授权管理系统",
    "模块": "授权管理",
    "场景": "授权管理-外部依赖异常处理-异常",
    "前提": "京东到家平台不可用",
    "步骤": [
      "1. 商家访问固定URL进行授权",
      "2. 输入正确的商家ID和网店名称",
      "3. 点击确认授权按钮"
    ],
    "预期结果": [
      "1. 显示授权失败提示",
      "2. 提示错误原因（例如：京东到家平台不可用）",
      "3. 提供备用方案以保证服务连续性"
    ],
    "关键词": "授权管理"
  },
  {
    "需求名称": "0010009-数据安全验证",
    "系统": "授权管理系统",
    "模块": "授权管理",
    "场景": "授权管理-数据安全验证-安全",
    "前提": "未授权用户尝试访问商家ID和秘钥",
    "步骤": [
      "1. 未授权用户尝试访问商家ID和秘钥",
      "2. 系统检测到未授权访问"
    ],
    "预期结果": [
      "1. 系统拒绝访问请求",
      "2. 返回403 Forbidden响应码"
    ],
    "关键词": "授权管理"
  },
  {
    "需求名称": "0010010-权限控制验证",
    "系统": "授权管理系统",
    "模块": "授权管理",
    "场景": "授权管理-权限控制验证-安全",
    "前提": "普通商户登录系统，尝试管理多个商家的授权信息",
    "步骤": [
      "1. 普通商户登录系统",
      "2. 尝试查看其他商家的授权信息",
      "3. 尝试修改其他商家的授权信息"
    ],
    "预期结果": [
      "1. 系统拒绝查看其他商家的授权信息",
      "2. 系统拒绝修改其他商家的授权信息",
      "3. 返回403 Forbidden响应码"
    ],
    "关键词": "授权管理"
  }
]
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714103027106|Thread-35 (process_request_thread):6191247360|INFO|views.py:1151|AI生成测试用例成功:{'status': 'success', 'total': 10, 'test_cases': [{'需求名称': '0010001-商家授权成功', '系统': '授权管理系统', '模块': '授权管理', '场景': '授权管理-商家授权成功-成功', '前提': '商家访问固定URL进行授权，输入正确的商家ID和网店名称', '步骤': ['1. 商家访问固定URL进行授权', '2. 输入正确的商家ID和网店名称', '3. 点击确认授权按钮', '4. 授权信息通过回调地址发送给接口中台', '5. OMS根据商家ID更新状态并拉取门店信息'], '预期结果': ['1. 商家成功登录京东到家账号并确认授权', '2. 回调地址接收到授权信息', '3. OMS成功更新商家状态并拉取门店信息'], '关键词': '授权管理'}, {'需求名称': '0010002-商家授权失败', '系统': '授权管理系统', '模块': '授权管理', '场景': '授权管理-商家授权失败-异常', '前提': '商家访问固定URL进行授权，输入错误的商家ID', '步骤': ['1. 商家访问固定URL进行授权', '2. 输入错误的商家ID和正确的网店名称', '3. 点击确认授权按钮'], '预期结果': ['1. 显示授权失败提示', '2. 提示错误原因（例如：商家ID不存在）', '3. 允许用户重新尝试授权'], '关键词': '授权管理'}, {'需求名称': '0010003-重复授权检测', '系统': '授权管理系统', '模块': '授权管理', '场景': '授权管理-重复授权检测-异常', '前提': '商家已经授权过，再次尝试授权', '步骤': ['1. 商家访问固定URL进行授权', '2. 输入已经授权过的商家ID和网店名称', '3. 点击确认授权按钮'], '预期结果': ['1. 显示授权失败提示', '2. 提示错误原因（例如：商家ID已存在或网店名称重复）', '3. 要求修改后重试'], '关键词': '授权管理'}, {'需求名称': '0010004-特殊字符兼容', '系统': '授权管理系统', '模块': '授权管理', '场景': '授权管理-包含特殊字符的网店名称-成功', '前提': '商家访问固定URL进行授权，输入包含特殊字符的网店名称', '步骤': ['1. 商家访问固定URL进行授权', '2. 输入正确的商家ID和包含特殊字符的网店名称', '3. 点击确认授权按钮'], '预期结果': ['1. 商家成功登录京东到家账号并确认授权', '2. 回调地址接收到授权信息', '3. OMS成功更新商家状态并拉取门店信息'], '关键词': '授权管理'}, {'需求名称': '0010005-边界值测试', '系统': '授权管理系统', '模块': '授权管理', '场景': '授权管理-边界值测试-成功', '前提': '商家访问固定URL进行授权，输入最大长度的商家ID和网店名称', '步骤': ['1. 商家访问固定URL进行授权', '2. 输入最大长度的商家ID和网店名称', '3. 点击确认授权按钮'], '预期结果': ['1. 商家成功登录京东到家账号并确认授权', '2. 回调地址接收到授权信息', '3. OMS成功更新商家状态并拉取门店信息'], '关键词': '授权管理'}, {'需求名称': '0010006-并发冲突测试', '系统': '授权管理系统', '模块': '授权管理', '场景': '授权管理-并发冲突测试-异常', '前提': '多个商家同时访问固定URL进行授权', '步骤': ['1. 多个商家同时访问固定URL进行授权', '2. 每个商家输入不同的商家ID和相同的网店名称', '3. 同时点击确认授权按钮'], '预期结果': ['1. 只有一个商家成功授权', '2. 其他商家显示授权失败提示', '3. 提示错误原因（例如：网店名称重复）'], '关键词': '授权管理'}, {'需求名称': '0010007-服务商权限验证', '系统': '授权管理系统', '模块': '授权管理', '场景': '授权管理-服务商权限验证-安全', '前提': '服务商登录系统，尝试管理多个商家的授权信息', '步骤': ['1. 服务商登录系统', '2. 尝试查看多个商家的授权信息', '3. 尝试修改某个商家的授权信息'], '预期结果': ['1. 服务商成功查看多个商家的授权信息', '2. 服务商成功修改某个商家的授权信息'], '关键词': '授权管理'}, {'需求名称': '0010008-外部依赖异常处理', '系统': '授权管理系统', '模块': '授权管理', '场景': '授权管理-外部依赖异常处理-异常', '前提': '京东到家平台不可用', '步骤': ['1. 商家访问固定URL进行授权', '2. 输入正确的商家ID和网店名称', '3. 点击确认授权按钮'], '预期结果': ['1. 显示授权失败提示', '2. 提示错误原因（例如：京东到家平台不可用）', '3. 提供备用方案以保证服务连续性'], '关键词': '授权管理'}, {'需求名称': '0010009-数据安全验证', '系统': '授权管理系统', '模块': '授权管理', '场景': '授权管理-数据安全验证-安全', '前提': '未授权用户尝试访问商家ID和秘钥', '步骤': ['1. 未授权用户尝试访问商家ID和秘钥', '2. 系统检测到未授权访问'], '预期结果': ['1. 系统拒绝访问请求', '2. 返回403 Forbidden响应码'], '关键词': '授权管理'}, {'需求名称': '0010010-权限控制验证', '系统': '授权管理系统', '模块': '授权管理', '场景': '授权管理-权限控制验证-安全', '前提': '普通商户登录系统，尝试管理多个商家的授权信息', '步骤': ['1. 普通商户登录系统', '2. 尝试查看其他商家的授权信息', '3. 尝试修改其他商家的授权信息'], '预期结果': ['1. 系统拒绝查看其他商家的授权信息', '2. 系统拒绝修改其他商家的授权信息', '3. 返回403 Forbidden响应码'], '关键词': '授权管理'}]}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714103027134|Thread-36 (recordSystemLog):6208073728|INFO|commonUtil.py:51|用户 liuyanxia 生成需求:534535的测试用例
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714103027135|Thread-35 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"id": 53}, "meta": {"msg": "用例生成成功", "status": 200}, "timestamp": 1752460227135}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714103027137|Thread-35 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:143|当前请求耗时：75.98240613937378 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714103027156|Thread-35 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/ai_generate_test_case_list/，请求方式：POST，请求参数为：b'{"system":"","module":"","status":"","recognize_record_id":53,"pagenum":1,"pagesize":10}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714103027157|Thread-35 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714103027158|Thread-35 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714103027174|Thread-37 (recordSystemLog):6208073728|INFO|commonUtil.py:51|用户 liuyanxia 查询AI生成用例列表，查询条件：系统=，模块= , 状态= 
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714103027183|Thread-35 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 10, "total": 10, "data": [{"id": 521, "recognize_record_id": 53, "requirement_id": 534535, "case_scene": "授权管理-权限控制验证-安全", "system": "授权管理系统", "module": "授权管理", "premise": "普通商户登录系统，尝试管理多个商家的授权信息", "test_steps": "1. 普通商户登录系统\n2. 尝试查看其他商家的授权信息\n3. 尝试修改其他商家的授权信息", "expected_result": "1. 系统拒绝查看其他商家的授权信息\n2. 系统拒绝修改其他商家的授权信息\n3. 返回403 Forbidden响应码", "status": "0", "create_time": "2025-07-14T10:30:27", "update_time": "2025-07-14T10:30:27", "update_person": "刘艳霞", "is_active": true}, {"id": 520, "recognize_record_id": 53, "requirement_id": 534535, "case_scene": "授权管理-数据安全验证-安全", "system": "授权管理系统", "module": "授权管理", "premise": "未授权用户尝试访问商家ID和秘钥", "test_steps": "1. 未授权用户尝试访问商家ID和秘钥\n2. 系统检测到未授权访问", "expected_result": "1. 系统拒绝访问请求\n2. 返回403 Forbidden响应码", "status": "0", "create_time": "2025-07-14T10:30:27", "update_time": "2025-07-14T10:30:27", "update_person": "刘艳霞", "is_active": true}, {"id": 519, "recognize_record_id": 53, "requirement_id": 534535, "case_scene": "授权管理-外部依赖异常处理-异常", "system": "授权管理系统", "module": "授权管理", "premise": "京东到家平台不可用", "test_steps": "1. 商家访问固定URL进行授权\n2. 输入正确的商家ID和网店名称\n3. 点击确认授权按钮", "expected_result": "1. 显示授权失败提示\n2. 提示错误原因（例如：京东到家平台不可用）\n3. 提供备用方案以保证服务连续性", "status": "0", "create_time": "2025-07-14T10:30:27", "update_time": "2025-07-14T10:30:27", "update_person": "刘艳霞", "is_active": true}, {"id": 518, "recognize_record_id": 53, "requirement_id": 534535, "case_scene": "授权管理-服务商权限验证-安全", "system": "授权管理系统", "module": "授权管理", "premise": "服务商登录系统，尝试管理多个商家的授权信息", "test_steps": "1. 服务商登录系统\n2. 尝试查看多个商家的授权信息\n3. 尝试修改某个商家的授权信息", "expected_result": "1. 服务商成功查看多个商家的授权信息\n2. 服务商成功修改某个商家的授权信息", "status": "0", "create_time": "2025-07-14T10:30:27", "update_time": "2025-07-14T10:30:27", "update_person": "刘艳霞", "is_active": true}, {"id": 517, "recognize_record_id": 53, "requirement_id": 534535, "case_scene": "授权管理-并发冲突测试-异常", "system": "授权管理系统", "module": "授权管理", "premise": "多个商家同时访问固定URL进行授权", "test_steps": "1. 多个商家同时访问固定URL进行授权\n2. 每个商家输入不同的商家ID和相同的网店名称\n3. 同时点击确认授权按钮", "expected_result": "1. 只有一个商家成功授权\n2. 其他商家显示授权失败提示\n3. 提示错误原因（例如：网店名称重复）", "status": "0", "create_time": "2025-07-14T10:30:27", "update_time": "2025-07-14T10:30:27", "update_person": "刘艳霞", "is_active": true}, {"id": 516, "recognize_record_id": 53, "requirement_id": 534535, "case_scene": "授权管理-边界值测试-成功", "system": "授权管理系统", "module": "授权管理", "premise": "商家访问固定URL进行授权，输入最大长度的商家ID和网店名称", "test_steps": "1. 商家访问固定URL进行授权\n2. 输入最大长度的商家ID和网店名称\n3. 点击确认授权按钮", "expected_result": "1. 商家成功登录京东到家账号并确认授权\n2. 回调地址接收到授权信息\n3. OMS成功更新商家状态并拉取门店信息", "status": "0", "create_time": "2025-07-14T10:30:27", "update_time": "2025-07-14T10:30:27", "update_person": "刘艳霞", "is_active": true}, {"id": 515, "recognize_record_id": 53, "requirement_id": 534535, "case_scene": "授权管理-包含特殊字符的网店名称-成功", "system": "授权管理系统", "module": "授权管理", "premise": "商家访问固定URL进行授权，输入包含特殊字符的网店名称", "test_steps": "1. 商家访问固定URL进行授权\n2. 输入正确的商家ID和包含特殊字符的网店名称\n3. 点击确认授权按钮", "expected_result": "1. 商家成功登录京东到家账号并确认授权\n2. 回调地址接收到授权信息\n3. OMS成功更新商家状态并拉取门店信息", "status": "0", "create_time": "2025-07-14T10:30:27", "update_time": "2025-07-14T10:30:27", "update_person": "刘艳霞", "is_active": true}, {"id": 514, "recognize_record_id": 53, "requirement_id": 534535, "case_scene": "授权管理-重复授权检测-异常", "system": "授权管理系统", "module": "授权管理", "premise": "商家已经授权过，再次尝试授权", "test_steps": "1. 商家访问固定URL进行授权\n2. 输入已经授权过的商家ID和网店名称\n3. 点击确认授权按钮", "expected_result": "1. 显示授权失败提示\n2. 提示错误原因（例如：商家ID已存在或网店名称重复）\n3. 要求修改后重试", "status": "0", "create_time": "2025-07-14T10:30:27", "update_time": "2025-07-14T10:30:27", "update_person": "刘艳霞", "is_active": true}, {"id": 513, "recognize_record_id": 53, "requirement_id": 534535, "case_scene": "授权管理-商家授权失败-异常", "system": "授权管理系统", "module": "授权管理", "premise": "商家访问固定URL进行授权，输入错误的商家ID", "test_steps": "1. 商家访问固定URL进行授权\n2. 输入错误的商家ID和正确的网店名称\n3. 点击确认授权按钮", "expected_result": "1. 显示授权失败提示\n2. 提示错误原因（例如：商家ID不存在）\n3. 允许用户重新尝试授权", "status": "0", "create_time": "2025-07-14T10:30:27", "update_time": "2025-07-14T10:30:27", "update_person": "刘艳霞", "is_active": true}, {"id": 512, "recognize_record_id": 53, "requirement_id": 534535, "case_scene": "授权管理-商家授权成功-成功", "system": "授权管理系统", "module": "授权管理", "premise": "商家访问固定URL进行授权，输入正确的商家ID和网店名称", "test_steps": "1. 商家访问固定URL进行授权\n2. 输入正确的商家ID和网店名称\n3. 点击确认授权按钮\n4. 授权信息通过回调地址发送给接口中台\n5. OMS根据商家ID更新状态并拉取门店信息", "expected_result": "1. 商家成功登录京东到家账号并确认授权\n2. 回调地址接收到授权信息\n3. OMS成功更新商家状态并拉取门店信息", "status": "0", "create_time": "2025-07-14T10:30:27", "update_time": "2025-07-14T10:30:27", "update_person": "刘艳霞", "is_active": true}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752460227183}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250714103027184|Thread-35 (process_request_thread):6191247360|INFO|userRequestMiddleware.py:143|当前请求耗时：0.026961803436279297 秒
