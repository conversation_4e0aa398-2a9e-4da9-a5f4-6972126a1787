|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715143318753|Thread-39 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/users/?userName=&pagenum=1&pagesize=5，请求方式：GET，请求参数为：<QueryDict: {'userName': [''], 'pagenum': ['1'], 'pagesize': ['5']}>
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715143318760|Thread-38 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/info/?type=roles，请求方式：GET，请求参数为：<QueryDict: {'type': ['roles']}>
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715143318771|Thread-39 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715143318771|Thread-38 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715143318771|Thread-39 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715143318771|Thread-38 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715143318865|Thread-40 (recordSystemLog):***********|INFO|commonUtil.py:51|来自IP：127.0.0.1的用户 liuyanxia 请求获取全部roles信息
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715143318881|Thread-38 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": [{"id": 9, "label": "开发"}, {"id": 19, "label": "大区"}, {"id": 1, "label": "测试人员"}, {"id": 0, "label": "超级管理员"}], "meta": {"msg": "获取成功", "status": 200}, "timestamp": 1752561198880}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715143318881|Thread-38 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.12109375 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715143345161|Thread-41 (recordSystemLog):***********|INFO|commonUtil.py:51|超级管理员 liuyanxia 查询了用户列表，查询条件：用户ID=，用户名=
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715143345163|Thread-39 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"total": 615, "pagenum": "1", "pagesize": "5", "userName": "", "userId": "", "users": [{"id": 1288, "username": "liuyahong", "mobile": "15191896313", "email": null, "chinese_name": "刘娅红", "staff_no": "604801637", "create_time": "2025-02-26T15:36:14.042", "mg_state": true, "role_name": "大区"}, {"id": 1603, "username": "zhangyi", "mobile": "13570274124", "email": null, "chinese_name": "张屹", "staff_no": "602290885", "create_time": "2025-02-26T15:36:14.103", "mg_state": true, "role_name": "开发"}, {"id": 1617, "username": "yaozhe", "mobile": "13574818878", "email": null, "chinese_name": "姚哲", "staff_no": "602290952", "create_time": "2025-02-26T15:36:14.119", "mg_state": true, "role_name": "大区"}, {"id": 1658, "username": "xiaofangqi", "mobile": "15574567853", "email": null, "chinese_name": "肖芳啟", "staff_no": "602651728", "create_time": "2025-02-26T15:36:14.119", "mg_state": true, "role_name": "大区"}, {"id": 1427, "username": "zhouyu", "mobile": "18570359870", "email": null, "chinese_name": "周宇", "staff_no": "602290312", "create_time": "2025-02-26T15:36:14.077", "mg_state": true, "role_name": "大区"}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752561225162}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715143345167|Thread-39 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：26.41086196899414 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715153204872|Thread-43 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/import_case_report_list/，请求方式：POST，请求参数为：b'{"business_id":"","iteration_name":"","creater":"","pagenum":1,"pagesize":5}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715153204874|Thread-42 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/info/?type=businesses，请求方式：GET，请求参数为：<QueryDict: {'type': ['businesses']}>
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715153204875|Thread-43 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715153204875|Thread-42 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715153204875|Thread-43 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715153204876|Thread-42 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715153204926|Thread-44 (recordSystemLog):***********|INFO|commonUtil.py:51|来自IP：127.0.0.1的用户 liuyanxia 请求获取全部businesses信息
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715153204927|Thread-45 (recordSystemLog):***********|INFO|commonUtil.py:51|用户 liuyanxia 查询参数信息，查询条件：迭代名称=，业务线= , 创建者= 
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715153204946|Thread-42 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": [{"id": 30, "label": "30-厂家端小程序"}, {"id": 29, "label": "29-一键造数"}, {"id": 28, "label": "28-医保云"}, {"id": 27, "label": "27-四季蝉"}, {"id": 26, "label": "26-数据中心"}, {"id": 25, "label": "25-云仓"}, {"id": 24, "label": "24-随心看"}, {"id": 23, "label": "23-直播"}, {"id": 22, "label": "22-服务商"}, {"id": 21, "label": "21-组织云"}, {"id": 20, "label": "20-微商城9"}, {"id": 19, "label": "19-随心享"}, {"id": 18, "label": "18-支付云"}, {"id": 17, "label": "17-优享会员"}, {"id": 4, "label": "4-OMS-B2C-h2（废除）"}, {"id": 11, "label": "11-OMS-B2C"}, {"id": 16, "label": "16-H2_O2O_Order"}, {"id": 15, "label": "15-H2_OMS_B2C"}, {"id": 14, "label": "14-药事云"}, {"id": 13, "label": "13-商品中台"}, {"id": 12, "label": "12-助手"}, {"id": 10, "label": "10-演示"}, {"id": 8, "label": "8-会员"}, {"id": 7, "label": "7-OMS-O2O"}, {"id": 6, "label": "6-营销"}, {"id": 2, "label": "2-自动化"}, {"id": 0, "label": "0-默认"}], "meta": {"msg": "获取成功", "status": 200}, "timestamp": 1752564724946}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715153204946|Thread-42 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.07188582420349121 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715153205070|Thread-43 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 5, "total": 19, "data": [{"is_active": true, "id": 44, "business_line": "6", "iteration_id": "", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "requirement_link": "http://c.hydee.cn/pages/viewpage.action?pageId=249483369", "smoke_result_statistics": "", "operator": "胡鹏", "update_time": "2025-06-19T11:06:22", "update_person": "", "businessName": "营销", "total_count": 32, "pass_count": 0}, {"is_active": true, "id": 43, "business_line": "6", "iteration_id": "", "iteration_name": "【V2.4.11.11】海川日常-0603", "requirement_link": "http://c.hydee.cn/pages/viewpage.action?pageId=252710206", "smoke_result_statistics": "", "operator": "胡鹏", "update_time": "2025-06-19T10:53:50", "update_person": "", "businessName": "营销", "total_count": 18, "pass_count": 0}, {"is_active": true, "id": 42, "business_line": "26", "iteration_id": "", "iteration_name": "【V2.3.4.22】商品云_铺货相关", "requirement_link": "https://doc.weixin.qq.com/doc/w3_AKMAQgZ5ALQCN0iOR65tmRImhBAma?scode=AOcA-wd2AAwgHYQFHLAKMAQgZ5ALQ", "smoke_result_statistics": "", "operator": "刘艳霞", "update_time": "2025-06-17T17:10:31", "update_person": "", "businessName": "数据中心", "total_count": 32, "pass_count": 0}, {"is_active": true, "id": 41, "business_line": "26", "iteration_id": "", "iteration_name": "【V2.3.4.22】商品云_铺货相关", "requirement_link": "https://doc.weixin.qq.com/doc/w3_AKMAQgZ5ALQCN0iOR65tmRImhBAma?scode=AOcA-wd2AAwgHYQFHLAKMAQgZ5ALQ", "smoke_result_statistics": "", "operator": "刘艳霞", "update_time": "2025-06-17T17:09:32", "update_person": "", "businessName": "数据中心", "total_count": 0, "pass_count": 0}, {"is_active": true, "id": 40, "business_line": "25", "iteration_id": "", "iteration_name": "【V2.3.4.22】商品云_铺货相关", "requirement_link": "https://doc.weixin.qq.com/doc/w3_AKMAQgZ5ALQCN0iOR65tmRImhBAma?scode=AOcA-wd2AAwgHYQFHLAKMAQgZ5ALQ", "smoke_result_statistics": "", "operator": "刘艳霞", "update_time": "2025-06-17T17:08:45", "update_person": "", "businessName": "云仓", "total_count": 0, "pass_count": 0}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752564725070}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715153205071|Thread-43 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.197951078414917 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715154450837|Thread-47 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/smoke_case_list/，请求方式：POST，请求参数为：b'{"report_id":"44","operator":"","operator_result":[],"requirement":"","developer":"","pagenum":1,"pagesize":10}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715154450838|Thread-46 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/smoke_case_list/，请求方式：POST，请求参数为：b'{"report_id":"44","operator":"","operator_result":"","pagenum":1,"pagesize":10}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715154450839|Thread-47 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715154450839|Thread-46 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715154450840|Thread-47 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715154450840|Thread-46 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715154450863|Thread-48 (recordSystemLog):***********|INFO|commonUtil.py:51|用户 liuyanxia 查询冒烟用例列表
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715154450864|Thread-49 (recordSystemLog):***********|INFO|commonUtil.py:51|用户 liuyanxia 查询冒烟用例列表
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715154450901|Thread-46 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 10, "total": 32, "data": [{"is_active": true, "id": 1479, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "查询及导出批量充值记录", "system": "商户后台", "module": "会员权益", "case_scene_id": null, "premise": "已成功充值储值卡", "test_steps": "1、进入商户后台-会员权益-储值卡-批量充值页签\n2、点击批量充值按钮，按导入和手动添加，输入会员及充值本金及赠送金额\n3、充值成功后，回到主列表查询\n4、点击导出按钮，下载导出数据", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：查询条件label去掉冒号，文案还原 导出与查询重置放一起；\n2：批量充值状态枚举值文案调整： 全部成功、部分成功、全部失败、处理中\n3：表格内调整字段名称（见ui），增加字段（充值本金，赠送金额）\n4：导出数据与查询条件一致，字段名称同步更新", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1481, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "指定会员人群", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、新建会员维护任务，选择指定会员人群，勾选自动过滤选项，创建任务\n3、查看任务详情-维护会员明细", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：任务创建成功\n2：维护会员明细可见自动过滤XX会员，分配失败XX会员\n3：成功下载名单，下载Excel字段：会员卡号、会员姓名、会员手机号、自动过滤原因（短信/电话黑名单用户、近X天已维护会员）或者分配失败原因", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1483, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "指定会员标签", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、新建会员维护任务，选择指定会员标签，勾选自动过滤选项，创建任务\n3、查看任务详情-维护会员明细", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：任务创建成功\n2：维护会员明细可见自动过滤XX会员，分配失败XX会员\n3：成功下载名单，下载Excel字段：会员卡号、会员姓名、会员手机号、自动过滤原因（短信/电话黑名单用户、近X天已维护会员）或者分配失败原因", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1485, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "导入会员维护名单", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、新建会员维护任务，选择导入会员维护名单，勾选自动过滤选项，创建任务\n3、查看任务详情-维护会员明细", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：任务创建成功\n2：维护会员明细可见自动过滤XX会员，分配失败XX会员\n3：成功下载名单，下载Excel字段：会员卡号、会员姓名、会员手机号、自动过滤原因（短信/电话黑名单用户、近X天已维护会员）或者分配失败原因", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1489, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "删除会员维护任务", "system": "商户后台+随心看客户端", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、删除各种状态下的会员维护任务\n3、随心看客户端任务中心+店员圈选择任务进入任务详情", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：所有状态的任务均可逻辑删除（二次确认弹框），删除后页面不可见\n2：随心看客户端任务中心+店员圈选择任务进入任务详情提示任务已失效\n3：参与维护任务的会员详情页不可见已删除的维护任务，添加维护任务记录时，不可见自动勾选当前任务已完成的选项\n4：B段维护记录明细，会员详情下的维护记录均展示", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1492, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "精准查询待维护或者已维护的会员", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护任务详情-维护会员\n2、选择待维护/已维护，输入会员卡号、会员名、手机号码搜索", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：精准匹配会员信息", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1494, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "店长重新分配任务", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有店长权限", "test_steps": "1、进入随心看会员端-会员维护任务详情-点击任务分配按钮\n2、重新分配任务量，点击确认修改按钮", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：顶部会员维护任务量取值为门店权限下待维护会员的总量\n2：确认修改逻辑：基于当前任务的未完成的任务量校验分配，去除不得低于已维护任务量的校验规则", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1498, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "新增批量发券任务", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择发券通知-勾选短信-优惠券信息-优惠券券码\n3、选择指定优惠券券码展示的优惠券\n4、选择短信效果监测时间段\n5、提交任务", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：活动创建成功，且发送时间展示位置移动至发送对象下\n2：优惠券券码】变量在短信输入框插入后的文案由【优惠券码】占位7位（本期无法实现占位10位）\n3：选择优惠券码的情况下，指定优惠券支持多选，最多十张，必选，数据源为全部的优惠券\n4：新增短信效果监测时间,必填,与是否勾选短信有关系，勾选才展示，默认为当前时间为开始时间，结束时间为七天后的当前时间，可自由修改，与发送时间无逻辑关联关系", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1501, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "查看批量发券任务", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "已创建任务", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择任务，点击查看详情按钮进入活动详情页", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：顶部栏展示短信效果监测时间\n2：指定优惠券券码展示在哪里查看呢-无地方可看\n3：发送短信内容正常展示", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1504, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "活动优惠券发送+短信发放数据正确", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "已创建任务", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择发送优惠券，选择短信勾选优惠券券码，并选择定优惠券券码展示（与发放优惠券有交叉的优惠券）\n3、选择短信效果监测时间段\n4、提交任务并执行发放", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：按正确人数发送优惠券张数正确\n2：短信发放正确，且短信里的优惠券码与选择定优惠券券码相匹配（所选优惠券在该会员下有未使用的优惠券的集合，如果同一张优惠券有多张，则只发最早发送的那张优惠券的券码）\n3：正确获取在短信监控时间段内的短信报表相关数据", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752565490901}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715154450902|Thread-46 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.06350827217102051 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715154450903|Thread-47 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 10, "total": 32, "data": [{"is_active": true, "id": 1479, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "查询及导出批量充值记录", "system": "商户后台", "module": "会员权益", "case_scene_id": null, "premise": "已成功充值储值卡", "test_steps": "1、进入商户后台-会员权益-储值卡-批量充值页签\n2、点击批量充值按钮，按导入和手动添加，输入会员及充值本金及赠送金额\n3、充值成功后，回到主列表查询\n4、点击导出按钮，下载导出数据", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：查询条件label去掉冒号，文案还原 导出与查询重置放一起；\n2：批量充值状态枚举值文案调整： 全部成功、部分成功、全部失败、处理中\n3：表格内调整字段名称（见ui），增加字段（充值本金，赠送金额）\n4：导出数据与查询条件一致，字段名称同步更新", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1481, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "指定会员人群", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、新建会员维护任务，选择指定会员人群，勾选自动过滤选项，创建任务\n3、查看任务详情-维护会员明细", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：任务创建成功\n2：维护会员明细可见自动过滤XX会员，分配失败XX会员\n3：成功下载名单，下载Excel字段：会员卡号、会员姓名、会员手机号、自动过滤原因（短信/电话黑名单用户、近X天已维护会员）或者分配失败原因", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1483, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "指定会员标签", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、新建会员维护任务，选择指定会员标签，勾选自动过滤选项，创建任务\n3、查看任务详情-维护会员明细", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：任务创建成功\n2：维护会员明细可见自动过滤XX会员，分配失败XX会员\n3：成功下载名单，下载Excel字段：会员卡号、会员姓名、会员手机号、自动过滤原因（短信/电话黑名单用户、近X天已维护会员）或者分配失败原因", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1485, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "导入会员维护名单", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、新建会员维护任务，选择导入会员维护名单，勾选自动过滤选项，创建任务\n3、查看任务详情-维护会员明细", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：任务创建成功\n2：维护会员明细可见自动过滤XX会员，分配失败XX会员\n3：成功下载名单，下载Excel字段：会员卡号、会员姓名、会员手机号、自动过滤原因（短信/电话黑名单用户、近X天已维护会员）或者分配失败原因", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1489, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "删除会员维护任务", "system": "商户后台+随心看客户端", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、删除各种状态下的会员维护任务\n3、随心看客户端任务中心+店员圈选择任务进入任务详情", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：所有状态的任务均可逻辑删除（二次确认弹框），删除后页面不可见\n2：随心看客户端任务中心+店员圈选择任务进入任务详情提示任务已失效\n3：参与维护任务的会员详情页不可见已删除的维护任务，添加维护任务记录时，不可见自动勾选当前任务已完成的选项\n4：B段维护记录明细，会员详情下的维护记录均展示", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1492, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "精准查询待维护或者已维护的会员", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护任务详情-维护会员\n2、选择待维护/已维护，输入会员卡号、会员名、手机号码搜索", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：精准匹配会员信息", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1494, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "店长重新分配任务", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有店长权限", "test_steps": "1、进入随心看会员端-会员维护任务详情-点击任务分配按钮\n2、重新分配任务量，点击确认修改按钮", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：顶部会员维护任务量取值为门店权限下待维护会员的总量\n2：确认修改逻辑：基于当前任务的未完成的任务量校验分配，去除不得低于已维护任务量的校验规则", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1498, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "新增批量发券任务", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择发券通知-勾选短信-优惠券信息-优惠券券码\n3、选择指定优惠券券码展示的优惠券\n4、选择短信效果监测时间段\n5、提交任务", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：活动创建成功，且发送时间展示位置移动至发送对象下\n2：优惠券券码】变量在短信输入框插入后的文案由【优惠券码】占位7位（本期无法实现占位10位）\n3：选择优惠券码的情况下，指定优惠券支持多选，最多十张，必选，数据源为全部的优惠券\n4：新增短信效果监测时间,必填,与是否勾选短信有关系，勾选才展示，默认为当前时间为开始时间，结束时间为七天后的当前时间，可自由修改，与发送时间无逻辑关联关系", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1501, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "查看批量发券任务", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "已创建任务", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择任务，点击查看详情按钮进入活动详情页", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：顶部栏展示短信效果监测时间\n2：指定优惠券券码展示在哪里查看呢-无地方可看\n3：发送短信内容正常展示", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1504, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "活动优惠券发送+短信发放数据正确", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "已创建任务", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择发送优惠券，选择短信勾选优惠券券码，并选择定优惠券券码展示（与发放优惠券有交叉的优惠券）\n3、选择短信效果监测时间段\n4、提交任务并执行发放", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：按正确人数发送优惠券张数正确\n2：短信发放正确，且短信里的优惠券码与选择定优惠券券码相匹配（所选优惠券在该会员下有未使用的优惠券的集合，如果同一张优惠券有多张，则只发最早发送的那张优惠券的券码）\n3：正确获取在短信监控时间段内的短信报表相关数据", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752565490903}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715154450903|Thread-47 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.06650209426879883 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155725455|Thread-50 (process_request_thread):6190673920|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/smoke_case_list/，请求方式：POST，请求参数为：b'{"report_id":"44","operator":"","operator_result":"","pagenum":1,"pagesize":10}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155725460|Thread-50 (process_request_thread):6190673920|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155725461|Thread-50 (process_request_thread):6190673920|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155725490|Thread-51 (recordSystemLog):6207500288|INFO|commonUtil.py:51|用户 liuyanxia 查询冒烟用例列表
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155725530|Thread-50 (process_request_thread):6190673920|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 10, "total": 32, "data": [{"is_active": true, "id": 1479, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "查询及导出批量充值记录", "system": "商户后台", "module": "会员权益", "case_scene_id": null, "premise": "已成功充值储值卡", "test_steps": "1、进入商户后台-会员权益-储值卡-批量充值页签\n2、点击批量充值按钮，按导入和手动添加，输入会员及充值本金及赠送金额\n3、充值成功后，回到主列表查询\n4、点击导出按钮，下载导出数据", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：查询条件label去掉冒号，文案还原 导出与查询重置放一起；\n2：批量充值状态枚举值文案调整： 全部成功、部分成功、全部失败、处理中\n3：表格内调整字段名称（见ui），增加字段（充值本金，赠送金额）\n4：导出数据与查询条件一致，字段名称同步更新", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1481, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "指定会员人群", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、新建会员维护任务，选择指定会员人群，勾选自动过滤选项，创建任务\n3、查看任务详情-维护会员明细", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：任务创建成功\n2：维护会员明细可见自动过滤XX会员，分配失败XX会员\n3：成功下载名单，下载Excel字段：会员卡号、会员姓名、会员手机号、自动过滤原因（短信/电话黑名单用户、近X天已维护会员）或者分配失败原因", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1483, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "指定会员标签", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、新建会员维护任务，选择指定会员标签，勾选自动过滤选项，创建任务\n3、查看任务详情-维护会员明细", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：任务创建成功\n2：维护会员明细可见自动过滤XX会员，分配失败XX会员\n3：成功下载名单，下载Excel字段：会员卡号、会员姓名、会员手机号、自动过滤原因（短信/电话黑名单用户、近X天已维护会员）或者分配失败原因", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1485, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "导入会员维护名单", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、新建会员维护任务，选择导入会员维护名单，勾选自动过滤选项，创建任务\n3、查看任务详情-维护会员明细", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：任务创建成功\n2：维护会员明细可见自动过滤XX会员，分配失败XX会员\n3：成功下载名单，下载Excel字段：会员卡号、会员姓名、会员手机号、自动过滤原因（短信/电话黑名单用户、近X天已维护会员）或者分配失败原因", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1489, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "删除会员维护任务", "system": "商户后台+随心看客户端", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、删除各种状态下的会员维护任务\n3、随心看客户端任务中心+店员圈选择任务进入任务详情", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：所有状态的任务均可逻辑删除（二次确认弹框），删除后页面不可见\n2：随心看客户端任务中心+店员圈选择任务进入任务详情提示任务已失效\n3：参与维护任务的会员详情页不可见已删除的维护任务，添加维护任务记录时，不可见自动勾选当前任务已完成的选项\n4：B段维护记录明细，会员详情下的维护记录均展示", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1492, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "精准查询待维护或者已维护的会员", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护任务详情-维护会员\n2、选择待维护/已维护，输入会员卡号、会员名、手机号码搜索", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：精准匹配会员信息", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1494, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "店长重新分配任务", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有店长权限", "test_steps": "1、进入随心看会员端-会员维护任务详情-点击任务分配按钮\n2、重新分配任务量，点击确认修改按钮", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：顶部会员维护任务量取值为门店权限下待维护会员的总量\n2：确认修改逻辑：基于当前任务的未完成的任务量校验分配，去除不得低于已维护任务量的校验规则", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1498, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "新增批量发券任务", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择发券通知-勾选短信-优惠券信息-优惠券券码\n3、选择指定优惠券券码展示的优惠券\n4、选择短信效果监测时间段\n5、提交任务", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：活动创建成功，且发送时间展示位置移动至发送对象下\n2：优惠券券码】变量在短信输入框插入后的文案由【优惠券码】占位7位（本期无法实现占位10位）\n3：选择优惠券码的情况下，指定优惠券支持多选，最多十张，必选，数据源为全部的优惠券\n4：新增短信效果监测时间,必填,与是否勾选短信有关系，勾选才展示，默认为当前时间为开始时间，结束时间为七天后的当前时间，可自由修改，与发送时间无逻辑关联关系", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1501, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "查看批量发券任务", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "已创建任务", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择任务，点击查看详情按钮进入活动详情页", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：顶部栏展示短信效果监测时间\n2：指定优惠券券码展示在哪里查看呢-无地方可看\n3：发送短信内容正常展示", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1504, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "活动优惠券发送+短信发放数据正确", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "已创建任务", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择发送优惠券，选择短信勾选优惠券券码，并选择定优惠券券码展示（与发放优惠券有交叉的优惠券）\n3、选择短信效果监测时间段\n4、提交任务并执行发放", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：按正确人数发送优惠券张数正确\n2：短信发放正确，且短信里的优惠券码与选择定优惠券券码相匹配（所选优惠券在该会员下有未使用的优惠券的集合，如果同一张优惠券有多张，则只发最早发送的那张优惠券的券码）\n3：正确获取在短信监控时间段内的短信报表相关数据", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752566245530}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155725530|Thread-50 (process_request_thread):6190673920|INFO|userRequestMiddleware.py:143|当前请求耗时：0.07529973983764648 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155732328|Thread-52 (process_request_thread):6190673920|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/update_smoke_case/，请求方式：POST，请求参数为：b'{"id":[1479],"operator":"liuyanxia","file_url":null,"operator_result":"1","operator_result_remark":"\xe6\xb5\x8b\xe8\xaf\x95"}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155732329|Thread-52 (process_request_thread):6190673920|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155732329|Thread-52 (process_request_thread):6190673920|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155732365|Thread-53 (recordSystemLog):6207500288|INFO|commonUtil.py:51|用户 liuyanxia 批量编辑冒烟用例成功，影响记录数: 1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155732365|Thread-52 (process_request_thread):6190673920|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": null, "meta": {"msg": "成功更新 1 条记录", "status": 200}, "timestamp": 1752566252366}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155732368|Thread-52 (process_request_thread):6190673920|INFO|userRequestMiddleware.py:143|当前请求耗时：0.03771781921386719 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155732394|Thread-52 (process_request_thread):6190673920|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/smoke_case_list/，请求方式：POST，请求参数为：b'{"report_id":"44","operator":"","operator_result":"","pagenum":1,"pagesize":10}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155732395|Thread-52 (process_request_thread):6190673920|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155732396|Thread-52 (process_request_thread):6190673920|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155732422|Thread-54 (recordSystemLog):6207500288|INFO|commonUtil.py:51|用户 liuyanxia 查询冒烟用例列表
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155732486|Thread-52 (process_request_thread):6190673920|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 10, "total": 32, "data": [{"is_active": true, "id": 1479, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "查询及导出批量充值记录", "system": "商户后台", "module": "会员权益", "case_scene_id": null, "premise": "已成功充值储值卡", "test_steps": "1、进入商户后台-会员权益-储值卡-批量充值页签\n2、点击批量充值按钮，按导入和手动添加，输入会员及充值本金及赠送金额\n3、充值成功后，回到主列表查询\n4、点击导出按钮，下载导出数据", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：查询条件label去掉冒号，文案还原 导出与查询重置放一起；\n2：批量充值状态枚举值文案调整： 全部成功、部分成功、全部失败、处理中\n3：表格内调整字段名称（见ui），增加字段（充值本金，赠送金额）\n4：导出数据与查询条件一致，字段名称同步更新", "case_type": "1", "keyword": "", "operator": "刘艳霞", "operator_result": "1", "operator_result_remark": "测试", "file_url": [null], "create_time": "2025-06-19T11:06:23", "update_time": "2025-07-15T15:57:32", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1481, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "指定会员人群", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、新建会员维护任务，选择指定会员人群，勾选自动过滤选项，创建任务\n3、查看任务详情-维护会员明细", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：任务创建成功\n2：维护会员明细可见自动过滤XX会员，分配失败XX会员\n3：成功下载名单，下载Excel字段：会员卡号、会员姓名、会员手机号、自动过滤原因（短信/电话黑名单用户、近X天已维护会员）或者分配失败原因", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1483, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "指定会员标签", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、新建会员维护任务，选择指定会员标签，勾选自动过滤选项，创建任务\n3、查看任务详情-维护会员明细", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：任务创建成功\n2：维护会员明细可见自动过滤XX会员，分配失败XX会员\n3：成功下载名单，下载Excel字段：会员卡号、会员姓名、会员手机号、自动过滤原因（短信/电话黑名单用户、近X天已维护会员）或者分配失败原因", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1485, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "导入会员维护名单", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、新建会员维护任务，选择导入会员维护名单，勾选自动过滤选项，创建任务\n3、查看任务详情-维护会员明细", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：任务创建成功\n2：维护会员明细可见自动过滤XX会员，分配失败XX会员\n3：成功下载名单，下载Excel字段：会员卡号、会员姓名、会员手机号、自动过滤原因（短信/电话黑名单用户、近X天已维护会员）或者分配失败原因", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1489, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "删除会员维护任务", "system": "商户后台+随心看客户端", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、删除各种状态下的会员维护任务\n3、随心看客户端任务中心+店员圈选择任务进入任务详情", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：所有状态的任务均可逻辑删除（二次确认弹框），删除后页面不可见\n2：随心看客户端任务中心+店员圈选择任务进入任务详情提示任务已失效\n3：参与维护任务的会员详情页不可见已删除的维护任务，添加维护任务记录时，不可见自动勾选当前任务已完成的选项\n4：B段维护记录明细，会员详情下的维护记录均展示", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1492, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "精准查询待维护或者已维护的会员", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护任务详情-维护会员\n2、选择待维护/已维护，输入会员卡号、会员名、手机号码搜索", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：精准匹配会员信息", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1494, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "店长重新分配任务", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有店长权限", "test_steps": "1、进入随心看会员端-会员维护任务详情-点击任务分配按钮\n2、重新分配任务量，点击确认修改按钮", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：顶部会员维护任务量取值为门店权限下待维护会员的总量\n2：确认修改逻辑：基于当前任务的未完成的任务量校验分配，去除不得低于已维护任务量的校验规则", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1498, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "新增批量发券任务", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择发券通知-勾选短信-优惠券信息-优惠券券码\n3、选择指定优惠券券码展示的优惠券\n4、选择短信效果监测时间段\n5、提交任务", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：活动创建成功，且发送时间展示位置移动至发送对象下\n2：优惠券券码】变量在短信输入框插入后的文案由【优惠券码】占位7位（本期无法实现占位10位）\n3：选择优惠券码的情况下，指定优惠券支持多选，最多十张，必选，数据源为全部的优惠券\n4：新增短信效果监测时间,必填,与是否勾选短信有关系，勾选才展示，默认为当前时间为开始时间，结束时间为七天后的当前时间，可自由修改，与发送时间无逻辑关联关系", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1501, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "查看批量发券任务", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "已创建任务", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择任务，点击查看详情按钮进入活动详情页", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：顶部栏展示短信效果监测时间\n2：指定优惠券券码展示在哪里查看呢-无地方可看\n3：发送短信内容正常展示", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1504, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "活动优惠券发送+短信发放数据正确", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "已创建任务", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择发送优惠券，选择短信勾选优惠券券码，并选择定优惠券券码展示（与发放优惠券有交叉的优惠券）\n3、选择短信效果监测时间段\n4、提交任务并执行发放", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：按正确人数发送优惠券张数正确\n2：短信发放正确，且短信里的优惠券码与选择定优惠券券码相匹配（所选优惠券在该会员下有未使用的优惠券的集合，如果同一张优惠券有多张，则只发最早发送的那张优惠券的券码）\n3：正确获取在短信监控时间段内的短信报表相关数据", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752566252485}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155732487|Thread-52 (process_request_thread):6190673920|INFO|userRequestMiddleware.py:143|当前请求耗时：0.09271407127380371 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155805012|Thread-55 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/update_smoke_case/，请求方式：POST，请求参数为：b'{"id":[1485],"operator":"liuyanxia","file_url":null,"operator_result":"2","operator_result_remark":"3123"}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155805015|Thread-55 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155805015|Thread-55 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155805049|Thread-56 (recordSystemLog):***********|INFO|commonUtil.py:51|用户 liuyanxia 批量编辑冒烟用例成功，影响记录数: 1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155805050|Thread-55 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": null, "meta": {"msg": "成功更新 1 条记录", "status": 200}, "timestamp": 1752566285050}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155805051|Thread-55 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.03747820854187012 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155805069|Thread-55 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/smoke_case_list/，请求方式：POST，请求参数为：b'{"report_id":"44","operator":"","operator_result":"","pagenum":1,"pagesize":10,"requirement":"234"}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155805069|Thread-55 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155805069|Thread-55 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155805085|Thread-57 (recordSystemLog):***********|INFO|commonUtil.py:51|用户 liuyanxia 查询冒烟用例列表
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155805097|Thread-55 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 10, "total": 0, "data": []}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752566285097}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155805098|Thread-55 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.028450727462768555 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155811436|Thread-58 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/smoke_case_list/，请求方式：POST，请求参数为：b'{"report_id":"44","operator":"","operator_result":"","pagenum":1,"pagesize":10}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155811437|Thread-58 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155811438|Thread-58 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155811458|Thread-59 (recordSystemLog):***********|INFO|commonUtil.py:51|用户 liuyanxia 查询冒烟用例列表
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155811525|Thread-58 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 10, "total": 32, "data": [{"is_active": true, "id": 1479, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "查询及导出批量充值记录", "system": "商户后台", "module": "会员权益", "case_scene_id": null, "premise": "已成功充值储值卡", "test_steps": "1、进入商户后台-会员权益-储值卡-批量充值页签\n2、点击批量充值按钮，按导入和手动添加，输入会员及充值本金及赠送金额\n3、充值成功后，回到主列表查询\n4、点击导出按钮，下载导出数据", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：查询条件label去掉冒号，文案还原 导出与查询重置放一起；\n2：批量充值状态枚举值文案调整： 全部成功、部分成功、全部失败、处理中\n3：表格内调整字段名称（见ui），增加字段（充值本金，赠送金额）\n4：导出数据与查询条件一致，字段名称同步更新", "case_type": "1", "keyword": "", "operator": "刘艳霞", "operator_result": "1", "operator_result_remark": "测试", "file_url": [null], "create_time": "2025-06-19T11:06:23", "update_time": "2025-07-15T15:57:32", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1481, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "指定会员人群", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、新建会员维护任务，选择指定会员人群，勾选自动过滤选项，创建任务\n3、查看任务详情-维护会员明细", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：任务创建成功\n2：维护会员明细可见自动过滤XX会员，分配失败XX会员\n3：成功下载名单，下载Excel字段：会员卡号、会员姓名、会员手机号、自动过滤原因（短信/电话黑名单用户、近X天已维护会员）或者分配失败原因", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1483, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "指定会员标签", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、新建会员维护任务，选择指定会员标签，勾选自动过滤选项，创建任务\n3、查看任务详情-维护会员明细", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：任务创建成功\n2：维护会员明细可见自动过滤XX会员，分配失败XX会员\n3：成功下载名单，下载Excel字段：会员卡号、会员姓名、会员手机号、自动过滤原因（短信/电话黑名单用户、近X天已维护会员）或者分配失败原因", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1485, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "导入会员维护名单", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、新建会员维护任务，选择导入会员维护名单，勾选自动过滤选项，创建任务\n3、查看任务详情-维护会员明细", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：任务创建成功\n2：维护会员明细可见自动过滤XX会员，分配失败XX会员\n3：成功下载名单，下载Excel字段：会员卡号、会员姓名、会员手机号、自动过滤原因（短信/电话黑名单用户、近X天已维护会员）或者分配失败原因", "case_type": "1", "keyword": "", "operator": "刘艳霞", "operator_result": "2", "operator_result_remark": "3123", "file_url": [null], "create_time": "2025-06-19T11:06:23", "update_time": "2025-07-15T15:58:05", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1489, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "删除会员维护任务", "system": "商户后台+随心看客户端", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、删除各种状态下的会员维护任务\n3、随心看客户端任务中心+店员圈选择任务进入任务详情", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：所有状态的任务均可逻辑删除（二次确认弹框），删除后页面不可见\n2：随心看客户端任务中心+店员圈选择任务进入任务详情提示任务已失效\n3：参与维护任务的会员详情页不可见已删除的维护任务，添加维护任务记录时，不可见自动勾选当前任务已完成的选项\n4：B段维护记录明细，会员详情下的维护记录均展示", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1492, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "精准查询待维护或者已维护的会员", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护任务详情-维护会员\n2、选择待维护/已维护，输入会员卡号、会员名、手机号码搜索", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：精准匹配会员信息", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1494, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "店长重新分配任务", "system": "商户后台", "module": "会员维护", "case_scene_id": null, "premise": "有店长权限", "test_steps": "1、进入随心看会员端-会员维护任务详情-点击任务分配按钮\n2、重新分配任务量，点击确认修改按钮", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：顶部会员维护任务量取值为门店权限下待维护会员的总量\n2：确认修改逻辑：基于当前任务的未完成的任务量校验分配，去除不得低于已维护任务量的校验规则", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1498, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "新增批量发券任务", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择发券通知-勾选短信-优惠券信息-优惠券券码\n3、选择指定优惠券券码展示的优惠券\n4、选择短信效果监测时间段\n5、提交任务", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：活动创建成功，且发送时间展示位置移动至发送对象下\n2：优惠券券码】变量在短信输入框插入后的文案由【优惠券码】占位7位（本期无法实现占位10位）\n3：选择优惠券码的情况下，指定优惠券支持多选，最多十张，必选，数据源为全部的优惠券\n4：新增短信效果监测时间,必填,与是否勾选短信有关系，勾选才展示，默认为当前时间为开始时间，结束时间为七天后的当前时间，可自由修改，与发送时间无逻辑关联关系", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1501, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "查看批量发券任务", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "已创建任务", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择任务，点击查看详情按钮进入活动详情页", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：顶部栏展示短信效果监测时间\n2：指定优惠券券码展示在哪里查看呢-无地方可看\n3：发送短信内容正常展示", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1504, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "活动优惠券发送+短信发放数据正确", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "已创建任务", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择发送优惠券，选择短信勾选优惠券券码，并选择定优惠券券码展示（与发放优惠券有交叉的优惠券）\n3、选择短信效果监测时间段\n4、提交任务并执行发放", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：按正确人数发送优惠券张数正确\n2：短信发放正确，且短信里的优惠券码与选择定优惠券券码相匹配（所选优惠券在该会员下有未使用的优惠券的集合，如果同一张优惠券有多张，则只发最早发送的那张优惠券的券码）\n3：正确获取在短信监控时间段内的短信报表相关数据", "case_type": "1", "keyword": "", "operator": null, "operator_result": "0", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752566291524}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715155811526|Thread-58 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.08867716789245605 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715160551063|Thread-60 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/scene_info/scene_list/?search_scene_name=&creater=liuyanxia&pagenum=1&pagesize=5，请求方式：GET，请求参数为：<QueryDict: {'search_scene_name': [''], 'creater': ['liuyanxia'], 'pagenum': ['1'], 'pagesize': ['5']}>
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715160551066|Thread-61 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/case_info/case_list/?search_case_name=&search_case_uri=&creater=liuyanxia&pagenum=1&pagesize=5&type=API，请求方式：GET，请求参数为：<QueryDict: {'search_case_name': [''], 'search_case_uri': [''], 'creater': ['liuyanxia'], 'pagenum': ['1'], 'pagesize': ['5'], 'type': ['API']}>
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715160551067|Thread-62 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/info/?type=businesses，请求方式：GET，请求参数为：<QueryDict: {'type': ['businesses']}>
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715160551068|Thread-60 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715160551068|Thread-61 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715160551068|Thread-62 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715160551068|Thread-60 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715160551068|Thread-61 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715160551069|Thread-62 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715160551092|Thread-63 (recordSystemLog):***********|INFO|commonUtil.py:51|用户 liuyanxia 查询场景，查询条件：场景编号=，场景名称=,案例编号=
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715160551095|Thread-64 (recordSystemLog):***********|INFO|commonUtil.py:51|用户 liuyanxia 查询案例信息，查询条件：案例编号=，案例名称=
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715160551102|Thread-65 (recordSystemLog):***********|INFO|commonUtil.py:51|来自IP：127.0.0.1的用户 liuyanxia 请求获取全部businesses信息
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715160551109|Thread-62 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": [{"id": 30, "label": "30-厂家端小程序"}, {"id": 29, "label": "29-一键造数"}, {"id": 28, "label": "28-医保云"}, {"id": 27, "label": "27-四季蝉"}, {"id": 26, "label": "26-数据中心"}, {"id": 25, "label": "25-云仓"}, {"id": 24, "label": "24-随心看"}, {"id": 23, "label": "23-直播"}, {"id": 22, "label": "22-服务商"}, {"id": 21, "label": "21-组织云"}, {"id": 20, "label": "20-微商城9"}, {"id": 19, "label": "19-随心享"}, {"id": 18, "label": "18-支付云"}, {"id": 17, "label": "17-优享会员"}, {"id": 4, "label": "4-OMS-B2C-h2（废除）"}, {"id": 11, "label": "11-OMS-B2C"}, {"id": 16, "label": "16-H2_O2O_Order"}, {"id": 15, "label": "15-H2_OMS_B2C"}, {"id": 14, "label": "14-药事云"}, {"id": 13, "label": "13-商品中台"}, {"id": 12, "label": "12-助手"}, {"id": 10, "label": "10-演示"}, {"id": 8, "label": "8-会员"}, {"id": 7, "label": "7-OMS-O2O"}, {"id": 6, "label": "6-营销"}, {"id": 2, "label": "2-自动化"}, {"id": 0, "label": "0-默认"}], "meta": {"msg": "获取成功", "status": 200}, "timestamp": 1752566751109}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715160551110|Thread-62 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.04170870780944824 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715160551153|Thread-60 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": "1", "pagesize": "5", "total": 818, "search_scene_id": "", "search_scene_name": "", "scenes": [{"is_active": true, "create_time": "2022-12-16T17:27:34.583", "update_time": "2025-07-09T09:42:53", "update_person": "liuyanxia", "scene_id": 1170, "scene_name": "lyx微商城搜索云仓品", "business_id": 20, "scene_describe": "微商城搜索云仓品", "before_param": "keyword=测试;authStatus=1;", "scene_relation_id": "212505|212506|212507|212508|212509", "scene_type": 0, "is_related": 1, "creater": "liuyanxia", "businessName": "微商城9"}, {"is_active": true, "create_time": "2022-12-01T14:55:54.621", "update_time": "2025-03-03T19:06:29", "update_person": "liuyanxia", "scene_id": 986, "scene_name": "lyx创建满减券", "business_id": 6, "scene_describe": "", "before_param": "erpCodeList=\"97\",\"9797\";type=2;typeName=满减券;", "scene_relation_id": "212370|212371|212372", "scene_type": 0, "is_related": 1, "creater": "liuyanxia", "businessName": "营销"}, {"is_active": true, "create_time": "2022-12-01T15:01:11.335", "update_time": "2024-12-25T15:43:09", "update_person": "liuyanxia", "scene_id": 987, "scene_name": "lyx创建礼品券", "business_id": 6, "scene_describe": "", "before_param": "erpCodeList=\"97\",\"9797\";type=3;typeName=礼品券;", "scene_relation_id": "212325|212326|212327", "scene_type": 0, "is_related": 1, "creater": "liuyanxia", "businessName": "营销"}, {"is_active": true, "create_time": "2022-12-01T15:38:21.474", "update_time": "2024-12-25T15:42:57", "update_person": "liuyanxia", "scene_id": 989, "scene_name": "lyx创建现金券", "business_id": 6, "scene_describe": "", "before_param": "erpCodeList=\"97\",\"9797\";type=5;typeName=现金券;", "scene_relation_id": "212322|212323|212324", "scene_type": 0, "is_related": 1, "creater": "liuyanxia", "businessName": "营销"}, {"is_active": true, "create_time": "2022-12-01T14:38:22.938", "update_time": "2024-12-25T15:42:06", "update_person": "liuyanxia", "scene_id": 985, "scene_name": "lyx创建折扣券", "business_id": 6, "scene_describe": "", "before_param": "erpCodeList=\"97\",\"9797\";type=1;typeName=折扣券;", "scene_relation_id": "212316|212317|212318", "scene_type": 0, "is_related": 1, "creater": "liuyanxia", "businessName": "营销"}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752566751153}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715160551156|Thread-60 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.09032583236694336 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715160551242|Thread-61 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": "1", "pagesize": "5", "total": 452, "search_case_id": "", "search_case_name": "", "cases": [{"case_id": 6705, "interface_uri": "/businesses-gateway/mer-manager/1.0/couponManage/queryReceiveCouponList", "is_active": true, "create_time": "2024-11-28 14:28:48", "creater": "liuyanxia", "update_time": "2024-11-28 14:28:48", "update_person": "liuyanxia", "interface_id": "6681", "case_name": "B端优惠券领取列表", "case_describe": "", "is_init": false, "init_database": null, "init_sql": "", "is_back": false, "back_sql": "", "is_encrypt": false, "interface_data": "{\n    \"pageSize\": 10,\n    \"currentPage\": 1,\n    \"cname\": \"\",\n    \"memberPhone\": \"\",\n    \"couponCode\": [],\n    \"memberCard\": \"\",\n    \"ctype\": null,\n    \"beginTime\": \"@{lyx_time_-7d}@\",\n    \"endTime\": \"@{lyx_time}@\",\n    \"state\": null,\n    \"hxStoreCodeDisplay\": [],\n    \"receiveChannel\": null,\n    \"sendStoreCode\": null,\n    \"sendStoreCodeDisplay\": [],\n    \"saleName\": \"\",\n    \"thirdSendId\": \"\",\n    \"couponId\": \"\",\n    \"empValue\": \"\",\n    \"couponIds\": [],\n    \"queryPageNum\": 10,\n    \"offset\": 1,\n    \"queryId\": 0,\n    \"merCode\": \"@{mer_Code}@\"\n}", "is_assert": true, "case_level": "", "asserts": "196793", "case_type": "API", "csv_file": null, "header": null, "is_related": 1, "interface_api": 6681, "interface_name": "B端优惠券领取列表"}, {"case_id": 6704, "interface_uri": "/businesses-gateway/customer/1.0/member/encrypt/queryByToken", "is_active": true, "create_time": "2024-11-28 14:25:22", "creater": "liuyanxia", "update_time": "2024-11-28 14:25:52", "update_person": "liuyanxia", "interface_id": "6625", "case_name": "C端-根据token查询会员基础信息（加密）", "case_describe": "", "is_init": false, "init_database": null, "init_sql": "", "is_back": false, "back_sql": "", "is_encrypt": false, "interface_data": "", "is_assert": true, "case_level": "", "asserts": "196792", "case_type": "API", "csv_file": null, "header": null, "is_related": 1, "interface_api": 6625, "interface_name": "C端-根据token查询会员基础信息（加密）"}, {"case_id": 6703, "interface_uri": "/businesses-gateway/customer/1.0/member/details/encrypt2/edit", "is_active": true, "create_time": "2024-11-28 14:18:23", "creater": "liuyanxia", "update_time": "2024-11-28 14:18:23", "update_person": "liuyanxia", "interface_id": "6623", "case_name": "C端-我的-会员信息-编辑会员信息（加密）", "case_describe": "", "is_init": false, "init_database": null, "init_sql": "", "is_back": false, "back_sql": "", "is_encrypt": false, "interface_data": "{\n    \"memberAddress\": \"sZyecmM5P+PiS/V8y6gFCw==\",\n    \"merCode\": \"@{mer_Code}@\"\n}", "is_assert": true, "case_level": "", "asserts": "196790", "case_type": "API", "csv_file": null, "header": null, "is_related": 1, "interface_api": 6623, "interface_name": "C端-我的-会员信息-编辑会员信息（加密）"}, {"case_id": 6702, "interface_uri": "/businesses-gateway/customer/1.0/member/details/encrypt2/getMemberBaseInfo", "is_active": true, "create_time": "2024-11-28 14:16:13", "creater": "liuyanxia", "update_time": "2024-11-28 14:16:13", "update_person": "liuyanxia", "interface_id": "6626", "case_name": "C端-我的-获取会员信息（加密）", "case_describe": "", "is_init": false, "init_database": null, "init_sql": "", "is_back": false, "back_sql": "", "is_encrypt": false, "interface_data": "", "is_assert": true, "case_level": "", "asserts": "196789", "case_type": "API", "csv_file": null, "header": null, "is_related": 1, "interface_api": 6626, "interface_name": "C端-我的-获取会员信息（加密）"}, {"case_id": 6701, "interface_uri": "/businesses-gateway/customer/1.0/member/details/encrypt2/search/@{mer_Code}@", "is_active": true, "create_time": "2024-11-28 14:12:37", "creater": "liuyanxia", "update_time": "2024-11-28 14:12:37", "update_person": "liuyanxia", "interface_id": "6624", "case_name": "C端-我的-会员信息(加密）", "case_describe": "", "is_init": false, "init_database": null, "init_sql": "", "is_back": false, "back_sql": "", "is_encrypt": false, "interface_data": "", "is_assert": true, "case_level": "", "asserts": "196788", "case_type": "API", "csv_file": null, "header": null, "is_related": 1, "interface_api": 6624, "interface_name": "C端-我的-会员信息(加密）"}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752566751243}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715160551243|Thread-61 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.17603516578674316 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715161316266|Thread-67 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/user_info/info/?type=businesses，请求方式：GET，请求参数为：<QueryDict: {'type': ['businesses']}>
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715161316271|Thread-66 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/core_case_list/，请求方式：POST，请求参数为：b'{"report_id":"44","requirement":"","case_scene":"","system":"","module":"","pagenum":1,"pagesize":10}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715161316271|Thread-67 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715161316272|Thread-66 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715161316272|Thread-68 (process_request_thread):***********|INFO|userRequestMiddleware.py:56|当前请求接口：http://127.0.0.1:8000/api/test_case_info/core_case_list/，请求方式：POST，请求参数为：b'{"report_id":"44","requirement":"","case_scene":"","system":"","module":"","pagenum":1,"pagesize":10}'
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715161316273|Thread-67 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715161316273|Thread-66 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715161316273|Thread-68 (process_request_thread):***********|INFO|userRequestMiddleware.py:58|当前接口请求：IP 127.0.0.1
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715161316282|Thread-68 (process_request_thread):***********|INFO|userRequestMiddleware.py:61|当前接口请求：IP 127.0.0.1 在白名单中，放行
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715161316303|Thread-69 (recordSystemLog):***********|INFO|commonUtil.py:51|来自IP：127.0.0.1的用户 liuyanxia 请求获取全部businesses信息
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715161316304|Thread-70 (recordSystemLog):***********|INFO|commonUtil.py:51|用户 liuyanxia 查询冒烟用例列表
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715161316304|Thread-71 (recordSystemLog):***********|INFO|commonUtil.py:51|用户 liuyanxia 查询冒烟用例列表
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715161316313|Thread-67 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": [{"id": 30, "label": "30-厂家端小程序"}, {"id": 29, "label": "29-一键造数"}, {"id": 28, "label": "28-医保云"}, {"id": 27, "label": "27-四季蝉"}, {"id": 26, "label": "26-数据中心"}, {"id": 25, "label": "25-云仓"}, {"id": 24, "label": "24-随心看"}, {"id": 23, "label": "23-直播"}, {"id": 22, "label": "22-服务商"}, {"id": 21, "label": "21-组织云"}, {"id": 20, "label": "20-微商城9"}, {"id": 19, "label": "19-随心享"}, {"id": 18, "label": "18-支付云"}, {"id": 17, "label": "17-优享会员"}, {"id": 4, "label": "4-OMS-B2C-h2（废除）"}, {"id": 11, "label": "11-OMS-B2C"}, {"id": 16, "label": "16-H2_O2O_Order"}, {"id": 15, "label": "15-H2_OMS_B2C"}, {"id": 14, "label": "14-药事云"}, {"id": 13, "label": "13-商品中台"}, {"id": 12, "label": "12-助手"}, {"id": 10, "label": "10-演示"}, {"id": 8, "label": "8-会员"}, {"id": 7, "label": "7-OMS-O2O"}, {"id": 6, "label": "6-营销"}, {"id": 2, "label": "2-自动化"}, {"id": 0, "label": "0-默认"}], "meta": {"msg": "获取成功", "status": 200}, "timestamp": 1752567196313}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715161316316|Thread-67 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.047872066497802734 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715161316351|Thread-68 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 10, "total": 14, "data": [{"is_active": true, "id": 1490, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "删除会员维护任务", "system": "商户后台+随心看客户端", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、删除各种状态下的会员维护任务\n3、随心看客户端任务中心+店员圈选择任务进入任务详情", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：所有状态的任务均可逻辑删除（二次确认弹框），删除后页面不可见\n2：随心看客户端任务中心+店员圈选择任务进入任务详情提示任务已失效\n3：参与维护任务的会员详情页不可见已删除的维护任务，添加维护任务记录时，不可见自动勾选当前任务已完成的选项\n4：B段维护记录明细，会员详情下的维护记录均展示", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1499, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "新增批量发券任务", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择发券通知-勾选短信-优惠券信息-优惠券券码\n3、选择指定优惠券券码展示的优惠券\n4、选择短信效果监测时间段\n5、提交任务", "developer": "", "requirement_id": "", "requirement": "062新增批量发券任务需求", "expected_result": "1：活动创建成功，且发送时间展示位置移动至发送对象下\n2：优惠券券码】变量在短信输入框插入后的文案由【优惠券码】占位7位（本期无法实现占位10位）\n3：选择优惠券码的情况下，指定优惠券支持多选，最多十张，必选，数据源为全部的优惠券\n4：新增短信效果监测时间,必填,与是否勾选短信有关系，勾选才展示，默认为当前时间为开始时间，结束时间为七天后的当前时间，可自由修改，与发送时间无逻辑关联关系", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-24T14:17:26", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1502, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "查看批量发券任务", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "已创建任务", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择任务，点击查看详情按钮进入活动详情页", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：顶部栏展示短信效果监测时间\n2：指定优惠券券码展示在哪里查看呢-无地方可看\n3：发送短信内容正常展示", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1505, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "活动优惠券发送+短信发放数据正确", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "已创建任务", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择发送优惠券，选择短信勾选优惠券券码，并选择定优惠券券码展示（与发放优惠券有交叉的优惠券）\n3、选择短信效果监测时间段\n4、提交任务并执行发放", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：按正确人数发送优惠券张数正确\n2：短信发放正确，且短信里的优惠券码与选择定优惠券券码相匹配（所选优惠券在该会员下有未使用的优惠券的集合，如果同一张优惠券有多张，则只发最早发送的那张优惠券的券码）\n3：正确获取在短信监控时间段内的短信报表相关数据", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1512, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "短信回头效果", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "已创建任务，且勾选了短信，并发送优惠券和短信", "test_steps": "1、成功创建含短信且设置了监控时间的批量发券任务\n2、发送优惠券和短信成功\n3、查看短信回头效果报表", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：展示短信回头效果页签\n2：发送统计，发送详情，，短信费用统计不受监测时间影响，受监测时间影响（转化效果，回头订单明细）数据均计算正确，", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1573, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "活动列表增加展示短信回头效果数据列及汇总展示", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "已创建任务，且勾选了短信，并发送优惠券和短信", "test_steps": "1、进入商户后台-券营销-查询任务\n2、查看列表汇总数据并点击复制按钮\n3、查看列表字段数据", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：汇总数据新增字段短信回头会员数，短信回头销售额计算及展示正确\n2：复制数据准确\n3：列表数据新增字段短信回头会员数，短信回头销售额：活动回头订单销售总额等字段正确\n4：前端调整【短信通知人数】列标题为【短信通知情况】，并调整位置至【用券订单毛利率】数据列后展示", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1589, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "操作推广扫码-H5，领券中心支持按照每周【周一 09:00-09:20】按周期重复", "system": "商户后台+小程序+H5", "module": "领券中心", "case_scene_id": null, "premise": "", "test_steps": "1、NR商户后台-营销-营销管理-场景营销-其他场景-领券中心-添加优惠券，领取时间下勾选按周期重复，每周【周一\n2、00-\n3、59】\n4、C端H5【周一\n5、59】去我的服务-领券中心查询该优惠券，核对结果\n6、C端H5【周一\n7、01】去我的服务-领券中心查询该优惠券，核对结果\n8、C端H5【周二\n9、21】去我的服务-领券中心查询该优惠券，核对结果", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "2、该券不可见\n3、可查看&领取该券；\n4、该券不可见；", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1595, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "人群营销-新建运营计划，发优惠券支持过滤1、3、5、7天已发券会员", "system": "NR商户后台", "module": "运营计划", "case_scene_id": null, "premise": "001会员3天内已发送过A优惠券\n002会员3天内已发送过B优惠券\n003会员3天内已发送过A+B优惠券\n已配置短信（已配置白名单）", "test_steps": "1、周倩雯\n2、运营计划\n3、需求名称：【【510273一品】【运营计划】：会员画像里面发送的短信增加短信营销分析】\nhttps://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001066316\n【【510273一品】【运营计划】：数仓支持-会员画像里面发送的短信增加短信营销分析】\nhttps://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001067137\n4、开发人员：赵运、杨新城\n5、系统：NR商户后台\n6、模块：运营计划\n7、测试场景\n8、运营活动配置相关\n9、运营计划-发优惠券\n10、场景：人群营销-新建运营计划，发优惠券支持过滤1、3、5、7天已发券会员\n11、前提：001会员3天内已发送过A优惠券\n002会员3天内已发送过B优惠券\n003会员3天内已发送过A+B优惠券\n已配置短信（已配置白名单）\n12、操作步骤：\n1、NR商户后台-营销-营销管理-人群营销-新建运营计划，发优惠券下配置过滤3天A、B券，勾选发券通知，其他字段填写完整后保存\n2、核对运营计划结果\n3、核对运营计划详情页", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "2.1、\n001收到B优惠券+短信\n002会员收到A优惠券+短信\n003会员不收到优惠券和短信通知，\n2.2、其他会员会收到AB优惠券和短信通知\n基于优惠券领取列表校验（未使用、已使用）即：券只要在领取列表里有过领取记录，就会被过滤掉\n3、 计划详情回显过滤条件：指定优惠券信息、短信效果监测时间\n包含发优惠券、发短信动作", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1600, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "人群营销-新建运营计划，发短信支持过滤X天已发短信或打电话会员", "system": "NR商户后台", "module": "运营计划", "case_scene_id": null, "premise": "1、已勾选过滤短信2天+语音会员7天；\n2、001会员1 天内已接收过短信，8天接收过语音；\n3、002会员3 天内已接收过短信，2天接收过语音；", "test_steps": "1、周倩雯\n2、运营计划\n3、需求名称：【【510273一品】【运营计划】：会员画像里面发送的短信增加短信营销分析】\nhttps://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001066316\n【【510273一品】【运营计划】：数仓支持-会员画像里面发送的短信增加短信营销分析】\nhttps://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001067137\n4、开发人员：赵运、杨新城\n5、系统：NR商户后台\n6、模块：运营计划\n7、测试场景\n8、运营活动配置相关\n9、运营计划-发短信\n10、场景：人群营销-新建运营计划，发短信支持过滤X天已发短信或打电话会员\n11、前提：\n1、已勾选过滤短信2天+语音会员7天；\n2、001会员1 天内已接收过短信，8天接收过语音；\n3、002会员3 天内已接收过短信，2天接收过语音；\n12、操作步骤：\n1、NR商户后台-营销-营销管理-人群营销-新建运营计划，其他字段填写完整后保存\n2、核对运营计划结果\n3、核对运营计划详情页", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "2.1、001会员不收到短信，收到语音电话\n2.2、002会员收到短信，不收到语音电话\n3、 计划详情回显过滤条件：发短信动作 ，过滤短信会员+语音会员", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1606, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "发送统计&发送优惠券转化统计&优惠券发送明细\n数据统计正确", "system": "NR商户后台", "module": "运营计划", "case_scene_id": null, "premise": "", "test_steps": "1、周倩雯\n2、运营计划\n3、需求名称：【【510273一品】【运营计划】：会员画像里面发送的短信增加短信营销分析】\nhttps://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001066316\n【【510273一品】【运营计划】：数仓支持-会员画像里面发送的短信增加短信营销分析】\nhttps://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001067137\n4、开发人员：赵运、杨新城\n5、系统：NR商户后台\n6、模块：运营计划\n7、测试场景\n8、报表相关\n9、营销-营销管理-人群营销-操作查看详情运营效果\n10、新增TAB\n11、【发券转化效果】\n12、场景：发送统计&发送优惠券转化统计&优惠券发送明细\n数据统计正确\n13、操作步骤：\n1、营销-营销管理-人群营销-操作详情-发券转化效果核对报表数据", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "各项指标统计正确；", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752567196351}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715161316352|Thread-68 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.07848119735717773 秒
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715161316354|Thread-66 (process_request_thread):***********|INFO|userRequestMiddleware.py:134|当前请求返回：{"data": {"pagenum": 1, "pagesize": 10, "total": 14, "data": [{"is_active": true, "id": 1490, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "删除会员维护任务", "system": "商户后台+随心看客户端", "module": "会员维护", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-会员维护\n2、删除各种状态下的会员维护任务\n3、随心看客户端任务中心+店员圈选择任务进入任务详情", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：所有状态的任务均可逻辑删除（二次确认弹框），删除后页面不可见\n2：随心看客户端任务中心+店员圈选择任务进入任务详情提示任务已失效\n3：参与维护任务的会员详情页不可见已删除的维护任务，添加维护任务记录时，不可见自动勾选当前任务已完成的选项\n4：B段维护记录明细，会员详情下的维护记录均展示", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1499, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "新增批量发券任务", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "有资源权限", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择发券通知-勾选短信-优惠券信息-优惠券券码\n3、选择指定优惠券券码展示的优惠券\n4、选择短信效果监测时间段\n5、提交任务", "developer": "", "requirement_id": "", "requirement": "062新增批量发券任务需求", "expected_result": "1：活动创建成功，且发送时间展示位置移动至发送对象下\n2：优惠券券码】变量在短信输入框插入后的文案由【优惠券码】占位7位（本期无法实现占位10位）\n3：选择优惠券码的情况下，指定优惠券支持多选，最多十张，必选，数据源为全部的优惠券\n4：新增短信效果监测时间,必填,与是否勾选短信有关系，勾选才展示，默认为当前时间为开始时间，结束时间为七天后的当前时间，可自由修改，与发送时间无逻辑关联关系", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-24T14:17:26", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1502, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "查看批量发券任务", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "已创建任务", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择任务，点击查看详情按钮进入活动详情页", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：顶部栏展示短信效果监测时间\n2：指定优惠券券码展示在哪里查看呢-无地方可看\n3：发送短信内容正常展示", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1505, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "活动优惠券发送+短信发放数据正确", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "已创建任务", "test_steps": "1、进入商户后台-券营销-批量发券\n2、选择发送优惠券，选择短信勾选优惠券券码，并选择定优惠券券码展示（与发放优惠券有交叉的优惠券）\n3、选择短信效果监测时间段\n4、提交任务并执行发放", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：按正确人数发送优惠券张数正确\n2：短信发放正确，且短信里的优惠券码与选择定优惠券券码相匹配（所选优惠券在该会员下有未使用的优惠券的集合，如果同一张优惠券有多张，则只发最早发送的那张优惠券的券码）\n3：正确获取在短信监控时间段内的短信报表相关数据", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1512, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "短信回头效果", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "已创建任务，且勾选了短信，并发送优惠券和短信", "test_steps": "1、成功创建含短信且设置了监控时间的批量发券任务\n2、发送优惠券和短信成功\n3、查看短信回头效果报表", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：展示短信回头效果页签\n2：发送统计，发送详情，，短信费用统计不受监测时间影响，受监测时间影响（转化效果，回头订单明细）数据均计算正确，", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1573, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "活动列表增加展示短信回头效果数据列及汇总展示", "system": "商户后台", "module": "券营销", "case_scene_id": null, "premise": "已创建任务，且勾选了短信，并发送优惠券和短信", "test_steps": "1、进入商户后台-券营销-查询任务\n2、查看列表汇总数据并点击复制按钮\n3、查看列表字段数据", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "1：汇总数据新增字段短信回头会员数，短信回头销售额计算及展示正确\n2：复制数据准确\n3：列表数据新增字段短信回头会员数，短信回头销售额：活动回头订单销售总额等字段正确\n4：前端调整【短信通知人数】列标题为【短信通知情况】，并调整位置至【用券订单毛利率】数据列后展示", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1589, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "操作推广扫码-H5，领券中心支持按照每周【周一 09:00-09:20】按周期重复", "system": "商户后台+小程序+H5", "module": "领券中心", "case_scene_id": null, "premise": "", "test_steps": "1、NR商户后台-营销-营销管理-场景营销-其他场景-领券中心-添加优惠券，领取时间下勾选按周期重复，每周【周一\n2、00-\n3、59】\n4、C端H5【周一\n5、59】去我的服务-领券中心查询该优惠券，核对结果\n6、C端H5【周一\n7、01】去我的服务-领券中心查询该优惠券，核对结果\n8、C端H5【周二\n9、21】去我的服务-领券中心查询该优惠券，核对结果", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "2、该券不可见\n3、可查看&领取该券；\n4、该券不可见；", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1595, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "人群营销-新建运营计划，发优惠券支持过滤1、3、5、7天已发券会员", "system": "NR商户后台", "module": "运营计划", "case_scene_id": null, "premise": "001会员3天内已发送过A优惠券\n002会员3天内已发送过B优惠券\n003会员3天内已发送过A+B优惠券\n已配置短信（已配置白名单）", "test_steps": "1、周倩雯\n2、运营计划\n3、需求名称：【【510273一品】【运营计划】：会员画像里面发送的短信增加短信营销分析】\nhttps://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001066316\n【【510273一品】【运营计划】：数仓支持-会员画像里面发送的短信增加短信营销分析】\nhttps://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001067137\n4、开发人员：赵运、杨新城\n5、系统：NR商户后台\n6、模块：运营计划\n7、测试场景\n8、运营活动配置相关\n9、运营计划-发优惠券\n10、场景：人群营销-新建运营计划，发优惠券支持过滤1、3、5、7天已发券会员\n11、前提：001会员3天内已发送过A优惠券\n002会员3天内已发送过B优惠券\n003会员3天内已发送过A+B优惠券\n已配置短信（已配置白名单）\n12、操作步骤：\n1、NR商户后台-营销-营销管理-人群营销-新建运营计划，发优惠券下配置过滤3天A、B券，勾选发券通知，其他字段填写完整后保存\n2、核对运营计划结果\n3、核对运营计划详情页", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "2.1、\n001收到B优惠券+短信\n002会员收到A优惠券+短信\n003会员不收到优惠券和短信通知，\n2.2、其他会员会收到AB优惠券和短信通知\n基于优惠券领取列表校验（未使用、已使用）即：券只要在领取列表里有过领取记录，就会被过滤掉\n3、 计划详情回显过滤条件：指定优惠券信息、短信效果监测时间\n包含发优惠券、发短信动作", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1600, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "人群营销-新建运营计划，发短信支持过滤X天已发短信或打电话会员", "system": "NR商户后台", "module": "运营计划", "case_scene_id": null, "premise": "1、已勾选过滤短信2天+语音会员7天；\n2、001会员1 天内已接收过短信，8天接收过语音；\n3、002会员3 天内已接收过短信，2天接收过语音；", "test_steps": "1、周倩雯\n2、运营计划\n3、需求名称：【【510273一品】【运营计划】：会员画像里面发送的短信增加短信营销分析】\nhttps://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001066316\n【【510273一品】【运营计划】：数仓支持-会员画像里面发送的短信增加短信营销分析】\nhttps://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001067137\n4、开发人员：赵运、杨新城\n5、系统：NR商户后台\n6、模块：运营计划\n7、测试场景\n8、运营活动配置相关\n9、运营计划-发短信\n10、场景：人群营销-新建运营计划，发短信支持过滤X天已发短信或打电话会员\n11、前提：\n1、已勾选过滤短信2天+语音会员7天；\n2、001会员1 天内已接收过短信，8天接收过语音；\n3、002会员3 天内已接收过短信，2天接收过语音；\n12、操作步骤：\n1、NR商户后台-营销-营销管理-人群营销-新建运营计划，其他字段填写完整后保存\n2、核对运营计划结果\n3、核对运营计划详情页", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "2.1、001会员不收到短信，收到语音电话\n2.2、002会员收到短信，不收到语音电话\n3、 计划详情回显过滤条件：发短信动作 ，过滤短信会员+语音会员", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}, {"is_active": true, "id": 1606, "case_report_id": 44, "business_line": "6", "iteration_name": "【V2.4.11.10】海川日（贵州一品+私域抽佣+会员选品三期）-0512", "case_scene": "发送统计&发送优惠券转化统计&优惠券发送明细\n数据统计正确", "system": "NR商户后台", "module": "运营计划", "case_scene_id": null, "premise": "", "test_steps": "1、周倩雯\n2、运营计划\n3、需求名称：【【510273一品】【运营计划】：会员画像里面发送的短信增加短信营销分析】\nhttps://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001066316\n【【510273一品】【运营计划】：数仓支持-会员画像里面发送的短信增加短信营销分析】\nhttps://www.tapd.cn/tapd_fe/61969829/story/detail/1161969829001067137\n4、开发人员：赵运、杨新城\n5、系统：NR商户后台\n6、模块：运营计划\n7、测试场景\n8、报表相关\n9、营销-营销管理-人群营销-操作查看详情运营效果\n10、新增TAB\n11、【发券转化效果】\n12、场景：发送统计&发送优惠券转化统计&优惠券发送明细\n数据统计正确\n13、操作步骤：\n1、营销-营销管理-人群营销-操作详情-发券转化效果核对报表数据", "developer": "", "requirement_id": "", "requirement": "", "expected_result": "各项指标统计正确；", "case_type": "2", "keyword": "", "operator": null, "operator_result": "1", "operator_result_remark": null, "file_url": null, "create_time": "2025-06-19T11:06:23", "update_time": "2025-06-19T11:06:22", "update_person": null, "businessName": "营销"}]}, "meta": {"msg": "查询成功", "status": 200}, "timestamp": 1752567196354}
|hydee-auto_server|************|99f57f119e6c415bac72bc7f19e505b0|||20250715161316355|Thread-66 (process_request_thread):***********|INFO|userRequestMiddleware.py:143|当前请求耗时：0.08325386047363281 秒
