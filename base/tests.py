
from datetime import datetime, timedelta
from django.utils import timezone


def get_week_of_year(date):
    # 找到当前年份的第一个周五
    year_start = date.replace(month=1, day=1)
    print(year_start)
    first_friday = year_start + timedelta(days=(4 - year_start.weekday() + 7) % 7)  # 确保总是得到正数天数差
    print(first_friday)
    # 找到给定日期或之前的最近一个周五
    this_friday = date - timedelta(days=(date.weekday() - 4 + 7) % 7)
    print(this_friday)
    # 计算周数（从第一个周五开始，加1以满足用户从1开始计数的期望）
    week_number = ((this_friday - first_friday).days // 7) + 1

    print((date.month - 1) // 3 + 1)

    return week_number

# 定义一个函数来解析日期字符串
def parse_date(date_str):
    # 假设日期格式为 '2024Q1第8周'
    year = int(date_str[:4])  # 提取年份
    quarter = int(date_str[5])  # 提取季度
    week = int(date_str.split('第')[1].split('周')[0])  # 提取周数
    return (year, quarter, week)


def get_week_of_year(date):
    # 找到当前年份的第一个周五
    year_start = date.replace(month=1, day=1)
    first_friday = year_start + timedelta(days=(4 - year_start.weekday() + 7) % 7)

    # 判断当前日期是否是周五
    if date.weekday() == 4:  # 如果是周五，则将其划分到下一周
        this_friday = date + timedelta(days=7)
    else:  # 否则，找到最近的下一个周五
        # this_friday = date - timedelta(days=(date.weekday() - 4 + 7) % 7)
        this_friday = date + timedelta(days=(4 - date.weekday() + 7) % 7)

    # 计算周数（从第一个周五开始，加1以满足用户从1开始计数的期望）
    week_number = ((this_friday - first_friday).days // 7) + 1
    print(week_number)
    return week_number


if __name__ == '__main__':
    # date_str = '2025-04-10'
    # date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
    # print(get_week_of_year(date_obj))
    # # 示例数据
    # dates = ['2024Q2第20周', '2024Q1第8周', '2024Q3第10周']
    #
    # # 按时间升序排序
    # sorted_dates = sorted(dates, key=parse_date, reverse=False)
    #
    # print("sorted_dates:", sorted_dates)
    # datetime.strptime(end_date_str, '%Y-%m-%d').date()
    start_date_str ='2025-03-20'
    date=datetime.strptime(start_date_str, '%Y-%m-%d').date()
    get_week_of_year(date)