""" Current project exception classes.
"""
from django.http import Http404

from rest_framework import exceptions
from rest_framework.exceptions import PermissionDenied, ErrorDetail
from rest_framework.response import Response
from rest_framework.views import set_rollback

from scaffold.exceptions.exceptions import AppError

import core.exceptions


class AppErrors(core.exceptions.AppErrors):
    """ 本应用的具体错误列表 """
    ERROR_USER_NAME_OR_PWD_EMPTY = AppError('HD0001', '用户名或密码不能为空')
    ERROR_USER_NAME_OR_PWD = AppError('HD0002', '用户名或密码错误')

    ERROR_USER_IS_EXISTS = AppError('HD2001', '用户已存在，创建失败')
    ERROR_USER_IS_NOT_EXISTS = AppError('HD2002', '用户不存在，删除失败')
    ERROR_ROLE_IS_EXISTS = AppError('HD2003', '角色已存在，请重新命名')
    ERROR_ROLE_IS_BIND_USER = AppError('HD2004', '角色已被分配给用户，请解除绑定后再删除')
    ERROR_ROLE_IS_NOT_EXISTS = AppError('HD2005', '角色不存在，删除失败')
    ERROR_USER_STATISTIC_TYPE = AppError('HD2006', 'type 类型不存在')


# pylint: disable=unused-argument
def exception_handler(exc, context):
    """
    Returns the response that should be used for any given exception.

    By default we handle the REST framework `APIException`, and also
    Django's built-in `Http404` and `PermissionDenied` exceptions.

    Any unhandled exceptions may return `None`, which will cause a 500 error
    to be raised.
    """
    if isinstance(exc, Http404):
        exc = exceptions.NotFound()
    elif isinstance(exc, PermissionDenied):
        exc = exceptions.PermissionDenied()

    if isinstance(exc, exceptions.APIException):
        headers = {}
        if getattr(exc, 'auth_header', None):
            headers['WWW-Authenticate'] = exc.auth_header
        if getattr(exc, 'wait', None):
            headers['Retry-After'] = '%d' % exc.wait

        if isinstance(exc.detail, ErrorDetail):
            if exc.detail.code == 'not_authenticated':
                exc.status_code = 401
                data = dict(
                    ok=False,
                    msg=str(exc.detail),
                    errcode=10006
                )
            else:
                data = dict(
                    ok=False,
                    msg=exc.detail
                )
        elif isinstance(exc.detail, (list, dict)):
            data = dict(
                ok=False,
                msg=str(exc.detail),
                data=exc.detail
            )
        else:
            data = dict(
                ok=False,
                msg=exc.detail
            )

        set_rollback()
        return Response(data, status=exc.status_code, headers=headers)

    return None
