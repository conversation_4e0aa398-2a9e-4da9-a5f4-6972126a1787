from django.db import models
from scaffold.models.abstract.meta import ActiveModel, HierarchicalModel

from core.models import DateModel


class UserInfo(DateModel, ActiveModel):
    user_id = models.AutoField(primary_key=True, verbose_name='唯一主键，用户ID')
    username = models.CharField(max_length=128, unique=True, verbose_name='唯一，用户名')
    chinese_name = models.CharField(max_length=128, verbose_name='姓名', blank=True, null=True)
    password = models.CharField(max_length=256, verbose_name='密码')
    email = models.EmailField(null=True, verbose_name='邮件地址，允许为空')
    mobile = models.CharField(max_length=32, null=True, verbose_name='手机号，允许为空')
    user_status = models.CharField(max_length=1, verbose_name='用户状态，0失效，1生效')  # 0失效，1生效
    role_id = models.CharField(max_length=32, null=True, verbose_name='用户状态，0失效，1生效')
    staff_no = models.Char<PERSON>ield(max_length=30, null=True, verbose_name='员工工号，允许为空')

    class Meta(object):
        # 定义表名
        db_table = "user_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = '用户信息表'


# 角色信息表
class RoleInfo(DateModel, ActiveModel):
    role_id = models.AutoField(primary_key=True)
    role_name = models.CharField(max_length=128)
    role_desc = models.TextField()
    role_rights = models.TextField()

    def __str__(self):
        return self.role_name

    class Meta(object):
        # 定义表名
        db_table = "role_info"
        # 定义表数据排序方式，当前以更新时间为倒叙排列
        ordering = ['-update_time']
        # 表别名
        verbose_name = '角色信息表'


# 权限信息表
class RightInfo(ActiveModel):
    right_id = models.AutoField(primary_key=True)
    right_name = models.CharField(max_length=256)
    right_path = models.CharField(max_length=128)
    parent_right_id = models.IntegerField(null=True)
    grandpa_right_id = models.IntegerField(null=True)
    right_level = models.CharField(max_length=8, null=True)

    def __str__(self):
        return self.right_name

    class Meta(object):
        # 定义表名
        db_table = "right_info"
        # 表别名
        verbose_name = '菜单栏信息表'


class UserToken(models.Model):
    user = models.CharField(max_length=512)
    token = models.CharField(max_length=512)
    create_time = models.DateTimeField(auto_now=True)
    ip = models.CharField(max_length=64, null=True)
    channel = models.IntegerField(default=0, verbose_name='来源 0：PC 1：H5')

    class Meta(object):
        # 定义表名
        db_table = "user_token"
        # 表别名
        verbose_name = '用户登录存储的token信息表'
