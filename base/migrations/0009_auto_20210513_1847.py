# Generated by Django 3.0.6 on 2021-05-13 18:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('base', '0008_auto_20210510_1637'),
    ]

    operations = [
        migrations.DeleteModel(
            name='AndroidCaseInfo',
        ),
        migrations.DeleteModel(
            name='AndroidElementInfo',
        ),
        migrations.DeleteModel(
            name='AppCaseRunningRecord',
        ),
        migrations.DeleteModel(
            name='ApplicationInfo',
        ),
        migrations.DeleteModel(
            name='AssertInfo',
        ),
        migrations.DeleteModel(
            name='AssertResultInfo',
        ),
        migrations.DeleteModel(
            name='CaseFormationInfo',
        ),
        migrations.DeleteModel(
            name='CaseInfo',
        ),
        migrations.DeleteModel(
            name='CaseRunningRecord',
        ),
        migrations.DeleteModel(
            name='CaseRunPreRecord',
        ),
        migrations.DeleteModel(
            name='ChunkInfo',
        ),
        migrations.DeleteModel(
            name='DataBaseInfo',
        ),
        migrations.DeleteModel(
            name='DeviceInfo',
        ),
        migrations.DeleteModel(
            name='ElementInfo',
        ),
        migrations.DeleteModel(
            name='EmailModelInfo',
        ),
        migrations.DeleteModel(
            name='InterfaceInfo',
        ),
        migrations.DeleteModel(
            name='LocustReport',
        ),
        migrations.DeleteModel(
            name='LocustTaskRecord',
        ),
        migrations.DeleteModel(
            name='LogRecord',
        ),
        migrations.DeleteModel(
            name='PageInfo',
        ),
        migrations.DeleteModel(
            name='ParameterInfo',
        ),
        migrations.DeleteModel(
            name='ProjectInfo',
        ),
        migrations.DeleteModel(
            name='PublishingTaskRunningRecode',
        ),
        migrations.DeleteModel(
            name='SceneInfo',
        ),
        migrations.DeleteModel(
            name='SceneRelation',
        ),
        migrations.DeleteModel(
            name='SceneRunningRecord',
        ),
        migrations.DeleteModel(
            name='TaskInfo',
        ),
        migrations.DeleteModel(
            name='TaskRunningRecord',
        ),
        migrations.DeleteModel(
            name='WebCaseInfo',
        ),
        migrations.DeleteModel(
            name='WebCaseRunningRecord',
        ),
        migrations.AlterField(
            model_name='roleinfo',
            name='create_time',
            field=models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='roleinfo',
            name='creater',
            field=models.CharField(max_length=128, verbose_name='创建人'),
        ),
        migrations.AlterField(
            model_name='roleinfo',
            name='update_person',
            field=models.CharField(max_length=128, verbose_name='修改人'),
        ),
        migrations.AlterField(
            model_name='roleinfo',
            name='update_time',
            field=models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='修改时间'),
        ),
        migrations.AlterField(
            model_name='userinfo',
            name='create_time',
            field=models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='userinfo',
            name='update_time',
            field=models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='修改时间'),
        ),
    ]
