# Generated by Django 3.0.6 on 2021-04-30 16:23

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AndroidCaseInfo',
            fields=[
                ('androidCase_id', models.AutoField(primary_key=True, serialize=False)),
                ('androidCase_name', models.Char<PERSON><PERSON>(max_length=256)),
                ('androidCase_describe', models.TextField()),
                ('androidCase_level', models.Char<PERSON>ield(max_length=32)),
                ('test_device', models.CharField(max_length=32)),
                ('test_application', models.Char<PERSON>ield(max_length=32)),
                ('androidCase_formation', models.TextField()),
                ('is_assert', models.BooleanField()),
                ('asserts', models.TextField()),
                ('recent_img_url', models.Char<PERSON>ield(default='', max_length=512)),
                ('create_time', models.DateTime<PERSON>ield()),
                ('creater', models.<PERSON>r<PERSON><PERSON>(max_length=128)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.<PERSON><PERSON><PERSON><PERSON>(max_length=128)),
            ],
            options={
                'verbose_name': '安卓案例信息表',
                'db_table': 'android_case_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='AndroidElementInfo',
            fields=[
                ('appElement_id', models.AutoField(primary_key=True, serialize=False)),
                ('appElement_name', models.CharField(max_length=256)),
                ('find_appElement_type', models.CharField(max_length=32)),
                ('appElement_value', models.TextField()),
                ('operate_appElement_type', models.CharField(max_length=32)),
                ('send_value', models.CharField(max_length=512, null=True)),
                ('appElement_exists', models.CharField(max_length=32, null=True)),
                ('appElement_not_exists', models.CharField(max_length=32, null=True)),
                ('sleep_time', models.CharField(default='0', max_length=8)),
                ('sweep_page', models.CharField(default='0', max_length=8)),
                ('remark', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': '安卓元素操作表',
                'db_table': 'android_element_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='AppCaseRunningRecord',
            fields=[
                ('app_case_record_id', models.AutoField(primary_key=True, serialize=False)),
                ('app_case_id', models.CharField(max_length=128)),
                ('app_case_name', models.CharField(max_length=256)),
                ('assert_result', models.BooleanField()),
                ('total_time', models.CharField(max_length=128)),
                ('run_result', models.CharField(max_length=128)),
                ('parent_id', models.CharField(default='', max_length=256)),
                ('running_log', models.TextField()),
            ],
            options={
                'verbose_name': '安卓案例执行记录表',
                'db_table': 'app_case_running_record',
            },
        ),
        migrations.CreateModel(
            name='ApplicationInfo',
            fields=[
                ('application_id', models.AutoField(primary_key=True, serialize=False)),
                ('application_name', models.CharField(max_length=256)),
                ('application_packageName', models.CharField(max_length=512)),
                ('application_activityName', models.CharField(max_length=512)),
                ('remark', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': '待测应用信息表',
                'db_table': 'application_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='AssertInfo',
            fields=[
                ('assert_id', models.AutoField(primary_key=True, serialize=False)),
                ('assert_type', models.CharField(max_length=1)),
                ('assert_operator', models.CharField(max_length=8)),
                ('assert_result', models.TextField()),
                ('assert_database', models.CharField(max_length=128, null=True)),
                ('assert_sql', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.CharField(max_length=128)),
            ],
            options={
                'db_table': 'assert_info',
            },
        ),
        migrations.CreateModel(
            name='AssertResultInfo',
            fields=[
                ('assert_id', models.AutoField(primary_key=True, serialize=False)),
                ('assert_type', models.CharField(max_length=1)),
                ('assert_operator', models.CharField(max_length=16)),
                ('return_value', models.TextField()),
                ('expect_result', models.TextField()),
                ('operation_value', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': '断言信息表',
                'db_table': 'assert_result_info',
                'ordering': ['-create_time'],
            },
        ),
        migrations.CreateModel(
            name='CaseFormationInfo',
            fields=[
                ('case_formation_id', models.AutoField(primary_key=True, serialize=False)),
                ('formation_type', models.CharField(max_length=1)),
                ('element_id', models.CharField(max_length=128, null=True)),
                ('chunk_id', models.CharField(max_length=128, null=True)),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
            ],
            options={
                'db_table': 'case_formation_info',
            },
        ),
        migrations.CreateModel(
            name='CaseInfo',
            fields=[
                ('case_id', models.AutoField(primary_key=True, serialize=False)),
                ('interface_id', models.CharField(max_length=32)),
                ('case_name', models.CharField(max_length=256)),
                ('case_describe', models.TextField()),
                ('is_init', models.BooleanField()),
                ('init_database', models.CharField(max_length=32, null=True)),
                ('init_sql', models.TextField()),
                ('is_back', models.BooleanField()),
                ('back_sql', models.TextField()),
                ('is_encrypt', models.BooleanField()),
                ('interface_data', models.TextField()),
                ('is_assert', models.BooleanField(default=False)),
                ('case_level', models.CharField(default='低', max_length=16)),
                ('asserts', models.TextField()),
                ('case_type', models.CharField(default='API', max_length=16)),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': '案例信息表',
                'db_table': 'case_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='CaseRunningRecord',
            fields=[
                ('case_record_id', models.AutoField(primary_key=True, serialize=False)),
                ('case_id', models.CharField(max_length=128)),
                ('case_name', models.CharField(max_length=256)),
                ('assert_result', models.BooleanField()),
                ('belong_task', models.BooleanField()),
                ('total_time', models.CharField(max_length=128)),
                ('run_result', models.CharField(max_length=128)),
                ('parent_id', models.CharField(default='', max_length=256)),
                ('running_log', models.TextField()),
            ],
            options={
                'verbose_name': '案例执行记录表',
                'db_table': 'case_running_record',
            },
        ),
        migrations.CreateModel(
            name='CaseRunPreRecord',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pre_record_id', models.CharField(max_length=64)),
                ('environment_id', models.CharField(max_length=1)),
                ('case_type', models.CharField(max_length=16)),
                ('run_type', models.CharField(max_length=16)),
                ('case_id', models.CharField(max_length=64)),
                ('case_name', models.CharField(max_length=256)),
                ('interface_agreement', models.CharField(max_length=32)),
                ('ip', models.CharField(max_length=128)),
                ('port', models.CharField(max_length=8)),
                ('interface_way', models.CharField(max_length=128)),
                ('interface_address', models.TextField()),
                ('header', models.TextField()),
                ('interface_data', models.TextField()),
                ('is_init', models.BooleanField()),
                ('init_database', models.CharField(max_length=64, null=True)),
                ('init_sql', models.TextField()),
                ('is_back', models.BooleanField()),
                ('back_sql', models.TextField()),
                ('is_encrypt', models.BooleanField()),
                ('asserts', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
            ],
            options={
                'db_table': 'case_run_pre_record',
            },
        ),
        migrations.CreateModel(
            name='ChunkInfo',
            fields=[
                ('chunk_id', models.AutoField(primary_key=True, serialize=False)),
                ('chunk_name', models.CharField(max_length=256)),
                ('chunk_describe', models.TextField()),
                ('element_type', models.CharField(default='WEB', max_length=32)),
                ('element_pool', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': 'UI块信息表',
                'db_table': 'chunk_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='DataBaseInfo',
            fields=[
                ('data_base_id', models.AutoField(primary_key=True, serialize=False)),
                ('data_base_name', models.CharField(max_length=256)),
                ('data_base_type', models.CharField(max_length=16)),
                ('ip_address', models.CharField(max_length=128)),
                ('data_base_port', models.CharField(max_length=10, null=True)),
                ('db_account', models.CharField(max_length=128)),
                ('db_pwd', models.CharField(max_length=256)),
                ('db_name', models.CharField(max_length=256)),
                ('remark', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': '数据源登记表',
                'db_table': 'data_base_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='DeviceInfo',
            fields=[
                ('device_id', models.AutoField(primary_key=True, serialize=False)),
                ('device_name', models.CharField(max_length=256)),
                ('platform_version', models.CharField(max_length=32)),
                ('device_udid', models.CharField(max_length=64)),
                ('connect_way', models.CharField(max_length=64)),
                ('remark', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': '设备信息表',
                'db_table': 'device_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='ElementInfo',
            fields=[
                ('element_id', models.AutoField(primary_key=True, serialize=False)),
                ('page_id', models.CharField(max_length=32)),
                ('element_name', models.CharField(max_length=256)),
                ('find_element_type', models.CharField(max_length=32)),
                ('element_value', models.TextField()),
                ('element_num', models.CharField(default='0', max_length=4)),
                ('operate_element_type', models.CharField(max_length=32)),
                ('send_value', models.CharField(max_length=128)),
                ('remark', models.TextField()),
                ('label_name', models.CharField(default='', max_length=128)),
                ('sleep_time', models.CharField(default='0', max_length=4)),
                ('sweep_page', models.CharField(default='0', max_length=4)),
                ('is_switch_window', models.CharField(default='0', max_length=1)),
                ('window_value', models.CharField(default='', max_length=512)),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': 'WEB元素操作信息表',
                'db_table': 'element_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='EmailModelInfo',
            fields=[
                ('email_model_id', models.AutoField(primary_key=True, serialize=False)),
                ('email_model_name', models.CharField(max_length=256)),
                ('email_subject', models.TextField()),
                ('email_TO', models.TextField()),
                ('email_CC', models.TextField()),
                ('email_content', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': '邮件模板信息表',
                'db_table': 'email_model_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='InterfaceInfo',
            fields=[
                ('interface_id', models.AutoField(primary_key=True, serialize=False)),
                ('project_id', models.CharField(max_length=32)),
                ('interface_name', models.CharField(max_length=256)),
                ('interface_agreement', models.CharField(max_length=16)),
                ('interface_way', models.CharField(max_length=128)),
                ('interface_address', models.CharField(max_length=512)),
                ('header', models.TextField()),
                ('remark', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': '接口信息表',
                'db_table': 'interface_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='LocustReport',
            fields=[
                ('record_id', models.AutoField(primary_key=True, serialize=False)),
                ('locust_file_name', models.CharField(max_length=256)),
                ('report_content', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=16)),
            ],
            options={
                'verbose_name': '性能测试任务报告表',
                'db_table': 'locust_report',
                'ordering': ['-create_time'],
            },
        ),
        migrations.CreateModel(
            name='LocustTaskRecord',
            fields=[
                ('locust_task_id', models.AutoField(primary_key=True, serialize=False)),
                ('locust_task_name', models.CharField(max_length=256)),
                ('locust_task_type', models.CharField(max_length=1)),
                ('case_id', models.CharField(max_length=512)),
                ('number_of_user', models.CharField(max_length=16)),
                ('user_spawned_second', models.CharField(max_length=16)),
                ('run_time', models.CharField(max_length=16)),
                ('locust_task_status', models.CharField(default='B', max_length=1)),
                ('remark', models.TextField()),
                ('locust_report_id', models.CharField(default='', max_length=32)),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=16)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.CharField(max_length=16)),
            ],
            options={
                'verbose_name': '性能测试任务记录表',
                'db_table': 'locust_task_record',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='LogRecord',
            fields=[
                ('log_id', models.AutoField(primary_key=True, serialize=False)),
                ('log_type', models.CharField(max_length=1)),
                ('running_log_type', models.CharField(max_length=1, null=True)),
                ('running_log_id', models.CharField(max_length=32, null=True)),
                ('log_content', models.TextField()),
                ('person', models.CharField(max_length=128)),
                ('temp_time', models.DateTimeField()),
            ],
            options={
                'verbose_name': '日志记录表',
                'db_table': 'log_record',
                'ordering': ['-temp_time'],
            },
        ),
        migrations.CreateModel(
            name='PageInfo',
            fields=[
                ('page_id', models.AutoField(primary_key=True, serialize=False)),
                ('page_name', models.CharField(max_length=256)),
                ('page_type', models.CharField(max_length=16)),
                ('page_url', models.TextField()),
                ('remark', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': '页面信息表',
                'db_table': 'page_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='ParameterInfo',
            fields=[
                ('parameter_id', models.AutoField(primary_key=True, serialize=False)),
                ('parameter_type', models.CharField(default='1', max_length=1)),
                ('database_id', models.CharField(max_length=64, null=True)),
                ('parameter_name', models.CharField(max_length=256)),
                ('produce_parameter', models.TextField()),
                ('uat_parameter', models.TextField()),
                ('test_parameter', models.TextField()),
                ('remark', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': '参数数据表',
                'db_table': 'parameter_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='ProjectInfo',
            fields=[
                ('project_id', models.AutoField(primary_key=True, serialize=False)),
                ('project_name', models.CharField(max_length=256)),
                ('ip', models.CharField(max_length=128)),
                ('port', models.CharField(max_length=16, null=True)),
                ('remark', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': '项目信息表',
                'db_table': 'project_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='PublishingTaskRunningRecode',
            fields=[
                ('record_id', models.AutoField(primary_key=True, serialize=False)),
                ('batch_number', models.CharField(max_length=128)),
                ('environment', models.CharField(max_length=8)),
                ('package', models.TextField()),
                ('report_content', models.TextField()),
                ('begin_time', models.DateTimeField()),
                ('end_time', models.DateTimeField(null=True)),
                ('status', models.CharField(max_length=8)),
            ],
            options={
                'verbose_name': '发版任务执行记录表',
                'db_table': 'publishing_task_running_record',
                'ordering': ['-begin_time'],
            },
        ),
        migrations.CreateModel(
            name='RightInfo',
            fields=[
                ('right_id', models.AutoField(primary_key=True, serialize=False)),
                ('right_name', models.CharField(max_length=256)),
                ('right_path', models.CharField(max_length=128)),
                ('parent_right_id', models.CharField(max_length=32, null=True)),
                ('right_level', models.CharField(max_length=8, null=True)),
            ],
            options={
                'verbose_name': '菜单栏信息表',
                'db_table': 'right_info',
            },
        ),
        migrations.CreateModel(
            name='RoleInfo',
            fields=[
                ('role_id', models.AutoField(primary_key=True, serialize=False)),
                ('role_name', models.CharField(max_length=128)),
                ('role_desc', models.TextField()),
                ('role_rights', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': '角色信息表',
                'db_table': 'role_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='SceneInfo',
            fields=[
                ('scene_id', models.AutoField(primary_key=True, serialize=False)),
                ('scene_name', models.CharField(max_length=256)),
                ('scene_describe', models.TextField()),
                ('before_param', models.TextField()),
                ('scene_relation_id', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': '场景信息表',
                'db_table': 'scene_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='SceneRelation',
            fields=[
                ('scene_relation_id', models.AutoField(primary_key=True, serialize=False)),
                ('relation_case_id', models.CharField(max_length=16)),
                ('is_get_param', models.BooleanField()),
                ('request_param', models.CharField(max_length=512)),
                ('response_param', models.CharField(max_length=512)),
                ('response_header_param', models.CharField(max_length=512)),
                ('sleep_time', models.CharField(default='0', max_length=16)),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': '场景组成关系表',
                'db_table': 'scene_relation',
                'ordering': ['-create_time'],
            },
        ),
        migrations.CreateModel(
            name='SceneRunningRecord',
            fields=[
                ('scene_record_id', models.AutoField(primary_key=True, serialize=False)),
                ('scene_id', models.CharField(max_length=128)),
                ('scene_name', models.CharField(max_length=256)),
                ('assert_result', models.BooleanField()),
                ('total_time', models.CharField(max_length=128)),
                ('run_result', models.CharField(max_length=128)),
                ('parent_id', models.CharField(default='', max_length=256)),
                ('running_log', models.TextField(default='')),
            ],
            options={
                'verbose_name': '场景执行记录表',
                'db_table': 'scene_running_record',
            },
        ),
        migrations.CreateModel(
            name='TaskInfo',
            fields=[
                ('task_id', models.AutoField(primary_key=True, serialize=False)),
                ('task_name', models.CharField(max_length=256)),
                ('environment', models.CharField(max_length=1)),
                ('case_pool', models.TextField()),
                ('task_status', models.CharField(max_length=1)),
                ('run_time', models.CharField(max_length=4)),
                ('is_send_email', models.BooleanField(default=False)),
                ('email_model_id', models.CharField(max_length=128, null=True)),
                ('recent_record_id', models.CharField(max_length=128, null=True)),
                ('task_type', models.CharField(max_length=16)),
                ('api_type', models.CharField(max_length=16)),
                ('remark', models.TextField()),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': '任务信息表',
                'db_table': 'task_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='TaskRunningRecord',
            fields=[
                ('task_record_id', models.AutoField(primary_key=True, serialize=False)),
                ('task_id', models.CharField(max_length=128)),
                ('task_name', models.CharField(max_length=256)),
                ('environment', models.CharField(max_length=1)),
                ('task_type', models.CharField(max_length=1)),
                ('test_all', models.IntegerField(default=0)),
                ('test_pass', models.IntegerField(default=0)),
                ('test_fail', models.IntegerField(default=0)),
                ('test_error', models.IntegerField(default=0)),
                ('total_time', models.CharField(max_length=128)),
                ('batch_number', models.CharField(max_length=128, null=True)),
                ('creater', models.CharField(max_length=128)),
                ('report_content', models.TextField(default='')),
                ('time_stamp', models.DateTimeField()),
            ],
            options={
                'verbose_name': '任务信息表',
                'db_table': 'task_running_record',
                'ordering': ['-time_stamp'],
            },
        ),
        migrations.CreateModel(
            name='UserInfo',
            fields=[
                ('user_id', models.AutoField(primary_key=True, serialize=False, verbose_name='唯一主键，用户ID')),
                ('username', models.CharField(max_length=128, unique=True, verbose_name='唯一，用户名')),
                ('password', models.CharField(max_length=256, verbose_name='密码')),
                ('email', models.EmailField(max_length=254, null=True, verbose_name='邮件地址，允许为空')),
                ('mobile', models.CharField(max_length=32, null=True, verbose_name='手机号，允许为空')),
                ('user_status', models.CharField(max_length=1, verbose_name='用户状态，0失效，1生效')),
                ('role_id', models.CharField(max_length=32, null=True, verbose_name='用户状态，0失效，1生效')),
                ('create_time', models.DateTimeField(verbose_name='创建时间')),
                ('creater', models.CharField(max_length=128, verbose_name='创建人')),
                ('update_time', models.DateTimeField(verbose_name='修改时间')),
                ('update_person', models.CharField(max_length=128, verbose_name='修改人')),
            ],
            options={
                'verbose_name': '用户信息表',
                'db_table': 'user_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='UserToken',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user', models.CharField(max_length=512)),
                ('token', models.CharField(max_length=512)),
                ('create_time', models.DateTimeField(auto_now=True)),
                ('ip', models.CharField(max_length=64, null=True)),
            ],
            options={
                'verbose_name': '用户登录存储的token信息表',
                'db_table': 'user_token',
            },
        ),
        migrations.CreateModel(
            name='WebCaseInfo',
            fields=[
                ('web_case_id', models.AutoField(primary_key=True, serialize=False)),
                ('web_case_name', models.CharField(max_length=256)),
                ('web_case_describe', models.TextField()),
                ('web_case_formation', models.TextField()),
                ('is_assert', models.BooleanField()),
                ('asserts', models.TextField()),
                ('web_case_level', models.CharField(default='低', max_length=64)),
                ('recent_img_url', models.CharField(max_length=256, null=True)),
                ('create_time', models.DateTimeField()),
                ('creater', models.CharField(max_length=128)),
                ('update_time', models.DateTimeField()),
                ('update_person', models.CharField(max_length=128)),
            ],
            options={
                'verbose_name': 'WEB案例信息表',
                'db_table': 'web_case_info',
                'ordering': ['-update_time'],
            },
        ),
        migrations.CreateModel(
            name='WebCaseRunningRecord',
            fields=[
                ('web_case_record_id', models.AutoField(primary_key=True, serialize=False)),
                ('web_case_id', models.CharField(max_length=128)),
                ('web_case_name', models.CharField(max_length=256)),
                ('assert_result', models.BooleanField()),
                ('total_time', models.CharField(max_length=128)),
                ('run_result', models.CharField(max_length=128)),
                ('parent_id', models.CharField(default='', max_length=256)),
                ('running_log', models.TextField()),
            ],
            options={
                'verbose_name': 'WEB案例执行记录表',
                'db_table': 'web_case_running_record',
            },
        ),
    ]
