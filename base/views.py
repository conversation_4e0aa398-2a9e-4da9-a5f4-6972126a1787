import json
import traceback

from django.core.paginator import Paginator
from django.db import connection
from rest_framework import viewsets
from rest_framework.decorators import action
from django.utils import timezone
import requests

from core.commonUtils.commonUtil import CommonUtil
from core.commonUtils.getModelInfoUtil import GetModelInfo
from core.commonUtils.jsonUtil import *
from core.commonUtils.menuUtil import *
from core.utils import ResponseUtils, UserUtils, DateUtils
from . import models as m
from . import serializers as s
from .exceptions import AppErrors
import os
from django.conf import settings
import re

# 日志对象实例化
from core.commonUtils.logger import Logger

logger =  Logger.get_logger()


class UserInfoViewSet(viewsets.ModelViewSet):
    queryset = m.UserInfo.objects.all()
    serializer_class = s.UserInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    # 用户登录
    @action(methods=['POST'], detail=False)
    def login(self, request):
        ip = request.META.get("REMOTE_ADDR")
        if request.method == "POST":
            temp = json.loads(request.body)
            user_name = temp["username"]
            password = temp["password"]
            try:
                user_info = UserInfo.objects.get(username=user_name, password=password, is_active=True)
            except UserInfo.DoesNotExist:
                return ResponseUtils.return_fail(AppErrors.ERROR_USER_NAME_OR_PWD)
            if not all([user_name, password]):
                return ResponseUtils.return_fail(AppErrors.ERROR_USER_NAME_OR_PWD_EMPTY)
            else:
                # 生成token
                temp = str(round(time.time() * 1000)) + user_name + password + CommonUtil().get_random_secure_key()
                token = CommonUtil().str2Md5(temp)
                create_time = timezone.now()
                user_token = UserToken.objects.filter(user=user_name, channel=0).order_by('-create_time').first()
                if user_token:  # 存在token，更新原有的
                    UserToken.objects.filter(id=user_token.id, user=user_name, channel=0).update(token=token, create_time=create_time)
                else:  # 不存在，创建新的token
                    new_user_token = UserToken(user=user_name, token=token, channel=0, create_time=create_time, ip=ip)
                    new_user_token.save()
                # 日志记录
                content = "用户 %s 登录成功" % user_name
                CommonUtil().recordSystemLog(user_name, content)  # 以系统日志类型记录到数据库
                CommonUtil().updateTestHeader()  # 替换测试环境请求头
                CommonUtil().updateUatHeader()  # 替换预发环境下请求头
                return ResponseUtils.return_success("登录成功", {"id": user_info.user_id, "token": token, "user_name": user_name, "role_id": user_info.role_id,"chinese_name":user_info.chinese_name,"staff_no":user_info.staff_no})

    # 获取微信授权信息
    @action(methods=['GET'], detail=False)
    def get_authInfo(self, request):
        logger.info("开始请求获取鉴权信息接口")
        ip = request.META.get("REMOTE_ADDR")
        code = request.GET.get("code")
        state = request.GET.get("state","H5-API")
        # 获取微信授权信息
        address = settings.CALLBACK_URL
        logger.info(f"请求接口: {address}")
        if code is None:
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION)
        params = {
            "code":code,
            "state":state
        }
        logger.info(f"请求参数: {params}")
        req = requests.get(url=address, params=params)
        logger.info(f"返回结果: {req}")
        response_json = req.json()
        logger.info(f"返回结果1: {response_json}")
        req_data = response_json.get('data', {})
        logger.info(f"返回数据: {req_data}")
        if req_data is None or 'userid' not in req_data:
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION)
        else:
            staff_no = req_data.get('userid')
        logger.info(f"staff_no: {staff_no}")

        if staff_no is not None:
            user_info = UserInfo.objects.filter(staff_no=staff_no, is_active=True).first()
            if not user_info:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION)

            # 生成token
            temp = str(round(
                time.time() * 1000)) + user_info.username + CommonUtil().get_random_secure_key()
            token = CommonUtil().str2Md5(temp)
            create_time = timezone.now()
            user_token = UserToken.objects.filter(user=user_info.username, channel=1).order_by('-create_time').first()

            if user_token:  # 存在token，更新原有的
                UserToken.objects.filter(id=user_token.id, user=user_info.username, channel=1).update(token=token, create_time=create_time)
            else:  # 不存在，创建新的token
                new_user_token = UserToken(user=user_info.username, token=token, channel=1, create_time=create_time, ip=ip)
                new_user_token.save()
            info_message = {"user_id": user_info.user_id, "token": token, "user_name": user_info.username,
                            "role_id": user_info.role_id, "chinese_name": user_info.chinese_name,
                            "staff_no": user_info.staff_no}
            logger.info(f"user_info的数据: {info_message}")
            logger.info(f"user_info的类型: {type(info_message)}")

            return ResponseUtils.return_success("获取用户信息成功", info_message)

        else:
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION)

    @action(methods=['GET'], url_path='menu', detail=False)
    def get_menu(self, request):
        if request.method == "GET":
            user_id = request.META.get("HTTP_USERID")
            user_info = UserInfo.objects.get(user_id=user_id)
            menu_list = userMenu(int(user_info.role_id))
        return ResponseUtils.return_success("获取菜单列表成功", menu_list)

    @action(methods=['GET'], url_path='index', detail=False)
    def get_index(self, request):
        data_dict = CommonUtil().getCount()
        return ResponseUtils.return_success("操作成功", data_dict)

    @action(methods=['GET'], url_path='users', detail=False)
    def get_user_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            user_name = request.GET.get("userName", "")
            user_id = request.GET.get("userId", "")
            page_num = request.GET["pagenum"]
            page_size = request.GET["pagesize"]
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        user_list = UserInfo.objects.filter(is_active=True)
        if user_id and user_name:
            user_list = user_list.filter(user_id=user_id).filter(
                username__icontains=user_name).order_by('-update_time')
        elif user_id:
            user_list = user_list.filter(user_id=user_id).order_by('-update_time')
        elif user_name:
            user_list = user_list.filter(username__icontains=user_name).order_by('-update_time')
        else:
            user_list = user_list.all().order_by('-update_time')
        user_list_dict = {"total": len(user_list), "pagenum": page_num, "pagesize": page_size, "userName": user_name,
                          "userId": user_id}
        users_list = []
        for user in user_list:
            users_dict = {"id": user.user_id, "username": user.username, "mobile": user.mobile, "email": user.email,
                          "chinese_name": user.chinese_name,"staff_no": user.staff_no, "create_time": user.create_time,
                          "mg_state": str2Boolean(user.user_status)}
            if RoleInfo.objects.filter(role_id=user.role_id):
                users_dict["role_name"] = RoleInfo.objects.get(role_id=user.role_id).role_name
            else:
                users_dict["role_name"] = "未授权"
            users_list.append(users_dict)
        paginator = Paginator(users_list, page_size)
        data = paginator.page(page_num)
        user_list_dict["users"] = data.object_list
        content = "超级管理员 %s 查询了用户列表，查询条件：用户ID=%s，用户名=%s" % (username, user_id, user_name)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("查询成功", user_list_dict)

    @action(methods=['GET'], url_path='info', detail=False)
    def get_info(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        if 'HTTP_X_FORWARDED_FOR' in request.META:
            ipaddress = request.META['HTTP_X_FORWARDED_FOR']
        else:
            ipaddress = request.META['REMOTE_ADDR']
        if request.method == "GET":
            try:
                type = request.GET['type']
                if type not in GetModelInfo().info_list:
                    return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, type)
            except Exception as e:
                errorInfo = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{errorInfo}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, errorInfo)
            content = "来自IP：%s的用户 %s 请求获取全部%s信息" % (ipaddress, username, type)
            # 日志记录
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("获取成功", GetModelInfo().getInfo(type))

    # 获取编辑时的信息 modify/info : getOnlyInfo
    @action(methods=['GET'], detail=False)
    def get_base_info(self, request):
        """
         获取接口、WEB、场景等详情信息
        :param request:  type: 类型 必填
        :return:
        """
        try:
            id = request.GET['id']
            type = request.GET['type']
            # # 判断get请求中是否包含分页参数，如有，则获取值
            # if 'pagenum' in request.GET:
            #     page = request.GET['pagenum']
            # if 'pagesize' in request.GET:
            #     limit = request.GET['pagesize']
            # 判断get请求中是否包含scene_relation_id参数，如有，则获取值
            if 'scene_relation_id' in request.GET:
                scene_relation_id = request.GET['scene_relation_id']
            if type not in ["WEB", "API", "Android", "SCENE", "Chunks", "Performance", "Task", "Project"]:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, "type参数异常：%s" % type)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        route_dict = {
            "API": GetModelInfo().getOnlyCase,
            "SCENE": GetModelInfo().getOnlyScene,
            "WEB": GetModelInfo().getOnlyWebCase,
            "Android": GetModelInfo().getOnlyAppCase,
            "Chunks": GetModelInfo().getOnlyChunk,
            "Performance": GetModelInfo().getOnlyPerformance,
            "Task": GetModelInfo().getOnlyTask,
            "Project": GetModelInfo().getOnlyProject
        }
        if type in route_dict:
            # 判断get请求中是否包含scene_relation_id参数
            if 'scene_relation_id' in request.GET:
                data = route_dict[type](id, scene_relation_id)
            # # 判断如果是任务场景任务，则传入分页参数
            # elif type == "Task":
            #     data = route_dict[type](id, page, limit)
            else:
                data = route_dict[type](id)
        else:
            data = None
        return ResponseUtils.return_success("数据获取成功", data)

    # 注意，如果POST请求报404，可能是请求地址最后少了一个反斜杠 “/"
    @action(methods=['POST'], url_path='userAdd', detail=False)
    def user_add(self, request):
        creater = UserInfo.objects.get(user_id=request.META.get("HTTP_USERID")).username
        if request.method == "POST":
            temp = json.loads(request.body)
            try:
                username = temp["username"]
                password = temp["password"]
                email = temp["email"]
                mobile = temp.get("mobile")
                chinese_name = temp.get("chinese_name", "")
                staff_no = temp.get("staff_no")
            except Exception as e:
                error_info = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{error_info}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
            user = UserInfo.objects.filter(username=username, is_active=True)
            if user:  # 用户已存在
                return ResponseUtils.return_fail(AppErrors.ERROR_USER_IS_EXISTS)
            create_time = timezone.now()
            user_info = UserInfo(username=username, password=password, email=email, mobile=mobile, creater=creater,
                                 user_status='1', update_person=creater, create_time=create_time,
                                 chinese_name=chinese_name, staff_no=staff_no,
                                 update_time=create_time)
            user_info.save()
            # 日志记录
            content = "超级管理员 %s 添加用户 %s 成功" % (username, user_info.username)
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            return ResponseUtils.return_success("用户创建成功")

    # 校验密码复杂度
    def validate_password_complexity(self, password):
        """
        校验密码复杂度：
        - 长度 6-12 位
        - 必须同时包含字母和数字
        """
        if len(password) < 6 or len(password) > 12:
            raise ValueError("密码必须长度为6-12位,包含字母和数字的组合")

        # 检查是否同时包含字母和数字
        has_letter = re.search(r'[a-zA-Z]', password)
        has_digit = re.search(r'[0-9]', password)

        if not (has_letter and has_digit):
            raise ValueError("密码必须长度为6-12位,包含字母和数字的组合")

    # 修改用户密码
    @action(methods=['POST'], detail=False)
    def user_password_reset(self, request):
        temp = json.loads(request.body)
        try:
            username = temp.get("username")
            password = temp.get("password")
            new_password = temp.get("new_password")
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        # 校验新密码复杂度
        try:
            self.validate_password_complexity(new_password)
        except ValueError as e:
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, str(e))

        user = UserInfo.objects.filter(username=username, password=password, is_active=True).first()
        if user:
            user.password = new_password
            user.update_time = timezone.now()
            user.save()
        else:
            return ResponseUtils.return_fail(AppErrors.ERROR_USER_NAME_OR_PWD)
        return ResponseUtils.return_success("操作成功")

    @action(methods=['PUT'], url_path='user', detail=True)
    def user_update(self, request, pk=None):
        user_id = pk
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        if request.method == "PUT":  # 编辑用户
            # 提取参数
            temp = json.loads(request.body)
            try:
                email = temp["email"]
                mobile = temp["mobile"]
                chinese_name = temp.get("chinese_name", "")
                staff_no = temp.get("staff_no")
            except Exception as e:
                error_info = traceback.format_exc(limit=2)
                logger.error(f'参数异常：{error_info}')
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
            user = UserInfo.objects.get(user_id=user_id)
            # 更新数据库
            UserInfo.objects.filter(user_id=user_id).update(email=email, mobile=mobile, update_person=username,
                                                            update_time=timezone.now(), chinese_name=chinese_name,staff_no=staff_no)

            data_dict = {"id": user_id, "username": user.username, "role_id": user.role_id, "mobile": user.mobile,
                         "email": user.email, "chinese_name": user.chinese_name, "staff_no": user.staff_no}
            return ResponseUtils.return_success("用户更新成功", data_dict)

    @action(methods=['DELETE'], detail=True)
    def delete_user(self, request, pk=None):
        username = UserUtils.get_login_user(request).username
        user = UserInfo.objects.filter(user_id=pk)
        if user:
            UserInfo.objects.filter(user_id=pk).update(is_active=False, update_person=username,
                                                       update_time=timezone.now())
            return ResponseUtils.return_success("删除成功")
        else:
            return ResponseUtils.return_fail(AppErrors.ERROR_USER_IS_NOT_EXISTS)

    @action(methods=['GET'], detail=False)
    def user_data(self, request):
        """
            获取api案例维护人累计数据，前10位
        :param request: type : 必填  scene 场景数据  case 案例数据
        :return:
        """
        try:
            type = request.GET['type']
            time = request.GET.get("time", "")
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, "type 不能为空")
        if type == 'scene':
            table_name = 'scene_info'
        elif type == 'case':
            table_name = 'case_info'
        else:
            return ResponseUtils.return_fail(AppErrors.ERROR_USER_STATISTIC_TYPE)
        if time:
            if time == 'year':
                query_time = '''and ci.create_time >= date_format(curdate(), '%Y-01-01')'''
            elif time =='quarter':
                query_time = '''and ci.create_time >= makedate(year(curdate()), 1) + interval quarter(curdate())-1 quarter'''
        else:
            query_time = 'and 1 = 1'

        cursor = connection.cursor()
        cursor.execute(f'''SELECT * FROM (
             SELECT ifnull(ui.chinese_name,ui.username) username,count(1) count_num FROM user_info ui left join {table_name} ci on ui.username=ci.creater
             where ui.user_id not in(1,9,10,11) and ci.is_active=1 {query_time}
             GROUP BY ui.user_id 
            ) a ORDER BY a.count_num desc limit 10''')
        result = cursor.fetchall()
        user_data = []
        count_data = []
        for row in result:
            user_data.append(row[0])
            count_data.append(row[1])
        return ResponseUtils.return_success("查询成功", dict(user_data=user_data, count_data=count_data))
    # 上传文件
    @action(methods=['POST'], detail=False)
    def upload_file(self, request):

        timestamp = int(time.time())

        if request.method == 'POST' and 'file' in request.FILES:
            uploaded_file = request.FILES['file']
            is_template = request.POST.get('is_template', False)

            if is_template:
                # 指定固定目录
                upload_dir = os.path.join(settings.FILE_PATH, 'template_path')
            else:
                # 自动生成保存文件的目录
                upload_dir = os.path.join(settings.FILE_PATH, str(timestamp))
            # 确保目录存在
            if not os.path.exists(upload_dir):
                os.makedirs(upload_dir)

            # 获取原始文件名
            original_name = uploaded_file.name
            name, ext = os.path.splitext(original_name)

            file_path = os.path.join(upload_dir, original_name)

            # 如果文件已存在，则尝试加数字直到不冲突
            counter = 1
            while os.path.exists(file_path):
                new_name = f"{name}_{counter}{ext}"
                file_path = os.path.join(upload_dir, new_name)
                counter += 1

            with open(file_path, 'wb+') as file:
                for chunk in uploaded_file.chunks():
                    file.write(chunk)
            return ResponseUtils.return_success('上传成功',
                                                {'file_path': file_path})

class RoleInfoViewSet(viewsets.ModelViewSet):
    queryset = m.RoleInfo.objects.all()
    serializer_class = s.RoleInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    # 给用户分配角色接口
    @action(methods=['PUT'], detail=True)
    def user_role(self, request, pk=None):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            user_id = temp["id"]
            role_id = temp["rid"]
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

        role_info = RoleInfo.objects.filter(role_id=role_id)
        if (str(user_id) != pk) or not role_info:
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION)
        else:
            user = UserInfo.objects.get(user_id=user_id)
            update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            # 更新数据库
            UserInfo.objects.filter(user_id=user_id).update(role_id=role_id, update_person=username,
                                                            update_time=update_time)
            # 日志记录
            content = "超级管理员 %s 给 %s 用户分配角色：%s成功" % (username, user.username, role_info[0].role_name)
            CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
            data_dict = {"id": user_id, "username": user.username, "rid": role_id, "mobile": user.mobile,
                         "email": user.email}
            return ResponseUtils.return_success("分配角色成功", data_dict)

    # 角色列表
    @action(methods=['GET'], detail=False)
    def role_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            role_name = request.GET.get("roleName", "")
            pagenum = request.GET["pagenum"]
            pagesize = request.GET["pagesize"]
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        if role_name:
            role_list = RoleInfo.objects.filter(role_name__icontains=role_name, is_active=True).order_by('-update_time')
        else:
            role_list = RoleInfo.objects.filter(is_active=True).all().order_by('-update_time')
        role_list_dict = {"total": len(role_list), "pagenum": pagenum, "pagesize": pagesize, "roleName": role_name}
        roles_list = []
        for role in role_list:
            roles_dict = {"id": role.role_id, "roleName": role.role_name, "roleDesc": role.role_desc}
            if role.role_rights:
                print('role.role_id=====>', role.role_id)
                roles_dict["children"] = userMenu(int(role.role_id))
            else:
                roles_dict["children"] = []
            roles_list.append(roles_dict)
        paginator = Paginator(roles_list, pagesize)
        data = paginator.page(pagenum)
        role_list_dict["roles"] = data.object_list
        content = "超级管理员 %s 查询了角色列表，查询条件：角色名=%s" % (username, role_name)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("查询成功", role_list_dict)

    # 角色新增
    @action(methods=['POST'], detail=False)
    def role_add(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        temp = json.loads(request.body)
        try:
            role_name = temp["roleName"]
            role = RoleInfo.objects.filter(role_name=role_name)
            if role:  # 用户已存在
                return ResponseUtils.return_fail(AppErrors.ERROR_ROLE_IS_EXISTS)
            role_desc = temp["roleDesc"]
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        create_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        role_info = RoleInfo(role_name=role_name, role_desc=role_desc, creater=username,
                             update_person=username, create_time=create_time, update_time=create_time)
        role_info.save()
        # 日志记录
        content = "超级管理员 %s 创建角色 %s 成功" % (username, role_info.role_name)
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("角色创建成功")

    # 角色编辑或删除
    @action(methods=['PUT'], detail=True)
    def role_update(self, request, pk):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        # 提取参数
        temp = json.loads(request.body)
        try:
            role_name = temp["roleName"]
            role_desc = temp["roleDesc"]
            role = RoleInfo.objects.filter(role_name=role_name).exclude(role_id=pk)
            if role:  # 用户已存在
                return ResponseUtils.return_fail(AppErrors.ERROR_ROLE_IS_EXISTS)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        role = RoleInfo.objects.get(role_id=pk)
        # 修改前的角色信息
        role_info_befor = role.__dict__
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        # 更新数据库
        RoleInfo.objects.filter(role_id=pk).update(role_name=role_name, role_desc=role_desc,
                                                   update_person=username,
                                                   update_time=update_time)
        # 更新后的用户信息
        role_info_after = role.__dict__
        # 日志记录
        content = "超级管理员 %s 修改角色信息成功，修改前：%s，修改后：%s" % (username, role_info_befor, role_info_after)
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        data_dict = {"id": pk, "roleName": role.role_name, "roleDesc": role.role_desc}
        return ResponseUtils.return_success("角色更新成功", data_dict)

    @action(methods=['DELETE'], detail=True)
    def delete_role(self, request, pk):
        username = UserUtils.get_login_user(request).username
        users = UserInfo.objects.filter(role_id=pk, is_active=True)
        if users:
            userID_list = []
            for user in users:
                userID_list.append(user.username)
            userID_list = list(set(userID_list))
            msg = "角色已被分配给%s用户，请先解除分配后再试" % str(userID_list)
            return ResponseUtils.return_fail(AppErrors.ERROR_ROLE_IS_BIND_USER, str(userID_list))
        else:
            role = RoleInfo.objects.filter(role_id=pk)
            if role:
                RoleInfo.objects.filter(role_id=pk).update(is_active=False, update_person=username,
                                                           update_time=DateUtils.get_current_time())
                content = "超级管理员 %s 删除了角色：%s" % (username, role.__dict__)
                # 日志记录
                CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
                return ResponseUtils.return_success("删除成功")
            else:
                return ResponseUtils.return_fail(AppErrors.ERROR_ROLE_IS_NOT_EXISTS)

    # 给用户分配角色接口
    @action(methods=['POST'], detail=True)
    def role_rights(self, request, pk):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        role_id = pk
        # 提取参数
        temp = json.loads(request.body)
        try:
            role_id = temp["id"]
            rights = temp["rids"]
            if role_id != role_id:
                return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION)
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)

        role = RoleInfo.objects.get(role_id=role_id)
        update_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        # 更新数据库
        RoleInfo.objects.filter(role_id=role_id).update(role_rights=rights, update_person=username,
                                                        update_time=update_time)
        # 日志记录
        content = "超级管理员 %s 给 %s 角色授权成功，授权：%s权限" % (username, role.role_name, rights)
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("角色授权成功")


class RightInfoViewSet(viewsets.ModelViewSet):
    queryset = m.RightInfo.objects.all()
    serializer_class = s.RightInfoSerializer
    filter_fields = '__all__'
    ordering = ['-pk']

    @action(methods=['GET'], detail=False)
    def right_list(self, request):
        # 从请求头中取操作人的用户ID
        username = UserUtils.get_login_user(request).username
        try:
            type = request.GET.get("type", "list")
            pagenum = request.GET["pagenum"]
            pagesize = request.GET["pagesize"]
        except Exception as e:
            error_info = traceback.format_exc(limit=2)
            logger.error(f'参数异常：{error_info}')
            return ResponseUtils.return_fail(AppErrors.ERROR_ARGS_EXCEPTION, error_info)
        right_info = RightInfo.objects.filter(is_active=True).all()
        right_list = []
        if type == "list":
            for x in right_info:
                right_dict = {"id": x.right_id, "authName": x.right_name, "path": x.right_path,
                              "pid": x.parent_right_id,
                              "level": x.right_level}
                right_list.append(right_dict)
        else:
            for right in right_info:
                right_dict = {"id": right.right_id, "authName": right.right_name, "path": right.right_path,
                              "pid": right.parent_right_id}
                erzi_right_info = RightInfo.objects.filter(parent_right_id=right.right_id, is_active=True)
                if erzi_right_info:
                    erzi_list = []
                    for y in erzi_right_info:
                        erzi_dict = {"id": int(y.right_id), "authName": y.right_name, "path": y.right_path}
                        sunzi_right_info = RightInfo.objects.filter(parent_right_id=y.right_id, is_active=True)
                        if sunzi_right_info:
                            sunzi_list = []
                            for z in sunzi_right_info:
                                sunzi_dict = {"id": int(z.right_id), "authName": z.right_name, "path": z.right_path}
                                sunzi_list.append(sunzi_dict)
                            erzi_dict["children"] = sunzi_list
                        else:
                            erzi_dict["children"] = []
                        erzi_list.append(erzi_dict)
                    right_dict["children"] = erzi_list
                else:
                    right_dict["children"] = []
                right_list.append(right_dict)
        paginator = Paginator(right_list, pagesize)
        data = paginator.page(pagenum)
        data_dict = {"total": len(right_list), "pagenum": pagenum, "pagesize": pagesize, "rights": data.object_list}
        content = "超级管理员 %s 查询了权限列表，查询条件：类型=%s，结果=%s" % (username, type, data_dict)
        # 日志记录
        CommonUtil().recordSystemLog(username, content)  # 以系统日志类型记录到数据库
        return ResponseUtils.return_success("查询成功", data_dict)
