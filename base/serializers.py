import re

from django.db.models import Max
from rest_framework import serializers
from scaffold.restframework.utils import auto_declare_serializers

from . import models as m


class UserInfoSerializer(serializers.ModelSerializer):
    userId = serializers.ReadOnlyField(source='user_id')

    class Meta:
        model = m.UserInfo
        fields = '__all__'


class RoleInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.RoleInfo
        fields = '__all__'


class RightInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = m.RightInfo
        fields = '__all__'


# # Auto declare serializer classes from models.
auto_declare_serializers(m, locals())
